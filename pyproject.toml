[build-system]
requires = [
  "setuptools >= 40.9.0, < 70.0",
  "wheel",
]
build-backend = "setuptools.build_meta"

[flake8]
exclude = '''
'''
max-line-length = 99

[tool.isort]
known_third_party = ["data", "adovehicle", "wandb"]
line_length = 99
profile = "black"
skip_glob = "*_pb2.py"

[tool.black]
line-length = 99
target-version = ["py311"]
extend-exclude = '''
()
'''

[tool.pytest.ini_options]
#addopts = "--typeguard-packages=hid_common"
testpaths = [
    "scripts/",
    "util/",
    "model_zoo/",
    "data_sources/",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "no_parallel: marks tests as needing to be run with a single test worker",
]
