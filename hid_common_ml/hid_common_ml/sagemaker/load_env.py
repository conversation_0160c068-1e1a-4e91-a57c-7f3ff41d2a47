# Load environment variables
import os
from pathlib import Path

from dotenv import dotenv_values


def load_env(repo_env_path: Path, sagemaker_env_path: Path, remove_aws_profile=False):
    """This will load env files, but not overriding existing system ENV
    .env will override .env.sagemaker
    """
    assert repo_env_path.exists()
    env_sagemaker = dotenv_values(sagemaker_env_path)
    # .env will overwrite .env.sagemaker because .env is not tracked by git, so people can change it freely
    env1 = dotenv_values(repo_env_path)

    # Merge the dictionaries: values in env1 take precedence over env_sagemaker
    merged_env = {**env_sagemaker, **env1}

    system_env = os.environ.copy()
    for key, value in merged_env.items():
        if key not in system_env:
            os.environ.setdefault(key, value)
    if remove_aws_profile:
        os.environ.pop("AWS_PROFILE", None)
        os.environ.pop("AWS_ACCESS_KEY_ID", None)
        os.environ.pop("AWS_SECRET_ACCESS_KEY", None)


def check_env_var(var_name):
    warning = "Follow the instructions in the hid_common/hid_common_ml/sagemaker/README.md to set the environment variables in .env"
    assert os.environ.get(var_name) is not None, f"{warning}\n{var_name} is not set"


def get_training_meta(repo_dir: Path = None, sagemaker_dir: Path = None):
    """
    This function will load the environment variables from the .env and .env.sagemaker files.
    It will also check that the required environment variables are set.
    It will return a dictionary with the training job name and a boolean indicating if the script is running on sagemaker.

    """
    if not repo_dir:
        # Assume this script is run from the repo root
        repo_dir = Path(".").resolve()
    if sagemaker_dir is None:
        sagemaker_dir = Path(os.path.dirname(os.path.abspath(__file__))).resolve()
    training_job_name = os.environ.get("TRAINING_JOB_NAME", None)
    on_sagemaker = training_job_name is not None

    # Load env files
    load_env(repo_dir / ".env", sagemaker_dir / ".env.sagemaker", remove_aws_profile=on_sagemaker)

    if on_sagemaker:
        check_env_var("MY_PROJECT")
        check_env_var("WANDB_API_KEY")

    if on_sagemaker:
        output_dir = (
            Path(os.environ.get("OUTPUT_DIR")) / f"model_outputs/{os.environ.get('MY_PROJECT')}/"
        )
    else:
        output_dir = Path(os.path.expanduser(f"~/model_outputs/{os.environ.get('MY_PROJECT')}/"))
    output_dir.mkdir(parents=True, exist_ok=True)

    # Set the wandb output directory so they don't pollute the repo and get uploaded properly by sagemaker
    os.environ["WANDB_DIR"] = str(output_dir)
    os.environ["WANDB_ARTIFACT_DIR"] = str(output_dir / "wandb_artifacts")

    run_meta = {
        "training_job_name": training_job_name,
        "on_sagemaker": on_sagemaker,
        "output_dir": output_dir,
    }
    return run_meta
