echo arguments:
for arg in "$@"; do
    echo arg:   "$arg"
done

echo df
df -h

echo
echo
echo lsblk
lsblk
echo
echo
echo
unset AWS_PROFILE
unset AWS_ACCESS_KEY_ID
unset AWS_SECRET_ACCESS_KEY


aws sts get-caller-identity
set -x
export OUTPUT_DIR=/opt/ml/output

echo "RUNNING - which python"
which python
# This code assumes this is at the repo root.
echo pwd, `pwd`

HID_COMMON_ML_DIR=hid_common/hid_common_ml
echo "RUNNING - setup"

pip install --upgrade-strategy only-if-needed -e $HID_COMMON_ML_DIR

# Run setup script if provided
if [ ! -z "$MY_TRAINING_SETUP_CMD" ]; then
    echo "RUNNING - Setup command: $MY_TRAINING_SETUP_CMD"
    $MY_TRAINING_SETUP_CMD
fi
# Assert hid_common_ml is installed
pip list | grep -q hid_common_ml || { echo "hid_common_ml is not installed.\n Need to pip install $HID_COMMON_ML_DIR/"; exit 1; }

# This is needed to run git command on sagemaker
git config --global --add safe.directory /opt/ml/code
python $HID_COMMON_ML_DIR/hid_common_ml/sagemaker/prep_script.py
. export_env.sh


# Run python and passthrough input arguments
echo "RUNNING - $MY_TRAINING_CMD $MY_TRAINING_SCRIPT $MY_TRAINING_ARGS"
$MY_TRAINING_CMD $MY_TRAINING_SCRIPT $MY_TRAINING_ARGS


if [ ! -z "$MY_POST_TRAINING_SCRIPT" ]; then
    echo "RUNNING - Post training script: $MY_POST_TRAINING_SCRIPT"
    python -u $MY_POST_TRAINING_SCRIPT
fi
