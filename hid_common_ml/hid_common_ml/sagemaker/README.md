# Training on Sagemaker
This library is an adapter to run training scripts on AWS SageMaker
* SageMaker AI is an umbrella product that includes a ton of stuff. 
  * SageMaker Training is a managed training service, which is what we are talking about this in context.
* Workflow of SageMaker Training
  * package your local repo dir and upload it to s3. 
  * Download a docker image you specify to the target instance
  * Download the packaged repo dir from s3 to the docker image
  * Run the script you specify
  * Upload the output to s3


# Assumptions
* Your project uses wandb
* All paths are relative to repo_root; The run command should be runnable from repo_root.
* The hid_common repo is directly under the repo_root: `[repo_root]/hid_common`
* Argument priority high to low: `--run-args  -> [repo_root]/.env -> .env.sagemaker`
  * You have a .env file at repo root, see details in setup
  * `.env.sagemaker` shouldn't be changed, customize your training by setting `--run-args` or `.env`
  * The default training image is `pytorch2.5.1-gpu-py311-cu124-ubuntu22.04`
* Sage<PERSON> will package your entire repo and upload it to s3 and run training using those files. So your local dir can't be big
* See [submitted training jobs here](https://us-east-1.console.aws.amazon.com/sagemaker/home?region=us-east-1#/jobs)
* You are using AWS_PROFILE=rad, but can be override in .env 

# Setup
* Add hid_common as your gitsubmodule
  * `git <NAME_EMAIL>:tri-projects/hid_common.git && git submodule init && git submodule update`
* Install this library, `pip install -e hid_common/hid_common_ml`
  * Do this as part of your repo setup, like put it in requirement.txt or conda env
* Open `hid_common/hid_common_ml/sagemaker/.env.sagemaker` and follow the instruction there to create/update `.env` file
* If you need to install dependencies/download data before training starts, write a bash script to run any setup you need. And put the path in MY_TRAINING_SETUP_CMD
  * The `pwd` would be in the root dir of your repo
* Import and run `hid_common_ml.sagemaker.load_env.get_training_meta()` to get the output_dir at the start of your run. The output dir on local runs is set to ~/model_outputs/[your project name]. And wandb files will be saved there as well
  * Do this when you run locally as well, so your local repo is free of any run output/artifacts.

# Set arguments
* training arguments passed to training:
* Training script, which python script to run training:
  * `--training-script my_script.py` or `MY_TRAINING_SCRIPT=my_script.py`
* Training arguments:
  * `--training-args --batch-size 10`
* Setup script, used to install any  dependencies
  * `--training-setup-script my_setup.sh` or `MY_TRAINING_SETUP_CMD=my_setup.sh`
* Docker image used to run on SageMaker
  * `--training-image docker.image.name` or `MY_TRAINING_IMAGE=docker.image.name`
* Entry script:
  * `--entry-script my_entry.sh` or `MY_ENTRY_SCRIPT=my_entry.sh`
* Post script
  * 
# Launch SageMaker Example:
* Run training with args:
  * ```MY_TRAINING_SETUP_CMD='sh my_setup_script.sh' python hid_common/hid_common_ml/hid_common_ml/sagemaker/launch_sagemaker.py --training-script my_train.py --training-args '--batch-size 10 --num-epochs 20' --run-name name-must-not-contain-underscore --max-training-time 24 --instance-type ml.g6.2xlarge```
* Run training with torchrun using 4 gpus
  * ```MY_TRAINING_CMD='torchrun  --standalone     --nnodes=1    --nproc-per-node=4 ' MY_TRAINING_SETUP_CMD='sh my_setup_script.sh' python hid_common/hid_common_ml/sagemaker/launch_sagemaker.py --training-script my_ddp_train.py --training-args '--batch-size 10 --num-epochs 20' --run-name ddp-test --max-training-time 24 --instance-type ml.g6.12xlarge```