#! /bin/bash
set -x

# Note double check which version of pytorch python uses when training after installing hail-datasets
#  For example, hail-datasets tried to install a new CPU only torch when I was testing. Thus added --no-deps
# pip install --upgrade-strategy only-if-needed --no-deps -e hail-datasets/ . --config-settings editable_mode=compat

# Download data if needed, example:
# aws s3 cp --only-show-errors --recursive s3://bucket .