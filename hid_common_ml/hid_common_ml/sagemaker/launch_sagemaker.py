"""
Luanch a training job on SageMaker.
1. Do not modify .env.sagemaker, it is tracked by git.
2. Modify .env file to set the environment variables.
    Some of the config can be set in the .env file, like MY_ENTRY_SCRIPT, MY_TRAINING_SCRIPT


Example:
    MY_TRAINING_SCRIPT=train.py python hid_foundation_model/aws/launcher_sagemaker.py \
        --training-args '--batch-size 20 --num-epochs 10' \
        --run-name 'my_run'
"""
import argparse
import logging
import os
from pathlib import Path

import boto3
import sagemaker
from sagemaker.estimator import Estimator

from hid_common_ml.sagemaker.load_env import get_training_meta
import urllib
# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")


THIS_SCRIPT_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
REPO_DIR = Path(".").resolve()

run_meta = get_training_meta(REPO_DIR, THIS_SCRIPT_DIR)

MY_EMAIL = os.environ.get("MY_EMAIL", "")
MY_PROJECT = os.environ.get("MY_PROJECT", "")
MY_TRAINING_CMD = os.environ.get("MY_TRAINING_CMD", "")
MY_TRAINING_SCRIPT = os.environ.get("MY_TRAINING_SCRIPT", "")
MY_ENTRY_SCRIPT = os.environ.get("MY_ENTRY_SCRIPT", "")
MY_TRAINING_SETUP_CMD = os.environ.get("MY_TRAINING_SETUP_CMD", "")
MY_TRAINING_IMAGE = os.environ.get("MY_TRAINING_IMAGE", "")
MY_POST_TRAINING_CMD = os.environ.get("MY_POST_TRAINING_CMD", "")
AWS_SAGEMAKER_ROLE = os.environ["AWS_SAGEMAKER_ROLE"]


def parse_arguments():
    parser = argparse.ArgumentParser()
    parser.add_argument("-p", "--project", type=str, default=MY_PROJECT)
    parser.add_argument("-e", "--email", type=str, default=MY_EMAIL)
    parser.add_argument(
        "-a",
        "--training-args",
        type=str,
        default="",
        help="Additional arguments passed to the training script ",
    )
    parser.add_argument(
        "--entry-script",
        type=str,
        default=MY_ENTRY_SCRIPT,
        help="The entry point of SageMaker instance, should be bash script. ",
    )
    parser.add_argument(
        "-t",
        "--training-script",
        type=str,
        default=MY_TRAINING_SCRIPT,
        help="The script to start the training. ",
    )

    parser.add_argument(
        "--training-cmd",
        type=str,
        default=MY_TRAINING_CMD,
        help="The command to start the training. Example: 'python -u' or 'torchrun' or 'accelerate' ",
    )
    parser.add_argument(
        "--training-setup-cmd",
        type=str,
        default=MY_TRAINING_SETUP_CMD,
        help="The cmd to run any setup you need. Example: 'bash install_dep.sh' ",
    )
    parser.add_argument(
        "--training-image",
        type=str,
        default=MY_TRAINING_IMAGE,
        help="The docker image to run the training on. ",
    )
    parser.add_argument(
        "--training-post-training-cmd",
        type=str,
        default=MY_POST_TRAINING_CMD,
        help="The post training cmd to run. Example: --training-post-training-cmd 'bash post.sh' ",
    )
    parser.add_argument("-n", "--run-name", type=str, default="", help="")
    parser.add_argument(
        "--max-training-time", type=int, default=24, help="The maximum training time, in hours."
    )
    parser.add_argument(
        "--instance-type",
        type=str,
        default="ml.g6.2xlarge",
        help="Which instance type to run. SageMaker instances start with 'ml.' ",
    )
    parser.add_argument(
        "--volume-size", type=int, default=100, help="The size of the extra volume in Gib."
    )
    result = parser.parse_args()
    return vars(result)


def check_vars(vars, args):
    for k in vars:
        assert args[k], f"{k} can't be empty, set it in --arguments or environment variables."


def submit_training_job(args, job_name, instance_type, instance_count=1, volume_size=100):
    """Submit a training job to SageMaker."""
    rad_session = boto3.session.Session(profile_name=os.environ["AWS_PROFILE"])
    sagemaker_session = sagemaker.Session(rad_session)
    role = AWS_SAGEMAKER_ROLE
    print(args)

    env_vars = {"JOB_NAME": job_name}
    env_vars["MY_TRAINING_CMD"] = args["training_cmd"]
    env_vars["MY_TRAINING_SCRIPT"] = args["training_script"]
    env_vars["MY_TRAINING_ARGS"] = args["training_args"]
    env_vars["MY_TRAINING_SETUP_CMD"] = args["training_setup_cmd"]
    env_vars["MY_TRAINING_IMAGE"] = args["training_image"]
    env_vars["MY_POST_TRAINING_CMD"] = args["training_post_training_cmd"]
    check_vars(["training_cmd", "training_script", "training_setup_cmd", "training_image"], args)

    # Upload datasets to S3
    del os.environ["AWS_PROFILE"]
    estimator = Estimator(
        py_version="py311",
        entry_point=args["entry_script"],
        source_dir=str(REPO_DIR),
        instance_type=instance_type,
        instance_count=instance_count,
        role=role,
        image_uri=args["training_image"],
        volume_size=volume_size,
        max_run=args["max_training_time"] * 60 * 60,  # in seconds
        base_job_name=job_name,
        tags=[
            {"Key": "Project", "Value": args["project"]},
            {"Key": "Email", "Value": args["email"]},
        ],
        sagemaker_session=sagemaker_session,
        environment=env_vars,
        hyperparameters={'test_param_1':'aaa','test-param-2':2,'test-3': [1,2,3]},
    )

    estimator.fit(wait=False)
    job = estimator.latest_training_job
    client = estimator.sagemaker_session.sagemaker_client

    response = client.describe_training_job(TrainingJobName=job.name)

    # Get the encoded S3 URI to the source directory
    source_dir_s3_path = response['HyperParameters']['sagemaker_submit_directory']






if __name__ == "__main__":
    print("MY_TRAINING_SCRIPT", MY_TRAINING_SCRIPT)

    args = parse_arguments()

    submit_training_job(
        args,
        job_name=args["run_name"],
        instance_type=args["instance_type"],
        volume_size=args["volume_size"],
    )
