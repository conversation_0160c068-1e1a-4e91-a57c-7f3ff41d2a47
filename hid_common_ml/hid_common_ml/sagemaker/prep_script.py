import os
from pathlib import Path

from hid_common_ml.sagemaker.load_env import get_training_meta

run_meta = get_training_meta()

# Path to the bash configuration file
export_env_path = Path("export_env.sh")


def make_export_env_line(line, var_name, var_value=None):
    if var_value is None:
        var_value = os.environ.get(var_name, "")
    line += f"export {var_name}='{var_value}'\n"
    return line


# Create the export command string
export_line = f"\n# Added by hid_common_ml/sagemaker/prep_script.py\n"
export_line = make_export_env_line(export_line, "HF_TOKEN")
export_line = make_export_env_line(export_line, "WANDB_API_KEY")
# This should be consistent with the wandb_dir in load_env.py
export_line = make_export_env_line(export_line, "WANDB_DIR", run_meta["output_dir"])
export_line = make_export_env_line(
    export_line, "WANDB_ARTIFACT_DIR", run_meta["output_dir"] / "wandb_artifacts"
)

# Append the export command to the file
with open(export_env_path, "w") as file:
    file.write(export_line)
