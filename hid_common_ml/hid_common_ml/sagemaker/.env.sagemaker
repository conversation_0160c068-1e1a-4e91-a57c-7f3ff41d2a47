# NOTE, do not edit this file.
# Any changes needed, do it in [repo_root]/.env, which will override settings in this file

# [ACTION REQUIRED] Copy these into [repo_root]/.env and fill
# HF_TOKEN=xxxxxxxxxx  # if needed
# WANDB_API_KEY=xxxxxxxxxx
# MY_EMAIL=<EMAIL>
# MY_PROJECT=xxx


AWS_SAGEMAKER_ROLE=arn:aws:iam::401298207814:role/SageMakerAccessRole
AWS_REGION=us-east-1
AWS_PROFILE=rad
AWS_DEFAULT_REGION=us-east-1


# Override this in case you run "torchrun" or "accelerate"
MY_TRAINING_CMD="python -u"
MY_ENTRY_SCRIPT=hid_common/hid_common_ml/hid_common_ml/sagemaker/start_training.sh
MY_TRAINING_SETUP_CMD=hid_common/hid_common_ml/hid_common_ml/sagemaker/training_setup.sh
MY_TRAINING_IMAGE=763104351884.dkr.ecr.us-east-1.amazonaws.com/pytorch-training:2.5.1-gpu-py311-cu124-ubuntu22.04-sagemaker