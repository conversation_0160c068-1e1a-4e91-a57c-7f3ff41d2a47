import os
from pathlib import Path

import pytest

from hid_common_ml.sagemaker.load_env import get_training_meta, load_env


def test_load_env(monkeypatch, tmp_path):
    # Create a temporary repository directory
    repo_dir = tmp_path / "repo"
    repo_dir.mkdir()

    # Create .env.sagemaker file with two variables
    sagemaker_env = repo_dir / ".env.sagemaker"
    sagemaker_env.write_text("VAR1=from_sagemaker\nVAR2=from_sagemaker")
    env_file = repo_dir / ".env"
    env_file.write_text("VAR1=from_env\nVAR3=from_env")

    # Call load_env with the temporary repository directory
    load_env(env_file, sagemaker_env)

    # Verify that the environment variables have been loaded correctly
    assert os.environ.get("VAR1") == "from_env"
    # Ensure these variables are not preset in the environment.
    monkeypatch.delenv("VAR1", raising=False)
    monkeypatch.delenv("VAR2", raising=False)
    monkeypatch.delenv("VAR3", raising=False)

    # Call load_env passing repo_dir as a string
    load_env(env_file, sagemaker_env)

    # Check that:
    # - VAR1 comes from .env (overriding .env.sagemaker)
    # - VAR2 comes from .env.sagemaker
    # - VAR3 comes from .env
    assert os.environ.get("VAR1") == "from_env"
    assert os.environ.get("VAR2") == "from_sagemaker"
    assert os.environ.get("VAR3") == "from_env"

    # Test that preexisting variables are not overridden:
    monkeypatch.setenv("VAR1", "preexisting")
    load_env(env_file, sagemaker_env)

    # VAR1 should remain unchanged because it was already in the environment.
    assert os.environ.get("VAR1") == "preexisting"


def test_remove_aws_profile(monkeypatch, tmp_path):
    # Create a temporary repo directory with empty .env files
    repo_dir = tmp_path / "repo"
    repo_dir.mkdir()
    env_file = repo_dir / ".env"
    env_file.write_text("")
    sagemaker_env = repo_dir / ".env.sagemaker"
    sagemaker_env.write_text("")

    # Set AWS-related environment variables before calling load_env
    monkeypatch.setenv("AWS_PROFILE", "test_profile")
    monkeypatch.setenv("AWS_ACCESS_KEY_ID", "test_access_key")
    monkeypatch.setenv("AWS_SECRET_ACCESS_KEY", "test_secret_key")

    load_env(env_file, sagemaker_env, remove_aws_profile=True)

    # Verify that the AWS variables have been removed
    assert "AWS_PROFILE" not in os.environ
    assert "AWS_ACCESS_KEY_ID" not in os.environ
    assert "AWS_SECRET_ACCESS_KEY" not in os.environ


def test_keep_aws_profile(monkeypatch, tmp_path):
    # Create a temporary repo directory with empty .env files
    repo_dir = tmp_path / "repo"
    repo_dir.mkdir()
    env_file = repo_dir / ".env"
    env_file.write_text("")
    sagemaker_env = repo_dir / ".env.sagemaker"
    sagemaker_env.write_text("")

    # Set AWS-related environment variables before calling load_env
    monkeypatch.setenv("AWS_PROFILE", "test_profile")
    monkeypatch.setenv("AWS_ACCESS_KEY_ID", "test_access_key")
    monkeypatch.setenv("AWS_SECRET_ACCESS_KEY", "test_secret_key")

    load_env(env_file, sagemaker_env, remove_aws_profile=False)

    # Verify that the AWS variables remain in the environment
    assert os.environ.get("AWS_PROFILE") == "test_profile"
    assert os.environ.get("AWS_ACCESS_KEY_ID") == "test_access_key"
    assert os.environ.get("AWS_SECRET_ACCESS_KEY") == "test_secret_key"


def test_repo_dir_as_path(monkeypatch, tmp_path):
    # Test that load_env accepts a Path object for the repo_dir.
    repo_dir = tmp_path / "repo"
    repo_dir.mkdir()
    env_file = repo_dir / ".env"
    env_file.write_text("")
    sagemaker_env = repo_dir / ".env.sagemaker"
    sagemaker_env.write_text("")
    # Create .env files where .env overrides .env.sagemaker
    sagemaker_env.write_text("VAR=from_sagemaker")
    env_file.write_text("VAR=from_env")

    monkeypatch.delenv("VAR", raising=False)
    # Pass repo_dir as a Path object instead of a string
    load_env(env_file, sagemaker_env)
    assert os.environ.get("VAR") == "from_env"


def test_get_training_meta_local(monkeypatch, tmp_path):
    # Create temporary directories
    repo_dir = tmp_path / "repo"
    repo_dir.mkdir()
    sagemaker_dir = tmp_path / "sagemaker"
    sagemaker_dir.mkdir()

    # Create .env files with required variables
    (repo_dir / ".env").write_text(
        "MY_PROJECT=test_project\nWANDB_API_KEY=test_key\nMY_TRAINING_SCRIPT=test_script.py"
    )
    (sagemaker_dir / ".env.sagemaker").write_text("")

    # Set up environment variables
    monkeypatch.setenv("REPO_DIR", str(repo_dir))
    monkeypatch.delenv("TRAINING_JOB_NAME", raising=False)
    monkeypatch.delenv("OUTPUT_DIR", raising=False)

    # Get training metadata
    meta = get_training_meta(repo_dir, sagemaker_dir)

    # Verify the metadata
    assert meta["training_job_name"] is None
    assert meta["on_sagemaker"] is False
    assert meta["output_dir"] == repo_dir / "model_outputs" / "test_project"


def test_get_training_meta_sagemaker(monkeypatch, tmp_path):
    # Create temporary directories
    repo_dir = tmp_path / "repo"
    repo_dir.mkdir()
    sagemaker_dir = tmp_path / "sagemaker"
    sagemaker_dir.mkdir()

    # Create .env files with required variables
    (repo_dir / ".env").write_text(
        "MY_PROJECT=test_project\nWANDB_API_KEY=test_key\nMY_TRAINING_SCRIPT=test_script.py"
    )
    (sagemaker_dir / ".env.sagemaker").write_text("")

    # Set up environment variables for SageMaker environment
    monkeypatch.setenv("TRAINING_JOB_NAME", "test-job")
    monkeypatch.setenv("OUTPUT_DIR", "/opt/ml/model")
    monkeypatch.setenv("MY_PROJECT", "test_project")

    # Get training metadata
    meta = get_training_meta(repo_dir, sagemaker_dir)

    # Verify the metadata
    assert meta["training_job_name"] == "test-job"
    assert meta["on_sagemaker"] is True
    assert meta["output_dir"] == Path("/opt/ml/model")


def test_get_training_meta_missing_env_vars(monkeypatch, tmp_path):
    # Create temporary directories
    repo_dir = tmp_path / "repo"
    repo_dir.mkdir()
    sagemaker_dir = tmp_path / "sagemaker"
    sagemaker_dir.mkdir()

    # Create empty .env files
    (repo_dir / ".env").write_text("")
    (sagemaker_dir / ".env.sagemaker").write_text("")

    # Remove required environment variables
    monkeypatch.delenv("MY_PROJECT", raising=False)
    monkeypatch.delenv("WANDB_API_KEY", raising=False)
    monkeypatch.delenv("MY_TRAINING_SCRIPT", raising=False)

    # Test that missing environment variables raise an assertion error
    with pytest.raises(AssertionError) as exc_info:
        get_training_meta(repo_dir, sagemaker_dir)
    assert "MY_PROJECT is not set" in str(exc_info.value)
