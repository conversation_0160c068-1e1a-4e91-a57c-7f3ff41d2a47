[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["."]

[project]
name = "hid_common_ml"
version = "0.1.0"
description = "HID Common ML Package"
readme = "README.md"
requires-python = ">=3.6"

authors = [
    {name = "HAIL"}
]
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]

dependencies=[
"python-dotenv  >=0.19.0",
"sagemaker >=2.190.0",
"boto3 >=1.26.0" ,
"pytest >=6.0.0",
]

[flake8]
exclude = '''
'''
max-line-length = 99

[tool.isort]
known_third_party = ["data", "adovehicle", "wandb"]
line_length = 99
profile = "black"
skip_glob = "*_pb2.py"

[tool.black]
line-length = 99
target-version = ["py311"]
extend-exclude = '''
()
'''