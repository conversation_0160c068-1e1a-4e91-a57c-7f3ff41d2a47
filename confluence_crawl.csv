page_id,depth_link,depth_child,via,title,url,parent_id,labels,last_updated,status,text
**********,0,0,seed,Compact Simulators,https://toyotaresearchinstitute.atlassian.net/wiki/spaces/HAIL/pages/**********/Compact+Simulators,,,2025-07-29T19:55:03.237Z,current,"This section contains documentation pertaining to the setup and use of the compact simulator study platform. General Compact Sim information CompactSim Locations CAM - Sim1-4, 7 HQ   - Sim5-6 IP addresses Sim1: ************ Sim2: ************ Sim3: ************ Sim4: ************ Sim5: ************ Sim6: ************ Sim7: ************"
2977660964,0,1,child,Hardware List,https://toyotaresearchinstitute.atlassian.net/wiki/spaces/HAIL/pages/2977660964/Hardware+List,**********,,2024-09-19T12:42:30.462Z,current,Fanatec wheel base 25 nm Fanatec Wheel Rim R330 V2 ClubSport Pedals V3 inverted TR80 racing cockpit with TR One Fanatec DD mount with chair and monitor stand Monitor
2978414610,0,1,child,Setup,https://toyotaresearchinstitute.atlassian.net/wiki/spaces/HAIL/pages/2978414610/Setup,**********,,2024-09-19T12:30:09.319Z,current,"Fanatec Instructions Unpack and assemble the steering wheel, xbox hub, and quick disconnect according to the instructions in the respective boxes. Plug the assembled Fanatec wheel into the wheel base. Plug in power, the e-stop/power button, and the usb cable to the wheel base On a windows machine install the fanatec driver Plug the wheel into the windows computer, turn on the wheel, and update the firmware for the wheel assembly Plug the USB cable directly into the pedals and update the firmware for the pedals Connect the pedals to the wheel base with the data cable and connect the wheel base via usb to the ubuntu machine Follow the software setup instructions for the ubuntu fanatec driver Multi Display Instructions In case of having multiple monitors with different refresh rates, please follow the following instructions: Use display port cables Make sure the order of the connections are same as shown in the picture on the left (Starting from the left, the first one is HDMI port which we are not using, the second one should be connected to Sim monitor and the other ones on the right side of the Sim port can be used for additional monitors) In Dispaly settings, change Refresh rate for the Sim monitor to be 120hz Carla Instructions Install from Source Clone motion sim workspace Follow setup instructions Try launching motion-sim with the following command from src/dsim-grpc/src/experimental/ : ../../../../tools/bazelisk run //experimental/carla_coordinator/runtime:runner -- --local-run --ros2 --joystick-type=dd2 --map=ThunderHill --start-x=-780 --start-y=560 --start-z=-7 --start-yaw=0 --visual-fps=120 --vehicle-physics-profile=tuned_2023_04_12 Run from Release Carla releases will be downloaded by the sim user, so you can just source and launch those source /opt/ros/humble/setup.bash source /opt/tri-runner-0-storage/motion-sim-0.15.1/install/setup.bash /opt/tri-runner-0-storage/motion-sim-0.15.1/bazel/experimental/carla_coordinator/runtime/runner --local-run --ros2 --joystick-type=dd2 --map=ThunderHill --start-x=-780 --start-y=560 --start-z=-7 --start-yaw=14 --visual-fps=120 --vehicle-physics-profile=tuned_2023_04_12 --log-name=foo"
**********,0,1,child,Rules of Use,https://toyotaresearchinstitute.atlassian.net/wiki/spaces/HAIL/pages/**********/Rules+of+Use,**********,,2024-09-19T12:27:23.967Z,current,"Rules for Using the Compact Sims Don’t update Ubuntu. This could break the Fanatec driver temporarily in an unexpected way Thomas will periodically (monthly?) update the compact sim machines while making sure this doesn’t break motion-sim or any of the drivers The sim user is pull-only, to discourage alterations to the studies.  Do not do any changes to the studies if logged into this account. If you need to install a package, use the sim user to do so. Development is strongly encouraged at your own workstation , the simulators themselves should be reserved for preparing and conducting studies. There should not be more than one person using a specific compact simulator at a time as that can cause ROS and other conflicts that can be difficult to identify. Before using any Sim machines (this goes for in-person and remote use), please make a calendar event for the duration you plan to use it and reserve the appropriate entry for (CompactSim)-CAM5-5TH-TrueLab-CompactSimX .  Ping #compact_sim if you run into trouble or cannot connect to a reserved machine (it may mean it’s reserved). Whenever possible and, most crucially, for extended use (e.g., user studies), please also reserve the time under the “CompactSim Use” shared calendar. When you finish using the sim user on a compact sim, make sure you commit everything you would like to keep. It is the responsibility of the user to back up any changes they’ve made to the user account reserved for studies. If you notice that there are changes that have not been committed, you can reach out to the previous user to make sure it is ok to overwrite them. Using the google calendar should help to determine who that person was. If you have a log or file that is used when running or developing a sim that you would like to access from both sim and your personal account, you should place that file in /opt/tri-runner-0-storage/study_assets/ ."
**********,0,1,child,Upload participant data to S3,https://toyotaresearchinstitute.atlassian.net/wiki/spaces/HAIL/pages/**********/Upload+participant+data+to+S3,**********,,2024-09-19T12:28:24.819Z,current,"Request access (one time only): Based on TRI-28527 b454de86-5fb3-35b4-a49f-493b7ae47334 System Jira request access from IE to hid-data-13504 account with tri-hid-data-shared-autonomy . Ensure that you have access to the account by visiting https://d-9067152ece.awsapps.com/start#/ This is also available via Okta - click on the “AWS Single Sign-On” icon: https://tri.okta.com/app/UserHome Upload data For reference, the very well documented AWS CLI is available here: cp — AWS CLI 1.29.57 Command Reference . It uses the familiar linux commands `cp`, `ls`, `mv` Access the site above, click on the account, click on `command line or programmatic access` Copy/Paste the short term credentials in a new terminal window This terminal window is now logged in to the proper account and you should be able to run the following commands to see the files/folders in the bucket: aws s3 ls s3://tri-hid-data-shared-autonomy/ Upload data with: ` --recursive ` is used for all files or objects under the specified directory ` --dryrun ` displays the operations that would be performed using the specified command without actually running them aws s3 cp --recursive <directory_to_upload> s3://tri-hid-data-shared-autonomy/23-D-01/Participants/<participant_id>/ Use of Exclude and Include Filters Currently, there is no support for the use of UNIX style wildcards in a command's path arguments. However, most commands have --exclude ""<value>"" and --include ""<value>"" parameters that can achieve the desired result. These parameters perform pattern matching to either exclude or include a particular file or object. The following pattern symbols are supported. * : Matches everything ? : Matches any single character` Any number of these parameters can be passed to a command. You can do this by providing an --exclude or --include argument multiple times, e.g. --include ""*.txt"" --include ""*.png"" . When there are multiple filters, the rule is the filters that appear later in the command take precedence over filters that appear earlier in the command. For example, if the filter parameters passed to the command were --exclude ""*"" --include ""*.txt"" All files will be excluded from the command except for files ending with .txt --include ""*.txt"" --exclude ""*"" All files will be excluded from the command."
2978414628,0,1,child,Other How-Tos,https://toyotaresearchinstitute.atlassian.net/wiki/spaces/HAIL/pages/2978414628/Other+How-Tos,**********,,2025-04-23T15:02:08.488Z,current,"Table of Content: Using existing steering wheel follower Recording Ros Bags Using existing steering wheel follower The existing steering wheel follower is designed to track a steering angle published on the topic /auto/usercontrols/status/steering_wheel. We can use this node to provide haptic feedback guiding the human driver to follow a steering command from an autonomy node by launching the follower node and then publishing its output to the aforementioned topic. Enabling the following haptic feedback, the max and min torques that can be applied to the wheel, as well as other parameters can be set online through the command line or through rqt or programmatically. Steps below: Launch carla with the mux command enabled and your chosen autonomy agent as you would normally. In a separate terminal (this may be streamlined once this is used for a study) source the motion sim release and set the ros domain id and fastRTPS profile env variables run ros2 run steering_follower steering_follower_node You can now enable haptic following with the following command ros2 param set /steering_follower_node enable_steering_follower true To disable, set the last argument to false . You can also change this by launching rqt and using the dynamic reconfigure module Recording Ros Bags This process will be helpful for creating ado car trajectories and/or line for steering follower. To record a ros bag, follow the steps below. cd to motion sim ~/motion_sim_hmi_scenarios/scripts Assure that you are in the correct branch for your study use git status or git branch to check which branch you are on If not on the correct branch, use git stash to save any unsaved changes on your current branch use git checkout <study branch name> to get to your desired branch use git status to check your branch use got fetch to sync any changes In file finder, create a folder in home to store your rosbags (ex: dissonance_logs) Run the following commands to set up your terminal source /opt/ros/humble/setup.bash
source /opt/tri-runner-0-storage/motion-sim-********/install/setup.bash
source ~/motion_sim_hmi_scenarios/install/setup.bash Run this command for when you want to record your rosbag. Note: you will have to record each trajectory in separate ros bags so be specific with your naming convention ros2 bag record -a --output ~/<folder name>/<rosbag name> -s mcap

ex: ros2 bag record -a --output ~/dissonance_logs/coin_following -s mcap Run AI coaching model Instructions for running BC model on CSIMs"
2978086936,0,1,child,Compact Sim FAQ,https://toyotaresearchinstitute.atlassian.net/wiki/spaces/HAIL/pages/2978086936/Compact+Sim+FAQ,**********,,2025-08-18T16:32:05.115Z,current,"I’m new to github.  Where do I start? Here are some resources on terms thrown around: https://unito.io/blog/guide-to-github-for-project-managers/ Here are some tutorials on how to do the common things we do: https://docs.github.com/en/get-started/quickstart/contributing-to-projects Fanatec Wheel is not detected by Carla Make sure the wheel is turned on and plugged in to the computer If the wheel is on, the led screen on the front of the base should be lit up, there should also be a glowing white button on the back of the wheel base, and the power button under the emergency stop should also be lit up Run ffcfstress -d /dev/input/by-id/usb-Fanatec_FANATEC_Podium_Wheel_Base_DD2-event-joystick. If you get an error saying force feedback is not enabled for the device, the fanatec driver most likely needs to be rebuilt. Because the driver is compiled locally on the ubuntu kernel, if Ubuntu updates its kernel, the driver may stop working. To fix this, perform the following steps: cd to the hid-fanatec repo (most likely cd ~/hid-fanatec) Run the following: make clean make sudo make install sudo cp ~/shared_decision_making/CompactSimAnsible/config/udev/99-fanatec.rules /etc/udev/rules.d/99-fanatec.rules Reboot the computer Test again by running ffcfstress -d /dev/input/by-id/usb-Fanatec_FANATEC_Podium_Wheel_Base_DD2-event-joystick . If the force feedback works, you should be good to go! How do I launch motion-sim? Detailed instructions can be found on the motion-sim wiki TLDR: There are now three options: ./motion-sim-workspace/src/dsim-grpc/src/bazel-bin/experimental/carla_coordinator/runtime/runner --local-run --ros2 --ros2-bag …….(add additional arguments as desired) from the motion-sim-workspace/src/dsim-grpc/src/experimental directory, run ../../../../tools/bazelisk run //experimental/carla_coordinator/runtime:runner -- --local-run --ros2 ..............(add additional arguments as desired) From motion_sim_hmi_scenarios , run: . scripts/run_carla.sh How do I run MPPI alongside Carla Make sure ackermann-msgs are installed for ros1 and ros2 (sudo apt install ros-noetic-ackermann-msgs ros-foxy-ackermann-msgs) Make sure ros1 bridge source is built and installed mkdir -p ros1_bridge_ws/src && cd ros1_bridge_ws/src git clone https://github.com/ros2/ros1_bridge.git cd .. source /opt/ros/noetic/setup.bash source /opt/ros/foxy/setup.bash colcon build --symlink-install --packages-select ros1_bridge --cmake-force-configure Terminal 1 source ~/motion_sim_workspace/install/setup.bash cd ~/motion_sim_workspace/src/dsim-grpc/src/experimental ../../../../tools/bazelisk run //experimental/carla_coordinator/runtime:runner -- --local-run --ros2 --joystick-type=dd2 --map=ThunderHill --start-x=-780 --start-y=560 --start-z=-7 --start-yaw=0 --visual-fps=120 --vehicle-physics-profile=tuned_2023_04_12 Terminal 2 source ~/motion_sim_workspace/install/setup.bash python3 ~/motion_sim_workspace/src/dsim-grpc/src/experimental/carla_client_joystick/spp_to_motion_sim_node.py Terminal 3 source ~/motion_sim_workspace/install/setup.bash python3 ~/shared_decision_making/src/shared_decision_making/scripts/shared_autonomy_node.py Terminal 4 source /opt/ros/noetic/setup.bash roscore Terminal 5 source ~/mppi_ws/devel/setup.bash CUDA_VISIBLE_DEVICES=1 roslaunch mppi_tri compactsim_vanilla_mppi_standard.launch (this forces MPPI to run on the second GPU, separate from the one running Carla) If you need to specify a map path or model path, you can either modify them in the launch file (~/mppi_ws/src/mppi_ros_bridge/mppi_tri/launch/compactsim_vanilla_mppi_standard.launch), or you can add them as launch arguments (map_path:=, model_path:=) Terminal 6 source /opt/ros/noetic/setup.bash Rviz -d ~/mppi_ws/src/mppi_ros_bridge/mppi_tri/launch/thunderhill_rviz.rviz Terminal 7 source /opt/ros/noetic/setup.bash source /opt/ros/foxy/setup.bash source ~/motion-sim-workspace/install/setup.bash source install/setup.bash ros2 run ros1_bridge dynamic_bridge --bridge-all-topics With this, you should see odometry data appearing in ros1 (rostopic echo in a ros1 terminal) and autonomy ackermann commands appearing in ros2 (ros2 topic echo in a ros2 terminal) How do I update the motion sim workspace to the latest on my branch? From inside the motion-sim-workspace directory, run the following: git pull (This makes sure you have the latest version of your current branch) for i in repos-common/*.repos repos-linux/*.repos repos-foxy/*.repos; do vcs import --recursive src -w 1 < $i ; done (This points all of the repos in src to the branches/commits defined by your branch) vcs pull src -w 1 (This fast-forwards all the repos in src pointing to a branch to their latest commit) colcon build (This builds motion-sim-workspace) cd src/dsim-grpc/src/experimental ../../../../tools/bazelisk build … (This builds the bazelisk workspace) What do I do if I see many errors or inability to connect to submodule repos when using vcs? It can be very helpful to add the following to your ssh config (in ~/.ssh/config). It should help stabilize the connections Host github.shared-services.aws.tri.global ControlMaster auto ControlPath ~/.ssh/socket-%C ControlPersist 60 User git How do I record rosbags for one agent, to be replayed later (either ego playback or ado car playback)? Open three terminal windows. In terminal 1, launch carla as usual. Option 1: source /opt/ros/humble/setup.bash source ~/motion-sim-workspace/install/setup.bash export PYTHONPATH=""$PYTHONPATH:/opt/tri-runner-0-storage/carla-0.9.12_tri-0.8.3/PythonAPI/carla/dist/carla-0.9.12-py3.8-linux-x86_64.egg"" export ROS_DOMAIN_ID=61 export FASTRPTS_DEFAULT_PROFILES_FILE=/opt/tri-runner-0-storage/motion-sim-0.15.1/bazel/experimental/carla_coordinator/runtime/files/fastrtps_profile_hq.xml cd ~/motion_sim_workspace/src/dsim-grpc/src/experimental ../../../../tools/bazelisk run //experimental/carla_coordinator/runtime:runner -- --local-run --ros2 --joystick-type=dd2 --map=ThunderHill --start-x=-780 --start-y=560 --start-z=-7 --start-yaw=0 --visual-fps=120 --vehicle-physics-profile=tuned_2023_04_12 Option 2: . ~/motion_sim_hmi_scenarios/scripts/run_carla.sh In terminal 2, display the ego car’s live position. source /opt/ros/humble/setup.bash source /opt/tri-runner-0-storage/motion-sim-0.15.1/install/setup.bash export PYTHONPATH=""$PYTHONPATH:/opt/tri-runner-0-storage/carla-0.9.12_tri-0.8.3/PythonAPI/carla/dist/carla-0.9.12-py3.8-linux-x86_64.egg"" export ROS_DOMAIN_ID=61 export FASTRPTS_DEFAULT_PROFILES_FILE=/opt/tri-runner-0-storage/motion-sim-0.15.1/bazel/experimental/carla_coordinator/runtime/files/fastrtps_profile_hq.xml (optional) display the list of topics using ros2 topic list , ensure you see /carla/objects listed there Run ros2 topic echo /carla/objects .  A live-updating printout of the states of the ego vehicle should be there (and only one object ID should be present).  Note the x, y positions. Drive the ego vehicle to a position where you want to begin collecting data.  This would be either: A predefined point, which you can tell you’ve reached by looking at the x, y coordinates displayed in terminal 2. Driving to a desired new location along the course.  After reaching this location, you’ll probably want to jot down the x, y coordinates in terminal 2 to recall later. In terminal 3, record the rosbags when ready, and drive the car. source /opt/ros/humble/setup.bash source ~/motion-sim-workspace/install/setup.bash ros2 bag record -ao /opt/tri-runner-0-storage/study_assets/compact_sim_logs/<enter a descriptive name> The above command will kick-off the recording, so be aware of your driving movements. After driving, stop the car, then CTRL-C the terminal 3 window to stop the recording. A log should appear in the folder /opt/tri-runner-0-storage/study_assets/compact_sim_logs/<your descriptive name> .  You can insert the path of the .db3 file in that folder into any scenario yaml file with a ros playback agent.  For ado vehicle playback, follow this link for an example. If making a new scenario, you may want to use the x, y coordinates at the beginning and end of the track segment you jotted down earlier to define the start_location and end_locations . How do I get set up with CARLA and MotionSim Study Tools for the first time?  I have an individual account on CompactSim (usually firstname-ml ). First, log into GSuite (Gmail, GDrive, etc.), and possibly slack also.  It will make life a lot easier. Add your AWS credentials to your account You should have an email with an attachment firstname.lastname_credentials.tar.gz Unzip that on the Sim machine you are using. Add the creds to your account using aws configure , following these instructions: https://intranet.tri.global/Interact/Pages/Content/Document.aspx?id=3295&SearchId=18417 Ensure you have access to the correct git repos and orgs.  In particular: https://github.shared-services.aws.tri.global/tri-projects (for all the motion-sim-related software) https://github.com/ToyotaResearchInstitute (for carla-ros-bridge) This is a public-facing github org where you will need a personal account.  Contact IT for help. Make a new ssh key and add it to the correct repos Follow the instructions here: https://docs.github.com/en/authentication/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent Add your ssh keys to the above two accounts (shared-services and ToyotaResearchInstitute) For both accounts (shared-services and public github), click your icon on the top-right corner, and select “Settings” Select “SSH and GPG Keys” Select “New SSH Key” Follow the instructions here to add the key to github: https://docs.github.com/en/authentication/connecting-to-github-with-ssh/adding-a-new-ssh-key-to-your-github-account Note that you might want to name the key according to the sim you are using, e.g. “CompactSim1” (TODO: update me) Try running source /opt/ros/humble/setup.bash export PYTHONPATH=""$PYTHONPATH:/opt/tri-runner-0-storage/ {carla_version_here} /PythonAPI/carla/dist/ {carla_version_here} - {python_version_here} -linux-x86_64.egg"" source /opt/tri-runner-0-storage/motion-sim-0.15.1/install/setup.bash /opt/tri-runner-0-storage/ {motion-sim-version-here} /bazel/experimental/carla_coordinator/runtime/runner --local-run --ros2 --carla-release-name {carla_version_here} --joystick-type=dd2 --map=ThunderHill --start-x=-780 --start-y=560 --start-z=-7 --start-yaw=0 --visual-fps=120 --vehicle-physics-profile=tuned_2023_04_12 –log-name= {your_log_name_here} If the above step fails, follow these steps: Clone https://github.shared-services.aws.tri.global/tri-projects/motion-sim-workspace Install motion-sim-workspace via these instructions: https://toyotaresearchinstitute.atlassian.net/wiki/spaces/DMS/pages/2234187827/Ubuntu+Jammy+22.04+ROS2+Humble You should skip anything requiring apt-get or sudo Clone https://github.shared-services.aws.tri.global/tri-projects/motion_sim_hmi_scenarios Follow the steps in https://github.shared-services.aws.tri.global/tri-projects/motion_sim_hmi_scenarios#readme You should skip anything requiring apt-get or sudo To fix any errors encountered with ros2 center console, do this: git submodule update --init --force --remote --recursive , then do colcon build . I’m getting issues regarding “address already in use, localhost:8080” (or something like this?) Check the terminal output for the port number it’s complaining about (usually 8080) Run lsof -ti :8080 Check the console output for the process id (let’s call it <PID>) Then run kill -9 <PID> (note: you can do this in one step via kill -9 $(lsof -ti :8080) ) Upgrading MS and CARLA release - how do I do this on the CompactSims? For more detailed instructions, see the motionsim documentation here Check here for the latest MS / CARLA release https://github.shared-services.aws.tri.global/tri-projects/motion-sim-workspace/releases Identify the version of motion sim and carla needed.  In the steps below, we will assume MS 23.3 / CARLA 0.13.5. Download the files from the releases page https://github.shared-services.aws.tri.global/tri-projects/motion-sim-workspace/releases/download/motion-sim-0.23.3/storage_lib_binary.linux.x86_64 https://github.shared-services.aws.tri.global/tri-projects/motion-sim-workspace/releases/download/motion-sim-0.23.3/carla-0.9.15_tri-0.13.5.yaml https://github.shared-services.aws.tri.global/tri-projects/motion-sim-workspace/releases/download/motion-sim-0.23.3/motion-sim-0.23.3.yaml chmod +x storage_lib_binary.linux.x86_64 ./storage_lib_binary.linux.x86_64 --localhost carla-0.9.15_tri-0.13.5.yaml  # this step will take awhile ./storage_lib_binary.linux.x86_64 --localhost motion-sim-0.23.3.yaml sudo chmod 0644 /opt/tri-runner-0-storage/carla*/PythonAPI/carla/dist/* You may need to provide other permissions.  For instance, the /tmp/egg* folders."
3557785603,0,1,child,Compact Sim Audio Guide,https://toyotaresearchinstitute.atlassian.net/wiki/spaces/HAIL/pages/3557785603/Compact+Sim+Audio+Guide,**********,,2025-05-29T22:14:34.439Z,current,"1 6 false none list true Two headphone for 1 simulator Create single output channel for all audio output to play to / listen from. sudo apt install -y paprefs && pacmd load-module module-combine-sink Restores an option that was removed from PulseAudio at some point. Open 'PulseAudio Preferences' go to last tab and make sure box is checked Within settings systems under sound have the Output to the newly created simultaneous output channel (from above step). Then input device to the front microphone (this is just the ""default"" main) For every microphone that you would like to listen to, run this following command: pacmd load-module module-loopback latency_msec=5 Coach to student is one stream / then student to coach is another. Open 'volume control' and under recording you should see two newly created loopback devices. Change these to the following drop downs. The names might be the same within the ""Loopback to Built-in Audio Stereo from"" that can be ignored. Under input devices these are the settings that were used. Under output devices these are the settings that were used. Under playback tab these are the settings that were used. did have to change the dropdown under the playback tab within volume control to the simultaneous channel. Then both headsets were able to hear the car then each either but not themselves. Start CARLA and then it will replace Firefox with CARLA and Python and change the dropdown to simultaneous In the event audio isn't working before starting from the top with this setup make sure to run pulseaudio -k This will unload all modules and likely refresh the UI panels if still open, they may need a close / open to refresh devices with in volume control. Extra: full name of plugable device within pulse audio alsa_output.usb-Plugable_Plugable_USB_Audio_Device_000000000000-00.analog-stereo monitor add .monitor to the above if listening on channel stream. Onboard sound front and back alsa_output.pci-0000_00_1f.3.analog-stereo"
2223669252,1,1,link,Workspace build (run from source),https://toyotaresearchinstitute.atlassian.net/wiki/spaces/DMS/pages/2223669252/Workspace+build+run+from+source,2978414610,,2025-06-05T16:53:34.037Z,current,"Initial builds See subpages for details on each platform. Please make sure to look through the FAQ, especially https://toyotaresearchinstitute.atlassian.net/wiki/spaces/DMS/pages/2233893188/FAQ#vcs_ssh_setup as it will cause unexpected build failures if you do not configure ssh. 1 Updating the workspace after Building There are a few issues we run into when updating the workspace. Below is a set of steps which should provide minimal problems. Git fetch / rebase the workspace directory Re-run VCS import from the instructions for your platform (for .... do vcs import) : https://toyotaresearchinstitute.atlassian.net/wiki/spaces/DMS/pages/2234122266/Ubuntu+Focal+20.04+ROS2+Foxy#Build-steps%3A https://toyotaresearchinstitute.atlassian.net/wiki/spaces/DMS/pages/2234187827/Ubuntu+Jammy+22.04+ROS2+Humble#Build-steps%3A https://toyotaresearchinstitute.atlassian.net/wiki/spaces/DMS/pages/2234187879/Windows+ROS2+Foxy+source+build#Build-steps%3A https://toyotaresearchinstitute.atlassian.net/wiki/spaces/DMS/pages/2229600257/WIP+Windows+ROS2+Humble#Build-steps%3A Run vcs pull src -w 1 . Make sure this command does not complain. Delete the build, log, and install directories cd into src/dsim-grpc/src/ and run ../../../tools/bazelisk clean --expunge You should now have an updated workspace with no dangling references to old code. This repository will have all default branches checked out. You will need to checkout your branches again. These steps should only be necessary when the workspace changes, especially after a release that modifies the repos-files directory. Building a release Update: 2025-06-02, less manual steps The following steps assume you will replace [RELEASE_BRANCH_NAME] and [RELEASE_META_NAME] and [RELEASE_PR_BRANCH] with valid values for your branch. Details: RELEASE_BRANCH_NAME -> The name of the working branch for the release. Necessary to sync between various repos RELEASE_META_NAME -> The basename of the metadata file that will be created by your new release RELEASE_PR_BRANCH -> The working branch will contain many changes we do not want to merge to master. This branch will cherry pick the specific files we need for master. Examples: Relase branch name: release/0.4.2 Release meta name motion-sim-0.4.2 Release pr branch name merge_0.4.2 Blessing the release in motion-sim-workspace This step should only be done for official main releases, not test releases. edit release-info/motion-sim in this repo, set the contents to be the new name if applicable, edit release-info/carla Step 1: Use Jenkins and do it NOTE : Steps have changed again! (2025-06-02) To build a release with Jenkins Go to https://jenkins.simm-sharedsvcs.aws.tri.global/job/motion-sim-release/build?delay=0sec and fill in the parameters (don't change the S3 prefix except for version numbers right now as Jenkins has limited write access). BRANCH_NAME : Name of branch in motion-sim-workspace to work on RELEASE_BRANCH_NAME : Name of the branch to create with build-release.py RELEASE_META_NAME : Name of the metadata yaml file Let it run to completion. If it succeeds, there will be two build ""artifacts""; one metadata yaml file that can be used directly to perform testing, and a git bundle called release.bundle that can be used to replay the commits that Jenkins made locally. Import the git bundle: cd ~/motion-sim-workspace
git fetch ~/release.bundle [RELEASE_BRANCH_NAME]
git checkout FETCH_HEAD
git checkout -b [RELEASE_BRANCH_NAME] Step 2: PR to master Now switch to a new branch based on master branch and copy the new metadata file, changelog, and release-info if necessary: git fetch
git checkout -b [RELEASE_PR_BRANCH] master
git checkout [RELEASE_BRANCH_NAME] metadata/[RELEASE_META_NAME].yaml
git add metadata/[RELEASE_META_NAME].yaml
# If the changelog was edited
git checkout [RELEASE_BRANCH_NAME] CHANGELOG.md
git add CHANGELOG.md
# If we're modifying the official release, update release info as well.
echo -n [RELEASE_META_NAME] > release-info/motion-sim
git add release-info/motion-sim
# Verify that ONLY the metadata file, changelog, and release-info are added
git status
git commit -m ""Added release [RELEASE_BRANCH_NAME]""
git push origin [RELEASE_PR_BRANCH] Follow the normal PR steps at this point. Step 3: Tag release in GHE and mirror to S3 After the PR from step 3 is merged, use the GHE UI and make a new release from the tip of master. Tag it with the same name that you used, i.e. motion-sim-X.X.X and then use the script ./dev-scripts/get-release-files.py in the workspace repo to arrange the necessary files for binary release. Upload to the metadata path specified in the jenkins build. Example for 0.25.1: ./dev-scripts/get-release-files.py --workdir /tmp/workdir
cd /tmp/workdir/
# ls
# carla-0.9.15_tri-0.14.2.yaml  storage_lib_binary.exe
# motion-sim-0.25.1.yaml        storage_lib_binary.linux.x86_64
# ros2-humble-p7.yaml Upload these files with a new release. Convention is to list any big changes in the release list other cool stuff worth noting link to the changelog file which lists all commits in all repos you can also let github add auto release notes below this attach the release files to the release To roll out a release on HQ installation: ./dev-scripts/distribute-latest.sh To install the release locally on Linux: ./src/dsim-grpc/src/bazel-bin/experimental/carla_coordinator/runtime/storage_lib_binary --localhost -- metadata/motion-sim-X.X.X.yaml To install the release locally on Windows: .\src\dsim-grpc\src\bazel-bin\experimental\carla_coordinator\runtime\storage_lib_binary.exe --localhost -- metadata/motion-sim-X.X.X.yaml To run the contents of the release on Linux: /opt/tri-runner-0-storage/motion-sim-X.X.X/bazel/experimental/carla_coordinator/runtime/runner ... args ... To run the contents of the release on Windows c:\opt\tri-runner-0-storage\motion-sim-X.X.X\bazel\experimental\carla_coordinator\runtime\runner.exe ... args ..."
2233630728,1,1,link,Fanatec Wheel Setup,https://toyotaresearchinstitute.atlassian.net/wiki/spaces/DMS/pages/2233630728/Fanatec+Wheel+Setup,2978414610,,2023-04-05T19:58:06.865Z,current,"Ubuntu Driver Follow the setup instructions to install the fanatec force feedback driver from this repo: https://github.com/gotzl/hid-fanatecff This will install a udev rule and set up the driver which allows ubuntu to command force feedback on the wheel. Calibration In order for the SDL library in carla to correctly, the udev rule installed in the previous section needs to be slightly modified so that the wheel’s range goes from 0->65535. When changed, line 8 of the rule will read as follows: ATTRS{idProduct}==""0001|0004|0005|0006|0007|0011|0020|6204|0e03"",  ATTRS{idVendor}==""0eb7"", RUN{program}+=""/usr/bin/evdev-joystick --evdev $devnode --deadzone 0 --fuzz 0; /usr/bin/evdev-joystick --evdev $devnode --axis 0 --minimum 0 --maximum 65535 --deadzone 0 --fuzz 0"" Debugging If the ubuntu kernel updates, that will sometimes break the driver. If force feedback stops working, try cleaning up the compiled driver, recompile, and install it according to the instructions in the repo. Be aware that when you reinstall the driver, it writes over the existing udev rule, so you will have to repeat the calibration step above! # Put in code commands here"
2234122266,1,1,link,Ubuntu Focal 20.04 / ROS2 Foxy,https://toyotaresearchinstitute.atlassian.net/wiki/spaces/DMS/pages/2234122266/Ubuntu+Focal+20.04+ROS2+Foxy,2978414610,,2025-07-29T21:18:36.054Z,current,"FOXY support has ended. These instructions will not work. Initial setup instructions Includes steps abbreviated from https://docs.ros.org/en/foxy/Installation/Ubuntu-Install-Debians.html locale  # check for UTF-8
sudo apt update && sudo apt install locales
sudo locale-gen en_US en_US.UTF-8
sudo update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8
export LANG=en_US.UTF-8
sudo apt install software-properties-common
sudo add-apt-repository universe
sudo apt update && sudo apt install curl
sudo curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg
echo ""deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://packages.ros.org/ros2/ubuntu $(. /etc/os-release && echo $UBUNTU_CODENAME) main"" | sudo tee /etc/apt/sources.list.d/ros2.list > /dev/null
sudo apt update
sudo apt install python3 \
    python3-pip \
    default-jdk \
    default-jre \
    locales \
    curl \
    gnupg2 \
    lsb-release \
    build-essential \
    python3-rosinstall \
    python3-vcstool \
    ros-foxy-image-transport \
    python3-colcon-common-extensions \
    ros-foxy-desktop \
    python3-rosdep2 \
    python3-pyqt5.qtquick \
    python3-cv-bridge \
    ros-foxy-cv-bridge \
    python3-opencv \
    libomp5-10 \
    openjdk-11-jdk \
    python-is-python3 \
    awscli
# If you want crash dumps to be backtraced automatically:
echo kernel.core_pattern=core.%t.%E.%p.coredump | sudo tee -a /etc/sysctl.conf && sudo sysctl -p /etc/sysctl.conf Similar to windows, the source of truth (should be) Ansible: https://github.awsinternal.tri.global/IE/build-ami/blob/master/images/ubuntu_focal_motionsim/roles/motion-sim-focal/tasks/main.yml Build steps: NOTE : If you are using a VPN (or even in office), sometimes the vcs step will fail. Make sure that all repos have been checked out (there should be no red errors from the vcs step.) You may need to wait ~1 minute between each invocation of vcs import. Also see the FAQ to configure ssh to reuse/multiplex a single connection. bash <NAME_EMAIL>:tri-projects/motion-sim-workspace.git
cd motion-sim-workspace
sudo apt-get install python3-rosinstall python3-vcstool ros-foxy-image-transport ros-foxy-ackermann-msgs python3-colcon-common-extensions ros-foxy-desktop python3-rosdep2 python3-pyqt5.qtquick python3-cv-bridge ros-foxy-cv-bridge python3-opencv libomp5-10
. /opt/ros/foxy/setup.sh
mkdir src
for i in repos-common/*.repos repos-linux/*.repos repos-foxy/*.repos; do vcs import --recursive src -w 1 < $i ; done
rosdep update --include-eol-distros
rosdep install --from-paths src --ignore-src
colcon build
mkdir -p tools; wget -O tools/bazelisk https://github.com/bazelbuild/bazelisk/releases/download/v1.11.0/bazelisk-linux-amd64 ; chmod +x tools/bazelisk
# Note: this step will fail if aws s3 ls s3://scratch-tri-global/anrp does not succeed. Contact IE for the appropriate permissions
( cd src/dsim-grpc/src/experimental/ ; ../../../../tools/bazelisk build ... )
sudo mkdir /opt/tri-runner-0-storage
sudo chown `id -u`:`id -g` /opt/tri-runner-0-storage
# Note that this step may fail if you have not set up AWS credentials and/or do not have access to the s3://scratch-tri-global bucket
cd motion-sim-workspace
./scripts/prepare-local.sh
./src/dsim-grpc/src/bazel-bin/experimental/carla_coordinator/runtime/runner --local-run --ros2 --ros2-bag"
2234187827,1,1,link,Ubuntu Jammy 22.04 / ROS2 Humble,https://toyotaresearchinstitute.atlassian.net/wiki/spaces/DMS/pages/2234187827/Ubuntu+Jammy+22.04+ROS2+Humble,2978086936,,2025-03-04T20:12:28.988Z,current,"Initial setup instructions Includes steps abbreviated from https://docs.ros.org/en/humble/Installation/Ubuntu-Install-Debians.html locale  # check for UTF-8
sudo apt update && sudo apt install locales
sudo locale-gen en_US en_US.UTF-8
sudo update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8
export LANG=en_US.UTF-8
sudo apt install software-properties-common
sudo add-apt-repository universe
sudo apt update && sudo apt install curl
sudo curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg
echo ""deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://packages.ros.org/ros2/ubuntu $(. /etc/os-release && echo $UBUNTU_CODENAME) main"" | sudo tee /etc/apt/sources.list.d/ros2.list > /dev/null
sudo apt update
sudo apt install python3 \
    python3-pip \
    default-jdk \
    default-jre \
    locales \
    curl \
    gnupg2 \
    lsb-release \
    build-essential \
    python3-rosinstall \
    python3-vcstool \
    ros-humble-image-transport \
    ros-humble-image-transport-plugins \
    python3-colcon-common-extensions \
    ros-humble-desktop \
    python3-rosdep2 \
    python3-pyqt5.qtquick \
    python3-cv-bridge \
    ros-humble-cv-bridge \
    python3-opencv \
    libomp5 \
    libopenmpi3 \
    python-is-python3 \
    awscli
# If you want crash dumps to be backtraced automatically:
echo kernel.core_pattern=core.%t.%E.%p.coredump | sudo tee -a /etc/sysctl.conf && sudo sysctl -p /etc/sysctl.conf Similar to windows, the source of truth (should be) Ansible: https://github.awsinternal.tri.global/IE/build-ami/blob/master/images/ubuntu_jammy_motionsim/roles/motion-sim-jammy/tasks/main.yml Build steps: NOTE : If you are using a VPN (or even in office), sometimes the vcs step will fail. Make sure that all repos have been checked out (there should be no red errors from the vcs step.) You may need to wait ~1 minute between each invocation of vcs import. bash <NAME_EMAIL>:tri-projects/motion-sim-workspace.git
cd motion-sim-workspace
sudo apt-get install python3-rosinstall python3-vcstool ros-humble-image-transport ros-humble-ackermann-msgs python3-colcon-common-extensions ros-humble-desktop python3-rosdep2 python3-pyqt5.qtquick python3-cv-bridge ros-humble-cv-bridge python3-opencv awscli libomp5 python-is-python3 gdb
. /opt/ros/humble/setup.sh
mkdir src
for i in repos-common/*.repos repos-linux/*.repos repos-humble/*.repos; do vcs import --recursive src -w 1 < $i ; done
rosdep update
rosdep install --from-paths src --ignore-src
colcon build
mkdir -p tools; wget -O tools/bazelisk https://github.com/bazelbuild/bazelisk/releases/download/v1.11.0/bazelisk-linux-amd64 ; chmod +x tools/bazelisk
( cd src/dsim-grpc/src/experimental/ ; ../../../../tools/bazelisk build ... )
sudo mkdir /opt/tri-runner-0-storage
sudo chown `id -u`:`id -g` /opt/tri-runner-0-storage
./dev-scripts/prepare-local.sh
./src/dsim-grpc/src/bazel-bin/experimental/carla_coordinator/runtime/runner --local-run --ros2 --ros2-bag --skip-checkpoints ic"
2234122241,1,1,link,Ubuntu Jammy / ROS2 Humble,https://toyotaresearchinstitute.atlassian.net/wiki/spaces/DMS/pages/2234122241/Ubuntu+Jammy+ROS2+Humble,2978086936,,2025-07-29T21:13:59.571Z,current,"Initial setup instructions Includes steps abbreviated from https://docs.ros.org/en/humble/Installation/Ubuntu-Install-Debians.html locale  # check for UTF-8
sudo apt update && sudo apt install locales
sudo locale-gen en_US en_US.UTF-8
sudo update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8
export LANG=en_US.UTF-8
sudo apt install software-properties-common
sudo add-apt-repository universe
sudo apt update && sudo apt install curl
sudo curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg
echo ""deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://packages.ros.org/ros2/ubuntu $(. /etc/os-release && echo $UBUNTU_CODENAME) main"" | sudo tee /etc/apt/sources.list.d/ros2.list > /dev/null
sudo apt update
sudo apt install python3 \
    python3-pip \
    default-jdk \
    default-jre \
    locales \
    curl \
    gnupg2 \
    lsb-release \
    build-essential \
    python3-rosinstall \
    python3-vcstool \
    ros-humble-image-transport \
    python3-colcon-common-extensions \
    ros-humble-desktop \
    python3-rosdep2 \
    python3-pyqt5.qtquick \
    python3-cv-bridge \
    ros-humble-cv-bridge \
    python3-opencv \
    libomp5 \
    libopenmpi3 \
    python-is-python3 \
    awscli
# Additional packages derived from rosdep for 0.9.0
#[apt] Installation commands:
  sudo -H apt-get install qml-module-qtquick-controls
  sudo -H apt-get install qml-module-qtquick-controls2
  sudo -H apt-get install qml-module-qtquick-window2
  sudo -H apt-get install qml-module-qtquick2
  sudo -H apt-get install python3-pytest
  sudo -H apt-get install qtbase5-dev
  sudo -H apt-get install pyqt5-dev-tools
  sudo -H apt-get install python3-setuptools
  sudo -H apt-get install libgstreamer1.0-dev
  sudo -H apt-get install libgstreamer-plugins-base1.0-dev
  sudo -H apt-get install python3-transforms3d
  sudo -H apt-get install libqt5core5a
  sudo -H apt-get install libqt5gui5
  sudo -H apt-get install libqt5widgets5
  sudo -H apt-get install libtclap-dev

# If you want crash dumps to be backtraced automatically:
echo kernel.core_pattern=core.%t.%E.%p.coredump | sudo tee -a /etc/sysctl.conf && sudo sysctl -p /etc/sysctl.conf Similar to windows, the source of truth (should be) Ansible: https://github.awsinternal.tri.global/IE/build-ami/blob/master/images/ubuntu_focal_motionsim/roles/motion-sim-focal/tasks/main.yml Install steps: Using the example release at https://github.shared-services.aws.tri.global/tri-projects/motion-sim-workspace/releases/tag/motion-sim-0.22.3 Grab these files (more up to date files may exist for newer releases): https://github.shared-services.aws.tri.global/tri-projects/motion-sim-workspace/releases/download/motion-sim-0.22.3/motion-sim-0.22.3.yaml https://github.shared-services.aws.tri.global/tri-projects/motion-sim-workspace/releases/download/motion-sim-0.22.3/storage_lib_binary.linux.x86_64 https://github.shared-services.aws.tri.global/tri-projects/motion-sim-workspace/releases/download/motion-sim-0.22.3/carla-0.9.15_tri-0.13.3.yaml Make storage_lib_binary executable chmod +x ~/ Download /storage_lib_binary.linux.x86_64 Prepare directories bash sudo mkdir -p /opt/tri-runner-0-storage
sudo chown `id -u`:`id -g` /opt/tri-runner-0-storage Download/install the binary release (make sure that you have AWS credentials and approximately 30GB disk space free) Remember to update the command below with the version of the simulator you want If you have permissions issues, post in Motion Simulator channel. All new releases are hosted on SSO buckets so you will need aws sso configured ~/Downloads/storage_lib_binary.linux.x86_64 --localhost -- ~/Downloads/motion-sim-0.22.3.yaml
~/Downloads/storage_lib_binary.linux.x86_64 --localhost -- ~/Downloads/carla-0.9.15_tri-0.13.3.yaml Make sure to reference the version numbers of your version, e.g. motion-sim- X.Y.Z .yaml Run it! /opt/tri-runner-0-storage/motion-sim-0.22.3/bazel/experimental/carla_coordinator/runtime/runner --local-run --ros2 --carla-release-name carla-0.9.15_tri-0.13.3 --skip-checkpoints ic --log-name $(date +%s)-$USERNAME"
2229600257,1,2,child,Windows / ROS2 Humble (OSRF),https://toyotaresearchinstitute.atlassian.net/wiki/spaces/DMS/pages/2229600257/Windows+ROS2+Humble+OSRF,2223669252,,2025-07-29T21:16:41.606Z,current,"Windows initial setup instructions Note that everything runs in command prompt if not otherwise mentioned . Note that CI is built automatically by Ansible; the most authoritative directions on how to set up a windows machine should be described in https://github.awsinternal.tri.global/IE/build-ami/blob/master/images/windows_jenkins_motionsim_humble_osrf/roles/motion-sim-windows-humble-osrf/tasks/main.yml Install DirectX Legacy Redistributable Note: you may need to temporarily uninstall the VC2010 runtime due to a bug in the (old) DirectX code https://docs.microsoft.com/en-us/troubleshoot/windows/win32/s1023-error-when-you-install-directx-sdk , run in an admin command prompt: MsiExec.exe /passive /X{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}
MsiExec.exe /passive /X{1D8E6291-B0D5-35EC-8441-6616F567A0F7} Installer: https://download.microsoft.com/download/8/4/A/84A35BF1-DAFE-4AE8-82AF-AD2AE20B6B14/directx_Jun2010_redist.exe Install chocolatey https://chocolatey.org/install#individual Reinstall VC2010 runtime choco update -y vcredist2010 Install Visual Studio 2019 If you want an IDE, get Professional (ask IT to procure a license). https://aka.ms/vs/16/release/vs_professional.exe If you don’t want an IDE then installing the build tools form is sufficient, get the bootstrapper here: https://aka.ms/vs/16/release/vs_buildtools.exe Make sure to install C++ development tools; the machine readable list of packages to install for build tools is as follows, which can be passed to the bootstrapper with --channelUri https://aka.ms/vs/16/release/channel --config path\to\vsconfig (note that this list includes additional tools necessary to build CARLA/Unreal): {
  ""version"": ""1.0"",
  ""components"": [
    ""Microsoft.VisualStudio.Component.Roslyn.Compiler"",
    ""Microsoft.Component.MSBuild"",
    ""Microsoft.VisualStudio.Component.CoreBuildTools"",
    ""Microsoft.VisualStudio.Workload.MSBuildTools"",
    ""Microsoft.VisualStudio.Component.Windows10SDK"",
    ""Microsoft.VisualStudio.Component.VC.CoreBuildTools"",
    ""Microsoft.VisualStudio.Component.VC.Tools.x86.x64"",
    ""Microsoft.VisualStudio.Component.VC.Redist.14.Latest"",
    ""Microsoft.VisualStudio.Component.Windows10SDK.19041"",
    ""Microsoft.VisualStudio.Component.VC.CMake.Project"",
    ""Microsoft.VisualStudio.Component.TestTools.BuildTools"",
    ""Microsoft.Net.Component.4.8.SDK"",
    ""Microsoft.VisualStudio.Component.VC.ASAN"",
    ""Microsoft.VisualStudio.Component.TextTemplating"",
    ""Microsoft.VisualStudio.Component.VC.CoreIde"",
    ""Microsoft.VisualStudio.ComponentGroup.NativeDesktop.Core"",
    ""Microsoft.VisualStudio.Workload.VCTools"",
    ""Microsoft.NetCore.Component.Runtime.3.1"",
    ""Microsoft.NetCore.Component.Runtime.6.0"",
    ""Microsoft.NetCore.Component.SDK"",
    ""Microsoft.Net.Component.4.6.2.TargetingPack"",
    ""Microsoft.Net.Component.4.8.TargetingPack""
  ]
} Install git for windows Tested version is url: https://github.com/git-for-windows/git/releases/download/v2.36.1.windows.1/Git-2.36.1-64-bit.exe but most recent will likely be fine Enable symlinks globally: git config --system core.symlinks true
git config --global core.symlinks true Install python 3.8 Get the correct binary version: https://www.python.org/ftp/python/3.8.10/python-3.8.10-amd64.exe Install it (note: the specific install path is important - it is expected to be there by all the exe wrappers): python-3.8.10-amd64.exe /quiet InstallAllUsers=1 ""TargetDir=C:\Python38"" Install VS2019 C++ redistributable: https://aka.ms/vs/16/release/VC_redist.x64.exe Make sure long paths are enabled: In an admin PowerShell prompt: New-ItemProperty -Path ""HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem"" -Name ""LongPathsEnabled"" -Value 1 -PropertyType DWORD -Force Then, reboot (sorry!) Install a lot of pip packages: set ""PATH=%PATH%;C:\Python38;C:\Python38\Scripts""
C:\Python38\scripts\pip.exe install -U colcon-common-extensions coverage flake8 flake8-blind-except flake8-builtins flake8-class-newline flake8-comprehensions flake8-deprecated flake8-docstrings flake8-import-order flake8-quotes mock mypy==0.931 pep8 pydocstyle pytest pytest-mock vcstool networkx catkin_pkg cryptography empy importlib-metadata lark==1.1.1 lxml matplotlib netifaces numpy opencv-python PyQt5 pillow psutil pycairo pydot pyparsing==2.4.7 pyyaml rosdistro transforms3d awscli boto3 networkx cv_bridge Install the ros2-humble-p7 rollup component (this is built from https://github.shared-services.aws.tri.global/tri-projects/ros2-windows-docker-prereq-builder ) Get storage lib from github release: https://github.shared-services.aws.tri.global/tri-projects/motion-sim-workspace/releases/download/motion-sim-0.25.1/storage_lib_binary.exe get ROS2 humble rollup from github: https://github.shared-services.aws.tri.global/tri-projects/motion-sim-workspace/releases/download/motion-sim-0.25.1/ros2-humble-p7.yaml Install the component: ( Note, the name of the ros component may change in newer releases) .\storage-lib-binary.exe --localhost --metadata ros2-humble-p7.yaml Build steps: rem These are done in cmd.exe
<NAME_EMAIL>:tri-projects/motion-sim-workspace.git
cd motion-sim-workspace
rem Get bazelisk and populate environment with VC2019 & ROS
call dev-scripts\prepare_env_for_building.bat
rem Init this directory:
mkdir src
for /r repos-common %i in (*.repos) do vcs import --recursive src -w 1 < %i
for /r repos-windows %i in (*.repos) do vcs import --recursive src -w 1 < %i
for /r repos-humble %i in (*.repos) do vcs import --recursive src -w 1 < %i
git config --local core.symlinks true
git reset --hard
colcon build --merge-install
cd src\dsim-grpc\src
bazelisk.exe build ...
cd ..\..\..
rem Note that this step may fail if you have not set up AWS credentials and/or do not have access to the s3://scratch-tri-global bucket
.\scripts\prepare-local.bat
.\src\dsim-grpc\src\bazel-bin\experimental\carla_coordinator\runtime\runner.exe --local-run --ros2 --ros2-bag"
2233893188,2,1,link,FAQ,https://toyotaresearchinstitute.atlassian.net/wiki/spaces/DMS/pages/2233893188/FAQ,2223669252,,2025-07-29T21:45:05.873Z,current,"Can I use anaconda/venv/pyenv/…? If the major and minor release versions of the python that you want to use match the system python, it may work. Anything else will not (Python does not have binary compatibility in its C API). Even if the version does match, this is not recommended. The “system” python on Windows is the version that the ROS2 release is compiled against (3.8) When I run ros2 topic list I don’t see anything, either in local run mode or on the installation. In an attempt at ROS2 namespace isolation, the RMW layer is configured to be pretty strict on who it communicates with. You’ll need to set variables like the below for ros2 topic ... commands to work in local run: export FASTRTPS_DEFAULT_PROFILES_FILE=/home/<USER>/motion-sim-workspace/src/dsim-grpc/src/experimental/carla_coordinator/runtime/files/fastrtps_profile_local_run.xml
export ROS_DOMAIN_ID=61 Additionally, to communicate with the installation, you will likely have to run directly on one of the HQ machines (probably motion-coordinator-linux) and use the platform-specific domain ID and FastRTPS config files. When I install from a binary release and run runner, no matter what map I choose it exits saying “Map not found”. This is a bug in the path handling code; to work around it pass runner --carla-release-name carla-0.9.12_tri-0.4.2 (or whichever version it is supposed to go with.) I ran storage_lib_binary against version 0.9.0 and nothing happened. storage_lib_binary itself also was updated, make sure you are using the new one. When I run runner with --ros2 it fails startup due to missing OpenAL. An oversight in how PyOpenAL looks up the bazel-shipped library means it can’t find it and uses the system one. Disable it with --skip-sound-server passed to runner, or install the system libopenal1 package. Should I follow the CARLA build directions? Probably not, unless you’re sure you want to edit something 3D related, or need to make a LibCarla change. The first build takes hours as it compiles and cooks 10s of GB of content. vcs_ssh_setup When I attempt to build the ROS2 components, I get pages of errors about missing packages the first time I clone the repository. There is a firewall within the network that is treating the rapid reconnections to GHE as a threat ( https://tri-internal.slack.com/archives/C1HSLQDDH/p1679431450150829 ). This is true even when running single threaded. This can be worked around by putting the following in your ~/.ssh/config file: Host github.shared-services.aws.tri.global
  ControlMaster auto
  ControlPath ~/.ssh/socket-%C
  ControlPersist 60
  User git This, combined with running single threaded ( -w 1 passed to all vcs commands) will cause there to be only one (TCP) connection to GHE, with ssh reusing the connections. There is nothing like this on Windows, however windows generally launches processes slower anyway, so you will hit it less often. Make sure that the vcs import ... steps have all succeeded, you may have to run them multiple times and wait between each invocation (about 1 minute.) I build the ROS2 components with colcon but when I run bazelisk I get an error about rules_python or another “can’t download” style error. Make sure that aws s3 ls s3://simm-production-ci-artifacts/artifacts-for-build/ returns a list of files and not a permissions error. When I launch the system, I get a message about carla_client_joystick exiting and failed startup. If you don’t have a joystick that is supported (some gamepad controllers, Logitech G29), then don’t run the joystick client and instead use the autopilot to watch it drive by passing --no-joystick --ego-autopilot to runner. When I launch the system with --ros2 on 22.04, I get an error about safety_driver_ui There’s an existing bug with 22.04 and safety_driver_ui. Skip running it by adding --skip-checkpoints ic to the runner command line. Almost immediately after starting runner, I see messages about Process local-ig1-unreal failed or similar, or rendering speed is extremely slow (<1FPS) For some reason Vulkan is unable to communicate with the GPU. Are you running interactively (this won’t work at all over SSH)? Did you recently perform a software update, and the nvidia driver versions don’t match between kernel and user space? You can check by running nvidia-smi , if it prints one line about Failed to initialize NVML: Driver/library version mismatch , then sometime recently a software update happened to the nvidia driver, and your system needs to be restarted. Do you have a discrete GPU? The stack “works” on an intel GPU but is not even slightly performant. You can reduce the visual quality significantly by passing --quality-level=mobile to runner which will allow for ~10 FPS of rendering. Attempting to use any of these with llvmpipe (software renderer) will give impossibly poor performance. If you’re sure you don’t need rendering and don’t mind running way faster than realtime (or you’re synchronous), you can run with no rendering at all by passing --local-num-ig 0 to runner. I just installed a new version of software on windows, and now nothing can connect. Windows Firewall will block connections by default if you do not accept the prompt that is only shown once for newly found binaries. Go to “Windows Advanced Firewall” and delete the added “Block” rules. Where are log files saved? If the system is being run from a source build, it is in src/dsim-grpc/src/experimental/carla_coordinator/runtime/logs/... , otherwise it is in ~/motion-simulator-logs Note that runner only saves ROS2 bags if you pass --ros2 --ros2-bag (and also pass --ros2-camera-bag to have it save camera data on the HQ install.) Every time runner is started, it updates a symlink called latest in the logs directory to point to the just-created directory. I am trying to build on Windows, and when I run bazel I get piles of errors about project_paths.py like so: ERROR: C:/motion-sim-workspace/src/dsim-grpc/src/tests/BUILD:5:15: Executing genrule //tests:shell_driver_project_paths_py_flake8 failed: (Exit 1): cmd.exe failed: error executing command cmd.exe /S /E:ON /V:ON /D /c py utils\flake8.py --fail-only external\flake8\flake8-4.0.1-win64.exe tests\project_paths.py > bazel-out\x64_windows-opt\bin\tests\shell_driver_project_paths.py.flake8
tests\project_paths.py:1:1: E999 SyntaxError: invalid syntax
1
INFO: Elapsed time: 173.315s, Critical Path: 32.18s
INFO: 2200 processes: 291 internal, 1909 local.
FAILED: Build did NOT complete successfully Symlinks are off by default on windows if Developer Mode was not turned on when git for windows was installed. Make sure developer mode is on then run git config --global core.symlinks true & git config --system core.symlinks true to enable it globally, and then git config --local core.symlinks true to fix your current checkout. Afterward, run git reset --hard to fix the symlinks in your checkout (make sure you don’t have any uncommitted changes.) The first time I run from a source build, I get an error like so: C:\motion-sim-workspace>src\dsim-grpc\src\bazel-bin\experimental\carla_coordinator\runtime\runner.exe --local-run
2023-03-23 10:32:26,309 ERROR:Unable to symlink latest logs dir This is OK (should not be printed/is a bug) and can be ignored. When I run runner on windows and try to directly execute the bazel-provided python wrapper exe, I get strange path errors about trying to open files/launch processes. On windows, bazel built programs cannot find the source directory from inside the bazel-bin workspace when NOT run by bazel i.e. not run like bazel run :runner -- ... Until this is fixed (may not be possible) always use bazel run //experimental/carla_coordinator/runtime:runner -- ... When running a ROS2 or GUI program on windows via ssh+process_wrapper, I get an error that looks like this: experimental/process_wrapper/server.cc:128:tri::stimulation::process_wrapper::ProcessWrapperServiceImpl::CheckLiveness Client didn't connect after 25 seconds, give up The way that process wrapper works requires that the ssh’d-to user on the target is also logged in on the interactive console (physical display). The error falls out from “the child process wasn’t launched because there was no screen, so I’m failing after some time.” After rebasing DSIM-GRPC, I am seeing failures in compiling with libCarla/CARLA class objects being incorrect DSIM-GRPC determines which version of CARLA to compile against by reading release-info from the workspace. Make sure your workspace is up to date and rebased against latest master. DSIM-GRPC may be compiling against the wrong version of libCarla. Run bazelisk shutdown after updating workspace, if not bazelisk clean --expunge will fix the problem When I try to build the ROS2 components with colcon, I get an error that looks like this: --- stderr: pcan_messages
make[2]: *** No rule to make target '/opt/ros/foxy/lib/libfastrtps.so.2.1.3', needed by 'libpcan_messages__rosidl_typesupport_fastrtps_cpp.so'.  Stop.
make[1]: *** [CMakeFiles/Makefile2:361: CMakeFiles/pcan_messages__rosidl_typesupport_fastrtps_cpp.dir/all] Error 2 This happens when the underlying system libraries get updated, and yet the old paths are still in some preexisting CMake-generated Makefiles. Remove the build, install, and log directories, and rebuild. When I use Windows Terminal as my default terminal, I am unable to communicate with some of the run processes/ros2 topic … gives bizzare results There’s an unknown issue when runner starts powershell.exe directly, where it seems like not using the windows console host as the outer process for powershell makes ROS communication not work properly. It is OK to use Windows Terminal as your terminal but until this is understood it can’t be made the default terminal. May be resolved as process_wrapper explicitly launches conhost.exe now. When building a new checkout on windows, I get errors about building proto files 1) Make sure you have the latest bazelisk executable 2) Make sure to run the script “prepare_env_for_building.bat” which will load the correct compiler. Otherwise you may get the wrong compiler paths …"
2234187879,2,1,link,Windows / ROS2 Foxy (source build),https://toyotaresearchinstitute.atlassian.net/wiki/spaces/DMS/pages/2234187879/Windows+ROS2+Foxy+source+build,2223669252,,2025-07-29T21:20:14.331Z,current,"FOXY support has ended. These instructions will not wok. Windows initial setup instructions Note that CI is built automatically by Ansible; the most authoritative directions on how to set up a windows machine should be described in https://github.awsinternal.tri.global/IE/build-ami/blob/master/images/windows_jenkins_motionsim/roles/motion-sim-windows/tasks/main.yml Install DirectX Legacy Redistributable Note: you may need to temporarily uninstall the VC2010 runtime due to a bug in the (old) DirectX code https://docs.microsoft.com/en-us/troubleshoot/windows/win32/s1023-error-when-you-install-directx-sdk , run in an admin command prompt: MsiExec.exe /passive /X{F0C3E5D1-1ADE-321E-8167-68EF0DE699A5}
MsiExec.exe /passive /X{1D8E6291-B0D5-35EC-8441-6616F567A0F7} Installer: https://download.microsoft.com/download/8/4/A/84A35BF1-DAFE-4AE8-82AF-AD2AE20B6B14/directx_Jun2010_redist.exe Install chocolatey https://chocolatey.org/install#individual Reinstall VC2010 runtime choco update -y vcredist2010 Install Visual Studio 2019 If you want an IDE, get Professional (ask IT to procure a license). https://aka.ms/vs/16/release/vs_professional.exe If you don’t want an IDE then installing the build tools form is sufficient, get the bootstrapper here: https://aka.ms/vs/16/release/vs_buildtools.exe Make sure to install C++ development tools; the machine readable list of packages to install for build tools is as follows, which can be passed to the bootstrapper with --channelUri https://aka.ms/vs/16/release/channel --config path\to\vsconfig (note that this list includes additional tools necessary to build CARLA/Unreal): {
  ""version"": ""1.0"",
  ""components"": [
    ""Microsoft.VisualStudio.Component.Roslyn.Compiler"",
    ""Microsoft.Component.MSBuild"",
    ""Microsoft.VisualStudio.Component.CoreBuildTools"",
    ""Microsoft.VisualStudio.Workload.MSBuildTools"",
    ""Microsoft.VisualStudio.Component.Windows10SDK"",
    ""Microsoft.VisualStudio.Component.VC.CoreBuildTools"",
    ""Microsoft.VisualStudio.Component.VC.Tools.x86.x64"",
    ""Microsoft.VisualStudio.Component.VC.Redist.14.Latest"",
    ""Microsoft.VisualStudio.Component.Windows10SDK.19041"",
    ""Microsoft.VisualStudio.Component.VC.CMake.Project"",
    ""Microsoft.VisualStudio.Component.TestTools.BuildTools"",
    ""Microsoft.Net.Component.4.8.SDK"",
    ""Microsoft.VisualStudio.Component.VC.ASAN"",
    ""Microsoft.VisualStudio.Component.TextTemplating"",
    ""Microsoft.VisualStudio.Component.VC.CoreIde"",
    ""Microsoft.VisualStudio.ComponentGroup.NativeDesktop.Core"",
    ""Microsoft.VisualStudio.Workload.VCTools"",
    ""Microsoft.NetCore.Component.Runtime.3.1"",
    ""Microsoft.NetCore.Component.Runtime.6.0"",
    ""Microsoft.NetCore.Component.SDK"",
    ""Microsoft.Net.Component.4.6.2.TargetingPack"",
    ""Microsoft.Net.Component.4.8.TargetingPack""
  ]
} Install ROS2 through choco choco source add -n=ros-win -s=""https://aka.ms/ros/public"" --priority=1
choco upgrade ros-foxy-desktop -y --execution-timeout=0 --pre Install git for windows Tested version is url: https://github.com/git-for-windows/git/releases/download/v2.36.1.windows.1/Git-2.36.1-64-bit.exe but most recent will likely be fine Enable symlinks globally: git config --system core.symlinks true
git config --global core.symlinks true Install python 3.8 Get the last 3.8 binary version: https://www.python.org/ftp/python/3.8.10/python-3.8.10-amd64.exe Install it: python-3.8.10-amd64.exe /quiet InstallAllUsers=1 ""TargetDir=C:\Python38"" Install VS2019 C++ redistributable: https://aka.ms/vs/16/release/VC_redist.x64.exe Make sure long paths are enabled: In an admin PowerShell prompt: New-ItemProperty -Path ""HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem"" -Name ""LongPathsEnabled"" -Value 1 -PropertyType DWORD -Force Then, reboot (sorry!) Install a few pip packages: C:\Python38\scripts\pip.exe install awscli numpy setuptools wheel Build steps: bash rem These are done in cmd.exe
<NAME_EMAIL>:tri-projects/motion-sim-workspace.git
cd motion-sim-workspace
rem Get bazelisk and populate environment with VC2019 & ROS
call scripts\prepare_env_for_building.bat
rem Init this directory:
mkdir src
for /r repos-common %i in (*.repos) do vcs import --recursive src -w 1 < %i
for /r repos-windows %i in (*.repos) do vcs import --recursive src -w 1 < %i
for /r repos-foxy %i in (*.repos) do vcs import --recursive src -w 1 < %i
git config --local core.symlinks true
git reset --hard
rem Install windows packages
pip install transforms3d awscli boto3
rem Note that merge install is a must - we have too many ROS packages
colcon build --merge-install
cd src\dsim-grpc\src
bazelisk.exe build ...
cd ..\..\..
rem Note that this step may fail if you have not set up AWS credentials and/or do not have access to the s3://scratch-tri-global bucket
.\scripts\prepare-local.bat
cd src\dsim-grpc\src
bazelisk.exe run //experimental/carla_coordinator/runtime:runner -- --local-run --ros2 --ros2-bag"
