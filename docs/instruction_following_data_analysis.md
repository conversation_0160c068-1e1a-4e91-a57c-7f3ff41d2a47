# Instruction Following Data Aggregation/Analysis Guide

To analyze the results and data from our "Quick Studies" study featuring instruction following, the main steps are:
1. Download the relevant data from S3
2. Download audio data/files from S3
3. Create a directory with map files
4. Run the `hid_common/scripts/create_quick_studies_24d13.py` script
5. Inspect resulting folders or move onto webdataset creation

### Data Location:
To get participant data, download data from:
`s3://tri-hid-data-shared-autonomy/fm-data/quick_study_instruction_following/participant_data/`

To get audio files, download data from: `s3://tri-hid-data-shared-autonomy/fm-data/quick_study_instruction_following/audio_files/`

### Map files

I created a directory in my home called `track_directory`, which holds `thunder_hill.csv` and `willow_springs.csv`.

You must have these 2 files, and they should be our standard `track.csv` files, but tailored for each map.

**NOTE** We do not currently have a Willow Springs map, so I am using the thunder hill map twice (but with different names).
This needs to be fixed eventually!

### Running the Script

Run the `create_quick_studies_24d13.py` script as follows:

```bash
python create_quick_studies_24d13.py \
--mcap-input-folder /home/<USER>/path/to/s3/download/participant_data/ \
--track-map-csv /home/<USER>/track_directory/ \
--trials-dir /home/<USER>/path/to/s3/download/participant_data/trials \
--plot-animation true \
--audio-file-dir /home/<USER>/path/to/s3/download/audio_files \
--split-trial-on-sound-name true
```

Replacing `path/to/s3/download` with your actual paths and replacing `<user>` with your actual username.

The `--trials_dir` argument is where your output data / videos will be saved.

You can set `--plot-animation false` if you want to speed things up and not get videos of the data.

Set `--split-trial-on-sound-name` to false (default) for entire laps, set it to true for snippets based on sounds being played.
