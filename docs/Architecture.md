# Design
[original design doc](https://docs.google.com/document/d/14QMlzE1J069jKB2fjuYrr0pkpIrv10iO6J0sYvDQf0o/edit)
# Terminology

[Terminology spreadsheet](https://docs.google.com/spreadsheets/d/11SO9yMxq5wQK1OkQvoDzPd4bXYNVbNCdM8pUlDtQr_c/edit#gid=0)
* Experiment
  * A continuously rosbag recorded for each participant
* Trial
  * A continuous run between start and end gate defined in the scenario
* Snippet
  * A continuous section clipped from an experiment or trial


# Data flow
[Link to Chart](https://lucid.app/lucidchart/773cbcb9-4ddf-43e8-a4f6-a6e84823028f/edit?page=0_0#)

# Data Notes
* All quaternions coming from carla (`/carla/objects`, `/carla/hero/vehicle_status`, etc) 
  are in ISO standard already (East = 0)

## Trials Data folder structure
*   Trials in this structure could be loaded from the `Trial.read_all()`
```
├── trials
    ├── P148_2023_11_08-13_50_31_0-trial_0
    │   ├── P148_2023_11_08-13_50_31_0-trial_0--overtake_v1_da2697cb.pkl
    │   ├── P148_2023_11_08-13_50_31_0-trial_0_v1.trial.pkl
    │   ├── anim.gif
    │   └── anim.png
    └── P148_2023_11_08-13_50_31_0-trial_1
        ├── P148_2023_11_08-13_50_31_0-trial_1--overtake_v1_da2697cb.pkl
        ├── P148_2023_11_08-13_50_31_0-trial_1_v1.trial.pkl
        ├── anim.gif
        └── anim.png
```

```

hid_data/
  workspace
    
  bucket [this is synced with s3 bucket, maybe we need a script to make it easy to sync specific subfolders]
    hid-shared-autonomy
      unit_test
      23-D-01
        trials_cache_2-13-2024/
```