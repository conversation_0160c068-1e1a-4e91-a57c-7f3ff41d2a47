# Resources on how to generate and run tests for code in this repo
## Where to find/add tests
    Unit tests should be added to the `test` directory. If there is already a file that contains tests for functions of a similar purpose (ie, testing for filters), add them to that file. Otherwise, create a new file for them.

## How to run tests
    Tests are currently set to run in CI and so any new code will have to pass all unit tests before it can be merged in.

## How to create a unit test
    The purpose of a unit test is to verify that a function is performing in the way it was intended. To do this, the unit test needs to provide enough different input data so as to test all possible outputs from the function. In order to make sure that the function is performing correctly, the reference output that is being checked against should be calculated and verified by hand given the same input values. There is an example of unit test creation below.

    This is how you might go about making a unit test for for a function that calculates the distance vector between 2 vehicles. The input to the function is a series of positions for 2 vehicles, and the output of the function is series of distance vectors. When you create fake data to put in, you then will want to calculate these vectors by hand and confirm that what comes out of the function matches what you expect. So for example, if car 1 is at (0, 0) and car2 is at (1, 0), you’ll want to make sure the distance vector that comes out has a value of 1. The input data should test all major categories of outputs by the function (in this example, in front, behind, at an angle, near, far away).