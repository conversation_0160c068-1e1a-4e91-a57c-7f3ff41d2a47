name: aic_bsd
channels:
  - pytorch
  - nvidia
dependencies:
  - boto3
  - conda-forge::awscli
  - conda-forge::addict
  - conda-forge::black-jupyter=23.12.1
  - conda-forge::ipdb
  - conda-forge::nbdime
  - conda-forge::pyquaternion
  - conda-lock
  - coverage
  - isort=5.13.2
  - jupyter
  - more-itertools
  - Pillow
  - pip
  - pre-commit
  - pytest
  - pytest-mock
  - pytest-timeout
  - pytest-xdist
  - python=3.11
  - scikit-learn
  - scipy
  - typeguard=4.*
  - Shapely
  - simplejson
  - tqdm
  - invoke
#  - easydict  # MTR
#  - pytorch::pytorch=2.2.*  # 2.0.1 and 2.1.0 should be avoided, https://github.com/pytorch/pytorch/issues/100974
#  - pytorch-cuda=11.8
#  - llvm-openmp<16 # https://github.com/pytorch/pytorch/issues/101850
  - pip:
      - setuptools
      - -e . --config-settings editable_mode=compat  # editable_mode=compat insure adding the package to sys.path
      - nvidia-ml-py3
#      - torch-scatter -f https://data.pyg.org/whl/torch-2.0.1+cu117.html # torch_scatter used by MPA
      - opencv-python
      - transformers==4.*
      - peft
      - draccus
      - jsonargparse
      - torchmetrics
      - pyarrow
      - srt
      - numpy<2.0
      - wandb
      - pandas
      - umap-learn
      - torch==2.4.1  --index-url https://download.pytorch.org/whl/cu118
      - matplotlib
      - lz4
      - imageio[ffmpeg]
      - git+ssh://**************/ToyotaResearchInstitute/hail_launch.git@main
      - grpcio-tools==1.63
      # XC improved version of cache_decorator, atomic_dump no race condition
      - git+ssh://**************/ToyotaResearchInstitute/cache_decorator.git@cxy
