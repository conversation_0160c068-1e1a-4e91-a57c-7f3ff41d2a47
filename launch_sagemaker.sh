#!/bin/bash

n=1  # change this to however many times you want to run it
set -e

for ((i=1; i<=n; i++)); do
  echo running i=$i
  echo running i=$i
  echo
  MY_TRAINING_SETUP_SCRIPT='bash download_data.sh' \
  python hid_common/hid_common_ml/hid_common_ml/sagemaker/launch_sagemaker.py \
    --training-script aic_bsd_model/training/train_skill_hf.py \
    --training-args '--run-description sagemaker_test --use-skill-embedding false --config-str "global_num_epochs=10"' \
    --instance-type ml.g5.2xlarge \
    --run-name bsd-${i}
done
