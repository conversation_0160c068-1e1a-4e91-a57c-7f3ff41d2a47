import argparse
import logging
import pickle
from datetime import time
from io import String<PERSON>
from typing import Dict, List

import Geometry3D as g3d
import numpy as np
import pandas as pd
import srt
import tqdm
from scipy.spatial.transform import Rotation as R
from shapely import Polygon, geometry

SEARCH_PARAMS_KEYS_EPISODE_ID = "episode_id"
SEARCH_PARAMS_KEYS_ANNOTATION_KEY = "annotation_key"
SEARCH_PARAMS_KEYS_EPISODE_MARGIN = "episode_margin"


def str2bool(v: str):
    if isinstance(v, bool):
        return v
    if v.lower() in ("yes", "true", "t", "y", "1"):
        return True
    elif v.lower() in ("no", "false", "f", "n", "0"):
        return False
    else:
        raise argparse.ArgumentTypeError("Boolean value expected.")


def search_episode_transitions(search_params: Dict, annotation_data: Dict):
    """
    Feeder for search_transitions which accepts a dict of episode data and converts it
    for consumption by search_transitions

    Parameters
    ----------
    search_params : Dict
        A dictionary with 'episode_key', 'annotation_key' and 'episode_margin' (the
        margin around the beginning/end of the episode to ignore detections, e.g. due
        to initialization of filters)
    annotation_data : Dict
        The annotation data to search in.

    Returns
    -------
    Dict
        A dictionary with 'detected_timestamps' to capture all the time instances that
        a transition from 0->1 occured for annotation_key in episode_id.
    """
    if SEARCH_PARAMS_KEYS_EPISODE_ID in search_params:
        episode_keys = [search_params[SEARCH_PARAMS_KEYS_EPISODE_ID]]
    else:
        episode_keys = list(annotation_data.keys())

    results = {}
    for episode_key in episode_keys:
        episode = annotation_data[episode_key][search_params[SEARCH_PARAMS_KEYS_ANNOTATION_KEY]][
            "annotation_df"
        ]
        results[episode_key] = search_transitions(search_params, pd.DataFrame.from_dict(episode))

    return results


def search_transitions(search_params: Dict, dataframe: pd.DataFrame):
    """
    Search through a boolean dataframe and return only rising edges and their
    corresponding timestamps. Also allow the user to specify a margin around the start
    and end of the dataframe to ignore to avoid spurious detections from data during
    the construction or destruction of a scenario.

    Parameters
    ----------
    search_params : Dict
        A dictionary with 'annotation_key' and 'episode_margin' (the margin around the
        beginning/end of the episode to ignore detections, e.g. due to initialization
        of filters)
    annotation_data : Dict
        The annotation data to search in.

    Returns
    -------
    Dict
        A dictionary with 'detected_timestamps' to capture all the time instances that
        a transition from 0->1 occured for annotation_key in episode_id.
    """
    if SEARCH_PARAMS_KEYS_ANNOTATION_KEY in search_params:
        annotation_key = search_params[SEARCH_PARAMS_KEYS_ANNOTATION_KEY]
    else:
        raise Exception(f"Missing search params key: {SEARCH_PARAMS_KEYS_ANNOTATION_KEY}")
    if SEARCH_PARAMS_KEYS_EPISODE_MARGIN in search_params:
        episode_margin = search_params[SEARCH_PARAMS_KEYS_EPISODE_MARGIN]
    else:
        episode_margin = 0.0

    results = {}
    if annotation_key in dataframe.keys() and dataframe[annotation_key] is not None:
        # TODO(guy.rosman): search, add to results
        res_vec = dataframe[annotation_key].fillna(
            value=False
        )  # Avoid incorrect detections by filling any NaN with False.
        timestamps = dataframe["carla_objects log time"]
        # Any rising or falling edge will be returned by `convolve` as a 1 or -1 respectively.
        # `valid` prevents an initial False value or trailing True value from being detected incorrectly.
        # Eliminate falling edge detections by checking for only positive values, and get their indices with `nonzero`
        detection_indices = np.nonzero(np.convolve(res_vec, [1, -1], "valid") > 0)[0]
        detected_timestamps = np.array([timestamps[x] for x in detection_indices])
        # Keep only timestamps that are not at the begining/end of the episode, as defined by episode_margin seconds.
        valid_time = np.logical_and(
            detected_timestamps - np.min(timestamps) > episode_margin,
            np.max(timestamps) - detected_timestamps > episode_margin,
        )

        results["detected_timestamps"] = np.array(detected_timestamps)[valid_time]
        results["detected_indices"] = detection_indices[valid_time]
    return results


def load_annotation(annotation_data_filename: str):
    """Load annotation dictionary

    Parameters
    ----------
    annotation_data_filename : str
        The name for the file containing annotations.

    Returns
    -------
    Dict_
        the loaded annotations dictionary.
    """
    with open(annotation_data_filename, "rb") as f:
        result = pickle.load(f)
    return result


def save_annotation(annotation_data_filename: str, annotation_data_dict: Dict):
    """Save annotation dictionary.

    Parameters
    ----------
    annotation_data_filename : str
        The name for the file containing annotations. Assumes this file should be overwritten.
    annotation_data_dict : Dict
        The dictionary of annotation data to save.
    """
    with open(annotation_data_filename, "wb") as f:
        pickle.dump(annotation_data_dict, f)
    return


def get_scenario_data(vehicle_data, start_time, end_time):
    """
    Extracts a subset of vehicle data within a specified time range.
    This function filters the input DataFrame `vehicle_data` to include only the rows where
    the "carla_objects log time" is between `start_time` and `end_time`, inclusive.
    Parameters:
    vehicle_data (pd.DataFrame): The DataFrame containing vehicle data with a column named "carla_objects log time".
    start_time (float): The start time for filtering the data, relative to "carla_objects log time".
    end_time (float): The end time for filtering the data, relative to "carla_objects log time".
    Returns:
    pd.DataFrame: A DataFrame containing the filtered vehicle data within the specified time range.
    """

    trimmed_df = vehicle_data[
        (vehicle_data["carla_objects log time"] >= start_time)
        & (vehicle_data["carla_objects log time"] <= end_time)
    ]
    return trimmed_df


def split_df_by_map_segments(df):
    """
    Split a data frame into smaller chunks according to the map segment id.

    Parameters
    ----------

    df: pd.DataFrame
        Dataframe to be chunked

    Returns
    -------
    segment_dfs: List[pd.DataFrame] or []
        List of dataframes each of which corresponds to a segment.
    """
    if "map_segment_ids" not in df.keys():
        return []
    else:
        segment_ids = set(df["map_segment_ids"].values)
        segment_dfs = []
        for segment_id in segment_ids:
            # TODO (deepak.gopinath) If a trial lasts more than a lap then each segment_df
            # can have non-contiguous data.
            segment_df = df[df["map_segment_ids"] == segment_id]
            segment_dfs.append((segment_id, segment_df))
        return segment_dfs


def get_start_end_time_subtitle(subtitle_srt):
    start_srt_time = subtitle_srt.start.seconds + subtitle_srt.start.microseconds * 10**-6
    end_srt_time = subtitle_srt.end.seconds + subtitle_srt.end.microseconds * 10**-6
    return start_srt_time, end_srt_time


def timestamp_to_python_time(timestamp: float) -> time:
    """Convert a timestamp (seconds from beginning) to a python time object

    Parameters
    ----------
    timestamp : float
        Seconds since episode beginning

    Returns
    -------
    time
        A python time object that captures the hours/minutes/seconds since episode
        beginning.
    """
    hour = int(timestamp // 3600)
    min = int(timestamp - hour * 3600) // 60
    sec = int(timestamp - hour * 3600 - min * 60)
    timepoint = time(hour, min, sec, 0)
    return timepoint


def get_yaw_from_quaternion(df, name, deg=True):
    """
    Extract yaw from a quaternion in a dataframe.
    Yaw defaults to returning in degrees.

    Parameters
    ----------
    df : pd.DataFrame
        The dataframe containing the quaternion.
    name : str
        The name of the object/vehicle to which the quaternion corresponds.
    deg : bool, optional
        Whether to return the yaw in degrees (vs radians), by default True.

    Returns
    -------
    np.array
        The yaw of the object/vehicle in degrees or radians.
    """
    try:
        yaw = R.from_quat(
            np.array(
                [
                    df[f"{name}_orientation_x"],
                    df[f"{name}_orientation_y"],
                    df[f"{name}_orientation_z"],
                    df[f"{name}_orientation_w"],
                ]
            ).T
            # from_quat expects a shape of (N, 4) or (4, ).
            # We transpose here to get the correct shape.
        ).as_euler("xyz", degrees=deg)[..., 2]
    except ValueError as e:
        logging.error(f"Error when parsing quaternion for {name}: {e}")
        yaw = 0

    return yaw


def compute_tangent_normal(polyline_x: List, polyline_y: List, params: Dict = None):
    """
    Compute tangents and normals after resampling

    Parameters
    ----------
    polyline_x : List
        x coordinates of the polyline.
    polyline_y : List
        y coordinates of the polyline.
    params : Dict, optional
        A parameters dictionary, by default None. Includes 'tangent_delta_s' for changes the step
        size, and 'sampling_step' for resampling the polyline.

    Returns
    -------
    Dict
        A result dictionary with 'linestring' (the resulting resampled LineString), 'tangent',
        and 'normal (np.array's of the tangent and normal coordinates around the resampled polyline
        points)
    """
    result = {}
    if params is None:
        params = {}

    linestring = geometry.LineString(zip(polyline_x, polyline_y))
    # A step size for computing the tangent on the polyline.
    ds = params.get("tangent_delta_s", 1)  # TODO(guy.rosman): use to allow bigger jumps
    sampling_step = params.get("sampling_step", 1)
    # List of shapely.geometry.point.Point objects
    resampled = [linestring.interpolate(s) for s in np.arange(0, linestring.length, sampling_step)]
    # (resampled N, 2)
    resampled_arr = np.array([(x.xy[0][0], x.xy[1][0]) for x in resampled])
    resampled_linestring = geometry.LineString(resampled_arr)

    # (resampled N-1, 2)
    tangent = resampled_arr[1:, :] - resampled_arr[:-1, :]
    # normalized the tangent vectors. 1e-10 to avoid divide by 0.0
    tangent = tangent / np.expand_dims(np.sqrt(np.sum(tangent**2, 1) + 1e-10), 1)
    # normal to tangent.
    normal = np.array([tangent[:, 1], -tangent[:, 0]]).transpose()
    # normalize the normal
    normal = normal / np.expand_dims(np.linalg.norm(np.array(normal), axis=1), 1)
    # add a normal computation for the last data point as well (forward and backward derivatives, but same array size)
    # essentially copying and pasting the last row
    normal = normal[list(range(tangent.shape[0])) + [-1], :]
    tangent = tangent[list(range(tangent.shape[0])) + [-1], :]
    result["resampled_line"] = resampled_arr
    result["linestring"] = resampled_linestring
    result["tangent"] = tangent
    result["normal"] = normal
    return result


def compute_intervehicle_metrics(vehicle_data: pd.DataFrame, car1_keys: list, car2_keys: list):
    """
    Compute the distance between two vehicles at each time step, the dot product of the
    vehicles' vectors, and distance vector rotated into the first vehicle's frame.
    This information is useful for making determinations about the relative positions
    of the two vehicles.

    Parameters
    ----------
    vehicle_data : pd.DataFrame
        pandas dataframe for an episode containing data for both vehicles.
    car1_keys : list
        list of keys pointing to position and orientation of the first vehicle.
        Structure of the list should be as follows:
        ["veh_x", "veh_y", "carla_objects log time", "veh_quat_x", "veh_quat_y", "veh_quat_z", "veh_quat_w"]
    car2_keys : list
        list of keys pointing to position and orientation of the second vehicle.
        Structure of the list should be as follows:
        ["veh_x", "veh_y"]

    Returns
    -------
    Tuple[np.array, np.array, np.array]
        The distance between the two vehicles at each time step
        The dot product of the car1-car2 vector wrt. the car1 orientation.
        The car1-car2 vector in car1's frame.
    """
    car1_x = vehicle_data[car1_keys[0]]
    car1_y = vehicle_data[car1_keys[1]]

    car1_o = get_yaw_from_quaternion(vehicle_data, car1_keys[3].split("_")[0], deg=False)
    car1_o_coords = np.column_stack([np.cos(car1_o), np.sin(car1_o)])

    car2_x = vehicle_data[car2_keys[0]]
    car2_y = vehicle_data[car2_keys[1]]

    dist_x = car2_x - car1_x
    dist_y = car2_y - car1_y
    dist = np.sqrt(np.power(dist_x, 2) + np.power(dist_y, 2))
    dist_dot = (car1_o_coords * np.column_stack([dist_x.values, dist_y.values])).sum(1)

    # Transform the distance vector into car1's frame and see if the y component is
    # +- for left/right overtake
    # + corresponds to car1's left
    # - corresponds to car1's right
    rotated_car1_car2 = (
        R.from_euler("Z", car1_o)
        .inv()
        .apply(np.column_stack([dist_x, dist_y, np.zeros(dist_x.shape)]))
    )
    return dist, dist_dot, rotated_car1_car2


def build_bbox(x: np.array, y: np.array, z: np.array, quat: np.array, bbox_sides: np.array):
    """
    Construct an array containing the vertices of a bbox in the provided coordinate frame.

    Parameters
    ----------
    x: np.array
        Array of x-coordinates of center of bounding box. Shape N x 1.
    y: np.array
        Array of y-coordinates of center of bounding box. Shape N x 1.
    z: np.array
        Array of z-coordinates of center of bounding box. Shape N x 1.
    quat: np.array
        Array of quaternions between global frame and bounding box frame in [x, y, z, w] format.
        Shape is N x 4.
    bbox_sides: np.array
        Array of lengths of the bounding box sides in [x, y, z] format. Shape is N x 3.

    Returns
    -------
    np.array of vertices of the bounding box at each step. Shape N x 8 x 3.

    """
    rot = []
    for q in quat:
        if np.any(np.isnan(q)):
            rot.append(R.identity())
            continue

        try:
            rot.append(R.from_quat(q))
        except ValueError as e:
            logging.error(f"Error when parsing quaternion for bbox rot: {e}")
    rot = np.array(rot)
    # Get the distance from the center to each edge, which requires dividing sides by 2.
    dx = bbox_sides[:, 0] / 2
    dy = bbox_sides[:, 1] / 2
    dz = bbox_sides[:, 2] / 2
    bbox_local = np.moveaxis(
        np.array(
            [
                [dx, dy, dz],
                [dx, -dy, dz],
                [-dx, -dy, dz],
                [-dx, dy, dz],
                [-dx, dy, -dz],
                [-dx, -dy, -dz],
                [dx, -dy, -dz],
                [dx, dy, -dz],
            ]
        ),
        -1,
        0,
    )
    center = np.concatenate([x, y, z], axis=1)
    rot_shift_bbox = np.empty((0, 8, 3))
    for i, bbox in enumerate(bbox_local):
        rot_shift_bbox = np.append(
            rot_shift_bbox, np.expand_dims(rot[i].apply(bbox) + center[i, :], axis=0), axis=0
        )

    return rot_shift_bbox


def flatten_bbox(bbox_vertices: np.array):
    """
    Flatten the bounding box vertices into a 2D array.

    Parameters
    ----------
    bbox_vertices: np.array
        Array of vertices for bounding box in 3D space. Shape N x 8 x 3.

    Returns
    -------
    np.array of flattened vertices for plotting. Shape N x 16 x 2.

    """
    flat_vertices = bbox_vertices[:, :, :2]

    # Sort the vertices to make sure the resulting 2d polygon is a convex hull
    for i, vertices in enumerate(flat_vertices):
        # Get the centroid of the vertices
        cx, cy = vertices.mean(0)
        # Transform vertices into format acceptable by np.arctan2
        x, y = vertices.T
        angles = np.arctan2(x - cx, y - cy)
        # Sort the angles in a clockwise manner
        indices = np.argsort(-angles)
        flat_vertices[i] = vertices[indices]

    return flat_vertices


def check_intersect(bbox1_vertices: np.array, bbox2_vertices: np.array):
    """
    Check whether two 2D bounding box polygons intersect with each other.

    Parameters
    ----------
    bbox1_vertices: np.array
        Array of vertices for first bounding box in 2D space. Shape N x 16 x 2.
    bbox2_vertices: np.array
        Array of vertices for second bounding box in 2D space. Shape N x 16 x 2.

    Returns
    -------
    np.array of booleans indicating whether there is an intersection at each step. Shape N x 1.

    """
    overlap = np.full(bbox1_vertices.shape[0], False, dtype=bool)
    for i, _ in enumerate(bbox1_vertices):
        if np.any(np.isnan(bbox1_vertices[i])) or np.any(np.isnan(bbox2_vertices[i])):
            overlap[i] = False
            continue
        bbox1 = Polygon(bbox1_vertices[i])
        bbox2 = Polygon(bbox2_vertices[i])
        overlap[i] = bbox1.intersects(bbox2)
    return overlap


def check_3d_overlap(bbox1_vertices, bbox2_vertices):
    """
    Check whether two 3D bounding boxes overlap with each other.

    Parameters
    ----------
    bbox1_vertices: np.array
        Array of vertices for first bounding box in 3D space. Shape N x 8 x 3.
    bbox2_vertices: np.array
        Array of vertices for second bounding box in 3D space. Shape N x 8 x 3.

    Returns
    -------
    np.array of booleans indicating whether there is an overlap at each step. Shape N x 1.

    """
    overlap = np.full(bbox1_vertices.shape[0], False, dtype=bool)
    for i, _ in enumerate(bbox1_vertices):
        if np.any(np.isnan(bbox1_vertices[i])) or np.any(np.isnan(bbox2_vertices[i])):
            overlap[i] = False
            continue
        base_pt = g3d.Point(bbox1_vertices[i, 0, :].flatten().tolist())
        vec1 = g3d.Vector((bbox1_vertices[i, 1, :] - bbox1_vertices[i, 0, :]).flatten().tolist())
        vec2 = g3d.Vector((bbox1_vertices[i, 3, :] - bbox1_vertices[i, 0, :]).flatten().tolist())
        vec3 = g3d.Vector((bbox1_vertices[i, 7, :] - bbox1_vertices[i, 0, :]).flatten().tolist())
        try:
            bbox1 = g3d.Parallelepiped(base_pt, vec1, vec2, vec3)
        except ValueError as e:
            print(f"Error constructing bbox1 to check overlap: {e}")
            print(
                f"vectors: {(bbox1_vertices[i, 1, :] - bbox1_vertices[i, 0, :]).flatten().tolist()}, {(bbox1_vertices[i, 3, :] - bbox1_vertices[i, 0, :]).flatten().tolist()}, {(bbox1_vertices[i, 7, :] - bbox1_vertices[i, 0, :]).flatten().tolist()}"
            )
            overlap[i] = False
            continue

        base_pt = g3d.Point(bbox2_vertices[i, 0, :].flatten().tolist())
        vec1 = g3d.Vector((bbox2_vertices[i, 1, :] - bbox2_vertices[i, 0, :]).flatten().tolist())
        vec2 = g3d.Vector((bbox2_vertices[i, 3, :] - bbox2_vertices[i, 0, :]).flatten().tolist())
        vec3 = g3d.Vector((bbox2_vertices[i, 7, :] - bbox2_vertices[i, 0, :]).flatten().tolist())
        try:
            bbox2 = g3d.Parallelepiped(base_pt, vec1, vec2, vec3)
        except ValueError as e:
            print(f"error constructing bbox2 to check overlap: {e}")
            print(
                f"vectors: {(bbox2_vertices[i, 1, :] - bbox2_vertices[i, 0, :]).flatten().tolist()}, {(bbox2_vertices[i, 3, :] - bbox2_vertices[i, 0, :]).flatten().tolist()}, {(bbox2_vertices[i, 7, :] - bbox2_vertices[i, 0, :]).flatten().tolist()}"
            )
            overlap[i] = False
            continue

        # Check overlap
        assert bbox1.intersection(bbox2) == bbox2.intersection(bbox1)
        overlap[i] = bbox1.intersection(bbox2) is not None
    # Return logical and of 2 results
    return overlap


def get_next_cone_placements(vehicle_dataframe, track_map, cones_coordinates):
    data = np.array(cones_coordinates)
    cone_x, cone_y = data[:, 0], (-1) * data[:, 1]
    veh_x, veh_y = np.array(vehicle_dataframe["ego_x"]), np.array(vehicle_dataframe["ego_y"])
    track_x = np.array(track_map["refline/x"])
    track_y = np.array(track_map["refline/y"])
    cone_long_coords, cone_lat_dist, _ = compute_lat_lon_coords(track_x, track_y, cone_x, cone_y)
    ego_long_coords, ego_lat_dist, _ = compute_lat_lon_coords(track_x, track_y, veh_x, veh_y)
    pos = []
    for long_coord, lat_dist in zip(ego_long_coords, ego_lat_dist):
        # difference between current ego position and all cones
        cone_ego_long_diff = cone_long_coords - long_coord
        # positive distances only since the negatives ones are behind
        positive_dist = cone_ego_long_diff[cone_ego_long_diff >= 0]
        # if all cones distances are negative, append -1
        if len(positive_dist) == 0:
            pos.append(-1)
        else:
            # Find index of the smallest distance
            index = np.argmin(positive_dist)
            # Find the corresponding index in the original array
            nearest_cone_index = np.where(cone_ego_long_diff == positive_dist[index])[0][0]
            # if lateral distance of cone is more, put to right(1) else left(0)
            if cone_lat_dist[nearest_cone_index] > lat_dist:
                pos.append(1)
            else:
                pos.append(0)

    return pos


def compute_lat_lon_coords(ref_line_x, ref_line_y, pt_x, pt_y):
    """
    Compute the longitudinal distance along the reference line and the lateral distance
    from the reference line of a set of points.

    Parameters
    ----------
    ref_line_x: np.array
        Array of x coordinates of the reference line.
    ref_line_y: np.array
        Array of y coordinates of the reference line.
    pt_x: np.array
        Array of x coordinates of the points.
    pt_y: np.array
        Array of y coordinates of the points.

    Returns
    -------
    np.array of booleans indicating whether there is an overlap at each step. Shape N x 1.
    """
    # TODO(guy.rosman): add more differential geometry properties later - such as curvature, etc.
    diff_geom = compute_tangent_normal(ref_line_x, ref_line_y)
    ref_array = diff_geom["resampled_line"]

    dist_vecs = []
    nearest_idxs = []
    nn_normal_vectors = []
    longitudinal_coords = []
    # Compute nearest points in the reference polyline and their properties
    for i in tqdm.tqdm(range(len(pt_x))):
        pt = np.array([pt_x[i], pt_y[i]])
        # distance (along the linestring) from the start of the linestring to the point on the
        # linestring that is closest to pt
        proj_pt_s = diff_geom["linestring"].project(geometry.Point(pt))
        # coordinates of the interpolated point on the linestring that is closest to pt
        proj_pt = np.array(diff_geom["linestring"].interpolate(proj_pt_s).xy)[:, 0]

        d_square_vec = np.sum((ref_array - proj_pt) ** 2, 1)
        idx = np.argmin(d_square_vec)
        nearest_idxs.append(idx)
        longitudinal_coords.append(proj_pt_s)
        nn_normal_vectors.append(diff_geom["normal"][idx, :])
        dist_vecs.append((pt - proj_pt))

    # longitudinal coordinates along the racing line
    longitudinal_coords = np.array(longitudinal_coords)
    # projection of perpendicular to the normal vector, with proper sign
    lateral_distances = np.sum(np.array(dist_vecs) * np.array(nn_normal_vectors), 1)

    return longitudinal_coords, lateral_distances, diff_geom


def parse_srt_content(srt_content):
    """
    Check and parse SRT subtitles.

    Args:
        srt_content (str): SRT formatted subtitle content.

    Returns:
        list: Parsed subtitles as SRT subtitle objects.
    """
    subtitles = list(srt.parse(StringIO(srt_content).read()))
    return subtitles
