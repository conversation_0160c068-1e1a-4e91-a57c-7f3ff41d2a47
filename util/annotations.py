import json
from pathlib import Path

import pandas as pd

from util.filters.trajectory_filters import (
    annotate_lat_long_coordinates,
    detect_ego_ado_proximity,
    detect_out_of_bounds,
    detect_overtake,
    detect_spinout,
    generate_concurrent_annotation_df,
    generate_transcript_df,
    mark_map_segment_ids,
)
from util.trial import Annotation, AutoAnnotation, ManualAnnotation
from util.visualization.visualization import (
    VIS_PARAM_ANIM_OUTPUT_DIR,
    VIS_PARAM_ANIM_TYPE,
    VIS_PARAM_EGO_CENTRIC,
    VIS_PARAM_FPS,
    VIS_PARAM_SHOW_COLUMNS,
    VIS_PARAM_SHOW_RACING_LINE,
    VIS_PARAM_SUBTITLE,
    VIS_PARAM_SUFFIX,
    VIS_PARAM_TIME_WINDOW,
    visualize_animation,
)


class OvertakeAnnotation(AutoAnnotation):
    VERSION_NUM = 1
    FILTER_NAME = "overtake"

    def __init__(self):
        super().__init__()

    def _get_hash_param_keys(self, params):
        return ["dist_threshold"]

    def _filter_impl(self, trial, track_map, params):
        overtake_result = detect_overtake(
            trial.get_dataframe(),
            reference_track_data=track_map,
            dist_threshold=params["dist_threshold"],
        )
        return overtake_result


class EgoAdoProximityAnnotation(AutoAnnotation):
    VERSION_NUM = 1
    FILTER_NAME = "ego_ado_proximity"

    def __init__(self):
        super().__init__()

    def _get_hash_param_keys(self, params):
        return ["dist_threshold"]

    def _filter_impl(self, trial, track_map, params):
        proximity_result = detect_ego_ado_proximity(
            trial.get_dataframe(),
            dist_threshold=params["dist_threshold"],
        )
        return proximity_result


class MapSegmentAnnotation(AutoAnnotation):
    VERSION_NUM = 1
    FILTER_NAME = "map_segment_ids"

    def __init__(self):
        super().__init__()

    def _get_hash_param_keys(self, params):
        return ["map_name"]  # The unique aspect would be the map name.

    def _filter_impl(self, trial, track_map, params):
        map_segments_ids_result = mark_map_segment_ids(
            trial.get_dataframe(), reference_track_data=track_map
        )
        return map_segments_ids_result


class LatLongAnnotation(AutoAnnotation):
    VERSION_NUM = 1
    FILTER_NAME = "lat_long_coordinates"

    def __init__(self):
        super().__init__()

    def _get_hash_param_keys(self, params):
        return ["map_name"]  # The unique aspect would be the map name.

    def _filter_impl(self, trial, track_map, params):
        lat_long_results = annotate_lat_long_coordinates(trial.get_dataframe(), track_map)
        return lat_long_results


class OutOfBoundsAnnotation(AutoAnnotation):
    VERSION_NUM = 1
    FILTER_NAME = "out_of_bounds"

    def __init__(self):
        super().__init__()

    def _get_hash_param_keys(self, params):
        return ["map_name"]  # The unique aspect would be the map name.

    def _filter_impl(self, trial, track_map, params):
        out_of_bounds_results = detect_out_of_bounds(trial.get_dataframe(), track_map)
        return out_of_bounds_results


class SpinoutAnnotation(AutoAnnotation):
    VERSION_NUM = 2
    FILTER_NAME = "spinout"

    def __init__(self):
        super().__init__()

    def _get_hash_param_keys(self, params):
        return ["slip_thresh"]

    def _filter_impl(self, trial, track_map, params):
        spinout_results = detect_spinout(
            trial.get_dataframe(), track_map, slip_thresh=params["slip_thresh"]
        )
        return spinout_results


class PlotAnimationAnnotation(AutoAnnotation):
    VERSION_NUM = 1
    FILTER_NAME = "animation"

    def __init__(self):
        super().__init__()
        self.anim_path = None

    def _get_hash_param_keys(self, params):
        return ["fps", "dpi"]

    def _filter_impl(self, trial, reference_track_data: pd.DataFrame, params: dict) -> dict:
        pass

    def _make_anim_path(self, trial, suffix):
        self.anim_path = trial.data_dir / f"{trial.uid}_anim_v{self.VERSION_NUM}.{suffix}"

    def filter(
        self,
        trial,
        reference_track_data: pd.DataFrame,
        params: dict,
        other_inputs: dict,
    ):
        self._make_anim_path(trial, params[VIS_PARAM_SUFFIX])
        if not self.anim_path.exists():
            plot_cones = True if "plot_cones" in params else False
            visualize_animation(
                vehicle_data=trial.get_dataframe(),
                reference_track_data=reference_track_data,
                params={
                    VIS_PARAM_FPS: params["fps"],
                    "dpi": params["dpi"],
                    VIS_PARAM_ANIM_OUTPUT_DIR: str(self.anim_path),
                    VIS_PARAM_TIME_WINDOW: 10,
                    "display": False,
                    VIS_PARAM_SHOW_COLUMNS: None,
                    VIS_PARAM_SHOW_RACING_LINE: False,
                    VIS_PARAM_ANIM_TYPE: "BEV",
                    VIS_PARAM_SUFFIX: params[VIS_PARAM_SUFFIX],
                    "plot_cones": plot_cones,
                },
            )

        return {"annotation_df": pd.DataFrame(), "additional_dict": {}}

    def merge_data(self, trial, params, other_inputs):
        assert self.anim_path, "Call self.read() before this function."
        trial.data["anim_path"] = self.anim_path

    def write(self, uid, dir_, data, params_hash):
        pass

    def read(self, trial, params):
        self._make_anim_path(trial, params[VIS_PARAM_SUFFIX])

        self.result = {"anim_path": self.anim_path}
        return self.result


class EgoCenterAnimationAnnotation(AutoAnnotation):
    VERSION_NUM = 1
    FILTER_NAME = "ego_centric_animation"

    def __init__(self):
        super().__init__()
        self.anim_path = None

    def _get_hash_param_keys(self, params):
        return ["fps", "dpi"]

    def _filter_impl(self, trial, reference_track_data: pd.DataFrame, params: dict) -> dict:
        pass

    def _make_anim_path(self, trial, suffix):
        self.anim_path = (
            trial.data_dir / f"{trial.uid}_ego_center_anim_v{self.VERSION_NUM}.{suffix}"
        )

    def filter(
        self,
        trial,
        reference_track_data: pd.DataFrame,
        params: dict,
        other_inputs: dict,
    ):
        self._make_anim_path(trial, params[VIS_PARAM_SUFFIX])
        if not self.anim_path.exists():
            plot_cones = True if "plot_cones" in params else False
            visualize_animation(
                vehicle_data=trial.get_dataframe(),
                reference_track_data=reference_track_data,
                params={
                    VIS_PARAM_FPS: params["fps"],
                    "dpi": params["dpi"],
                    VIS_PARAM_ANIM_OUTPUT_DIR: str(self.anim_path),
                    VIS_PARAM_TIME_WINDOW: 10,
                    "display": False,
                    VIS_PARAM_SHOW_COLUMNS: None,
                    VIS_PARAM_SHOW_RACING_LINE: False,
                    VIS_PARAM_ANIM_TYPE: VIS_PARAM_EGO_CENTRIC,
                    VIS_PARAM_SUFFIX: params[VIS_PARAM_SUFFIX],
                    VIS_PARAM_SUBTITLE: params.get(VIS_PARAM_SUBTITLE, None),
                    "plot_cones": plot_cones,
                    **params,  # Add all other parameters into this dict. Could probably also replace "dpi"/"fps", etc...
                },
            )

        return {"annotation_df": pd.DataFrame(), "additional_dict": {}}

    def merge_data(self, trial, params, other_inputs):
        assert self.anim_path, "Call self.read() before this function."
        trial.data["anim_path"] = self.anim_path

    def write(self, uid, dir_, data, params_hash):
        pass

    def read(self, trial, params):
        self._make_anim_path(trial, params[VIS_PARAM_SUFFIX])

        self.result = {"anim_path": self.anim_path}
        return self.result


class SrtManualAnnotation(ManualAnnotation):
    """
    SRT refers to the standard SubRip subtitle file format. This class parses .srt files
    and aligns the subtitles contained in these files with the associated trajectory dataframe.
    Since timestamps in .srt files are with respect to the start time of the audio file from which
    the transcriptions were generated, this class also relies on absolute ros time for proper alignment
    and then merges the subtitle dataframe with the trajectory frame with respect to the "carla objects log time" column
    """

    VERSION_NUM = 1
    FILTER_NAME = "srt"
    AUX_INFO_KEYS = [
        "subject_coach_srts",
        "subject_driver_srts",
        "initial_carla_objects_log_time_for_each_srt",
        "initial_timestamp_for_each_srt",
    ]

    def __init__(self):
        super().__init__()

    def _get_hash_param_keys(self, params):
        return []

    def _prepare_manual_annotation_df_impl(
        self, trial, reference_track_data, params, other_inputs
    ):
        srt_results = generate_transcript_df(
            trial.get_dataframe(), reference_track_data, params, other_inputs[self.FILTER_NAME]
        )
        return srt_results


class ConcurrentFeedbackAnnotation24D05(ManualAnnotation):
    """
    This class parse the concurrent feedback annotations from Crest and merges it with the dataframe.
    The annotation for each trial will be in a csv, where each row will contain
    1. The unique utterance in order of occurence in a trial.
    2. Annotations (type, category, subcategory) from each of the 5 annotators.

    """

    VERSION_NUM = 1
    FILTER_NAME = "concurrent_feedback_24d05"

    def __init__(self):
        super().__init__()

    def _get_hash_param_keys(self, params):
        return []

    def _prepare_manual_annotation_df_impl(
        self, trial, reference_track_data, params, other_inputs
    ):
        concurrent_annotation_results = generate_concurrent_annotation_df(
            trial.get_dataframe(), reference_track_data, params, other_inputs[self.FILTER_NAME]
        )
        return concurrent_annotation_results
