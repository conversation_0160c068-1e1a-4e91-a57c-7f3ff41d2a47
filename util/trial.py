import abc
import hashlib
import json
import logging
from abc import ABC
from pathlib import Path

import pandas as pd
import yaml
from pandas.core.api import DataFrame as DataFrame

from util.utility import split_df_by_map_segments


def get_self_default(var, self_var):
    var = var if var else self_var
    assert var
    return var


class Trial:
    VERSION_NUM = 2

    def __init__(self, uid=None, dataframe=None):
        self.uid = uid
        self.data = None
        if dataframe is not None:
            self.data = {
                "dataframe": dataframe,
                "uid": self.uid,
                "version_num": self.VERSION_NUM,
                "additional_annotation_dict": {},
                "additional_metrics_dict": {},
            }
        self.auto_annotation = []
        self.manual_annotation = []
        self.data_dir: Path = None

    @property
    def dataframe(self):
        assert self.data is not None, "this trial is not loaded from file yet."
        return self.data["dataframe"]

    def get_dataframe(self):
        assert self.data is not None, "this trial is not loaded from file yet."
        return self.data["dataframe"]

    def set_dataframe(self, dataframe):
        self.data["dataframe"] = dataframe

    def get_additional_annotation_dict(self):
        assert self.data is not None, "this trial is not loaded from file yet."
        if "additional_annotation_dict" not in self.data:
            self.data["additional_annotation_dict"] = {}
        return self.data["additional_annotation_dict"]

    def set_additional_annotation_dict(self, additional_annotation_dict):
        self.data["additional_annotation_dict"] = additional_annotation_dict

    def get_additional_metrics_dict(self):
        assert self.data is not None, "this trial is not loaded from file yet."
        if "additional_metrics_dict" not in self.data:
            self.data["additional_metrics_dict"] = {}
        return self.data["additional_metrics_dict"]

    def set_additional_metrics_dict(self, additional_metrics_dict):
        self.data["additional_metrics_dict"] = additional_metrics_dict

    def get_uid(self):
        assert self.data is not None, "this trial is not loaded from file yet."
        if "uid" not in self.data:
            raise ValueError("No uid found")
        return self.data["uid"]

    def write(self, uid=None, data_dir=None, name_suffix="", sub_dir=""):
        uid = get_self_default(uid, self.uid)

        dir_ = Path(f"{data_dir}/{uid}/{sub_dir}").expanduser()
        dir_.mkdir(exist_ok=True, parents=True)
        self.data_dir = dir_

        file_prefix = f"{uid}_v{self.VERSION_NUM}{name_suffix}"
        other_objects = dict()

        df_to_write = None
        for key, value in self.data.items():
            if key == "dataframe" and isinstance(value, pd.DataFrame):
                df_to_write = value.copy()
            else:
                other_objects[key] = value

        dataframe_path = dir_ / f"{file_prefix}.trial.parquet"
        assert df_to_write is not None, "data['annotation_df'] should be a valid pd.DataFrame"
        df_to_write.to_parquet(dataframe_path, engine="pyarrow", compression="snappy")

        metadata_path = dir_ / f"{file_prefix}.trial.yaml"
        with open(metadata_path, "w") as file:
            yaml.dump(other_objects, file)

        logging.info(f"Writing trial to {dir_ / uid}")

    @staticmethod
    def load_file_from_yaml(filename):
        config = {}
        with open(filename, "r") as file:
            try:
                # unsafe load is ok for our application. If we use safe_load, we will need to write custom handlers for numpy objects
                config = yaml.load(file, Loader=yaml.UnsafeLoader)
            except yaml.YAMLError as exc:
                print(exc)
        return config

    def read(self, file_path=None):
        # TODO (deepak.gopinath) - Add support to take in a name_suffix and open the appropriate file,
        path = Path(file_path).expanduser()
        yaml_path = path.with_suffix(".yaml")

        if path.exists() and yaml_path.exists():
            df_read = pd.read_parquet(path)
            configs = self.load_file_from_yaml(yaml_path)
            self.data = {"dataframe": df_read} | configs
            self.data_dir = path.parent
            return self.data
        return None

    @staticmethod
    def read_all(
        data_dir,
        auto_annotation_class: dict = None,
        manual_annotation_class: dict = None,
        metrics_class: dict = None,
        name_suffix="",
        version_suffix=f"v{VERSION_NUM}",
    ):
        data_dir = Path(data_dir).expanduser()
        out = []
        for file in data_dir.rglob(f"*{version_suffix}{name_suffix}.trial.parquet"):
            file_name = Path(file).stem
            if name_suffix != "" and name_suffix not in file_name:
                continue
            uid = file_name[: file_name.rfind("_v")]
            trial = Trial(uid)
            trial.read(file)
            out.append(trial)

            if auto_annotation_class is not None:
                trial.read_auto_annotations(auto_annotation_class)
            if manual_annotation_class is not None:
                trial.read_manual_annotations(manual_annotation_class)
            if metrics_class is not None:
                trial.read_metrics(metrics_class)

        return out

    def read_auto_annotations(self, auto_annotation_class: dict = None):
        if not auto_annotation_class:
            return
        for cls, params in auto_annotation_class.items():
            auto_annotation: AutoAnnotation = cls()
            auto_annotation.read(self, params)
            auto_annotation.merge_data(self, params, {})

    def read_manual_annotations(self, manual_annotation_class: dict = None):
        if not manual_annotation_class:
            return
        for cls, params in manual_annotation_class.items():
            manual_annotation: ManualAnnotation = cls()
            manual_annotation.read(self, params)
            manual_annotation.merge_data(self, params, {})

    def read_metrics(self, metrics_class: dict = None):
        if not metrics_class:
            return
        for cls, params in metrics_class.items():
            metric: Metric = cls()
            metric.read(self, params)
            metric.merge_results(self, params, {})

    def run_annotations(
        self,
        auto_annotation_class: dict,
        manual_annotation_class: dict,
        track_map,
        other_inputs: dict = None,
    ):
        """Run and merge auto and manual annotations for a trial."""
        manual_annotations = self.run_manual_annotations(
            manual_annotation_class, track_map, other_inputs
        )
        auto_annotations = self.run_auto_annotations(
            auto_annotation_class, track_map, other_inputs
        )
        return auto_annotations, manual_annotations

    def run_manual_annotations(
        self, manual_annotation_class: dict, track_map, other_inputs: dict = None
    ):
        """Run and merge manual annotations for a trial."""
        manual_annotations = []
        for cls, params in manual_annotation_class.items():
            manual_annotation: ManualAnnotation = cls()
            manual_annotation.prepare_manual_annotation_df(self, track_map, params, other_inputs)
            manual_annotation.merge_data(self, params, other_inputs)
            manual_annotations.append(manual_annotation)
        return manual_annotations

    def run_auto_annotations(
        self, auto_annotation_class: dict, track_map, other_inputs: dict = None
    ):
        """Run and merge auto annotations for a trial."""
        auto_annotations = []
        for cls, params in auto_annotation_class.items():
            auto_annotation: AutoAnnotation = cls()
            auto_annotation.filter(self, track_map, params, other_inputs)
            auto_annotation.merge_data(self, params, other_inputs)
            auto_annotations.append(auto_annotation)
        return auto_annotations

    def run_compute_metrics(self, metric_class: dict, track_map, other_inputs: dict = None):
        metrics = []
        for metric_cls, params in metric_class.items():
            metric: Metric = metric_cls()
            metric.compute_metric(self, track_map, params, other_inputs)
            metric.merge_results(self, params, other_inputs)
            metrics.append(metric)
        return metrics


class Annotation(ABC):
    def __init__(self):
        self.result = None
        assert self.VERSION_NUM
        assert self.FILTER_NAME

    def _get_hash_param_keys(self, params):
        return list(params.keys())

    def compute_hash(self, params):
        hash_params = {k: params[k] for k in self._get_hash_param_keys(params)}
        hash_ = hashlib.md5(json.dumps(hash_params).encode()).hexdigest()[:8]
        return hash_

    def write(self, uid, dir_, data, params_hash):
        other_objects = dict()
        file_prefix = f"{dir_}/{uid}--{self.FILTER_NAME}_v{self.VERSION_NUM}_{params_hash}"

        df_to_write = None
        for key, value in data.items():
            if key == "annotation_df" and isinstance(value, pd.DataFrame):
                df_to_write = value
            else:
                other_objects[key] = value

        assert df_to_write is not None, "data['annotation_df'] should be a valid pd.DataFrame"
        df_to_write.to_parquet(
            f"{file_prefix}.parquet",
            engine="pyarrow",
            compression="snappy",
        )
        metadata_path = f"{file_prefix}.yaml"
        with open(metadata_path, "w") as file:
            yaml.dump(other_objects, file)

    def read(self, trial, params):
        self.result = self._read(
            trial.uid, trial.data_dir, trial.load_file_from_yaml, self.compute_hash(params)
        )
        return self.result

    def _read(self, uid, dir_, load_file_from_yaml, params_hash=None):
        file_path = Path(
            f"{dir_}/{uid}--{self.FILTER_NAME}_v{self.VERSION_NUM}_{params_hash}.parquet"
        )
        yaml_path = file_path.with_suffix(".yaml")
        if file_path.exists() and yaml_path.exists():
            configs = load_file_from_yaml(yaml_path)
            return {"annotation_df": pd.read_parquet(file_path)} | configs
        else:
            return None

    def merge_data(self, trial: Trial, params, other_inputs):
        """
        Merge the annotation result into the Trial's main dataframe. Merge additional
        annotation dictionaries into Trial's additional annotation_dict
        NOTE: The trial class is modified in place.
        """
        assert self.result is not None, "Should call self.read() before calling merge_data()"
        main_dataframe = trial.get_dataframe()

        annotation_df = self.result["annotation_df"]
        assert main_dataframe.shape[0] == annotation_df.shape[0]
        # Append the "result" dataframe to the main dataframe column wise
        # using "inner" so that "carla objects log time" field isn't repeated
        # Assumes that the main data frame and annotation df has at least one common column.
        # (this would be the "carla objects log time" column)
        main_dataframe = pd.merge(main_dataframe, annotation_df, how="inner")
        trial.set_dataframe(main_dataframe)

        # merge additional autoannotation dict
        trial_additional_annotation_dict = trial.get_additional_annotation_dict()
        # create unique dictionary names by appending filter name to keys
        additional_annotation_dict = {
            k + "_" + self.result["filter_name"]: v
            for k, v in self.result["additional_dict"].items()
        }
        trial_additional_annotation_dict = {
            **trial_additional_annotation_dict,
            **additional_annotation_dict,
        }
        trial.set_additional_annotation_dict(trial_additional_annotation_dict)

    def process_result(
        self,
        result: dict,
        trial: Trial,
        reference_track_data: pd.DataFrame,
        params: dict,
        other_inputs: dict,
    ):
        assert (
            "annotation_df" in result and "additional_dict" in result
        ), "Please modify filter impl to the expected output structure"
        self.result = {
            **result,
            "params": params,
            "trial_uid": trial.uid,
            "filter_name": self.FILTER_NAME,
            "filter_version_num": self.VERSION_NUM,
        }
        params_hash = self.compute_hash(params)
        self.write(trial.uid, trial.data_dir, self.result, params_hash)
        return self.result


class AutoAnnotation(Annotation):
    @abc.abstractmethod
    def _filter_impl(self, trial: Trial, reference_track_data: pd.DataFrame, params: dict) -> dict:
        """Run filter logic.
        Returns:
            dict: Dictionary
                'annotation_df': pd.DataFrame. This is the main result of the AutoAnnotation.
                    The number of rows should match the number of rows in the trial_df
                'additional_dict' : dict. This contains any additional return values returned
                    by the filter. Can be an empty dict
        """
        pass

    def filter(
        self,
        trial: Trial,
        reference_track_data: pd.DataFrame,
        params: dict,
        other_inputs: dict,
    ):
        result = self._filter_impl(trial, reference_track_data, params)
        return self.process_result(result, trial, reference_track_data, params, other_inputs)


class ManualAnnotation(Annotation):
    @abc.abstractmethod
    def _prepare_manual_annotation_df_impl(
        self, trial: Trial, reference_track_data: pd.DataFrame, params: dict, other_inputs: dict
    ) -> dict:
        """Run filter logic.
        Returns:
            dict: Dictionary
                'annotation_df': pd.DataFrame. This is the main result of the ManualAnnotation.
                    The number of rows should match the number of rows in the trial_df
                'additional_dict' : dict. This contains any additional return values returned
                    by the filter. Can be an empty dict
        """
        pass

    def prepare_manual_annotation_df(
        self,
        trial: Trial,
        reference_track_data: pd.DataFrame,
        params: dict,
        other_inputs: dict,
    ):
        result = self._prepare_manual_annotation_df_impl(
            trial, reference_track_data, params, other_inputs
        )
        return self.process_result(result, trial, reference_track_data, params, other_inputs)


class Metric(ABC):
    def __init__(self):
        self.result = None
        assert self.METRIC_NAME
        assert self.VERSION_NUM

    @abc.abstractmethod
    def _compute_metric_impl(
        self,
        trial: Trial,
        reference_track_data: pd.DataFrame,
        params: dict,
        other_inputs: dict,
    ) -> dict:
        pass

    def _get_hash_param_keys(self, params):
        return list(params.keys())

    def compute_hash(self, params):
        hash_params = {k: params[k] for k in self._get_hash_param_keys(params)}
        hash_ = hashlib.md5(json.dumps(hash_params).encode()).hexdigest()[:8]
        return hash_

    def read(self, trial, params):
        self.result = self._read(
            trial.uid, trial.data_dir, trial.load_file_from_yaml, self.compute_hash(params)
        )
        return self.result

    def _read(self, uid, dir_, load_file_from_yaml, params_hash=None):
        yaml_path = Path(
            f"{dir_}/{uid}--{self.METRIC_NAME}_v{self.VERSION_NUM}_{params_hash}.yaml"
        )
        if yaml_path.exists():
            configs = load_file_from_yaml(yaml_path)
            return configs
        else:
            return None

    def write(self, uid, dir_, data, params_hash):
        file_prefix = f"{dir_}/{uid}--{self.METRIC_NAME}_v{self.VERSION_NUM}_{params_hash}"
        yaml_path = f"{file_prefix}.yaml"
        with open(yaml_path, "w") as file:
            yaml.dump(data, file)

    def process_result(
        self,
        result: dict,
        trial: Trial,
        reference_track_data: pd.DataFrame,
        params: dict,
        other_inputs: dict,
    ):
        self.result = {
            **result,
            "params": params,
            "trial_uid": trial.uid,
            "metric_name": self.METRIC_NAME,
            "metric_version_num": self.VERSION_NUM,
        }
        params_hash = self.compute_hash(params)
        self.write(trial.uid, trial.data_dir, self.result, params_hash)
        return self.result

    def compute_metric(
        self,
        trial: Trial,
        reference_track_data: pd.DataFrame,
        params: dict,
        other_inputs: dict,
    ):
        result = self._compute_metric_impl(trial, reference_track_data, params, other_inputs)
        return self.process_result(result, trial, reference_track_data, params, other_inputs)

    def compute_metric_per_segment(self, trial: Trial, metric_func):
        segment_dfs = split_df_by_map_segments(trial.get_dataframe())
        segment_metrics = []
        for segment_id, segment_trial_df in segment_dfs:
            segment_metric_dict = metric_func(segment_trial_df)
            segment_metric_dict["segment_id"] = segment_id
            segment_metrics.append(segment_metric_dict)

        return segment_metrics

    def get_result(self):
        assert (
            self.result is not None
        ), "Should call self.compute_metric() before calling get_result()"
        return self.result

    def merge_results(self, trial: Trial, params, other_inputs):
        """
        Merge the metric computation result into the Trial's additional_metric_dict.
        NOTE: The trial class is modified in place.
        """
        assert (
            self.result is not None
        ), "Should call self.compute_metric() before calling merge_results()"

        # merge additional autoannotation dict
        trial_additional_metrics_dict = trial.get_additional_metrics_dict()
        # create unique dictionary names by appending filter name to keys

        additional_metrics_dict = {}
        additional_metrics_dict = {k: v for k, v in self.result.items() if self.METRIC_NAME in k}

        trial_additional_metrics_dict = {
            **trial_additional_metrics_dict,
            **additional_metrics_dict,
        }

        trial.set_additional_metrics_dict(trial_additional_metrics_dict)
