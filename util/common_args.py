import argparse
from pathlib import Path

import yaml

from util.utility import str2bool


def parse_args(args=None, additional_arg_setters=(), doc=None) -> argparse.ArgumentParser:
    parser = argparse.ArgumentParser(description=doc)
    for setter in additional_arg_setters:
        parser = setter(parser)

    return parser


def common_dir_arguments(parser):
    sys_paths_yaml = Path(__file__).expanduser().resolve().parents[1] / "workspace_paths.yaml"
    track_map_path = ""
    mcap_file_path = ""
    mcap_dir_path = ""
    cache_dir_path = ""
    trials_cache_dir_path = ""
    if sys_paths_yaml.exists():
        # read in yaml
        with open(str(sys_paths_yaml), "r") as f:
            default_paths = yaml.safe_load(f)
            root_dir = default_paths["root_data_path"]
            if root_dir[-1] != "/":
                root_dir += "/"
            for k, v in default_paths.items():
                # Check whether any of the workspace paths have been left empty
                # and print a warning if they have.
                # Disregard the mcap file and folder paths because those may be
                # left blank on purpose.
                if not v and "mcap" not in k:
                    print(
                        "Warning: There are empty paths in your \
                           workspace_paths.yaml"
                    )
                if v and not v.startswith(root_dir):
                    v = root_dir + v
        # set path variables according to yaml
        track_map_path = default_paths["track_csv_path"]
        mcap_file_path = default_paths["mcap_file_path"]
        mcap_dir_path = default_paths["mcap_dir_path"]
        cache_dir_path = default_paths["cache_dir_path"]
        trials_cache_dir_path = default_paths["trials_cache_dir_path"]

    parser.add_argument(
        "--track-map-csv",
        type=str,
        help="Input .csv file to read for the track map.",
        default=track_map_path,
    )
    parser.add_argument(
        "--mcap-input-file",
        type=str,
        help="Input file to read, should be an mcap file, translated from a compactsim db3 file.",
        default=mcap_file_path,
    )
    parser.add_argument(
        "--mcap-input-folder",
        type=str,
        help="Input folder to read, should be populated with mcap files, each of which is \
              translated from a compactsim db3 file.",
        default=mcap_dir_path,
    )
    parser.add_argument(
        "--compactsim-cache-folder",
        type=str,
        help="Cache folder to write compactsim parsed data to",
        default=cache_dir_path,
    )
    parser.add_argument(
        "--trials-dir",
        type=str,
        help="The dir where the trials are written to.",
        default=trials_cache_dir_path,
    )
    parser.add_argument(
        "--is-multiple-mcaps-per-subject",
        type=str2bool,
        help="True when there are multiple mcaps logged for a single subject *_x.mcap with x in [0...N]",
        default=False,
    )

    return parser
