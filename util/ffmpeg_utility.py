"""FFMPEG related common re-usable functionality are implemented here
    Functions:
        - get_video_details_ffprobe
        - get_fps_ffprobe
        - get_pix_fmt_ffprobe
"""

import json
import subprocess
from typing import Dict, List, Optional


def get_video_details_ffprobe(video_path: str, fps: bool = True, pix_fmt=True) -> Optional[Dict]:
    """Collect the video details like width and height of video frame, number of frames.

    Args:
        video_path (str): Video file path.
        fps (bool): include the details of fps
        pix_fmt (bool): include the details of bit rate and pixel format

    Returns:
        Dict: Dictionary of requested parameter.
    """
    try:
        video_details: Dict = dict()
        # return {"width": width, "height": height, "total_frames": nb_frames}
        # Command to get video details including width, height, and frame count
        cmd: List = [
            "ffprobe",
            "-v",
            "error",
            "-count_frames",
            "-select_streams",
            "v:0",
            "-show_entries",
            "stream=width,height,nb_read_frames",
            "-of",
            "json",
            "-i",
            video_path,
        ]

        # Run the command
        result: subprocess.CompletedProcess = subprocess.run(
            cmd, capture_output=True, text=True, check=True
        )

        output = json.loads(result.stdout)

        width = output["streams"][0].get("width")
        height = output["streams"][0].get("height")
        nb_frames = output["streams"][0].get("nb_read_frames")

        try:
            nb_frames = int(nb_frames)
        except ValueError:
            print("Error: The nb_read_frames value is not a valid integer.")
            nb_frames = None

        video_details.update({"width": width, "height": height, "total_frames": nb_frames})

        if fps:
            fps_details: Dict = get_fps_ffprobe(video_path)
            video_details.update(fps_details)

        if pix_fmt:
            pix_fmt_details: Dict = get_pix_fmt_ffprobe(video_path)
            video_details.update(pix_fmt_details)

        return video_details
    except subprocess.CalledProcessError as e:
        print(f"An error occurred: {e}")
        return None


def get_fps_ffprobe(video_file: str) -> Dict:
    """Get the frame per second (FPS) for given video file using ffprobe

    Args:
        video_file (str): Video file path

    Returns:
        Dict: return dictionary of extracted details.
    """
    try:
        # Run the ffprobe command
        result: subprocess.CompletedProcess = subprocess.run(
            [
                "ffprobe",
                "-v",
                "error",
                "-select_streams",
                "v:0",
                "-show_entries",
                "stream=r_frame_rate",
                "-of",
                "json",
                video_file,
            ],
            stdout=subprocess.PIPE,
            text=True,
        )

        output = json.loads(result.stdout)

        frame_rate: str = output["streams"][0].get("r_frame_rate")

        # Extract and convert FPS
        frame_rate: str = frame_rate.strip()
        num, denom = map(int, frame_rate.split("/"))
        fps: float = num / denom

        return {"fps": fps}

    except Exception as e:
        print(f"Error: {e}")

    return {"fps": None}


def get_pix_fmt_ffprobe(video_file: str) -> Dict:
    """Extract the pixel format and bit rate from the given video file. using ffprobe

    Args:
        video_file (str): Video file path

    Returns:
        Dict: return dictionary of extracted details.
    """
    try:
        command: List = [
            "ffprobe",
            "-v",
            "error",
            "-select_streams",
            "v:0",
            "-show_entries",
            "stream=bit_rate,pix_fmt",
            "-of",
            "json",
            video_file,
        ]

        # print("Running command Bit Rate:", ' '.join(command))
        # Run the ffprobe command to get the bit rate and pixel format
        result = subprocess.run(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
        )

        output = json.loads(result.stdout)

        # Extract the bit rate and pixel format
        bit_rate = output["streams"][0].get("bit_rate")
        pix_fmt = output["streams"][0].get("pix_fmt")

        return {"bit_rate": bit_rate, "pix_fmt": pix_fmt}

    except Exception as e:
        print(f"Error: {e}")
        return {"bit_rate": None, "pix_fmt": None}
