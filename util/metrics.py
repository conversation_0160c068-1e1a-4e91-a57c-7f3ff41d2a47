import numpy as np

from util.trial import Metric


class TrialTimeMetric(Metric):
    METRIC_NAME = "trial_time"
    VERSION_NUM = 1

    def __init__(self):
        super().__init__()

    def _get_hash_param_keys(self, params):
        return ["compute_per_segment"]

    def compute_trial_time(self, df):
        return {
            self.METRIC_NAME: df["carla_objects log time"].iloc[-1]
            - df["carla_objects log time"].iloc[0]
        }

    def _compute_metric_impl(
        self, trial, reference_track_data, params: dict, other_inputs: dict
    ) -> dict:
        trial_df = trial.get_dataframe()
        trial_time = (
            trial_df["carla_objects log time"].iloc[-1]
            - trial_df["carla_objects log time"].iloc[0]
        )
        result = {self.METRIC_NAME: trial_time}
        if "compute_per_segment" in params and params["compute_per_segment"]:
            result["segment_" + self.METRIC_NAME] = self.compute_metric_per_segment(
                trial, self.compute_trial_time
            )
        return result


class SteeringScoreMetric(Metric):
    METRIC_NAME = "steering_score"
    VERSION_NUM = 1

    def __init__(self):
        super().__init__()

    def _get_hash_param_keys(self, params):
        return ["compute_per_segment"]

    def compute_steering_score(self, df):
        steering_change = np.diff(df["steering"].values)
        return {
            self.METRIC_NAME + "_mean": np.mean(steering_change),
            self.METRIC_NAME + "_std": np.std(steering_change),
            self.METRIC_NAME + "_max": np.max(steering_change),
            self.METRIC_NAME + "_abs_mean": np.mean(np.abs(steering_change)),
            self.METRIC_NAME + "_abs_std": np.std(np.abs(steering_change)),
            self.METRIC_NAME + "_abs_max": np.max(np.abs(steering_change)),
        }

    def _compute_metric_impl(
        self, trial, reference_track_data, params: dict, other_inputs: dict
    ) -> dict:
        trial_df = trial.get_dataframe()
        result = self.compute_steering_score(trial_df)
        if "compute_per_segment" in params and params["compute_per_segment"]:
            result["segment_" + self.METRIC_NAME] = self.compute_metric_per_segment(
                trial, self.compute_steering_score
            )
        return result


class RacingLineScoreMetric(Metric):
    METRIC_NAME = "racing_line_score"
    VERSION_NUM = 1

    def __init__(self):
        super().__init__()

    def _get_hash_param_keys(self, params):
        return ["compute_per_segment"]

    def compute_racing_line_score(self, df):
        lateral_distances = df["lateral_distances"].values
        return {
            self.METRIC_NAME + "_mean": np.mean(lateral_distances),
            self.METRIC_NAME + "_std": np.std(lateral_distances),
            self.METRIC_NAME + "_abs_max": np.max(np.abs(lateral_distances)),
            self.METRIC_NAME + "_abs_mean": np.mean(np.abs(lateral_distances)),
            self.METRIC_NAME + "_abs_std": np.std(np.abs(lateral_distances)),
        }

    def _compute_metric_impl(
        self, trial, reference_track_data, params: dict, other_inputs: dict
    ) -> dict:
        trial_df = trial.get_dataframe()
        result = self.compute_racing_line_score(trial_df)
        if "compute_per_segment" in params and params["compute_per_segment"]:
            result["segment_" + self.METRIC_NAME] = self.compute_metric_per_segment(
                trial, self.compute_racing_line_score
            )
        return result


class OutOfBoundsMetric(Metric):
    METRIC_NAME = "oob"
    VERSION_NUM = 1

    def __init__(self):
        super().__init__()

    def _get_hash_param_keys(self, params):
        return ["compute_per_segment"]

    def compute_oob(self, df):
        percentage_oob = 100.0 * sum(df["out_of_bounds"].values) / len(df)
        # Note: mean and std only makes sense if the distribution is close to normal. Adding it to
        # result dict for bookkeeping
        return {
            self.METRIC_NAME + "_percentage": percentage_oob,
            self.METRIC_NAME + "_max": np.max(df["out_of_bounds_distances"].values),
            self.METRIC_NAME + "_mean": np.mean(df["out_of_bounds_distances"].values),
            self.METRIC_NAME + "_std": np.std(df["out_of_bounds_distances"].values),
        }

    def _compute_metric_impl(
        self, trial, reference_track_data, params: dict, other_inputs: dict
    ) -> dict:
        trial_df = trial.get_dataframe()
        result = self.compute_oob(trial_df)
        if "compute_per_segment" in params and params["compute_per_segment"]:
            result["segment_" + self.METRIC_NAME] = self.compute_metric_per_segment(
                trial, self.compute_oob
            )
        return result


class ThrottleScoreMetric(Metric):
    METRIC_NAME = "throttle_score"
    VERSION_NUM = 1

    def __init__(self):
        super().__init__()

    def _get_hash_param_keys(self, params):
        return ["compute_per_segment"]

    def compute_throttle_score(self, df):
        throttle_change = np.diff(df["throttle"].values)
        return {
            self.METRIC_NAME + "_mean": np.mean(throttle_change),
            self.METRIC_NAME + "_std": np.std(throttle_change),
            self.METRIC_NAME + "_max": np.max(throttle_change),
            self.METRIC_NAME + "_abs_mean": np.mean(np.abs(throttle_change)),
            self.METRIC_NAME + "_abs_std": np.std(np.abs(throttle_change)),
            self.METRIC_NAME + "_abs_max": np.max(np.abs(throttle_change)),
        }

    def _compute_metric_impl(
        self, trial, reference_track_data, params: dict, other_inputs: dict
    ) -> dict:
        trial_df = trial.get_dataframe()
        result = self.compute_throttle_score(trial_df)
        if "compute_per_segment" in params and params["compute_per_segment"]:
            result["segment_" + self.METRIC_NAME] = self.compute_metric_per_segment(
                trial, self.compute_throttle_score
            )
        return result


class BrakingScoreMetric(Metric):
    METRIC_NAME = "braking_score"
    VERSION_NUM = 1

    def __init__(self):
        super().__init__()

    def _get_hash_param_keys(self, params):
        return ["compute_per_segment"]

    def compute_braking_score(self, df):
        brake_change = np.diff(df["brake"].values)
        return {
            self.METRIC_NAME + "_mean": np.mean(brake_change),
            self.METRIC_NAME + "_std": np.std(brake_change),
            self.METRIC_NAME + "_max": np.max(brake_change),
            self.METRIC_NAME + "_abs_mean": np.mean(np.abs(brake_change)),
            self.METRIC_NAME + "_abs_std": np.std(np.abs(brake_change)),
            self.METRIC_NAME + "_abs_max": np.max(np.abs(brake_change)),
        }

    def _compute_metric_impl(
        self, trial, reference_track_data, params: dict, other_inputs: dict
    ) -> dict:
        trial_df = trial.get_dataframe()
        result = self.compute_braking_score(trial_df)
        if "compute_per_segment" in params and params["compute_per_segment"]:
            result["segment_" + self.METRIC_NAME] = self.compute_metric_per_segment(
                trial, self.compute_braking_score
            )
        return result


class SmoothnessScoreMetric(Metric):
    METRIC_NAME = "smoothness_score"
    VERSION_NUM = 1

    def __init__(self):
        super().__init__()
        self.kernel_size = (
            10  # we are assuming that there are no segments shorter than 10 timesteps
        )
        self.kernel = np.ones(self.kernel_size) / self.kernel_size

    def _get_hash_param_keys(self, params):
        return ["compute_per_segment"]

    def compute_smoothness_score(self, df):
        if len(df) < len(self.kernel):
            smoothness_score_vec = [0, 0]
        else:
            acc_x = np.diff(df["ego_vx"].values) / np.diff(df.timestamp.values)
            # smoothing is added to deal with the chatter in the signal. To be removed if fixed at the sources
            smoothed_acc_x = np.convolve(acc_x, self.kernel, mode="same")
            acc_y = np.diff(df["ego_vy"].values) / np.diff(df.timestamp.values)
            smoothed_acc_y = np.convolve(acc_y, self.kernel, mode="same")
            acc_z = np.diff(df["ego_vz"].values) / np.diff(df.timestamp.values)
            smoothed_acc_z = np.convolve(acc_z, self.kernel, mode="same")
            # (L, 3)
            smoothed_acc_vec = np.vstack((smoothed_acc_x, smoothed_acc_y, smoothed_acc_z)).T
            # (L-1, 3)
            try:
                smoothed_jerk_vec = np.diff(smoothed_acc_vec, axis=0) / np.expand_dims(
                    np.diff(df["carla_objects log time"].values[:-1]), 1
                )
            except ValueError as e:
                # Note: This can arise if something goes wrong with windowed smoothing,
                #   leading to an off-by-one size mismatch
                print(f"Encountered ValueError during Jerk calculation: {e}")
                smoothed_jerk_vec = np.diff(smoothed_acc_vec, axis=0) / np.expand_dims(
                    np.diff(df["carla_objects log time"].values[:]), 1
                )
            # (L-1, )
            smoothness_score_vec = np.linalg.norm(smoothed_jerk_vec, axis=1)

        return {
            self.METRIC_NAME + "_mean": np.mean(smoothness_score_vec),
            self.METRIC_NAME + "_std": np.std(smoothness_score_vec),
            self.METRIC_NAME + "_max": np.max(smoothness_score_vec),
            self.METRIC_NAME + "_min": np.min(smoothness_score_vec),
        }

    def _compute_metric_impl(
        self, trial, reference_track_data, params: dict, other_inputs: dict
    ) -> dict:
        trial_df = trial.get_dataframe()
        result = self.compute_smoothness_score(trial_df)
        if "compute_per_segment" in params and params["compute_per_segment"]:
            result["segment_" + self.METRIC_NAME] = self.compute_metric_per_segment(
                trial, self.compute_smoothness_score
            )
        return result


class FrictionUtilizationScore(Metric):
    METRIC_NAME = "friction_utilization_score"
    VERSION_NUM = 1
    # TODO (deepak.gopinath) move const to a common location
    GRAVITATION_CONST = -9.81

    def __init__(self):
        super().__init__()
        self.kernel_size = 10
        self.kernel = np.ones(self.kernel_size) / self.kernel_size

    def _get_hash_param_keys(self, params):
        return ["compute_per_segment"]

    def compute_friction_utilization_score(self, df):
        acc_x = np.diff(df["ego_vx"].values) / np.diff(df.timestamp.values)
        # smoothing is added to deal with the chatter in the signal. To be removed if fixed at the sources
        smoothed_acc_x = np.convolve(acc_x, self.kernel, mode="same")
        acc_y = np.diff(df["ego_vy"].values) / np.diff(df.timestamp.values)
        smoothed_acc_y = np.convolve(acc_y, self.kernel, mode="same")
        acc_z = np.diff(df["ego_vz"].values) / np.diff(df.timestamp.values)
        smoothed_acc_z = np.convolve(acc_z, self.kernel, mode="same")

        # (L, 3)
        smoothed_acc_vec = np.vstack((smoothed_acc_x, smoothed_acc_y, smoothed_acc_z)).T
        g_force_vec = smoothed_acc_vec / abs(self.GRAVITATION_CONST)
        friction_utilization_vec = np.linalg.norm(g_force_vec, axis=1)

        return {
            self.METRIC_NAME + "_mean": np.mean(friction_utilization_vec),
            self.METRIC_NAME + "_std": np.std(friction_utilization_vec),
            self.METRIC_NAME + "_max": np.max(friction_utilization_vec),
            self.METRIC_NAME + "_min": np.min(friction_utilization_vec),
        }

    def _compute_metric_impl(
        self, trial, reference_track_data, params: dict, other_inputs: dict
    ) -> dict:
        trial_df = trial.get_dataframe()
        result = self.compute_friction_utilization_score(trial_df)
        if "compute_per_segment" in params and params["compute_per_segment"]:
            result["segment_" + self.METRIC_NAME] = self.compute_metric_per_segment(
                trial, self.compute_friction_utilization_score
            )
        return result
