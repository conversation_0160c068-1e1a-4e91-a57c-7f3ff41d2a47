import argparse
import io
import itertools
import math
import multiprocessing as mp
import pickle
import traceback
from pathlib import Path
from typing import Callable, Dict, List, Optional

import cv2
import imageio
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from matplotlib.lines import Line2D
from matplotlib.patches import Rectangle
from PIL import Image

from data_sources.compact_sim.dataloading import DATA_DICT_KEY_EPISODES, DATA_DICT_KEY_MAP
from util.utility import get_scenario_data, get_yaw_from_quaternion, parse_srt_content, str2bool

# Buffer value derived by trial and error method
WINDOW_ZOOM = 35
VIS_PARAM_SHOW_COLUMNS = "show_columns"
VIS_PARAM_TIME_WINDOW = "time_window"
VIS_PARAM_ANIM_OUTPUT_DIR = "anim_output_dir"
VIS_PARAM_FPS = "fps"
VIS_PARAM_SHOW_RACING_LINE = "show_racing_line"
VIS_PARAM_VERBOSE = "verbose"
VIS_PARAM_ANIM_TYPE = "animation_type"
VIS_PARAM_EGO_CENTRIC = "ego_centric"
VIS_PARAM_SUFFIX = "anim_suffix"
VIS_PARAM_SUBTITLE = "visualize_subtitles"
VIS_SYNC_OFFSET = "sync_offset"
TIMESTAMP_KEY = "timestamp"
CONES_COORDINATES = [
    [-682, -2, 1.7],  # end of straightaway
    [-700, -144, 4.4],  # turn 1 right
    [-615, -239, 12.0],  # turn 2 left
    [-912, -117, -1.48],  # turn 3 left
    [-938, -72, -1.65],  # turn 3 right
    [-974, 80, -3.9],  # turn 4 left
    [-935, 109, -2.6],  # turn 4 right
    [-990, 2, -4.1],  # turn 4 brake zone
    [-991.5, 6, -4.2],  # turn 4 brake zone
    [-855, 171, -2.5],  # turn 5 right
    [-835, 256, -5.5],  # turn 5 left
    [-834, 330, -7.6],  # turn 5 right 2
    [-849, 518, -10.7],  # turn 6 left
    [-767, 569, -10.6],  # turn 6 right
    [-615, 587, -11.8],  # turn 7 left
    [-682, 611.5, -13.0],  # turn 7 brake zone
    [-684, 611.5, -13.0],  # turn 7 brake zone
    [-673, 535, -10.67],  # turn 8 right straight
    [-773, 420, -5.3],  # turn 8 right apex
    [-747, 289, -5.4],  # turn 9 right
    [-779, 335, -6.7],  # turn 9 brake zone
    [-780, 338, -6.7],  # turn 9 brake zone
    [-645, 365, -4.45],  # turn 10 left
]
MARKERS = ["o", "v", "s", "p", "P", "*", "h", "H", "+", "x", "X", "D", "d", "|", "_"]


# Reference
class RotatingRectangle(Rectangle):
    """Draw a rectangle with rotation around given point"""

    def __init__(self, xy, width, height, rel_point_of_rot, **kwargs):
        super().__init__(xy, width, height, **kwargs)
        self.rel_point_of_rot = rel_point_of_rot
        self.xy_center = self.get_xy()
        self.set_angle(self.angle)

    def _apply_rotation(self):
        angle_rad = self.angle * np.pi / 180
        m_trans = np.array(
            [
                [np.cos(angle_rad), -np.sin(angle_rad)],
                [np.sin(angle_rad), np.cos(angle_rad)],
            ]
        )
        shift = -m_trans @ self.rel_point_of_rot
        a = self.xy_center + shift
        self.set_xy(self.xy_center + shift)

    def set_angle(self, angle):
        self.angle = angle
        self._apply_rotation()

    def set_rel_point_of_rot(self, rel_point_of_rot):
        self.rel_point_of_rot = rel_point_of_rot
        self._apply_rotation()

    def set_xy_center(self, xy):
        self.xy_center = xy
        self._apply_rotation()


class RotatedVehicle(RotatingRectangle):
    """This is the matplotlib patch representing a rotated vehicle"""

    def __init__(self, xy, width=4.77, height=1.92, **kwargs):
        """The width and height taken from the size of 2020 Lexus LC500"""
        back_axle = 0.3
        super().__init__(
            (xy),
            width,
            height,
            (back_axle * width, 0.5 * height),
            rotation_point="xy",
            **kwargs,
        )


def subsample_dataframe(df, target_fps):
    """
    Downsample a given dataframe to a target FPS to avoid huge dataframes.
    Args:
        df (pd.DataFrame): The dataframe to be downsampled.
        target_fps (int): Frames per second to which the dataframe is to be downsampled.
    Returns:
        subsampled_df (pd.DataFrame): Downsampled dataframe.
    """
    # Find and use the median timestamp difference as an estimate for the frame interval
    timestamp_diff = df[TIMESTAMP_KEY].diff()
    time_interval = timestamp_diff.median()
    recorded_fps = int(1 / time_interval)

    # Calculate the target frame interval based on the desired FPS
    target_frame_interval = 1 / target_fps
    if recorded_fps < target_fps:
        # To avoid sub sampling be set to zero
        subsampling_factor = 1
    else:
        # Calculate the subsampling factor based on the recorded frame rate
        subsampling_factor = int(round(recorded_fps * target_frame_interval))
    subsampled_df = df.iloc[::subsampling_factor]

    return subsampled_df


def create_bounding_box(point_x, point_y, bbox_width, bbox_height, color):
    bbox = plt.Rectangle(
        (point_x - bbox_width / 2, point_y - bbox_height / 2),
        bbox_width,
        bbox_height,
        fill=False,
        color=color,
        linewidth=2,
        label="Bounding Box",
    )
    plt.gca().add_patch(bbox)


def plot_track(ax, track_map, show_racing_line):
    """
    Plot the race track on a matplotlib figure.
    Args:
        ax (matplotlib.axs): The axis object of the matplotlib figure.
        track_map (pd.DataFrame): The map dataframe.
        show_racing_line (bool): Set True for showing the main reference line. Default is False.
    """
    ax.plot(
        track_map["inner_edge/x"],
        track_map["inner_edge/y"],
        linestyle="--",
        label="inner",
    )
    ax.plot(
        track_map["outer_edge/x"],
        track_map["outer_edge/y"],
        linestyle=":",
        label="outer",
    )
    if show_racing_line:
        ax.plot(
            track_map["refline/x"],
            track_map["refline/y"],
            label="raceline",
            color="magenta",
            alpha=0.3,
        )


def save_plot(output_filename, dpi=300):
    """
    Save a figure as a png.
    Args:
        output_filename (str): Name for the saved file (without extension).
        dpi (int, optional): Clarity of the saved image. Defaults to 300.
    """
    # Save the current plot as an image file
    output_filename = output_filename + ".png"
    plt.savefig(output_filename, dpi=dpi)


def save_gif(frames, output_filename, fps):
    """
    Save a list of frames as a GIF.
    Args:
        frames (List): List of frames.
        output_filename (str): Name for the saved file.
        fps (int): frames per seconds for the GIF.
    """
    # Save frames as a GIF using imageio
    duration = 1 / fps
    output_filename = output_filename
    imageio.mimsave(output_filename, frames, duration=duration)
    print("File saved as {}".format(output_filename))


def save_mp4(frames, output_filename, fps):
    """
    Save a list of frames as an mp4.
    Args:
        frames (List): List of frames.
        output_filename (str): Name for the saved file.
        fps (int): frames per seconds for the mp4.
    """
    height, width, _ = np.array(frames[0]).shape

    fourcc = cv2.VideoWriter_fourcc(*"mp4v")
    out = cv2.VideoWriter(output_filename, fourcc, fps, (width, height))

    for frame in frames:
        frame_array = np.array(frame)
        # Convert RGB to BGR (OpenCV uses BGR)
        frame_array = cv2.cvtColor(frame_array, cv2.COLOR_RGB2BGR)
        out.write(frame_array)

    out.release()


def get_past_and_future_trajectories(dataframe, timepoint):
    """
    Calculates the past and future trajectories from a given timepoint, half of timewindow in future and half in past.
    Args:
        dataframe (pd.DataFrame): Vehicle dataframe.
        timepoint (int): Timepoint from which to calcuate trajectories.
    Returns:
        vehicle_subset (pd.DataFrame): Subset of dataframe from start to end point.
        closest_timepoint (pd.Series): Row with the timestamp that is closest to the timepoint provided.
        positive_window (pd.DataFrame): Future trajectory dataframe.
        negative_window (pd.DataFrame): Past trajectory dataframe.
    """
    time_window_start = timepoint - params[VIS_PARAM_TIME_WINDOW] / 2
    time_window_end = timepoint + params[VIS_PARAM_TIME_WINDOW] / 2

    dataframe["timepoint_delta_t"] = abs(dataframe[TIMESTAMP_KEY] - timepoint)
    vehicle_timestamp = dataframe[TIMESTAMP_KEY]

    # Find the row with the minimum absolute difference
    closest_timepoint = dataframe.loc[dataframe["timepoint_delta_t"].idxmin()]
    positive_window = dataframe[
        (vehicle_timestamp >= closest_timepoint[TIMESTAMP_KEY])
        & (
            vehicle_timestamp
            <= closest_timepoint[TIMESTAMP_KEY] + (params[VIS_PARAM_TIME_WINDOW] / 2)
        )
    ]
    negative_window = dataframe[
        (
            vehicle_timestamp
            >= closest_timepoint[TIMESTAMP_KEY] - (params[VIS_PARAM_TIME_WINDOW] / 2)
        )
        & (vehicle_timestamp < closest_timepoint[TIMESTAMP_KEY])
    ]
    vehicle_subset = dataframe[
        (vehicle_timestamp >= time_window_start) & (vehicle_timestamp <= time_window_end)
    ]

    return vehicle_subset, closest_timepoint, positive_window, negative_window


def visualize_animation(
    vehicle_data: pd.DataFrame,
    reference_track_data: pd.DataFrame = None,
    params: Dict = None,
):
    if params[VIS_PARAM_ANIM_TYPE] == VIS_PARAM_EGO_CENTRIC:
        frames = ego_centric_animation(vehicle_data, reference_track_data, params)
    else:
        frames = traj_animation(vehicle_data, reference_track_data, params)
    save_mp4(frames, params[VIS_PARAM_ANIM_OUTPUT_DIR], params[VIS_PARAM_FPS])


def visualize_timepoint(
    vehicle_data: pd.DataFrame,
    timepoint: float,
    reference_track_data: pd.DataFrame = None,
    params: Dict = None,
    callback: Optional[Callable] = None,
):
    """Visualize a birds-eye-view of the scenario at a specific timepoint

    Parameters
    ----------
    vehicle_data : pd.DataFrame
        The episode's panda frame
    reference_track_data : pd.DataFrame, optional
        The map data panda frame
    timepoint : time, optional
        The timepoint in the episode to visualize
    params : Dict, optional
        _description_, by default {}, can include
            "time_window" - time window in which trajectory will be displayed.
            "show_columns" - columns in the pandas frame to show the recent past/future time signal from these columns.
            "anim_output_dir" - name of the file and plot to be saved.
            "fps" - frames per second for the video
            "dpi" - the clarity of animation, default 250
            "show_racing_line" - show the racing line in the plot

    Returns
    -------
    _type_
        fig, axs
    """
    if "dpi" not in params:
        params["dpi"] = 200
    if "show_racing_line" not in params:
        params["show_racing_line"] = False

    (
        vehicle_subset,
        closest_timepoint,
        positive_window,
        negative_window,
    ) = get_past_and_future_trajectories(vehicle_data, timepoint)

    timepoint_x = np.array(closest_timepoint["ego_x"])
    timepoint_y = np.array(closest_timepoint["ego_y"])
    pos_traj_x = np.array(positive_window["ego_x"])
    pos_traj_y = np.array(positive_window["ego_y"])
    neg_traj_x = np.array(negative_window["ego_x"])
    neg_traj_y = np.array(negative_window["ego_y"])

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))

    plot_track(ax1, reference_track_data, params[VIS_PARAM_SHOW_RACING_LINE])
    ax1.plot(pos_traj_x, pos_traj_y, c="green", label="future trajectory", alpha=0.5)
    ax1.plot(neg_traj_x, neg_traj_y, c="blue", label="past trajectory", alpha=0.5)
    ax1.scatter(timepoint_x, timepoint_y, c="red", label="ego")
    ax1.set_title("Bird's Eye View")
    plt.legend()

    if len(vehicle_subset["ego_x"]) > 0:
        ax1.set_xlim(
            min(vehicle_subset["ego_x"]) - WINDOW_ZOOM,
            max(vehicle_subset["ego_x"]) + WINDOW_ZOOM,
        )  # Zoom in on ego vehicle
        ax1.set_ylim(
            min(vehicle_subset["ego_y"]) - WINDOW_ZOOM,
            max(vehicle_subset["ego_y"]) + WINDOW_ZOOM,
        )

    ego_yaw = get_yaw_from_quaternion(closest_timepoint, "ego")
    rect = RotatedVehicle((timepoint_x, timepoint_y), angle=ego_yaw, alpha=0.5)
    ax1.add_patch(rect)

    if callback is not None:
        callback(vehicle_data, reference_track_data, timepoint, params)

    if (
        VIS_PARAM_SHOW_COLUMNS in params
        and params[VIS_PARAM_SHOW_COLUMNS] is not None
        and params[VIS_PARAM_TIME_WINDOW] is not None
    ):
        for column in params[VIS_PARAM_SHOW_COLUMNS]:
            ax2.plot(vehicle_subset[TIMESTAMP_KEY], vehicle_subset[column], label=column)

        ax2.set_xlabel(TIMESTAMP_KEY)
        ax2.set_ylabel("Column Values")
        ax2.set_title("Time Evolution of Selected Columns")
        plt.legend()

    if params[VIS_PARAM_ANIM_OUTPUT_DIR]:
        save_plot(params[VIS_PARAM_ANIM_OUTPUT_DIR])
        plt.clf()

    return fig, (ax1, ax2)


def visualize_scenario_trajectories(
    dataframe_list: List, reference_track_data: pd.DataFrame, params: Dict = None
):
    """
    Plot trajectories of ego vehicle from multiple scenarios

    Parameters
    ----------
    dataframe_list : List
        A list of scenario dataframes
    reference_track_data : pd.DataFrame
        The map data panda frame
    params : Dict, optional
        _description_, by default {}, can include
            "time_window" - time window in which trajectory will be displayed.
            "show_columns" - columns in the pandas frame to show the recent past/future time signal from these columns.
            "anim_output_dir" - name of the file and plot to be saved.
            "fps" - frames per second for the video
            "show_racing_line" - show the recing line in the plot

    Returns
    -------
    _type_
        fig, axs
    """
    fig, axs = plt.subplots()
    plot_track(axs, reference_track_data, params[VIS_PARAM_SHOW_RACING_LINE])

    colormap = plt.cm.get_cmap("viridis", len(dataframe_list))
    plot_ado_flag = True
    # Iterate over the list of DataFrames and plot
    for i, df in enumerate(dataframe_list):
        # Plot ado trajectory only once
        if plot_ado_flag:
            plt.plot(df["ado_x"], df["ado_y"], label="ado_traj", color="black", alpha=0.5)
            plot_ado_flag = False
        # Plot start and ending points for scenarios if 'show racing line'flag  is set
        if params[VIS_PARAM_SHOW_RACING_LINE]:
            plt.scatter(
                df["ego_x"].iloc[0],
                df["ego_y"].iloc[0],
                label="First Point",
                color="red",
                marker="s",
            )
            plt.scatter(
                df["ego_x"].iloc[-1],
                df["ego_y"].iloc[-1],
                label="Last Point",
                color="blue",
                marker="s",
            )
        plt.plot(
            df["ego_x"],
            df["ego_y"],
            label=f"Sc{i + 1}_traj",
            color=colormap(i),
            alpha=0.5,
        )
        plt.scatter(df["ego_x"].iloc[0], df["ego_y"].iloc[0], color=colormap(i))

    plt.legend()
    return fig, axs


def plot_ado(ax, vehicle_data, row):
    """
    Plots ado and its trajectory

    Parameters
    ----------
    vehicle_data : pd.DataFrame
        Dataframe containing vehicle data
    row:
        A single row of vehicle_data

    Returns
    -------
        None
    """
    ax.scatter(row["ado_x"], row["ado_y"], c="red", label="ado")
    ax.plot(
        vehicle_data["ado_x"],
        vehicle_data["ado_y"],
        c="red",
        label="ado trajectory",
        alpha=0.2,
    )
    ado_yaw = get_yaw_from_quaternion(row, "ado")
    rect = RotatedVehicle((row["ado_x"], row["ado_y"]), angle=ado_yaw, alpha=0.5)
    ax.add_patch(rect)


def plot_status_bars(fig, vehicle_data, row):
    """
    Plots status bars (speedometer, throttle/brakes and steering)

    Parameters
    ----------
    vehicle_data : pd.DataFrame
        Dataframe containing vehicle data
    row:
        A single row of vehicle_data

    Returns
    -------
        None
    """
    ax_small1 = fig.add_axes([0.15, 0.82, 0.2, 0.02])
    ax_small2 = fig.add_axes([0.15, 0.75, 0.2, 0.02])
    ax_small3 = fig.add_axes([0.15, 0.68, 0.2, 0.02])

    bar_width = 0.1
    ax_small1.barh(y=0.1, width=row["speedometer"], height=bar_width, color="green")
    ax_small1.set_xlim(min(vehicle_data["speedometer"]), max(vehicle_data["speedometer"]))
    ax_small1.set_yticks([])
    ax_small1.set_title("Speed")
    ax_small1.set_xlim(1, 100)  # cap speedometer at 100

    ax_small2.barh("thbr", np.maximum(0, row["throttle"]), color="blue")
    # Plot negative values (left side) for mph
    ax_small2.barh("thbr", (-1) * np.minimum(0, -1 * row["brake"]), color="red")
    # Add zero line
    ax_small2.axvline(0, color="black", linewidth=1)
    ax_small2.set_xlim(-1, 1)
    ax_small2.set_yticks([])
    ax_small2.set_title("brake/throttle")

    ax_small3.barh("steering", np.maximum(0, row["steering"]), color="blue", label="Speed")
    # Plot negative values (left side) for mph
    ax_small3.barh("steering", np.minimum(0, row["steering"]), color="red")
    # Add zero line
    ax_small3.axvline(0, color="black", linewidth=1)
    ax_small3.set_xlim(-1, 1)


def get_basic_plot(ax, vehicle_data, reference_track_data, row, title, params):
    """
    Get a basic plot with ego and its orientation and ado if its available

    Parameters
    ----------
    vehicle_data : pd.DataFrame
        Dataframe containing vehicle data
    reference_track_data : pd.DataFrame
        The map data panda frame
    row:
        A single row of vehicle_data
    title:
        Title of the BEV plot
    params : Dict, optional

    Returns
    -------
        None
    """
    ax.scatter(row["ego_x"], row["ego_y"], c="green", label="ego")
    plot_track(ax, reference_track_data, params[VIS_PARAM_SHOW_RACING_LINE])
    ax.plot(
        vehicle_data["ego_x"],
        vehicle_data["ego_y"],
        c="green",
        label="ego trajectory",
        alpha=0.2,
    )
    ego_yaw = get_yaw_from_quaternion(row, "ego")
    rect = RotatedVehicle((row["ego_x"], row["ego_y"]), angle=ego_yaw, alpha=0.5)
    ax.add_patch(rect)

    if "ado_x" in vehicle_data:
        plot_ado(ax, vehicle_data, row)

    ax.text(
        0.5,
        -0.1,
        f"Timestamp: {row['timestamp']}",
        transform=ax.transAxes,
        ha="center",
        va="center",
        color="Black",
    )

    ax.set_title(title)
    handles, labels = plt.gca().get_legend_handles_labels()
    by_label = dict(zip(labels, handles))
    plt.legend(by_label.values(), by_label.keys())


def split_dataframe(df, batch_size):
    """Split the dataframe in batches.

    Args:
        df (pd.DataFrame): Input dataframe
        batch_size (int): Number of sample in single batch.

    Return:
        List: List of dataframes.
    """
    return [df.iloc[i : i + batch_size] for i in range(0, len(df), batch_size)]


def traj_animation(vehicle_data, reference_track_data, params):
    """
    Get a list of frames which can be used for trajectory animation

    Returns:
        _type_: List
    """
    vehicle_data = subsample_dataframe(vehicle_data, params[VIS_PARAM_FPS])

    frames = []

    fig, ax = plt.subplots(figsize=(11, 11))

    # Iterate over the dataframe
    for index, row in vehicle_data.iterrows():
        get_basic_plot(ax, vehicle_data, reference_track_data, row, "BEV animation", params)

        if len(vehicle_data["ego_x"]) > 0:
            ax.set_xlim(
                min(vehicle_data["ego_x"]) - WINDOW_ZOOM,
                max(vehicle_data["ego_x"]) + WINDOW_ZOOM,
            )  # Zoom in on ego vehicle
            ax.set_xlim(
                min(vehicle_data["ego_y"]) - WINDOW_ZOOM,
                max(vehicle_data["ego_y"]) + WINDOW_ZOOM,
            )

        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format="png")
        # Reset the buffer position to the beginning
        img_buffer.seek(0)
        # Open the image from the buffer and append it to the frames list
        frames.append(Image.open(img_buffer))

        # Clear the figure for the next iteration
        ax.cla()

    return frames


def overtake_animation(vehicle_data, reference_track_data, bool_series, params):
    """
    Get a list of frames for overtake animation

    Args:
        vehicle_data (pd.DataFrame): Scenario vehicle data
        reference_track_data (pd.DataFrame): track data
        bool_series (np.array): bool series depicting overtake as True or False
        params (dict): _description_, by default {}, can include
                "time_window" - time window in which trajectory will be displayed.
                "show_columns" - columns in the pandas frame to show the recent past/future time signal from these columns.
                "anim_output_dir" - name of the file and plot to be saved.
                "fps" - frames per second for the video
                "show_racing_line" - show the recing line in the plot

    Returns:
        _type_: List
    """
    frames = []
    vehicle_data["overtake_bool"] = bool_series
    vehicle_data = subsample_dataframe(vehicle_data, params[VIS_PARAM_FPS])

    # Iterate over the dataframe
    for index, row in vehicle_data.iterrows():
        ax = plt.gca()
        plot_track(ax, reference_track_data, params[VIS_PARAM_SHOW_RACING_LINE])
        plt.plot(vehicle_data["ego_x"], vehicle_data["ego_y"], c="yellow", label="trajectory")
        plt.scatter(row["ego_x"], row["ego_y"], c="green", label="ego", marker="s")
        if row["overtake_bool"]:
            plt.scatter(row["ego_x"], row["ego_y"], c="red", label="alert")
        plt.title("Overtake animation")
        plt.legend()

        if len(vehicle_data["ego_x"]) > 0:
            plt.xlim(
                min(vehicle_data["ego_x"]) - WINDOW_ZOOM,
                max(vehicle_data["ego_x"]) + WINDOW_ZOOM,
            )  # Zoom in on ego vehicle
            plt.ylim(
                min(vehicle_data["ego_y"]) - WINDOW_ZOOM,
                max(vehicle_data["ego_y"]) + WINDOW_ZOOM,
            )

        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format="png")
        # Reset the buffer position to the beginning
        img_buffer.seek(0)
        # Open the image from the buffer and append it to the frames list
        frames.append(Image.open(img_buffer))

        # Clear the figure for the next iteration
        plt.clf()

    return frames


def ego_centric_animation(vehicle_data, reference_track_data, params):
    """Get a list of frames which can be used for trajectory animation
    Returns:
        _type_: List
    """
    vehicle_data = subsample_dataframe(vehicle_data, params[VIS_PARAM_FPS])

    frames = []
    # Iterate over the dataframe
    for index, row in vehicle_data.iterrows():
        fig, ax = plt.subplots(figsize=(11, 11))
        get_basic_plot(
            ax, vehicle_data, reference_track_data, row, "Ego centric animation", params
        )
        if "plot_sound_name" in params and params["plot_sound_name"]:
            ax.text(
                0.5,
                -0.05,
                f"Sound name: {row['sound_name']}",
                transform=ax.transAxes,
                ha="center",
                va="center",
                color="Black",
            )

        plot_status_bars(fig, vehicle_data, row)
        if len(vehicle_data["ego_x"]) > 0:
            ax.set_xlim(
                row["ego_x"] - WINDOW_ZOOM, row["ego_x"] + WINDOW_ZOOM
            )  # Zoom in on ego vehicle
            ax.set_ylim(row["ego_y"] - WINDOW_ZOOM, row["ego_y"] + WINDOW_ZOOM)

        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format="png")

        # Reset the buffer position to the beginning
        img_buffer.seek(0)
        # Open the image from the buffer and append it to the frames list
        frames.append(Image.open(img_buffer))

    return frames


def visualize_overtake_timepoint(vehicle_data, reference_track_data, timepoint, params):
    """
    Given an overtake dataframe and timepoint, visualize ego and ado vehicle
    and their past and future trajectories for the next few seconds.

    Args:
        vehicle_data (pd.DataFrame): overtake snippet dataframe
        reference_track_data (pd.DataFrame): track data
        timepoint (float): Specific timepoint at which BEV is required
    """
    (
        vehicle_subset,
        closest_timepoint,
        positive_window,
        negative_window,
    ) = get_past_and_future_trajectories(vehicle_data, timepoint)
    fig, ax = plt.subplots(figsize=(8, 8))
    plot_track(ax, reference_track_data, params[VIS_PARAM_SHOW_RACING_LINE])
    plt.plot(
        positive_window["ego_x"],
        positive_window["ego_y"],
        c="green",
        label="future trajectory",
        alpha=0.5,
    )
    plt.plot(
        negative_window["ego_x"],
        negative_window["ego_y"],
        c="blue",
        label="past trajectory",
        alpha=0.5,
    )
    plt.scatter(closest_timepoint["ego_x"], closest_timepoint["ego_y"], c="red", label="ego")
    plt.plot(
        positive_window["ado_x"],
        positive_window["ado_y"],
        c="green",
        label="future trajectory",
        alpha=0.5,
    )
    plt.plot(
        negative_window["ado_x"],
        negative_window["ado_y"],
        c="blue",
        label="past trajectory",
        alpha=0.5,
    )
    plt.scatter(closest_timepoint["ado_x"], closest_timepoint["ado_y"], c="black", label="ado")

    ax.set_xlim(
        min(vehicle_subset["ego_x"]) - WINDOW_ZOOM, max(vehicle_subset["ego_x"]) + WINDOW_ZOOM
    )  # Zoom in on ego vehicle
    ax.set_ylim(
        min(vehicle_subset["ego_y"]) - WINDOW_ZOOM, max(vehicle_subset["ego_y"]) + WINDOW_ZOOM
    )

    ax.set_title("Bird's Eye View")
    ax.legend()

    # Save the plot
    save_plot(params[VIS_PARAM_ANIM_OUTPUT_DIR])
    if params[VIS_PARAM_VERBOSE]:
        print(f"file saved as {params[VIS_PARAM_ANIM_OUTPUT_DIR]}")


def plot_spatial_distribution(trials_list, track_map, map_segment_id: int = None):
    """
    Plot sound cues form a list of trials only where the soundname changes
    Args:
        trials_list (List): List of Trials
        track_map (pd.DataFrame): Track map
        map_segment_id (int, optional): Provide the segment of a map. If no segment is provided, entire track will be used.
    """
    fig, ax = plt.subplots(figsize=(8, 8))
    plot_track(ax, track_map, False)

    unique_colors = {}
    color_id = 0

    for trial in trials_list:
        vehicle_data = trial.get_dataframe()

        # Crop the dataframe according to the map segment id
        if map_segment_id is not None:
            vehicle_data = vehicle_data[vehicle_data["map_segment_ids"] == map_segment_id]

        nonzero_idxs = np.nonzero(
            np.diff(vehicle_data["sound_name_finished log time"].values) > 0
        )[0]
        if nonzero_idxs.size == 0:
            continue
        sound_names_at_nz_idx = vehicle_data["sound_name"].iloc[nonzero_idxs]
        nonzero_idxs = [id + vehicle_data.index[0] for id in nonzero_idxs]

        for sound_name in sound_names_at_nz_idx.unique():
            if sound_name not in unique_colors.keys() and isinstance(sound_name, str):
                unique_colors[sound_name] = f"C{color_id}"
                color_id += 1

        # plot the change in cues
        for idx in nonzero_idxs:
            x, y = vehicle_data.loc[idx, ["ego_x", "ego_y"]]
            sound_name = sound_names_at_nz_idx.get(
                idx, default=None
            )  # Get sound name for the index
            if sound_name is not None:
                ax.scatter(x, y, color=unique_colors[sound_name], label=sound_name, s=7)

    ax.set_xlim(
        min(vehicle_data["ego_x"]) - WINDOW_ZOOM,
        max(vehicle_data["ego_x"]) + WINDOW_ZOOM,
    )  # Zoom in on ego vehicle
    ax.set_ylim(
        min(vehicle_data["ego_y"]) - WINDOW_ZOOM,
        max(vehicle_data["ego_y"]) + WINDOW_ZOOM,
    )
    # Remove standard from labels
    for key in list(unique_colors.keys()):
        new_key = " ".join(key.split("_")[1:])
        unique_colors[new_key] = unique_colors.pop(key)

    ax.set_title("Spatial distribution of teaching cues")
    legend_elements = [
        Line2D([0], [0], marker="o", color="w", label=label, markerfacecolor=color, markersize=10)
        for label, color in unique_colors.items()
    ]
    ax.legend(handles=legend_elements)
    plt.show()


def bev_fpv_animation(vehicle_data, reference_track_data, start_time, timeframe_length, params):
    """
    Provided a start time and timeframe length, get BEV and FPV animations side by side
    Args:
        vehicle_data (pd.DataFrame): vehicle dataframe
        reference_track_data (pd.DataFrame): Track map
        start_time (Float): Time at which to start in seconds
        timeframe_length (Int): Length of the animation in seconds
        params (dict): list of parameters
    """

    if start_time < vehicle_data.iloc[0][TIMESTAMP_KEY]:
        print(
            f"warning: Start time not in dataframe. Defaulting to the start time {vehicle_data.iloc[0]['timestamp']}"
        )
        start_time = vehicle_data.iloc[0][TIMESTAMP_KEY]
    end_time = start_time + timeframe_length
    if end_time > vehicle_data.iloc[-1][TIMESTAMP_KEY]:
        print(
            f"warning: Timeframe exceeded the possible limit. Defaulting to the end time {vehicle_data.iloc[-1]['timestamp']}"
        )
        end_time = vehicle_data.iloc[-1][TIMESTAMP_KEY]

    # Trim the dataframe based on start and end time
    start_index = abs(vehicle_data[TIMESTAMP_KEY] - start_time).idxmin()
    end_index = abs(vehicle_data[TIMESTAMP_KEY] - end_time).idxmin()
    vehicle_data = vehicle_data.loc[start_index : end_index + 1]

    # Make sure that you have this set - "export QT_QPA_PLATFORM=offscreen" before using this function
    bev_frames = traj_animation(vehicle_data, reference_track_data, params)
    vehicle_data = subsample_dataframe(vehicle_data, params[VIS_PARAM_FPS])
    frames = []
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))

    # Iterate over the dataframe
    for (_, row), list_item in zip(vehicle_data.iterrows(), bev_frames):
        bytestring = row["chase_cam_image_data"]
        np_arr = np.frombuffer(bytestring, np.uint8)
        image_np = cv2.imdecode(np_arr, cv2.IMREAD_COLOR)
        # Converting color because matplotlib requires RGB ordering whereas OpenCV (perversely) uses BGR
        image_np = cv2.cvtColor(image_np, cv2.COLOR_BGR2RGB)
        ax1.imshow(np.array(list_item))
        ax2.imshow(image_np)
        ax2.axis("off")
        ax2.set_title("Image")

        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format="png")
        # Reset the buffer position to the beginning
        img_buffer.seek(0)
        # Open the image from the buffer and append it to the frames list
        frames.append(Image.open(img_buffer))

        # Clear the figure for the next iteration
        ax1.cla()
        ax2.cla()

    return frames


def side_by_side_bev(
    vehicle_data1, timepoint1, vehicle_data2, timepoint2, track_map, timeframe_length, params
):
    """
    Get side by side birds eye view for 2 dataframes based on given timepoints and sync offset
    Args:
        vehicle_data1 (pd.DataFrame): first dataframe
        timepoint1 (Float): The timepoint of interest for first dataframe in seconds
        vehicle_data2 (pd.DataFrame): second dataframe
        timepoint2 (Float): The timepoint of interest for second dataframe in seconds
        track_map (pd.DataFrame): Track map
        timeframe_length (Int): Length of the timeframe starting from the timepoint in seconds
        params (dict): list of parameters
    """
    if params[VIS_SYNC_OFFSET] > timeframe_length:
        raise ValueError("Sync offset cannot be larger than the timeframe length")
    timepoint1 = timepoint1 - params[VIS_SYNC_OFFSET]
    timepoint2 = timepoint2 - params[VIS_SYNC_OFFSET]
    if timepoint1 < vehicle_data1.iloc[0][TIMESTAMP_KEY]:
        print(
            "Warning: Timepoint 1 is less than the minimum value. Defaulting it to the minimum possible value"
        )
        timepoint1 = vehicle_data1.iloc[0][TIMESTAMP_KEY]
    if timepoint2 < vehicle_data2.iloc[0][TIMESTAMP_KEY]:
        print(
            "Warning: Timepoint 2  is less than the minimum value. Defaulting it to the minimum possible value"
        )
        timepoint2 = vehicle_data2.iloc[0][TIMESTAMP_KEY]
    end_time1 = timepoint1 + timeframe_length
    end_time2 = timepoint2 + timeframe_length
    if end_time1 > vehicle_data1.iloc[-1][TIMESTAMP_KEY]:
        print(
            "warning: Vechicle 1 timeframe exceeded the possible limit. Defaulting to the end of dataframe"
        )
        end_time1 = vehicle_data1.iloc[-1][TIMESTAMP_KEY]
    if end_time2 > vehicle_data2.iloc[-1][TIMESTAMP_KEY]:
        print(
            "warning: Vehicle 2 timeframe exceeded the possible limit. Defaulting to the end of dataframe"
        )
        end_time2 = vehicle_data2.iloc[-1][TIMESTAMP_KEY]

    # Calculate the timedifference and get the start index and end index
    start_index1 = abs(vehicle_data1[TIMESTAMP_KEY] - timepoint1).idxmin()
    start_index2 = abs(vehicle_data2[TIMESTAMP_KEY] - timepoint2).idxmin()
    end_index1 = abs(vehicle_data1[TIMESTAMP_KEY] - end_time1).idxmin()
    end_index2 = abs(vehicle_data2[TIMESTAMP_KEY] - end_time2).idxmin()

    # Trim the DataFrame to start from the nearest point
    trimmed_vehicle_data1 = vehicle_data1.loc[start_index1 : end_index1 + 1]
    trimmed_vehicle_data2 = vehicle_data2.loc[start_index2 : end_index2 + 1]

    frames1 = traj_animation(trimmed_vehicle_data1, track_map, params)
    frames2 = traj_animation(trimmed_vehicle_data2, track_map, params)

    # Repeat last element of the either array to make them equal in length
    diff = abs(len(frames1) - len(frames2))
    for i in range(diff):
        if len(frames1) > len(frames2):
            frames2.append(frames2[-1])
        else:
            frames1.append(frames1[-1])

    frames = []
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))

    # Iterate over the dataframe
    for f1, f2 in zip(frames1, frames2):
        ax1.imshow(np.array(f1))
        ax1.axis("off")
        ax1.set_title("dataframe1")
        ax2.imshow(np.array(f2))
        ax2.axis("off")
        ax2.set_title("dataframe2")

        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format="png")
        # Reset the buffer position to the beginning
        img_buffer.seek(0)
        # Open the image from the buffer and append it to the frames list
        frames.append(Image.open(img_buffer))
        ax1.cla()
        ax2.cla()

    return frames


def bev_startgate_based(dataframe1, dataframe2, track_map, timeframe_length, params):
    """
    Get side by side birds eye view for 2 dataframes based on the first start gate
    Args:
        dataframe1 (pd.DataFrame): first dataframe
        dataframe2 (pd.DataFrame): second dataframe
        track_map (pd.DataFrame): Track map
        timeframe_length (Int): Length of the timeframe starting from the timepoint in seconds
        params (dict): list of parameters
    """

    # Getting indices of the first occurence of start gate
    startgate_index1 = dataframe1.index[dataframe1["sound_name"] == "start gate passed"].min()
    startgate_index2 = dataframe2.index[dataframe2["sound_name"] == "start gate passed"].min()

    # Getting timestamps
    timepoint1 = dataframe1[TIMESTAMP_KEY][startgate_index1]
    timepoint2 = dataframe2[TIMESTAMP_KEY][startgate_index2]

    return side_by_side_bev(
        dataframe1, timepoint1, dataframe2, timepoint2, track_map, timeframe_length, params
    )


def bev_poi_based(dataframe1, dataframe2, track_map, poi_coords: tuple, timeframe_length, params):
    """
    Get side by side birds eye view for 2 dataframes based on a point of interest
    Args:
        dataframe1 (pd.DataFrame): first dataframe
        dataframe2 (pd.DataFrame): second dataframe
        track_map (pd.DataFrame): Track map
        poi_coords (tuple): coordinates of a point of interest
        timeframe_length (Int): Length of the timeframe starting from the timepoint in seconds
        params (dict): list of parameters
    """

    # Adding euclidian distance column to calculate distance between ego coords and POI coords
    dataframe1["distance_to_poi"] = np.sqrt(
        (dataframe1["ego_x"] - poi_coords[0]) ** 2 + (dataframe1["ego_y"] - poi_coords[1]) ** 2
    )
    dataframe2["distance_to_poi"] = np.sqrt(
        (dataframe2["ego_x"] - poi_coords[0]) ** 2 + (dataframe2["ego_y"] - poi_coords[1]) ** 2
    )

    # Get the index of the row closest to POI
    closest_row_index1 = dataframe1["distance_to_poi"].idxmin()
    closest_row_index2 = dataframe2["distance_to_poi"].idxmin()

    # Getting the timestamp of that specific row
    timepoint1 = dataframe1[TIMESTAMP_KEY][closest_row_index1]
    timepoint2 = dataframe2[TIMESTAMP_KEY][closest_row_index2]

    return side_by_side_bev(
        dataframe1, timepoint1, dataframe2, timepoint2, track_map, timeframe_length, params
    )


def bev_multicar(vehicle_data, reference_track_data, params):
    """
    Get a BEV for multicar session
    Args:
        vehicle_data (pd.DataFrame): dataframe consisting of the vehicle data
        reference_track_data (pd.DataFrame): dataframe consisting track data
        params (dict): Necessary parameters
    """
    vehicle_data = subsample_dataframe(vehicle_data, params[VIS_PARAM_FPS])
    frames = []

    # Iterate over the dataframe
    for index, row in vehicle_data.iterrows():
        ax = plt.gca()
        plot_track(ax, reference_track_data, params[VIS_PARAM_SHOW_RACING_LINE])
        plt.scatter(row["ego_x"], row["ego_y"], c="green", label="vehicle1")
        plt.plot(
            vehicle_data["ego_x"],
            vehicle_data["ego_y"],
            c="green",
            label="vehicle1 trajectory",
            alpha=0.2,
        )

        ego_yaw = get_yaw_from_quaternion(row, "ego")
        rect = RotatedVehicle((row["ego_x"], row["ego_y"]), angle=ego_yaw, alpha=0.5)
        ax.add_patch(rect)

        if len(vehicle_data["ego_x"]) > 0:
            plt.xlim(
                min(vehicle_data["ego_x"]) - WINDOW_ZOOM,
                max(vehicle_data["ego_x"]) + WINDOW_ZOOM,
            )  # Zoom in on ego vehicle
            plt.ylim(
                min(vehicle_data["ego_y"]) - WINDOW_ZOOM,
                max(vehicle_data["ego_y"]) + WINDOW_ZOOM,
            )

        # TODO There are chances that the human-controlled vehicle won't have the same identifier as the one below. If those differ, then we need to find all the human controlled vehicles and loop over them.
        plt.scatter(
            row["carla_object_1000011_x"], row["carla_object_1000011_y"], c="red", label="vehicle2"
        )
        ado_yaw = get_yaw_from_quaternion(row, "carla_object_1000011")
        rect = RotatedVehicle(
            (row["carla_object_1000011_x"], row["carla_object_1000011_y"]),
            angle=ado_yaw,
            alpha=0.5,
        )
        ax.add_patch(rect)
        plt.plot(
            vehicle_data["carla_object_1000011_x"],
            vehicle_data["carla_object_1000011_y"],
            c="red",
            label="vehicle2 trajectory",
            alpha=0.2,
        )

        plt.title("Bird's Eye View for multicar session")
        plt.legend()

        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format="png")
        # Reset the buffer position to the beginning
        img_buffer.seek(0)
        # Open the image from the buffer and append it to the frames list
        frames.append(Image.open(img_buffer))

        # Clear the figure for the next iteration
        plt.clf()

    return frames


def ado_centric_multiple(dataframe_list, reference_track_data, params):
    dataframe_list = [
        subsample_dataframe(dataframe, params[VIS_PARAM_FPS]) for dataframe in dataframe_list
    ]
    dataframe_list = [
        dataframe.reset_index(drop=True) for dataframe in dataframe_list
    ]  # make the indices from 0 to n-1 where n is the length of df
    ado_dataframe = dataframe_list[0]  # grab a constant dataframe for ado
    frames = []
    fig, ax = plt.subplots(figsize=(11, 11))

    # Iterate over any dataframe for ado as it is the same across all episodes
    for index, row in ado_dataframe.iterrows():
        plot_track(ax, reference_track_data, params[VIS_PARAM_SHOW_RACING_LINE], False)
        ax.scatter(row["ado_x"], row["ado_y"], c="green", label="ado")
        ax.plot(
            ado_dataframe["ado_x"],
            ado_dataframe["ado_y"],
            c="green",
            label="ado trajectory",
            alpha=0.2,
        )

        ado_yaw = get_yaw_from_quaternion(row, "ado")
        rect = RotatedVehicle((row["ado_x"], row["ado_y"]), angle=ado_yaw, alpha=0.5)
        ax.add_patch(rect)

        if len(ado_dataframe["ado_x"]) > 0:
            ax.set_xlim(
                row["ado_x"] - WINDOW_ZOOM, row["ado_x"] + WINDOW_ZOOM
            )  # Zoom in on ado vehicle
            ax.set_ylim(row["ado_y"] - WINDOW_ZOOM, row["ado_y"] + WINDOW_ZOOM)

        # iterate over all the dataframes for egos
        for i, dataframe in enumerate(dataframe_list):
            ax.scatter(
                dataframe["ego_x"][index],
                dataframe["ego_y"][index],
                label=f"ego-trial-{i + 1}",
            )
            ax.plot(
                dataframe["ego_x"],
                dataframe["ego_y"],
                label=f"ego-{i + 1}-trajectory",
                alpha=0.2,
            )
            ego_yaw = get_yaw_from_quaternion(dataframe.loc[index], "ego")
            rect = RotatedVehicle(
                (dataframe["ego_x"][index], dataframe["ego_y"][index]), angle=ego_yaw, alpha=0.5
            )
            ax.add_patch(rect)

        ax.text(
            0.5,
            1.05,
            f"Timestamp: {row['timestamp']}",
            transform=ax.transAxes,
            ha="center",
            va="center",
            fontsize=13,
            color="black",
        )
        ax.set_title("Ado centric animation")
        ax.legend()

        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format="png")
        # Reset the buffer position to the beginning
        img_buffer.seek(0)
        # Open the image from the buffer and append it to the frames list
        frames.append(Image.open(img_buffer))

        # Clear the figure for the next iteration
        ax.cla()

    return frames


def callback_function(vehicle_data, reference_track_data, timepoint, params):
    """
    Callback function implementation
    """

    return None


def plot_annotated_spatial_distribution(
    trials_list: list,
    track_map: pd.DataFrame,
    marker_distinction: bool,
    map_segment_id: int = None,
    viz_details: dict = {},
) -> None:
    """
    Plot annonated spatial distribution of trials on a map segment.

    Args:
        trials_list (list): A list of tuples [(vehicle_df, annotation_df), ...].
                            Each tuple consists of:
                            - vehicle_df (pd.DataFrame): Vehicle trial data.
                            - annotation_df (pd.DataFrame): Annotation data mapped to subtitles.
        track_map (pd.DataFrame): DataFrame representing the track map.
        marker_distinction (bool): Enable marker distinction for trials.
        map_segment_id (int, optional): Map segment ID to filter data. If None, entire track is plotted.

    Returns:
        None
    """
    # Combine all vehicle_data into a single DataFrame
    all_vehicle_data = pd.concat([pair[0] for pair in trials_list], ignore_index=True)

    # Filter by map segment ID if provided
    if map_segment_id is not None:
        all_vehicle_data = all_vehicle_data[all_vehicle_data["map_segment_ids"] == map_segment_id]

    # Compute global min/max values for setting plot limits
    global_min_x = all_vehicle_data["ego_x"].min()
    global_max_x = all_vehicle_data["ego_x"].max()
    global_min_y = all_vehicle_data["ego_y"].min()
    global_max_y = all_vehicle_data["ego_y"].max()

    fig, ax = plt.subplots(figsize=(8, 8))
    plot_track(ax, track_map, False)  # Plot the track as a background

    unique_colors = {}
    color_id = 0

    for i, combine_file in enumerate(trials_list):
        vehicle_data = combine_file[0]
        annotated_data = combine_file[1]

        # Filter by map segment ID if provided
        if map_segment_id is not None:
            vehicle_data = vehicle_data[vehicle_data["map_segment_ids"] == map_segment_id]

        # Extract and parse SRT data
        vehicle_coach_data = vehicle_data["subject_coach_srts"].apply(parse_srt_content)

        # Extract subtitle contents and find indices where content changes
        contents = vehicle_coach_data.apply(
            lambda subtitle: subtitle[0].content if subtitle else None
        )
        content_changes = contents != contents.shift()
        indices_where_content_changes = content_changes[content_changes].index.tolist()

        # Create mapping dictionary for annotations
        df1_unique = annotated_data.drop_duplicates(subset=["coach_subtitle"], keep="first")
        mapping_dict = df1_unique.set_index("coach_subtitle").to_dict(orient="index")

        # Map SRT content to categories
        def map_content_to_category(subtitles):
            for subtitle in subtitles:
                if subtitle and subtitle.content.strip() != "NA":
                    if subtitle.content in mapping_dict:
                        return mapping_dict[subtitle.content]["Category"]
                    return subtitle.content

        final_content = vehicle_coach_data.apply(map_content_to_category)

        # Assign unique colors for each subtitle category
        for sound_name in final_content.unique():
            if isinstance(sound_name, str) and sound_name not in unique_colors:
                unique_colors[sound_name] = f"C{color_id}"
                color_id += 1

        # Plot scatter points for content changes
        for idx in indices_where_content_changes:
            x, y = vehicle_data.loc[idx, ["ego_x", "ego_y"]]
            subtitle_content = final_content.get(idx)
            if subtitle_content is not None and subtitle_content in unique_colors:
                ax.scatter(
                    x,
                    y,
                    color=unique_colors[subtitle_content],
                    label=f"{subtitle_content}_trial{i}",
                    s=80,
                    marker=MARKERS[i % len(MARKERS)] if marker_distinction else "o",
                )

    # Set the plot limits based on global values
    ax.set_xlim(global_min_x - WINDOW_ZOOM, global_max_x + WINDOW_ZOOM)
    ax.set_ylim(global_min_y - WINDOW_ZOOM, global_max_y + WINDOW_ZOOM)
    ax.set_title(f"Spatial Distribution - Map Segment {map_segment_id}")

    # Create legend entries for categories
    category_legend_elements = [
        Line2D([0], [0], marker="o", color="w", label=label, markerfacecolor=color, markersize=10)
        for label, color in unique_colors.items()
    ]

    # Create legend entries for trials if marker distinction is enabled
    trial_legend_elements = (
        [
            Line2D(
                [0],
                [0],
                marker=MARKERS[j % len(MARKERS)],
                color="black",
                label=f"Trial {j}",
                markersize=10,
                linestyle="None",
            )
            for j in range(len(trials_list))
        ]
        if marker_distinction
        else []
    )

    ax.legend(
        handles=category_legend_elements + trial_legend_elements,
        fontsize="small",
    )

    if viz_details.get("save_result"):
        path_to_save = Path(viz_details.get("viz_dir")) / viz_details.get("save_as")
        plt.savefig(path_to_save)

    plt.show()
    plt.close(fig)


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--show-only", type=str2bool, default=False, help="Show the visualization."
    )
    parser.add_argument("--time-window", type=int, help="Time window in seconds.")
    parser.add_argument("--columns", nargs="*", help="Desired columns to visualize.")
    parser.add_argument("--anim-output-dir", type=str, help="save a file (provide file name)")
    parser.add_argument("--fps", type=int, default=10, help="frames per second for video")
    parser.add_argument(
        "--show-racing-line", type=str2bool, default=False, help="Show the racing line"
    )
    parser.add_argument(
        "-v", "--verbose", type=str2bool, default=False, help="Enable verbose mode"
    )
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()

    # Code in run_data_environment.py can save a dataframe whole pickle.
    with open("mcap_dataframe.pkl", "rb") as f:
        data_dict = pickle.load(f)

    panda_frame = list(data_dict[DATA_DICT_KEY_EPISODES].values())[0]
    track_map = data_dict[DATA_DICT_KEY_MAP]
    params = {
        VIS_PARAM_SHOW_COLUMNS: args.columns,
        VIS_PARAM_TIME_WINDOW: args.time_window,
        VIS_PARAM_ANIM_OUTPUT_DIR: args.anim_output_dir,
        VIS_PARAM_FPS: args.fps,
        VIS_PARAM_SHOW_RACING_LINE: args.show_racing_line,
        VIS_PARAM_VERBOSE: args.verbose,
    }
    timepoint = 0

    visualize_timepoint(
        vehicle_data=panda_frame,
        reference_track_data=track_map,
        timepoint=timepoint,
        params=params,
    )

    if args.show_only:
        plt.show()
