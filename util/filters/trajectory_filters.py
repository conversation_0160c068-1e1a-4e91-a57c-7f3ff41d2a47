import collections
import datetime
import math
from datetime import time
from typing import Callable, Dict, List, Tuple

import numpy as np
import pandas as pd
import scipy.signal
import srt
from scipy.spatial.distance import cdist
from scipy.spatial.transform import Rotation as R
from shapely import geometry

from data_sources.compact_sim.dataloading import (
    DATA_DICT_KEY_EPISODES,
    DATA_DICT_KEY_MAP,
    load_and_cache_compactsim_data,
)
from util.utility import (
    build_bbox,
    check_intersect,
    compute_intervehicle_metrics,
    compute_lat_lon_coords,
    flatten_bbox,
    get_start_end_time_subtitle,
    get_yaw_from_quaternion,
)
from util.visualization.visualization import visualize_timepoint

PARAMS_DICT_KEY_REF_LINE = "reference_line_columns"
PARAMS_DICT_KEY_LEFT = "lane_left_columns"
PARAMS_DICT_KEY_RIGHT = "lane_right_columns"

PARAMS_DICT_KEY_NEGATIVE_SPEED_THRESHOLD = "negative_speed_threshold"
DATA_DICT_KEY_LATLONG = "lat_long_coordinates"
DATA_DICT_KEY_DECEL = "deceleration"
DATA_DICT_KEY_SPINOUT = "spinout"
DATA_DICT_KEY_OVERTAKE = "overtake"
DATA_DICT_KEY_PROXIMITY = "in_proximity"
DATA_DICT_KEY_OUT_OF_BOUNDS = "out_of_bounds"
DATA_DICT_KEY_TRIAL_IDS = "trial_ids"
DATA_DICT_KEY_MAP_SEGMENT_IDS = "map_segment_ids"

MAP_LANE_LEFT_X = "inner_edge/x"
MAP_LANE_LEFT_Y = "inner_edge/y"

MAP_LANE_RIGHT_X = "outer_edge/x"
MAP_LANE_RIGHT_Y = "outer_edge/y"

MAP_REFERENCE_X = "refline/x"
MAP_REFERENCE_Y = "refline/y"

MAP_SEGMENT_ID_KEY = "seq_num"

RESULT_KEY_TIMESTAMPS = "carla_objects log time"


def default_filter_params() -> Dict:
    """Populates default filter parameters.

    Returns
    -------
    Dict
        A dictionary with default param values
    """
    params = {
        PARAMS_DICT_KEY_REF_LINE: (MAP_REFERENCE_X, MAP_REFERENCE_Y),
        PARAMS_DICT_KEY_LEFT: (MAP_LANE_LEFT_X, MAP_LANE_LEFT_Y),
        PARAMS_DICT_KEY_RIGHT: (MAP_LANE_RIGHT_X, MAP_LANE_RIGHT_Y),
    }
    return params


def annotate_lat_long_coordinates(
    vehicle_data: pd.DataFrame,
    reference_track_data: pd.DataFrame = None,
    vehicle_columns: list = ("ego_x", "ego_y", "carla_objects log time"),
    params: Dict = None,
):
    """
    Annotate lateral distances of the vehicle from the reference line and longitudinal coordinates
    along it.

    Parameters
    ----------
    vehicle_data : pd.DataFrame
        panda frame with all the vehicle data for an episode
    reference_track_data : pd.DataFrame, optional
        panda frame with the map data
    vehicle_columns : list, optional
        columns to capture the egovehicle position.
    params: Dict, optional
        A parameters dictionary, has by default reference_line_columns

    Returns
    -------
    Dict
        Includes: 'lateral_distances','longitudinal_coords','longitudinal_speed','delta_t',
        'diff_geometry' (a dictionary with normals and tangents around a resampling of the
        reference line).

    """
    if params is None:
        params = {
            PARAMS_DICT_KEY_REF_LINE: ("refline/x", "refline/y"),
            "negative_speed_threshold": -200,
        }
    reference_line_columns = params[PARAMS_DICT_KEY_REF_LINE]
    ref_line_x = reference_track_data[reference_line_columns[0]]
    ref_line_y = reference_track_data[reference_line_columns[1]]
    veh_x = vehicle_data[vehicle_columns[0]]
    veh_y = vehicle_data[vehicle_columns[1]]
    veh_t = vehicle_data[vehicle_columns[2]]

    # The time gap between consecutive samples
    delta_t = np.array(veh_t[1:]) - np.array(veh_t[:-1])
    # repeat last row to make the size same as original timestamp series
    delta_t = np.array((list(delta_t) + [delta_t[-1]]))

    # Compute lat/lon coordinates of the vehicle poses.
    longitudinal_coords, lateral_distances, diff_geom = compute_lat_lon_coords(
        ref_line_x, ref_line_y, veh_x, veh_y
    )

    # TODO(guy.rosman): consider using heading information in addition to longitudinal
    # coordinates.
    speed = (longitudinal_coords[1:] - longitudinal_coords[:-1]) / delta_t[1:]
    negative_speed_threshold = params[PARAMS_DICT_KEY_NEGATIVE_SPEED_THRESHOLD]

    # Detect points where the longitudinal coordinates have rolled back to 0/track beginning
    # TODO(guy.rosman): verify these are roll over points of the longitudinal coordinates,
    # rather than jumps
    rollover_points = np.where(speed < negative_speed_threshold)
    for idx in rollover_points:
        speed[idx] = speed[idx - 1]

    result = pd.DataFrame(
        {
            "lateral_distances": lateral_distances,
            "longitudinal_coords": longitudinal_coords,
            "longitudinal_speed": np.insert(
                speed, 0, 0
            ),  # add 0.0 to the beginning so that speed is the same length
            "delta_t": delta_t,
            RESULT_KEY_TIMESTAMPS: veh_t,
        }
    )
    return {"annotation_df": result, "additional_dict": {"diff_geometry": diff_geom}}


def detect_spinout(
    vehicle_data: pd.DataFrame,
    reference_track_data: pd.DataFrame = None,
    vehicle_columns: list = (
        "ego_x",
        "ego_y",
        "carla_objects log time",
        "ego_orientation_x",
        "ego_orientation_y",
        "ego_orientation_z",
        "ego_orientation_w",
    ),
    slip_thresh: float = 0.5,
):
    """Mark the time instances where the vehicle has a higher slip rate, indicating a spin.

    Parameters
    ----------
    vehicle_data : pd.DataFrame
        panda frame with all the vehicle data for an episode
    reference_track_data : pd.DataFrame, optional
        panda frame with the map data
    vehicle_columns : list, optional
        columns to capture the egovehicle position.
    slip_thresh: float, optional
        The threshold above which a slip rate is considered a spinout

    Returns
    -------
    Dict
        annotation_df" to a pd.DataFrame containing a) timestamps b) boolean array indicating
        spinout and c) the calculated sideslip ratio and "additional_dict" to an empty dict
        (required by function signature)
    """
    veh_x = vehicle_data[vehicle_columns[0]]
    veh_y = vehicle_data[vehicle_columns[1]]
    veh_t = vehicle_data[vehicle_columns[2]]
    ego_yaw = get_yaw_from_quaternion(vehicle_data, vehicle_columns[3].split("_")[0], deg=False)

    dt = np.diff(veh_t)

    # epsilon to avoid dividing by 0
    EPS = 1e-10
    velocity_x = np.insert(np.diff(veh_x) / (dt + EPS), 0, 0.0)
    velocity_y = np.insert(np.diff(veh_y) / (dt + EPS), 0, 0.0)

    vel_ego_frame = (
        R.from_euler("Z", ego_yaw)
        .inv()
        .apply(np.column_stack([velocity_x, velocity_y, np.zeros(velocity_x.shape)]))
    )

    # Get the ratio of lateral velocity to longitudinal velocity
    sideslip = vel_ego_frame[:, 1] / (vel_ego_frame[:, 0] + EPS)

    # Spinout is true if ratio is above a threshold and longitudinal velocity is above a low value.
    # Checking the long vel reduces the chance of noise at a stop.
    min_long_vel = 5.0  # m/s
    spinout = np.logical_and(
        np.abs(vel_ego_frame[:, 0]) > min_long_vel, np.abs(sideslip) > slip_thresh
    )

    result = pd.DataFrame(
        {RESULT_KEY_TIMESTAMPS: veh_t, "spinout": spinout, "sideslip_ratio": sideslip}
    )
    return {"annotation_df": result, "additional_dict": {}}


def detect_deceleration(
    vehicle_data: pd.DataFrame,
    reference_track_data: pd.DataFrame = None,
    vehicle_columns: list = ("ego_x", "ego_y", "carla_objects log time"),
    threshold: float = -0.6,
    params: Dict = None,
):
    """Mark the time instances where the vehicle is deccelerating

    Parameters
    ----------
    vehicle_data : pd.DataFrame
        panda frame with all the vehicle data for an episode
    reference_track_data : pd.DataFrame, optional
        panda frame with the map data
    vehicle_columns : list, optional
        columns to capture the egovehicle position.
    params: Dict, optional
        A parameters dictionary, has by default reference_line_columns

    Returns
    -------
    Dict
        A dictionary mapping "result" to an np boolean vector of whether the vehicle is doing a
        deceleration.
    """
    if params is None:
        params = default_filter_params()
    veh_x = vehicle_data[vehicle_columns[0]]
    veh_y = vehicle_data[vehicle_columns[1]]
    veh_t = vehicle_data[vehicle_columns[2]]

    dt = np.gradient(veh_t)
    velocity_x = np.gradient(veh_x) / (dt + np.finfo(float).eps)
    velocity_y = np.gradient(veh_y) / (dt + np.finfo(float).eps)
    speed = np.linalg.norm(np.vstack((velocity_x, velocity_y)), axis=0)
    ds = np.gradient(speed) / (dt + np.finfo(float).eps)
    median_filter_size = 5
    # TODO(guy.rosman): check the sign of the threshold and operator once there's
    # unit testing / visualization.
    deceleration = scipy.signal.medfilt(ds, median_filter_size) / (speed + 1.0) < threshold
    result = pd.DataFrame({RESULT_KEY_TIMESTAMPS: veh_t, "deceleration": deceleration})

    return {"annotation_df": result, "additional_dict": {}}


def filter_all_episodes(
    data_dict: Dict, annotation_data_dict: Dict, filter: Callable, annotation_key: str
) -> Tuple[Dict, Dict]:
    """Filter all episodes with an auto-annotator/filter

    Parameters
    ----------
    data_dict : Dict
        The episodes data dictionary.
    annotation_data_dict : Dict
        The annotation data dictionary.
    filter : Callable
        A filter to auto-annotate an episode
    annotation_key : str
        The key to store the annotation under

    Returns
    -------
    Tuple[Dict,Dict]
        The updated data_dict, annotation_data_dict
    """
    for episode_idx in range(len(data_dict[DATA_DICT_KEY_EPISODES])):
        panda_frame = list(data_dict[DATA_DICT_KEY_EPISODES].values())[episode_idx]
        panda_frame_key = list(data_dict[DATA_DICT_KEY_EPISODES].keys())[episode_idx]
        track_map = data_dict[DATA_DICT_KEY_MAP]

        # Run spinout detector for the episode
        filter_results = filter(panda_frame, track_map)
        if panda_frame_key not in annotation_data_dict:
            annotation_data_dict[panda_frame_key] = {}
        annotation_data_dict[panda_frame_key][annotation_key] = filter_results
    return data_dict, annotation_data_dict


def detect_ego_ado_proximity(
    vehicle_data: pd.DataFrame,
    ego_keys: list = (
        "ego_x",
        "ego_y",
        "carla_objects log time",
        "ego_orientation_x",
        "ego_orientation_y",
        "ego_orientation_z",
        "ego_orientation_w",
    ),
    ado_keys: list = ("ado_x", "ado_y"),
    dist_threshold: float = 50.0,  # [m]
):
    """
    Detect when the ego and ado are in close proximity to each other, as a proxy for
    interactions.

    Parameters
    ----------
    vehicle_data : pd.DataFrame
        panda frame with all the vehicle data for an episode
    ego_keys : list, optional
        list of keys pointing to position and orientation of the ego vehicle.
        Structure of the list should be as follows:
        ["veh_x", "veh_y", "carla_objects log time", "veh_quat_x", "veh_quat_y", "veh_quat_z", "veh_quat_w"]
    ado_keys : list, optional
        list of keys pointing to position of the ado vehicle. Structure of the list
        should be as follows:
        ["veh_x", "veh_y"]
    dist_threshold: float, optional
        threshold value within which to consider as proximal.

    Returns
    -------
    Dict
        A dictionary mapping "result" to an np boolean vector of whether the ego and
        ado are in relative proximity to one another.
    """
    ts = vehicle_data[ego_keys[2]]
    ego_ado_dist, _, _ = compute_intervehicle_metrics(vehicle_data, ego_keys, ado_keys)

    # if proximity is less than threshold, then consider the vehicles in proximity of one another.
    in_proximity = ego_ado_dist.values < dist_threshold

    result = pd.DataFrame(
        {
            RESULT_KEY_TIMESTAMPS: ts,
            "in_proximity": in_proximity,
            "ego_ado_dist": ego_ado_dist,
        }
    )
    return {"annotation_df": result, "additional_dict": {}}


def detect_overtake(
    vehicle_data: pd.DataFrame,
    reference_track_data: pd.DataFrame = None,
    overtaker_keys: list = (
        "ego_x",
        "ego_y",
        "carla_objects log time",
        "ego_orientation_x",
        "ego_orientation_y",
        "ego_orientation_z",
        "ego_orientation_w",
    ),
    overtakee_keys: list = ("ado_x", "ado_y"),
    dist_threshold: float = 8.0,  # This default value needs to be honed in.
):
    """
    Mark the time instances where the ego vehicle overtakes the ado vehicle. An
    overtake is defined as the ego position passing the ado position on the track,
    taking into accound the heading of the ego vehicle.

    Parameters
    ----------
    vehicle_data : pd.DataFrame
        panda frame with all the vehicle data for an episode
    reference_track_data : pd.DataFrame, optional
        panda frame with the map data
    overtaker_keys : list, optional
        list of keys pointing to position and orientation of the overtaking vehicle.
        Structure of the list should be as follows:
        ["veh_x", "veh_y", "carla_objects log time", "veh_quat_x", "veh_quat_y", "veh_quat_z", "veh_quat_w"]
    overtakee_keys : list, optional
        list of keys pointing to position of the vehicle being overtaken. Structure of
        the list should be as follows:
        ["veh_x", "veh_y"]
    dist_threshold: float, optional
        threshold value within which an overtake is considered (if the vehicles are at
        least this close, passing is considered an overtake). This prevents track
        geometry triggering the overtak filter.

    Returns
    -------
    Dict
        A dictionary mapping "annotation_df" to an pd.DataFrame that contains a boolean
        vector of whether the ego vehicle has completed an overtake, the timestamps and
        the side of overtake.

    """
    if overtakee_keys[0] not in vehicle_data.keys():
        return {"annotation_df": pd.DataFrame(), "additional_dict": {}}
    ts = vehicle_data[overtaker_keys[2]]
    ego_ado_dist, ego_ado_dot, rotated_ego_ado = compute_intervehicle_metrics(
        vehicle_data, overtaker_keys, overtakee_keys
    )

    # if proximity is less than threshold and dot product is negative, overtake occurred.
    overtake = np.logical_and(ego_ado_dist.values < dist_threshold, ego_ado_dot < 0)
    overtake_side = ["" for i in overtake]
    for i, ot in enumerate(overtake):
        if ot:
            # Vehicle frame has the +x-axis out the front of the car, so +y is the left side, -y the right.
            if rotated_ego_ado[i, 1] > 0:
                overtake_side[i] = "left"
            else:
                overtake_side[i] = "right"
    result = pd.DataFrame(
        {
            RESULT_KEY_TIMESTAMPS: ts,
            "overtake": overtake,
            "overtake_side": overtake_side,
        }
    )
    return {"annotation_df": result, "additional_dict": {}}


def detect_overtake_attempt():
    """
    Stub for overtake attempt detection filter. Data introspection needs to happen
    before this can be fleshed out.
    TODO: ThomasB
    """
    pass


def detect_out_of_bounds(
    vehicle_data: pd.DataFrame,
    reference_track_data: pd.DataFrame = None,
    vehicle_columns: list = ("ego_x", "ego_y", "carla_objects log time"),
    params: Dict = None,
) -> Dict:
    """Mark the time instances where the vehicle getting out of the track bounds.
    Assumes a circular topology of the track (right lane boundary = outer bound, left
    lane boundary = inner bound)

    Parameters
    ----------
    vehicle_data : pd.DataFrame
        panda frame with all the vehicle data for an episode

    reference_track_data : pd.DataFrame, optional
        panda frame with the map data
    vehicle_columns : list, optional
        columns to capture the egovehicle position.
    params: Dict, optional
        A parameters dictionary, has by default reference_line_columns

    Returns
    -------
    Dict
        A dictionary mapping annotation_df" to an pd.DataFrame containing a) timestamps
        b) boolean indicating out of bounds and c) distances when out of bounds
        (0.0 when on track)
        and "additional_dict" to an empty dict (required by function signature)
    """
    if params is None:
        params = default_filter_params()
    map_left_x_column = params[PARAMS_DICT_KEY_LEFT][0]
    map_left_y_column = params[PARAMS_DICT_KEY_LEFT][1]
    map_right_x_column = params[PARAMS_DICT_KEY_RIGHT][0]
    map_right_y_column = params[PARAMS_DICT_KEY_RIGHT][1]
    veh_x = vehicle_data[vehicle_columns[0]]
    veh_y = vehicle_data[vehicle_columns[1]]
    veh_t = vehicle_data[vehicle_columns[2]]

    map_left_x = reference_track_data[map_left_x_column]
    map_left_y = reference_track_data[map_left_y_column]
    map_right_x = reference_track_data[map_right_x_column]
    map_right_y = reference_track_data[map_right_y_column]
    external = geometry.Polygon(zip(np.array(map_right_x), np.array(map_right_y)))
    internal = geometry.Polygon(zip(np.array(map_left_x), np.array(map_left_y)))

    # Create a shapely polygon of the track's bounds
    track_polygon = external.difference(internal)

    # Get distances from polygon using shapely (dist 0 == inside)
    dists = [track_polygon.distance(geometry.Point(x, y)) for x, y in zip(veh_x, veh_y)]
    result = pd.DataFrame(
        {
            RESULT_KEY_TIMESTAMPS: veh_t,
            "out_of_bounds": np.array(dists) > 0,
            "out_of_bounds_distances": np.array(dists),
        }
    )
    return {"annotation_df": result, "additional_dict": {}}


def mark_trial_ids(vehicle_data: pd.DataFrame):
    """
    Mark the time instances with proper trial id.
    Valid trials are marked with trial id in [0...N]
    Incomplete trials are marked with -1.The ones where proctor had to intervene and
    restart the scenario.
    The rest of the timestamps (downtime between trials) are marked with np.nan

    Returns
    -------
    Dict
        A dictionary mapping "result" which is an array of trial ids and "timestamp"
        which is the timestamps of the dataframe
    """
    # There are NaNs at the beginning of this column because carla starts recording
    # much before scenario condition rosmsgs.
    scenario_condition = vehicle_data["condition"].fillna("nan")
    # identify indices at which the scenario condition changed
    change_indices = scenario_condition.ne(scenario_condition.shift()).values.nonzero()[0].tolist()
    data_at_change_indices = scenario_condition.iloc[change_indices]
    # grab indices of ALL start gate passed (this could be more than the number of
    # trials because of restarts)
    all_indices_start_gate = data_at_change_indices.index[
        data_at_change_indices == "start gate passed"
    ].tolist()
    indices_after_start_gate = [
        change_indices[change_indices.index(i) + 1] for i in all_indices_start_gate
    ]
    # check for each pair of start and next messgae. If next message is finished or
    # gate to gate, valid. if it is scenario ended invalid.
    # create empty trial id pd.Series initialized to np.nan
    trial_ids = pd.Series([np.nan] * len(vehicle_data["carla_objects log time"]))
    # retrieve time stamp series
    veh_t = vehicle_data["carla_objects log time"]

    trial_id = 0
    for start_gate_ind, next_ind_after_start_gate in zip(
        all_indices_start_gate, indices_after_start_gate
    ):
        next_ind_data = data_at_change_indices[next_ind_after_start_gate]
        # the reason for the check for either condition is because when dfs are merged
        # we use 'backward' and it is possible that between two rows of the reference
        # col, both finish gate passed and gate-to-gate time would have been published
        # and therefore only gate-to-gate time would be recorded.
        if next_ind_data == "finish gate passed" or next_ind_data == "gate-to-gate time":
            print(
                f"VALID TRIAL: {trial_id}, between {start_gate_ind}, {next_ind_after_start_gate} - next data: {next_ind_data}"
            )
            trial_ids[start_gate_ind : next_ind_after_start_gate + 1] = trial_id
            trial_id += 1
        if next_ind_data == "scenario ended":
            print(
                f"INVALID/INCOMPLETE TRIAL: between {start_gate_ind}, {next_ind_after_start_gate} - next data: {next_ind_data}"
            )
            # incomplete trial
            trial_ids[start_gate_ind : next_ind_after_start_gate + 1] = -1

    result = pd.DataFrame(
        {
            RESULT_KEY_TIMESTAMPS: veh_t,
            "trial_ids": trial_ids.values,
        }
    )
    return {"annotation_df": result, "additional_dict": {}}


def mark_map_segment_ids(
    vehicle_data: pd.DataFrame,
    reference_track_data: pd.DataFrame = None,
):
    """
    Mark all time stamps with the id of the map segment on which the ego car is at
    those timestamps

    Parameters
    ----------
    vehicle_data : pd.DataFrame
        panda frame with all the vehicle data for an episode
    reference_track_data : pd.DataFrame
        panda frame with the map data

    Returns
    -------
    Dict
        A dictionary mapping "annotation_df" to an pd.DataFrame containing the map
        segment ids and timestamps and "additional_dict" to an empty dict
        (required by function signature)
    """

    xy = vehicle_data[["ego_x", "ego_y"]].values  # (M, 2)
    veh_t = vehicle_data["carla_objects log time"]
    track_xy = reference_track_data[[MAP_REFERENCE_X, MAP_REFERENCE_Y]].values  # (N, 2)
    # compute pairwise distance between each vehicle position and all track reference points
    pairwise_dist = cdist(xy, track_xy)  # (M, N)
    # extract map segment id for the index that corresponds to minimum distance to map for each vehicle position
    # (M, )
    map_segment_ids = reference_track_data[MAP_SEGMENT_ID_KEY][np.argmin(pairwise_dist, axis=1)]
    result = pd.DataFrame(
        {RESULT_KEY_TIMESTAMPS: veh_t, "map_segment_ids": map_segment_ids.values}
    )
    return {"annotation_df": result, "additional_dict": {}}


def generate_transcript_df(
    vehicle_data: pd.DataFrame,
    reference_track_data: pd.DataFrame = None,
    params: Dict = None,
    srt_data: Dict = None,
):
    """
    Function that takes extracted transcripts from srt files and align them temporally with the trials
    according to timestamp column

    Parameters
    ----------
    vehicle_data : pd.DataFrame
        panda frame with all the vehicle data for an episode

    reference_track_data : pd.DataFrame
        panda frame with the map data

    params: Dict, optional
        A parameters dictionary, has by default reference_line_columns

    srt_data: Dict
        Additional dict containing required metadata information about list of srts to merge, starting rostime and timestamps
        of the rosbags from which the srts were extracted. This is from the subject from which this trial originated

    Returns
    -------
    Dict
        A dictionary mapping "annotation_df" to an pd.DataFrame containing the transcripts
        from both coach and the driver and timestamps and "additional_dict" to an empty dict
        (required by function signature)
    """
    veh_t = vehicle_data["carla_objects log time"]
    veh_carla_objects_ros_time = vehicle_data["carla_objects log time"]

    # grab srt metadata for the subject from which this trial originated
    # grab start ros time and timestamp list. one for each transcript file
    srt_start_ros_time_list = srt_data["initial_rostime_for_each_srt"]
    # init output dataframe
    result = pd.DataFrame({RESULT_KEY_TIMESTAMPS: veh_t})

    for key in srt_data.keys():
        # only process srt lists. The keys corresponding to these lists are
        # 'subject_coach_srts', 'subject_driver_srts'
        if key != "subject_coach_srts" and key != "subject_driver_srts":
            continue
        print(f"PROCESSING SRT KEY {key}")
        # list of list of subtitles. There could be subtitle lists from multiple srt files
        _srt = srt_data[key]
        # create df to be merged
        _srt_df = pd.Series(
            [
                srt.Subtitle(
                    index=0, start=datetime.timedelta(0), end=datetime.timedelta(0), content="NA"
                ).to_srt()
            ]
            * len(vehicle_data["carla_objects log time"])
        )
        # iterate over full list of srts of a given type (coach/driver) for the subject corresponding to the trial
        for srt_i, subtitle_list in enumerate(_srt):
            # grab the start ros time and timestamp for the rosbag from which the srt was extracted
            srt_rosbag_start_ros_time = srt_start_ros_time_list[srt_i]
            # iterate through each subtitle and figure out the timestamps at which it should be merged into
            for subtitle in subtitle_list:
                # in seconds
                start_srt_time, end_srt_time = get_start_end_time_subtitle(subtitle)
                # start and end rostime for the subtitle
                start_srt_rostime = (
                    srt_rosbag_start_ros_time + start_srt_time  # - srt_rosbag_start_timestamp
                )
                end_srt_rostime = (
                    srt_rosbag_start_ros_time + end_srt_time  # - srt_rosbag_start_timestamp
                )

                # if subtitle start rostime is already past the end time of the trial stop processing to save computation
                if start_srt_rostime > veh_carla_objects_ros_time.iloc[-1]:
                    break

                # if subtitle falls within the trial.
                # TODO (deepak.gopinath) Only check if the start_rostime for subtitle is within the trial and also account for subtitles
                # which ends after the trial. Need to update how the "closest_end_veh_t_idx" is computed
                if (
                    start_srt_rostime >= veh_carla_objects_ros_time.iloc[0]
                    and end_srt_rostime <= veh_carla_objects_ros_time.iloc[-1]
                ):
                    # TODO (deepak.gopinath add verbose option)
                    print(subtitle.content)
                    # find veh_t index closest to start of subtitle
                    closest_start_veh_t_dx = np.nonzero(
                        (veh_carla_objects_ros_time.values - start_srt_rostime) > 0
                    )[0][0]
                    # find veh_t index closest to end of subtitle
                    closest_end_veh_t_idx = np.nonzero(
                        (veh_carla_objects_ros_time.values - end_srt_rostime) > 0
                    )[0][0]
                    # for all timestamps in the range assign the same subtitle as a str object so that parquetifying is successful
                    _srt_df.iloc[closest_start_veh_t_dx:closest_end_veh_t_idx] = subtitle.to_srt()

        # add to the result dataframe
        result[key] = _srt_df.values

    return {"annotation_df": result, "additional_dict": {}}


def generate_concurrent_annotation_df(
    vehicle_data: pd.DataFrame,
    reference_track_data: pd.DataFrame = None,
    params: Dict = None,
    concurrent_feedback_annotation_data: Dict = None,
):
    """
    Function that takes extracted transcripts from srt files and align them temporally with the trials
    according to timestamp column

    Parameters
    ----------
    vehicle_data : pd.DataFrame
        panda frame with all the vehicle data for an episode

    reference_track_data : pd.DataFrame
        panda frame with the map data

    params: Dict, optional
        A parameters dictionary, has by default reference_line_columns

    concurrent_feedback_annotation_data: Dict
        Additional dict containing required metadata information to the full paths to the annotation csvs for each trial

    Returns
    -------
    Dict
        A dictionary mapping "annotation_df" to an pd.Dataframe containing the concurrent feedback annotation for all annotators
        from both coach and the driver and timestamps and "additional_dict" to an empty dict or potentially containing info about worker ids, GPT model used for GPt annotations
        (required by function signature)
    """

    concurrent_feedback_annotation_csv_path = concurrent_feedback_annotation_data[
        "annotation_csv_path"
    ]
    # annotation_worker_ids is a list of worker ids, each of which looks like private-us-east-1.311daa...
    annotation_worker_ids = concurrent_feedback_annotation_data["worker_ids"]
    veh_carla_objects_ros_time = vehicle_data["carla_objects log time"]

    # init output dataframe
    result = pd.DataFrame({RESULT_KEY_TIMESTAMPS: veh_carla_objects_ros_time})

    concurrent_feedback_annotation_df = pd.read_csv(concurrent_feedback_annotation_csv_path)
    # check if the unique list of coach subtitles extracted from the dataframe matches the list of
    # utterances from the csv. Should match. If it matches, then we have a 1:1 map from dataframe utterances
    # to annotation utterances
    all_change_inds = np.nonzero(
        (vehicle_data["subject_coach_srts"] != vehicle_data["subject_coach_srts"].shift(1)).values
    )[0]
    coach_utterances_vehicle_df = []
    for idx, ind in enumerate(all_change_inds):
        utterance_at_ind = list(srt.parse(vehicle_data["subject_coach_srts"][ind]))[0].content
        if utterance_at_ind != "NA":
            # keep track of range of indices for each valid subtitle to be filled later. this is given by
            # idx, and all_change_inds[idx+1]. Then we can use range(ind, all_change_inds[idx+1])
            # note that idx + 1 is safe to use and won't result in index not in bounds as the last subtitle in every trial is NA
            # (checked indepedently)
            coach_utterances_vehicle_df.append((utterance_at_ind, ind, all_change_inds[idx + 1]))

    coach_utterances_annotation_df_full = list(
        concurrent_feedback_annotation_df["coach_subtitle"].values
    )
    coach_utterances_annotation_df = []
    # remove all nans. Happens when there are no valid utterances.
    for a in coach_utterances_annotation_df_full:
        if type(a) != str and np.isnan(a):
            continue
        coach_utterances_annotation_df.append(a)

    # compare the utterances grabbed from the dataframe and the annotation csvs and make sure they match exactly
    assert len(coach_utterances_annotation_df) == len(coach_utterances_vehicle_df)
    assert coach_utterances_annotation_df == [s[0] for s in coach_utterances_vehicle_df]

    for annotation_worker_id in annotation_worker_ids:
        annotation_worker_id_df = pd.Series(
            [("INVALID", "INVALID", "INVALID")] * len(vehicle_data["carla_objects log time"])
        )
        for u_idx, (coach_utterance, start_ind, end_ind) in enumerate(coach_utterances_vehicle_df):
            utterance_annotation_from_worker_id = tuple(
                concurrent_feedback_annotation_df.iloc[u_idx][
                    [
                        f"{annotation_worker_id}_Type",
                        f"{annotation_worker_id}_Category",
                        f"{annotation_worker_id}_Subcategory",
                    ]
                ].values
            )
            # TODO (deepak.gopinath) Add option for verbose
            print(
                f"Annotation for {coach_utterance} from worker_id {annotation_worker_id} is {utterance_annotation_from_worker_id}"
            )
            annotation_worker_id_df.iloc[start_ind:end_ind] = [
                utterance_annotation_from_worker_id
            ] * (end_ind - start_ind)
        result[annotation_worker_id] = annotation_worker_id_df.values

    # TODO (deepak.gopinath) add parsing of GPT labels, the ones given to the annottaors as well.
    # TODO add info regarding GPT model used for generating the labels that were given as prompts to the annotators in the additional dict.

    # Potentially create new  labels with newer version of GPTs and add that as well
    return {"annotation_df": result, "additional_dict": {}}


def extract_bsd_srt_for_trial_id(bsd_srt_info_dict: Dict):
    """
    Helper function to extract transcripts between two trials for BSD AIC.
    Parameters
    ----------
    bsd_srt_info_dict: Dict
    dictionary containing all necessary information for extracting between sessions dialog between trials.
    The dictionary contains the following keys.
    'srt_info_dict' - dictionary of srt lists, and associated rostime and timestamp information.
    'prev_trial_info' - information about the end rostime and timestamp of previous trial
    'next_trial_info' - information about the start rostime and timestamp of next trial

    Returns
    -------
    Dict
        A dictionary containing the between sessions dialogue (and corresponding ros start time for the subtitle)
        extracted from each of the srt keys (subject_coach_srts, subject_driver_srts)
    """
    srt_data = bsd_srt_info_dict["srt_info_dict"]
    prev_trial_info = bsd_srt_info_dict["prev_trial_info"]
    next_trial_info = bsd_srt_info_dict["next_trial_info"]

    # grab start ros time and timestamp list. one for each transcript file
    srt_start_ros_time_list = srt_data["initial_rostime_for_each_srt"]

    bsd_srt_dict_for_trial = collections.defaultdict(list)
    for key in srt_data.keys():
        # only process srt lists. The keys corresponding to these lists are
        # 'subject_coach_srts', 'subject_driver_srts'
        if key != "subject_coach_srts" and key != "subject_driver_srts":
            continue

        print()
        print(f"PROCESSING SRT KEY {key}")
        # list of list of subtitles. There could be subtitle lists from multiple srt files
        _srt = srt_data[key]

        # iterate over full list of srts of a given type (coach/driver) for the subject corresponding to the trial
        for srt_i, subtitle_list in enumerate(_srt):
            # grab the start ros time and timestamp for the rosbag from which the srt was extracted
            srt_rosbag_start_ros_time = srt_start_ros_time_list[srt_i]
            # iterate through each subtitle and determine if the subtitle fall in between prev and next trial
            for subtitle in subtitle_list:
                # in seconds
                start_srt_time, end_srt_time = get_start_end_time_subtitle(subtitle)
                # start rostime for the subtitle
                start_srt_rostime = (
                    srt_rosbag_start_ros_time + start_srt_time  # - srt_rosbag_start_timestamp
                )

                if (
                    start_srt_rostime > prev_trial_info["end_rostime"]
                    and start_srt_rostime < next_trial_info["start_rostime"]
                ):
                    # TODO (deepak.gopinath add verbose option)
                    # print(subtitle.content)
                    bsd_srt_dict_for_trial[key].append(
                        {"subtitle": subtitle, "subtitle_start_rostime": start_srt_rostime}
                    )

    return bsd_srt_dict_for_trial


def trials_by_laps(vehicle_data: pd.DataFrame, distance_threshold: int = 10):
    """
    Use initial ego position as the lap start, and count a new trial as each time the ego passes back by the start
    Args
    -------
        - vehicle_data (pd.DataFrame): dataframe containing ego position data and trajectory info
        - distance_threshold (int): How many meters around do we consider as "back at the origin"
             (What is the radius of the "done sphere" centered on the origin?)

    Returns
    -------
        - Optional[Dict]
            A dictionary mapping "result" which is an array of trial ids and "timestamp"
                which is the timestamps of the dataframe
            OR None if the data dictionary is empty
    """
    if len(vehicle_data) == 0:
        return None

    veh_t = vehicle_data["carla_objects log time"]
    # Define the origin
    origin_x, origin_y, origin_z = vehicle_data.loc[0, ["ego_x", "ego_y", "ego_z"]]

    # Calculate the distance from the origin for every row
    distances_from_origin = np.sqrt(
        (vehicle_data["ego_x"] - origin_x) ** 2
        + (vehicle_data["ego_y"] - origin_y) ** 2
        + (vehicle_data["ego_z"] - origin_z) ** 2
    )

    # Determine when the user passes back through the origin (lap points)
    is_at_origin = distances_from_origin < distance_threshold

    # Find the points where a new lap starts
    laps = []
    prev_at_origin = False

    lap_num = -1
    for index, at_origin in enumerate(is_at_origin):
        if at_origin and not prev_at_origin:
            # User just crossed back to the origin, mark this as a new lap
            lap_num += 1
        laps.append(lap_num)
        prev_at_origin = at_origin

    result = pd.DataFrame({"trial_ids": laps, RESULT_KEY_TIMESTAMPS: veh_t})

    return {"annotation_df": result, "additional_dict": {}}
