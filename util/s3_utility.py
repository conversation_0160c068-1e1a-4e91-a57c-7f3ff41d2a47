import os
import sys
import urllib
from pathlib import Path
from urllib.parse import urlparse

import boto3
from botocore.exceptions import NoCredentialsError, PartialCredentialsError


def download_s3_files_recursively(aws_profile, s3_uri, download_dir):
    """Download consolidated JSONs.

    Args:
        aws_profile (str): AWS profile used to download videos.
        s3_uri (str): S3 URI from which files needs to be downloaded.
        download_dir (str): sink directory where all the files are stored locally.

    Returns:
        None
    """

    # Use s3_uri flag to "exit" if you want to this downloading step.
    if s3_uri.lower() == "exit":
        print("Skip downloading data from S3.")
        return

    # Validate S3 URI
    if not s3_uri.startswith("s3://"):
        print("Invalid S3 URI format. Please start with 's3://'.")
        sys.exit(1)

    directory: Path = Path(download_dir)
    # Create the sink directory if not exists
    if not directory.exists():
        directory.mkdir(parents=True, exist_ok=True)

    try:
        download_s3_files(aws_profile, s3_uri, download_dir)
    except Exception as e:
        print(f"An error occurred: {e}")
        sys.exit(1)


def download_s3_files(aws_profile, s3_uri, download_dir):
    """Download all the files from given s3 bucket folder.

    Args:
        aws_profile (str): AWS profile used to download videos.
        s3_uri (str): S3 URI from which files needs to be downloaded.
        download_dir (str): sink directory where all the files are stored locally.

    Returns:
        None
    """

    verbose: bool = bool(os.environ.get("VERBOSE", False))
    # Parse the S3 URI
    parsed_uri: urllib.parse.ParseResult = urlparse(s3_uri)
    bucket_name: str = parsed_uri.netloc
    prefix: str = parsed_uri.path.lstrip("/")

    session: boto3.Session = boto3.Session(profile_name=aws_profile)
    s3_client: boto3.client = session.client("s3")

    download_path: Path = Path(download_dir)
    # Create a paginator to handle large numbers of objects
    paginator = s3_client.get_paginator("list_objects_v2")
    for page in paginator.paginate(Bucket=bucket_name, Prefix=prefix):
        if "Contents" not in page:
            print("No files found in the specified bucket and path.")
            return

        for obj in page["Contents"]:
            file_key: str = obj["Key"]
            file_name: str = Path(file_key).name
            local_file_path: str = str(download_path.joinpath(file_name))

            if verbose:
                print(f"Downloading {file_key} to {local_file_path}...")

            # Download the file
            s3_client.download_file(bucket_name, file_key, local_file_path)

            if verbose:
                print(f"Downloaded {file_name}")


def upload_to_s3_recursively(source_folder: str, s3_bucket: str, s3_folder: str):
    """Upload files from source folder to S3 bucket recursively.

    Args:
        source_folder (str): Local source folder path.
        s3_bucket (str): s3 bucket name.
        s3_folder (str): s3 folder name.

    Returns:
        None:
    """
    s3_client = boto3.client("s3")

    source: Path = Path(source_folder)
    # Ensure the local folder exists
    if not source.is_dir():
        print(f"Error: The directory '{source_folder}' does not exist.")
        return

    # Walk through the local folder
    for path in source.rglob("*"):
        if path.is_file():
            # Create S3 object path
            relative_path = path.relative_to(source)
            s3_file_path = str(Path(s3_folder).joinpath(relative_path)).replace("\\", "/")

            try:
                s3_client.upload_file(str(path), s3_bucket, s3_file_path)
                print(f"Uploaded {str(path)} to s3://{s3_bucket}/{s3_file_path}")
            except FileNotFoundError:
                print(f"File not found: {str(path)}")
            except NoCredentialsError:
                print("Error: AWS credentials not found.")
                return
            except PartialCredentialsError:
                print("Error: Incomplete AWS credentials.")
                return
            except Exception as e:
                print(f"Failed to upload {str(path)}: {e}")
