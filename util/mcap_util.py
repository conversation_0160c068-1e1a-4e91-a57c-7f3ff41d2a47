import collections
from pathlib import Path
from typing import Dict, List, Optional, <PERSON>ple

import numpy as np
import srt
import yaml

from data_sources import rosbag_info
from data_sources.compact_sim.dataloading import (
    DATA_DICT_KEY_EPISODES,
    DATA_DICT_KEY_MAP,
    load_and_cache_compactsim_data,
    read_all_track_maps,
)


def make_timestamp_monotonic(subject_df):
    """
    Function that detects if there is a break in ros recording and adjusts for the break in the timestamp series so that it is
    always monotonically increasing
    """
    if len(np.nonzero(np.diff(subject_df["timestamp"].values) < 0)[0]):
        print("DETECTED NON-MONOTONIC TIMESTAMP COLUMN. FIXING USING rostime COLUMN INFORMATION")
        # there is a break and reset in the timestamps values.
        # grab the delta in ros log time at the breakpoint and add it to the timestamps with the right offset
        ts_bps = list(np.nonzero(np.diff(subject_df["timestamp"].values) < 0)[0])
        ts_bps.append(subject_df.shape[0])  # append total length of subject_df
        for bp1, bp2 in zip(ts_bps[:-1], ts_bps[1:]):
            delta_ros_time_at_bp1 = (
                subject_df["carla_objects log time"].values[bp1 + 1]
                - subject_df["carla_objects log time"].values[bp1]
            )
            subject_df.loc[bp1 + 1 : bp2, "timestamp"] = subject_df.loc[
                bp1 + 1 : bp2, "timestamp"
            ] + (
                subject_df.loc[bp1, "timestamp"]
                + delta_ros_time_at_bp1
                - subject_df.loc[bp1 + 1, "timestamp"]
            )


def clean_up_nans_in_scenario_condition(subject_df):
    """
    Function to clean nan values in the field that keeps track of scenario change conditions.
    Necessary for cleaner parsing of trials, espcially when there is a restart that occurred mid trial
    """
    # Fill up location with NaN with the string "nan"
    scenario_condition = subject_df["condition"].fillna("nan")
    # identify indices at which the scenario condition msgs changed
    change_indices = scenario_condition.ne(scenario_condition.shift()).values.nonzero()[0].tolist()
    data_at_change_indices = scenario_condition.iloc[change_indices]
    # get all indices where message changed to "nan"
    all_indices_nan = data_at_change_indices.index[data_at_change_indices == "nan"].tolist()
    # append last index of df as an endpoint
    change_indices.append(len(subject_df) - 1)
    indices_after_nan = [change_indices[change_indices.index(i) + 1] for i in all_indices_nan]
    # skip 0th nan index. These nans are valid and corresponding to the nans added at the very beginning of the first mcap
    indices_before_nan = [change_indices[change_indices.index(i) - 1] for i in all_indices_nan[1:]]
    assert len(all_indices_nan) == len(indices_after_nan) == len(indices_before_nan) + 1
    for i, j, k in zip(all_indices_nan[1:], indices_after_nan[1:], indices_before_nan):
        # nan parsing and replacement with condition message that was present
        # immediately before due to break in rosbag recording.
        subject_df.loc[i : j - 1, "condition"] = data_at_change_indices[k]


def get_list_of_mcaps_for_subject(subject_info: Dict) -> Dict:
    """Create and return a dictionary containing the names of the mcap files associated with each subject.

    :param subject_info: dictionary of subject info

    :rtype: Dict
    :return: subject wise list of mcap
    """
    subject_pid_sorted_mcap_names = collections.defaultdict(list)
    for pid, bag_name_list in subject_info.items():
        for sim_bag_name, num_mcaps in bag_name_list:
            # create mcap file name in chronological order
            all_mcap_names = [sim_bag_name + "_" + str(i) for i in range(num_mcaps)]
            for subject_mcap_name in all_mcap_names:
                subject_pid_sorted_mcap_names[pid].append(subject_mcap_name)
    return subject_pid_sorted_mcap_names


def retrieve_rostime_and_timestamp_for_srt(
    subject_srt_files_info: Dict, video_compositing_metadata_dir: Path
):
    """
    Function to retrieve the rostime and timestamp of an srt file by grabbing it from the video metadata file
    : params: subject_srt_files_info: Dictionary containing subject specific experiment info
    : params: video_compositing_metadata_dir: Path to the directory containing the metadata for the videos files from which the srt files were extracted
    """
    # modify srt files info dict in place and add new info regarding rostime and timestamp for each srt
    for pid in subject_srt_files_info.keys():
        srt_rosbag_list = subject_srt_files_info[pid]["srt_rosbag"]
        coach_srt_name_list = subject_srt_files_info[pid]["coach"]
        subject_srt_files_info[pid]["srt_rosbag_initial_rostime"] = []
        # subject_srt_files_info[pid]["srt_rosbag_initial_timestamp"] = []
        for coach_srt_name, srt_rosbag_name in zip(coach_srt_name_list, srt_rosbag_list):
            pid_prefix_end_index = coach_srt_name.find("_")
            pid_prefix = coach_srt_name[:pid_prefix_end_index]
            # go to the video metadata diretcory and get the yaml with same name as pid_prefix. get start time
            video_metadata_file = f"output_{pid_prefix}_videoonly_composite_meta.yaml"
            video_metadata_filepath = video_compositing_metadata_dir / video_metadata_file
            with open(video_metadata_filepath, "r") as fp:
                video_metadata = yaml.safe_load(fp)

            video_start_rostime = video_metadata["start_timestamp"]
            # get start rostime of the video from the video_metadata
            subject_srt_files_info[pid]["srt_rosbag_initial_rostime"].append(video_start_rostime)


def read_mcap_topics_24_d_13(
    args: Dict, experiment: str, additional_topics_dict: Optional[Dict] = None
) -> Tuple:
    """Read MCAP topics and additional topics for given experiment and return experiment details

    :param args: dictionary of command line arguments
    :param experiment: Name of the experiment. i.e. "ifm_quick_studies"
    :param additional_topics_dict: additional topic dictionary

    :rtype: Tuple
    :return: Detail containing subject info, subject srt files info, subject pid sorted mcap names, data dict
    """
    per_subect_mcap_dict = None

    if args["mcap_input_folder"] is not None and len(args["mcap_input_folder"]) > 0:
        per_subect_mcap_dict = rosbag_info.get_sim_bag_data(args["mcap_input_folder"])

    data_dict = load_and_cache_compactsim_data(
        args,
        override=False,
        additional_topics_dict=additional_topics_dict,
        verbose=True,
        per_subject_mcap_dict=per_subect_mcap_dict,
        include_map=False,
    )

    all_maps = read_all_track_maps(args["track_map_csv"])
    data_dict[DATA_DICT_KEY_MAP] = all_maps
    return per_subect_mcap_dict, data_dict


def read_mcap_topics_24_d_05(
    args: Dict, experiment: str, additional_topics_dict: Optional[Dict] = None
) -> Tuple:
    """Read MCAP topics and additional topics for given experiment and return experiment details

    :param args: dictionary of command line arguments
    :param experiment: Name of the experiment. i.e. "24_D_05"
    :param additional_topics_dict: additional topic dictionary

    :rtype: Tuple
    :return: Detail containing subject info, subject srt files info, subject pid sorted mcap names, data dict
    """
    # For each participant set the mcap prefix and number of mcaps with that prefix in chronological order
    variable_name = f"SUBJECT_INFO_{experiment}"
    subject_info = getattr(rosbag_info, variable_name)

    # srt files for each subject and rosbag from which each srt was extracted
    function_name = f"get_{experiment.lower()}_subject_srt_info"
    get_subject_srt_info_func = getattr(rosbag_info, function_name)

    if callable(get_subject_srt_info_func):
        subject_srt_files_info = get_subject_srt_info_func(subject_info)
    else:
        raise Exception(
            f"No callable function '{function_name}' found for experiment '{experiment}' in {rosbag_info.__file__}"
        )

    # get list of mcap files for each subject.
    # args[mcap_input_folder] is the folder containing the different subject folders
    # (each of which contains multiple mcaps)
    # create a dictionary with subject ids as keys and individual mcaps names as values
    subject_pid_sorted_mcap_names = get_list_of_mcaps_for_subject(subject_info)

    data_dict = load_and_cache_compactsim_data(
        args,
        override=False,
        additional_topics_dict=additional_topics_dict,
        verbose=True,
        per_subject_mcap_dict=subject_pid_sorted_mcap_names,
    )

    video_compositing_metadata_dir: Optional[str] = args.get(
        "video_compositing_metadata_dir", None
    )

    if not video_compositing_metadata_dir:
        video_compositing_metadata_path = Path().cwd()
    else:
        video_compositing_metadata_path = Path(video_compositing_metadata_dir)

    retrieve_rostime_and_timestamp_for_srt(subject_srt_files_info, video_compositing_metadata_path)
    return subject_info, subject_srt_files_info, subject_pid_sorted_mcap_names, data_dict


def read_mcap_topics_24_d_16(
    args: Dict, experiment: str, additional_topics_dict: Optional[Dict] = None
) -> Tuple:
    """Read MCAP topics and additional topics for given experiment and return experiment details

    :param args: dictionary of command line arguments
    :param experiment: Name of the experiment. i.e. "24_D_05"
    :param additional_topics_dict: additional topic dictionary

    :rtype: Tuple
    :return: Detail containing subject info, subject srt files info, subject pid sorted mcap names, data dict
    """
    # For each participant set the mcap prefix and number of mcaps with that prefix in chronological order
    variable_name = f"SUBJECT_INFO_{experiment}"
    subject_info = getattr(rosbag_info, variable_name)

    # get list of mcap files for each subject.
    # args[mcap_input_folder] is the folder containing the different subject folders
    # (each of which contains multiple mcaps)
    # create a dictionary with subject ids as keys and individual mcaps names as values
    subject_pid_sorted_mcap_names = get_list_of_mcaps_for_subject(subject_info)
    data_dict = load_and_cache_compactsim_data(
        args,
        override=False,
        additional_topics_dict=additional_topics_dict,
        verbose=True,
        per_subject_mcap_dict=subject_pid_sorted_mcap_names,
    )

    return subject_info, subject_pid_sorted_mcap_names, data_dict


def get_srt(transcriptions_dir: str, subject_pid: str, srt_file: str) -> List:
    """Helper function to read in a srt file, which is the standard SubRip subtitle file.

    :param transcriptions_dir: Transcription directory
    :param subject_pid: subject PID
    :param srt_file: srt file name.

    :rtype List:
    :return srt_list: List of srt text.
    """
    file_path = str(Path(transcriptions_dir).joinpath(subject_pid).joinpath(srt_file))
    fp = None

    try:
        fp = open(file_path, "r", encoding="utf-8")
        srt_text = fp.read()
        srt_gen = srt.parse(srt_text)
        # srt_list is a list of Subtitle objects.
        # For example of a subtitle object in the list can look like
        # Subtitle(index=5, start=datetime.timedelta(seconds=96),
        # end=datetime.timedelta(seconds=106), content='Thank you.', proprietary='')
        srt_list = list(srt_gen)
    except:
        fp = open(file_path, "r", encoding="latin-1")

        srt_text = fp.read()
        srt_gen = srt.parse(srt_text)
        # srt_list is a list of Subtitle objects.
        # For example of a subtitle object in the list can look like
        # Subtitle(index=5, start=datetime.timedelta(seconds=96), end=datetime.timedelta(seconds=106),
        # content='Thank you.', proprietary='')
        srt_list = list(srt_gen)
    finally:
        if fp:
            fp.close()

    return srt_list


def get_mcap_dfs_for_subject(
    subject_pid_sorted_mcap_names: Dict, data_dict: Dict, valid_subject_ids: Optional[str] = None
) -> Dict:
    """Generate dictionary containing the dataframes for all mcaps per subject. Remove data fields that pertain
    to bounding boxes of carla objects for reducing dataframe size.

    :param subject_pid_sorted_mcap_names: subject wise list of MCAPs
    :param data_dict: data dictionary
    :param valid_subject_ids: list of valid subject IDs.

    :rtype Dict:
    :return subject_pid_df_list: subject wise list of pandas dataframe for each
    """
    subject_pid_df_list = collections.defaultdict(list)
    for pid, mcap_name_list in subject_pid_sorted_mcap_names.items():
        if valid_subject_ids is not None and pid not in valid_subject_ids:
            continue
        for subject_mcap_name in mcap_name_list:
            print(subject_mcap_name)
            assert subject_mcap_name in data_dict[DATA_DICT_KEY_EPISODES]
            mcap_df = data_dict[DATA_DICT_KEY_EPISODES][subject_mcap_name]
            subject_pid_df_list[pid].append(mcap_df)
    return subject_pid_df_list


def get_valid_subject_ids(experiment: str):
    variable_name = f"VALID_SUBJECT_IDS_{experiment}"
    valid_subject_ids: List = getattr(rosbag_info, variable_name)

    return valid_subject_ids
