# --------------------------------------------------------
#!/usr/bin/env python3
"""
Confluence crawler: follow inline links (up to N hops) AND recursively crawl all sub-pages.

- Cloud base sample: https://your-domain.atlassian.net/wiki
- Server/DC base:    https://confluence.your-company.com

Auth:
- Cloud: email + API token
- Server/DC: username + password (or adapt to PAT header if desired)

CSV columns:
page_id, depth_link, depth_child, via, title, url, parent_id, labels, last_updated, status, text
"""

import csv
import html
import queue
import re
import sys
from typing import Iterator, Optional, Tuple
from urllib.parse import urlparse, urljoin, parse_qs, unquote

import requests
from bs4 import BeautifulSoup


# ------------------ CONFIG: fill these ------------------
BASE_URL      = "https://toyotaresearchinstitute.atlassian.net/wiki"  # no trailing slash okay
EMAIL_OR_USER = "<EMAIL>"       # Cloud: email; Server/DC: username
API_TOKEN     = "ATATT3xFfGF0Xpp1ftzWlDXIFnbJ-POxTSSyGneI7yTZQLC-N8E3PbdGgSAvxRKxg2y9vrqUJ0rBW5rLZZHZOobw_imIuY0P2C0VO4dpeUchAoFNtZhWzt1Udjhr5Io1nvmk_BnF6ADWnmLf-9AZbstBuBO7-y0CnxwE34A13J504yVP1Ol-zN8=28AC8A95"
START_URL     = "https://toyotaresearchinstitute.atlassian.net/wiki/spaces/HAIL/pages/2976579954/Compact+Simulators"
OUTPUT_CSV    = "confluence_crawl.csv"

# Crawl limits
MAX_LINK_DEPTH  = 2          # follow inline links up to this many hops
MAX_CHILD_DEPTH = None       # set to an int (e.g. 10) or None for unlimited sub-page recursion

# Networking
REQUEST_TIMEOUT = 30
CHILD_PAGE_PAGE_SIZE = 100   # pagination size for child page listing
# ==========================================================


def build_api_base(base_url: str) -> str:
    """Return REST API base path for Confluence."""
    base_url = base_url.rstrip("/")
    # For Cloud, BASE_URL often ends with /wiki; Server/DC often not.
    return base_url + "/rest/api"


def session_with_auth(email_or_user: str, api_token: str) -> requests.Session:
    s = requests.Session()
    s.auth = (email_or_user, api_token)
    s.headers.update({"Accept": "application/json"})
    return s


def extract_page_id_from_url(url: str) -> Optional[str]:
    """Try to get numeric pageId from common URL patterns."""
    parsed = urlparse(url)

    # query param ?pageId=12345678
    q = parse_qs(parsed.query)
    if "pageId" in q and q["pageId"]:
        return q["pageId"][0]

    # path /pages/<id>/
    m = re.search(r"/pages/(\d+)", parsed.path)
    if m:
        return m.group(1)

    return None


def extract_space_and_title_from_pretty_url(url: str) -> Tuple[Optional[str], Optional[str]]:
    """
    For pretty URLs like /spaces/SPACEKEY/pages/<id>/Page-Title
    or /spaces/SPACEKEY/...
    """
    path = urlparse(url).path
    parts = [p for p in path.split("/") if p]
    try:
        i = parts.index("spaces")
    except ValueError:
        return None, None

    space_key = parts[i + 1] if len(parts) > i + 1 else None
    title = unquote(parts[-1]) if parts else None
    if title:
        title = title.replace("+", " ").split("#", 1)[0].strip()
    return space_key, title


def resolve_page_id(api_base: str, sess: requests.Session, url: str) -> Optional[str]:
    """Resolve a page ID from a URL; fall back to space+title search when needed."""
    page_id = extract_page_id_from_url(url)
    if page_id:
        return page_id

    space_key, title = extract_space_and_title_from_pretty_url(url)
    if not space_key or not title:
        return None

    try:
        resp = sess.get(
            f"{api_base}/content",
            params={"spaceKey": space_key, "title": title, "expand": "version"},
            timeout=REQUEST_TIMEOUT,
        )
        resp.raise_for_status()
        data = resp.json()
        if data.get("results"):
            return data["results"][0]["id"]
    except Exception:
        return None

    return None


def get_page(api_base: str, sess: requests.Session, page_id: str) -> Optional[dict]:
    expand = "body.storage,version,ancestors,metadata.labels"
    try:
        r = sess.get(f"{api_base}/content/{page_id}", params={"expand": expand}, timeout=REQUEST_TIMEOUT)
        r.raise_for_status()
        return r.json()
    except Exception as e:
        print(f"Failed to fetch page {page_id}: {e}", file=sys.stderr)
        return None


def confluence_url_for_page(base_url: str, page: dict) -> str:
    links = page.get("_links", {})
    webui = links.get("webui")
    base = links.get("base") or base_url.rstrip("/")
    if webui:
        return urljoin(base + "/", webui.lstrip("/"))
    return f"{base_url.rstrip('/')}/pages/{page['id']}/{page.get('title','')}"


def html_to_text(html_str: str) -> str:
    soup = BeautifulSoup(html_str or "", "html.parser")
    for tag in soup(["script", "style"]):
        tag.decompose()
    for br in soup.find_all(["br", "p", "li", "h1", "h2", "h3", "h4", "h5"]):
        br.append(soup.new_string("\n"))
    text = soup.get_text(separator=" ", strip=True)
    text = html.unescape(text)
    text = re.sub(r"[ \t]+\n", "\n", text)
    text = re.sub(r"\n{3,}", "\n\n", text)
    return text.strip()


def extract_links_from_storage(base_url: str, page: dict) -> set:
    """
    Collect Confluence-internal links from the storage body:
    - <a href="/wiki/pages/<id>/..."> or "...displaypage.action?pageId=..."
    - <ac:link><ri:page ...> macros are captured as 'ri:<space>::<title>' pseudo-links
    """
    out = set()
    storage = page.get("body", {}).get("storage", {}).get("value", "") or ""
    soup = BeautifulSoup(storage, "html.parser")

    # Anchor tags that look like Confluence page links (same host)
    for a in soup.find_all("a", href=True):
        href = a["href"]
        abs_url = urljoin(base_url if base_url.endswith("/") else base_url + "/", href)
        if urlparse(abs_url).netloc != urlparse(base_url).netloc:
            continue
        if re.search(r"/pages/\d+", abs_url) or "displaypage.action" in abs_url:
            out.add(abs_url)

    # Confluence macros: <ri:page ri:content-title="..." ri:space-key="...">
    for ri in soup.find_all("ri:page"):
        title = ri.get("ri:content-title") or ri.get("ri:page-title") or ""
        space = ri.get("ri:space-key") or ""
        if title:
            out.add(f"ri:{space}::{title}")

    return out


def get_labels(page: dict) -> list:
    try:
        labels = page.get("metadata", {}).get("labels", {}).get("results", [])
        return [lbl.get("name", "") for lbl in labels if lbl.get("name")]
    except Exception:
        return []


def iter_child_page_ids(api_base: str, sess: requests.Session, page_id: str) -> Iterator[str]:
    """
    Yield all child page IDs for a given page, handling pagination.
    """
    start = 0
    limit = CHILD_PAGE_PAGE_SIZE
    while True:
        try:
            r = sess.get(
                f"{api_base}/content/{page_id}/child/page",
                params={"start": start, "limit": limit},
                timeout=REQUEST_TIMEOUT,
            )
            r.raise_for_status()
            data = r.json()
        except Exception as e:
            print(f"Failed to list children of {page_id}: {e}", file=sys.stderr)
            return

        results = data.get("results", []) or []
        for child in results:
            cid = child.get("id")
            if cid:
                yield cid

        # Stop when fewer than limit items returned or no next link
        if len(results) < limit and not data.get("_links", {}).get("next"):
            break
        start += limit


def main():
    api_base = build_api_base(BASE_URL)
    sess = session_with_auth(EMAIL_OR_USER, API_TOKEN)

    start_id = resolve_page_id(api_base, sess, START_URL)
    if not start_id:
        print("Could not resolve page ID from START_URL. Ensure it contains a numeric pageId or a standard Confluence URL.", file=sys.stderr)
        sys.exit(1)

    visited_ids = set()
    rows = []

    # Queue items: (page_id, depth_link, depth_child, parent_id, via)
    q = queue.Queue()
    q.put((start_id, 0, 0, None, "seed"))

    while not q.empty():
        pid, d_link, d_child, parent_id, via = q.get()

        # Resolve pseudo 'ri:' links before visiting
        if isinstance(pid, str) and pid.startswith("ri:"):
            # Form: ri:<space>::<title>
            _, rest = pid.split("ri:", 1)
            space, _, title = rest.partition("::")
            space = (space or "").strip()
            title = (title or "").strip()
            if not title:
                continue
            try:
                r = sess.get(
                    f"{api_base}/content",
                    params={"spaceKey": space or None, "title": title, "expand": "version"},
                    timeout=REQUEST_TIMEOUT,
                )
                r.raise_for_status()
                js = r.json()
                if not js.get("results"):
                    continue
                pid = js["results"][0]["id"]
            except Exception:
                continue

        if pid in visited_ids:
            continue
        visited_ids.add(pid)

        page = get_page(api_base, sess, pid)
        if not page:
            continue

        page_url = confluence_url_for_page(BASE_URL, page)
        title = page.get("title", "")
        updated = page.get("version", {}).get("when", "")
        status = page.get("status", "")  # 'current' or 'archived'
        text = html_to_text(page.get("body", {}).get("storage", {}).get("value", ""))
        labels = ",".join(get_labels(page))

        rows.append({
            "page_id": page["id"],
            "depth_link": d_link,
            "depth_child": d_child,
            "via": via,
            "title": title,
            "url": page_url,
            "parent_id": parent_id or "",
            "labels": labels,
            "last_updated": updated,
            "status": status,
            "text": text,
        })

        # ---- Enqueue children recursively (respect MAX_CHILD_DEPTH) ----
        if MAX_CHILD_DEPTH is None or d_child < MAX_CHILD_DEPTH:
            for child_id in iter_child_page_ids(api_base, sess, page["id"]):
                if child_id not in visited_ids:
                    q.put((child_id, d_link, d_child + 1, page["id"], "child"))

        # ---- Enqueue linked pages up to MAX_LINK_DEPTH ----
        if d_link < MAX_LINK_DEPTH:
            for link in extract_links_from_storage(BASE_URL, page):
                if isinstance(link, str) and link.startswith("ri:"):
                    q.put((link, d_link + 1, d_child, page["id"], "link"))
                    continue
                pid2 = extract_page_id_from_url(link) or resolve_page_id(api_base, sess, link)
                if pid2 and pid2 not in visited_ids:
                    q.put((pid2, d_link + 1, d_child, page["id"], "link"))

    # Write CSV
    fieldnames = [
        "page_id", "depth_link", "depth_child", "via",
        "title", "url", "parent_id", "labels", "last_updated", "status", "text"
    ]
    with open(OUTPUT_CSV, "w", newline="", encoding="utf-8") as f:
        w = csv.DictWriter(f, fieldnames=fieldnames)
        w.writeheader()
        for row in rows:
            w.writerow(row)

    print(f"Wrote {len(rows)} rows to {OUTPUT_CSV}")


if __name__ == "__main__":
    main()
