"""
Shared utilities for metric processing and computation.

This module provides common functionality for metric handling, smoothing,
and best score computation used by both load_wandb_metrics.py and vis_metrics.py.
"""

from typing import Dict, List, Optional, Tuple
import numpy as np
import pandas as pd
from wandb_utils import MyRun


class Metric<PERSON>ey:
    """
    Represents a metric with its optimization direction.
    
    Args:
        key: The metric name/key
        opt: Optimization direction, either "max" or "min"
    """
    def __init__(self, key: str, opt: str = "max"):
        assert opt in {"max", "min"}, "opt must be 'max' or 'min'"
        self.key = key
        self.opt = opt

    def __repr__(self):
        return f"<PERSON><PERSON><PERSON><PERSON>(key='{self.key}', opt='{self.opt}')"


# Standard metric definitions used across both scripts
METRIC_KEYS: List[MetricKey] = [
    MetricKey("val/snippet_teacher_action_prediction_loss", "min"),
    <PERSON><PERSON><PERSON><PERSON>("val/f1score_weighted"),
    <PERSON><PERSON><PERSON><PERSON>("val/segment_traj_prediction_ade_loss", "min"),
    <PERSON><PERSON><PERSON><PERSON>("val/fbetascore_recall_higher_weighted"),
    <PERSON><PERSON><PERSON><PERSON>("val/fbetascore_precision_higher_weighted"),
    MetricKey("val/precision_weighted"),
    MetricKey("val/pred_accuracy_weighted"),
    MetricKey("val/recall_weighted"),
    MetricKey("val/segment_traj_prediction_mse_loss"),
    MetricKey("val/segment_traj_prediction_mse_loss_scaled"),
]

LOSS_KEY = MetricKey("val/snippet_teacher_action_prediction_loss", "min")


def smooth_metrics(values: pd.Series, span: int = 5) -> pd.Series:
    """
    Apply exponential weighted moving average smoothing to a pandas Series.
    
    Args:
        values: Series to smooth
        span: Smoothing span for EWMA
        
    Returns:
        Smoothed Series
    """
    values = values.dropna()
    if span <= 1:
        return values
    return values.ewm(span=span).mean()


def ewma_smooth(s: pd.Series, span: int) -> pd.Series:
    """
    Alias for smooth_metrics for backward compatibility.
    
    Args:
        s: Series to smooth
        span: Smoothing span for EWMA
        
    Returns:
        Smoothed Series
    """
    return smooth_metrics(s, span)


def parse_config_str(config_str: str) -> Dict[str, str]:
    """
    Parse configuration strings like "a=1,b=2,seed=3" into dictionary.
    
    Args:
        config_str: Configuration string to parse
        
    Returns:
        Dictionary with parsed key-value pairs (all values as strings)
    """
    out = {}
    if not isinstance(config_str, str):
        return out
    
    parts = [p.strip() for p in config_str.split(",") if p.strip()]
    for p in parts:
        if "=" in p:
            k, v = p.split("=", 1)
            out[k.strip()] = v.strip()
    return out


def infer_sweep_keys(runs: List[MyRun]) -> List[str]:
    """
    Infer sweep keys from run configurations.
    
    Prefers config_str if present, otherwise uses varying keys across runs.
    Excludes 'seed' and non-varying keys.
    
    Args:
        runs: List of MyRun objects
        
    Returns:
        List of inferred sweep key names
    """
    # Try to use config_str if available
    config_str_keys: List[str] = []
    for r in runs:
        cs = r.config.get("config_str")
        if isinstance(cs, str) and cs:
            config_str_keys.extend(parse_config_str(cs).keys())
    
    if config_str_keys:
        keys = sorted(set(config_str_keys))
    else:
        # Use varying keys across runs
        all_keys = sorted({k for r in runs for k in r.config.keys()})
        # Exclude W&B system keys
        bad = {"_wandb", "_timestamp", "_runtime", "_step", "seed"}
        keys = [k for k in all_keys if k not in bad]
        
        # Keep only varying keys
        varying = []
        for k in keys:
            vals = {str(r.config.get(k)) for r in runs}
            if len(vals) > 1:
                varying.append(k)
        keys = varying
    
    # Remove seed if present
    keys = [k for k in keys if k != "seed"]
    return keys


def compute_best_scores_valloss_based(
    histories: Dict[MyRun, pd.DataFrame], 
    metric_keys: List[MetricKey],
    loss_key: MetricKey = LOSS_KEY,
    smooth_span: int = 5
) -> Dict[MyRun, pd.DataFrame]:
    """
    Compute best scores based on validation loss early stopping.
    
    For each run, finds the index where the loss metric is optimal,
    then records all metric values at that index.
    
    Args:
        histories: Dictionary mapping runs to their history DataFrames
        metric_keys: List of metrics to compute best scores for
        loss_key: The loss metric to use for early stopping
        smooth_span: Smoothing span for metrics
        
    Returns:
        Dictionary mapping runs to their best scores DataFrames
    """
    metrics_keys = [k.key for k in metric_keys]
    best_scores = {}
    
    for run, df in histories.items():
        # Smooth the metrics
        value = df[metrics_keys].apply(lambda s: smooth_metrics(s, smooth_span))
        run.smoothed_metrics = value
        
        # Find the optimal index for the loss key
        if loss_key.opt == 'min':
            best_idx = value[loss_key.key].idxmin()
        else:
            best_idx = value[loss_key.key].idxmax()
        
        # Record metrics at that index
        best_val = value.loc[[best_idx]].copy()
        best_val.columns = [f"{col}/best_valloss" for col in best_val.columns]
        best_scores[run] = best_val
        best_scores[run]["best_valloss_idx"] = best_idx
        
        print(f"Run {run.name}: best_valloss_idx={best_idx}, scores={best_scores[run].iloc[0].to_dict()}")
    
    return best_scores


def compute_best_scores_max(
    histories: Dict[MyRun, pd.DataFrame], 
    metric_keys: List[MetricKey]
) -> Dict[MyRun, pd.DataFrame]:
    """
    Compute per-metric best scores (independent optimization for each metric).
    
    Args:
        histories: Dictionary mapping runs to their history DataFrames  
        metric_keys: List of metrics to compute best scores for
        
    Returns:
        Dictionary mapping runs to their best scores DataFrames
    """
    best_scores = {}
    
    def rename_series(s: pd.Series, suffix: str):
        """Helper to rename series index with suffix."""
        s.index = s.index.map(lambda c: f"{c}/{suffix}")
    
    max_keys = [k.key for k in metric_keys if k.opt == 'max']
    min_keys = [k.key for k in metric_keys if k.opt == 'min']
    
    for run, df in histories.items():
        # Use pre-computed smoothed metrics
        value = getattr(run, 'smoothed_metrics', df)
        
        # Compute best values and indices for max metrics
        best_val_max = value[max_keys].max() if max_keys else pd.Series(dtype=float)
        best_val_max_idx = value[max_keys].idxmax() if max_keys else pd.Series(dtype=float)
        
        # Compute best values and indices for min metrics  
        best_val_min = value[min_keys].min() if min_keys else pd.Series(dtype=float)
        best_val_min_idx = value[min_keys].idxmin() if min_keys else pd.Series(dtype=float)
        
        # Combine results
        best_val = pd.concat([best_val_max, best_val_min])
        best_val_idx = pd.concat([best_val_max_idx, best_val_min_idx])
        
        # Rename with suffixes
        rename_series(best_val, "best_max")
        rename_series(best_val_idx, "best_max_idx")
        
        # Combine into single row DataFrame
        best_val_combined = pd.concat([best_val, best_val_idx]).to_frame().T
        best_scores[run] = best_val_combined
    
    return best_scores


def make_combo_labels(df: pd.DataFrame, keys: List[str]) -> pd.Series:
    """
    Create combination labels by joining values of specified keys.

    Args:
        df: DataFrame containing the keys
        keys: List of column names to combine

    Returns:
        Series with combined labels
    """
    if not keys:
        return pd.Series(["all" for _ in range(len(df))], index=df.index)
    return df[keys].astype(str).agg(" | ".join, axis=1)


def build_tidy_histories(
    entity: str,
    project: str,
    runs: List[MyRun],
    metric_keys: List[str],
    smooth_span: int = 1,
    samples: int = 2000,
    downsample_every: int = 1,
    get_history_func=None
) -> Tuple[pd.DataFrame, str]:
    """
    Build a tidy DataFrame with columns:
    [run_id, run_name, step, metric_name, value, value_smooth, <config keys...>]

    Args:
        entity: W&B entity name
        project: W&B project name
        runs: List of MyRun objects
        metric_keys: List of metric names to include
        smooth_span: Smoothing span for EWMA
        samples: Number of samples to fetch per run
        downsample_every: Downsample factor for long histories
        get_history_func: Optional function to get run history (for dependency injection)

    Returns:
        Tuple of (tidy DataFrame, step column name)
    """
    from wandb_utils import get_run_history, detect_step_col

    if get_history_func is None:
        get_history_func = get_run_history

    frames = []
    chosen_step_col: Optional[str] = None

    for r in runs:
        df = get_history_func(entity, project, r.run_id, samples=samples)

        # Identify step column (use first run's choice globally)
        step_col = detect_step_col(df)
        if chosen_step_col is None:
            chosen_step_col = step_col or "index"

        # Keep only relevant columns
        cols_keep = [c for c in metric_keys if c in df.columns]
        if step_col and step_col not in cols_keep:
            cols_keep = [step_col] + cols_keep

        small = df[cols_keep].copy() if cols_keep else pd.DataFrame(index=df.index)

        # Add index as fallback step
        if step_col is None:
            small = small.reset_index().rename(columns={"index": "index"})
            step_series = small["index"].astype(float)
        else:
            step_series = pd.to_numeric(small[step_col], errors="coerce").astype(float)
            small = small.reset_index(drop=True)

        # Downsample if requested
        if downsample_every > 1 and len(small) > 0:
            small = small.iloc[::downsample_every].reset_index(drop=True)
            step_series = step_series.iloc[::downsample_every].reset_index(drop=True)

        # Melt to tidy format
        metric_cols = [c for c in small.columns if c != step_col and c != "index"]
        if not metric_cols:
            continue

        tidy = small.copy()
        tidy["__step__"] = step_series
        tidy = tidy.melt(
            id_vars=["__step__"],
            value_vars=metric_cols,
            var_name="metric_name",
            value_name="value"
        )

        # Apply smoothing per metric
        tidy.sort_values(["metric_name", "__step__"], inplace=True)
        tidy["value_smooth"] = tidy.groupby("metric_name", group_keys=False)["value"].apply(
            lambda s: smooth_metrics(s, smooth_span)
        )

        # Add run metadata and config
        for k, v in r.config.items():
            tidy[k] = str(v)
        tidy["run_id"] = r.run_id
        tidy["run_name"] = r.name

        frames.append(tidy)

    if not frames:
        return pd.DataFrame(), (chosen_step_col or "index")

    df_all = pd.concat(frames, ignore_index=True)
    df_all.rename(columns={"__step__": (chosen_step_col or "index")}, inplace=True)

    return df_all, (chosen_step_col or "index")


def compute_best_tables(
    tidy: pd.DataFrame,
    step_col: str,
    runs: List[MyRun],
    metrics: List[MetricKey],
    loss_key: MetricKey,
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Compute per-run table with best scores at validation loss index and per-metric best.

    Args:
        tidy: Tidy DataFrame with run histories
        step_col: Name of the step column
        runs: List of MyRun objects
        metrics: List of MetricKey objects
        loss_key: MetricKey for validation loss

    Returns:
        Tuple of (per_run table, groupable table)
    """
    if tidy.empty:
        return pd.DataFrame(), pd.DataFrame()

    # Prepare lookup structures
    opt_map = {m.key: m.opt for m in metrics}
    metric_list = [m.key for m in metrics]

    per_run_rows = []
    for r in runs:
        view = tidy[tidy["run_id"] == r.run_id]
        row = {"run_id": r.run_id, "run_name": r.name}

        # Copy config values
        for k, v in r.config.items():
            row[k] = str(v)

        # Compute best validation loss index
        if loss_key.key in metric_list and loss_key.key in view["metric_name"].unique():
            vloss = view[view["metric_name"] == loss_key.key][[step_col, "value_smooth"]].dropna()
            if not vloss.empty:
                if loss_key.opt == "min":
                    idx_row = vloss.iloc[vloss["value_smooth"].argmin()]
                else:
                    idx_row = vloss.iloc[vloss["value_smooth"].argmax()]
                best_idx = float(idx_row[step_col])
            else:
                best_idx = np.nan
        else:
            best_idx = np.nan

        row["best_valloss_idx"] = best_idx

        # For each metric, record value at best_valloss idx and per-metric best
        for mk in metric_list:
            mv = view[view["metric_name"] == mk][[step_col, "value_smooth"]].dropna()

            # Value at best validation loss index
            if np.isfinite(best_idx) and not mv.empty:
                # Find closest step to best_idx
                mv2 = mv.iloc[(mv[step_col] - best_idx).abs().argsort()[:1]]
                val_at_vloss = float(mv2["value_smooth"].values[0])
            else:
                val_at_vloss = np.nan
            row[f"{mk}/best_valloss"] = val_at_vloss

            # Per-metric best (independent optimization)
            if not mv.empty:
                if opt_map.get(mk, "max") == "max":
                    j = mv["value_smooth"].argmax()
                else:
                    j = mv["value_smooth"].argmin()
                best_val = float(mv.iloc[j]["value_smooth"]) if len(mv) else np.nan
                best_idx_m = float(mv.iloc[j][step_col]) if len(mv) else np.nan
            else:
                best_val, best_idx_m = np.nan, np.nan

            row[f"{mk}/best_max"] = best_val
            row[f"{mk}/best_max_idx"] = best_idx_m

        per_run_rows.append(row)

    per_run = pd.DataFrame(per_run_rows)
    return per_run, per_run.copy()
