import numpy
import pytorch_lightning as pl
import torch
from torch import nn
from torch.utils.data import DataLoader, TensorDataset


# Define a simple MLP model using PyTorch Lightning
class SimpleMLP(pl.LightningModule):
    def __init__(self, input_dim, hidden_dim, output_dim, embedding):
        super(SimpleMLP, self).__init__()
        self.embedding = embedding
        self.transition_model = nn.Sequential(
            nn.Linear(input_dim, input_dim),
        )

        self.emission_model = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim),
        )

    def forward(self, x):
        y_hat = self.emission_model(x)
        x_next = self.transition_model(x)
        y_next = self.emission_model(x_next)
        return y_next, y_hat

    def training_step(self, batch, batch_idx):
        x, y, y_next, t = batch
        # import IPython; IPython.embed()
        x_hat = self.embedding(t.long()).squeeze(1)
        y_next2, y_hat = self(x_hat)
        loss = (x - x_hat).abs().mean()
        x_next = self.transition_model(x)
        x_next_loss = (
            (x_next - x - 1).abs().mean()
        )  # can we do x_next - (self.embedding((t+1).long())).squeeze(1).detach()
        # import IPython; IPython.embed()
        loss += (y_hat - y).abs().mean()
        loss += (y_next2 - y_next).abs().mean()

        self.log(
            "x_next_loss",
            x_next_loss,
            on_step=True,
            on_epoch=True,
            prog_bar=True,
            logger=True,
        )
        self.log(
            "train_loss", loss, on_step=True, on_epoch=True, prog_bar=True, logger=True
        )
        return loss

    def configure_optimizers(self):
        return torch.optim.Adam(self.parameters(), lr=0.05)


# Create random dataset
input_dim = 1
hidden_dim = 5
output_dim = 3
num_samples = 100

coeff = 1e-4
X = torch.randn(num_samples, input_dim) * coeff
y = torch.randn(num_samples, output_dim) * coeff
yp1 = torch.randn(num_samples, output_dim) * coeff
rows = torch.arange(X.size(0))
cols = torch.arange(X.size(1))
grid_rows, grid_cols = torch.meshgrid(rows, cols, indexing="ij")
time_index = grid_rows[:, 0].unsqueeze(1).float()
# step_ = torch.randint(0, input_dim, (num_samples,)).unsqueeze(1).float()
# time_index = torch.meshgrid(X)
X[:, 0] += time_index[:, 0]
y[:, 0] += time_index[:, 0]
yp1[:, 0] += time_index[:, 0] + 1
emb = nn.Embedding(num_samples, input_dim)

dataset = TensorDataset(X, y, yp1, time_index)
sampler = torch.utils.data.RandomSampler(
    dataset, replacement=True, num_samples=10000000
)
dataloader = DataLoader(dataset, batch_size=10240, sampler=sampler, num_workers=16)
# Initialize model
model = SimpleMLP(input_dim, hidden_dim, output_dim, emb)

# Set up PyTorch Lightning Trainer
trainer = pl.Trainer(max_epochs=500, devices=1)


# Train the model
print(model.embedding(time_index[:10].long()))

trainer.fit(model, dataloader)
print(model.embedding(time_index[:10].long()))

import IPython

IPython.embed()
