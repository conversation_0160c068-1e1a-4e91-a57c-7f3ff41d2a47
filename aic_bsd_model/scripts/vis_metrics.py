# app.py — Parameter Sweep Explorer for W&B runs
#
# Features
# 1) Visualize **individual runs** for a selected metric (raw + smoothed)
# 2) Visualize **grouped runs** with mean ± std bands for any set of sweep keys
# 3) Compare **metrics mean/std** in a sortable table across parameter combos, with easy filtering
# 4) Refactored, readable utilities for loading, smoothing, summarizing
#
# Usage
#   $ pip install streamlit wandb pandas numpy plotly
#   $ streamlit run app.py
#
# Notes
# - Relies on your W&B credentials (WANDB_API_KEY env var or `wandb login`).
# - Compatible with your existing METRIC_KEYS & LOSS_KEY below. Adjust as needed.
# - Designed for large histories: supports downsampling & caching.

from __future__ import annotations
import os
import re
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional

import numpy as np
import pandas as pd
import streamlit as st
import plotly.graph_objects as go
import wandb

# Import shared utilities
from wandb_utils import MyRun, detect_step_col
from metrics_utils import (
    <PERSON><PERSON><PERSON><PERSON>, METRIC_KEYS, LOSS_KEY,
    smooth_metrics, ewma_smooth, parse_config_str, infer_sweep_keys,
    make_combo_labels, build_tidy_histories, compute_best_tables
)

# -----------------------------
# Config & constants
# -----------------------------

st.set_page_config(page_title="Param Sweep Explorer", layout="wide")

# Where to keep optional CSV exports if you choose to save
EXPORT_DIR = os.path.expanduser("~/wandb_sweep_exports")
os.makedirs(EXPORT_DIR, exist_ok=True)


# All helper functions are now imported from shared utilities


# -----------------------------
# W&B loading with caching
# -----------------------------

# Streamlit-cached versions of shared utilities
@st.cache_data(show_spinner=True)
def list_runs_cached(entity: str, project: str, group: str) -> List[MyRun]:
    """Streamlit-cached version of list_runs from wandb_utils."""
    from wandb_utils import list_runs
    return list_runs(entity, project, group)


@st.cache_data(show_spinner=False)
def get_run_history_cached(entity: str, project: str, run_id: str, samples: int = 2000) -> pd.DataFrame:
    """Streamlit-cached version of get_run_history from wandb_utils."""
    from wandb_utils import get_run_history
    return get_run_history(entity, project, run_id, samples=samples, use_cache=True)


# -----------------------------
# Tidy assembly & summaries
# -----------------------------

# Use shared build_tidy_histories with Streamlit caching wrapper
def build_tidy_histories_cached(
    entity: str,
    project: str,
    runs: List[MyRun],
    metric_keys: List[str],
    smooth_span: int = 1,
    samples: int = 2000,
    downsample_every: int = 1,
) -> Tuple[pd.DataFrame, str]:
    """Streamlit-cached wrapper for build_tidy_histories from metrics_utils."""
    return build_tidy_histories(
        entity, project, runs, metric_keys, smooth_span, samples, downsample_every,
        get_history_func=get_run_history_cached
    )


# compute_best_tables and make_combo_labels are now imported from metrics_utils


# -----------------------------
# UI — Sidebar controls
# -----------------------------

st.title(":bar_chart: Parameter Sweep Explorer (W&B)")

with st.sidebar:
    st.header("Data source")
    entity = st.text_input("Entity", value="tri")
    project = st.text_input("Project", value="aic_base_uncoached_model")
    group = st.text_input("Group", value="nonadaptive-metrics_sweep-bz")
    samples = st.number_input("History samples (W&B)", 200, 200000, value=5000, step=500)
    smooth_span = st.slider("Smoothing span (EWMA)", 1, 101, value=5, step=2)
    downsample_every = st.number_input("Downsample every N points", 1, 1000, value=1)
    load_btn = st.button("Load / Refresh runs", type="primary")

# Load runs on first render as well
if load_btn or "_loaded_once" not in st.session_state:
    runs: List[MyRun] = list_runs_cached(entity, project, group)
    st.session_state["runs"] = runs
    st.session_state["_loaded_once"] = True
else:
    runs = st.session_state.get("runs", [])

if not runs:
    st.info("No runs found for the given entity/project/group.")
    st.stop()

st.caption(f"Loaded **{len(runs)}** runs from group '{group}'.")

# Infer sweep keys to power groupings
sweep_keys = infer_sweep_keys(runs)

# Available metrics = intersection of METRIC_KEYS and what's actually logged
preferred_metric_keys = [m.key for m in METRIC_KEYS]

# Build tidy histories for just the metrics we care about (faster). If none present, we'll fall back later.
with st.spinner("Building histories (tidy) ..."):
    tidy, step_col = build_tidy_histories_cached(
        entity, project, runs, metric_keys=preferred_metric_keys,
        smooth_span=smooth_span, samples=samples, downsample_every=downsample_every,
    )

# If none of the preferred metrics exist, pull a metric sample to offer a list
if tidy.empty:
    # Load first run's history to detect metric names
    df0 = get_run_history_cached(entity, project, runs[0].run_id, samples=samples)
    # Heuristic: metrics that look like 'val/' or 'train/'
    guess_metrics = [c for c in df0.columns if isinstance(c, str) and ("/" in c or c.startswith("val") or c.startswith("train"))]
    with st.spinner("Rebuilding histories with detected metrics ..."):
        tidy, step_col = build_tidy_histories_cached(
            entity, project, runs, metric_keys=guess_metrics,
            smooth_span=smooth_span, samples=samples, downsample_every=downsample_every,
        )

if tidy.empty:
    st.warning("No usable metrics found in these runs.")
    st.stop()

# Metric selection based on actually present metrics
present_metrics = sorted(tidy["metric_name"].unique())

# Compute per-run best tables (used for both plot annotations & summary tables)
per_run_table, groupable = compute_best_tables(
    tidy, step_col=step_col, runs=runs, metrics=[MetricKey(m) for m in present_metrics], loss_key=LOSS_KEY
)

# -----------------------------
# Tabs: Runs | Groups | Tables
# -----------------------------

tab_runs, tab_groups, tab_tables = st.tabs(["Individual runs", "Grouped bands", "Summary tables"])

# --------------
# Tab 1: Individual runs
# --------------
with tab_runs:
    st.subheader("Individual run viewer")
    c1, c2, c3 = st.columns([2,2,1])
    with c1:
        metric = st.selectbox("Metric", present_metrics, index=(present_metrics.index(LOSS_KEY.key) if LOSS_KEY.key in present_metrics else 0))
    with c2:
        show_raw = st.checkbox("Show raw series", value=True)
        show_smooth = st.checkbox("Show smoothed series", value=True)
    with c3:
        annotate_best = st.checkbox("Annotate best indices", value=True)

    # Filter runs via multiselect (nice for quick on/off toggles)
    run_options = [(r.name, r.run_id) for r in runs]
    default_sel = [run_options[0][1]] if run_options else []
    selected_run_ids = st.multiselect("Runs to display", options=[rid for _, rid in run_options], format_func=lambda rid: next((n for n,i in run_options if i==rid), rid), default=[rid for _, rid in run_options])

    fig = go.Figure()
    # Build traces per run
    for rid in selected_run_ids:
        rname = next((r.name for r in runs if r.run_id == rid), rid)
        view = tidy[(tidy["run_id"] == rid) & (tidy["metric_name"] == metric)]
        if view.empty:
            continue
        xs = view[step_col].astype(float).values
        if show_raw:
            fig.add_trace(go.Scatter(x=xs, y=view["value"].values, mode="lines", name=f"{rname} (raw)", hovertemplate="step=%{x}<br>val=%{y:.4f}<extra>raw</extra>", opacity=0.4))
        if show_smooth:
            fig.add_trace(go.Scatter(x=xs, y=view["value_smooth"].values, mode="lines", name=f"{rname} (sm)", hovertemplate="step=%{x}<br>val=%{y:.4f}<extra>smooth</extra>"))
        if annotate_best:
            # From per_run_table
            row = per_run_table[per_run_table["run_id"] == rid]
            if not row.empty:
                best_idx_global = row["best_valloss_idx"].values[0]
                best_at_global = row[f"{metric}/best_valloss"].values[0] if f"{metric}/best_valloss" in row.columns else np.nan
                if np.isfinite(best_idx_global):
                    fig.add_vline(x=float(best_idx_global), line=dict(dash="dash"), annotation_text=f"valloss idx", annotation_position="top left", opacity=0.4)
                # per metric best (min/max depending on metric opt)
                best_idx_m = row.get(f"{metric}/best_max_idx")
                best_val_m = row.get(f"{metric}/best_max")
                if best_idx_m is not None and not pd.isna(best_idx_m.values[0]):
                    fig.add_vline(x=float(best_idx_m.values[0]), line=dict(dash="dot"), annotation_text="metric best", annotation_position="top right", opacity=0.4)

    fig.update_layout(
        xaxis_title=step_col,
        yaxis_title=metric,
        hovermode="x unified",
        legend_title="Run",
    )
    st.plotly_chart(fig, use_container_width=True)


# --------------
# Tab 2: Grouped bands (mean ± std)
# --------------
with tab_groups:
    st.subheader("Grouped bands (mean ± std)")
    # pick which keys define a "combo"
    group_by = st.multiselect("Group by sweep keys", options=sweep_keys, default=sweep_keys[: min(2, len(sweep_keys))])
    if not group_by:
        st.info("Select one or more sweep keys to form groups.")
    else:
        tidy = tidy.copy()
        tidy["combo"] = make_combo_labels(tidy, group_by)
        combos = sorted(tidy["combo"].unique())
        selected_combos = st.multiselect("Combos to include", options=combos, default=combos)
        metric_g = st.selectbox("Metric", present_metrics, index=(present_metrics.index(LOSS_KEY.key) if LOSS_KEY.key in present_metrics else 0), key="metric_group")

        dff = tidy[(tidy["metric_name"] == metric_g) & (tidy["combo"].isin(selected_combos))]
        if dff.empty:
            st.warning("No data for these selections.")
        else:
            # Aggregate: mean & std per (combo, step)
            agg = (dff.groupby(["combo", step_col])["value_smooth"].agg(["mean", "std"]).reset_index())
            fig2 = go.Figure()
            for combo, g in agg.groupby("combo"):
                xs = g[step_col].astype(float).values
                fig2.add_trace(go.Scatter(x=xs, y=g["mean"].values, mode="lines", name=f"{combo} • mean", line=dict(width=4, dash="dash")))
                # band
                fig2.add_trace(go.Scatter(
                    x=np.concatenate([xs, xs[::-1]]),
                    y=np.concatenate([g["mean"].values + g["std"].values, (g["mean"].values - g["std"].values)[::-1]]),
                    fill="toself", mode="lines", line=dict(width=0), opacity=0.2, name=f"{combo} ±1σ", showlegend=False
                ))
            fig2.update_layout(xaxis_title=step_col, yaxis_title=metric_g, hovermode="x unified", legend_title="Combo")
            st.plotly_chart(fig2, use_container_width=True)


# --------------
# Tab 3: Summary tables (mean/std across combos)
# --------------
with tab_tables:
    st.subheader("Summary tables: mean/std across combos")
    st.caption("Each run contributes its per-metric **best** (by that metric) and its value **at best validation loss index**. Then we compute mean/std across the selected combos.")

    group_by_tbl = st.multiselect("Group by sweep keys", options=sweep_keys, default=sweep_keys)
    if not group_by_tbl:
        st.info("Select at least one key for grouping.")
    else:
        # Build a per-run table carrying only useful columns
        kept_cols = ["run_id", "run_name"] + group_by_tbl
        # Auto-add group_by columns from config if missing (string typed already in per_run_table)
        for k in group_by_tbl:
            if k not in per_run_table.columns:
                per_run_table[k] = np.nan
        metric_cols_vloss = [f"{m}/best_valloss" for m in present_metrics if f"{m}/best_valloss" in per_run_table.columns]
        metric_cols_best = [f"{m}/best_max" for m in present_metrics if f"{m}/best_max" in per_run_table.columns]
        z = per_run_table[kept_cols + metric_cols_vloss + metric_cols_best]

        # Allow combo filtering
        z = z.copy()
        z["combo"] = make_combo_labels(z, group_by_tbl)
        combos_tbl = sorted(z["combo"].unique())
        selected_combos_tbl = st.multiselect("Combos to include", options=combos_tbl, default=combos_tbl)
        z = z[z["combo"].isin(selected_combos_tbl)]

        # Aggregate
        mean_tbl = z.groupby(group_by_tbl)[metric_cols_vloss + metric_cols_best].mean().reset_index()
        std_tbl = z.groupby(group_by_tbl)[metric_cols_vloss + metric_cols_best].std().reset_index()

        # Display with toggles
        show_which = st.radio("Values to show", ["best_valloss", "best_max", "both"], horizontal=True)
        cols_to_show: List[str] = []
        if show_which in ("best_valloss", "both"):
            cols_to_show += metric_cols_vloss
        if show_which in ("best_max", "both"):
            cols_to_show += metric_cols_best

        # Interleave mean/std columns for readability
        def interleave_mean_std(means: pd.DataFrame, stds: pd.DataFrame, cols: List[str]) -> pd.DataFrame:
            base_cols = group_by_tbl
            out = means[base_cols].copy()
            for c in cols:
                out[c+" (mean)"] = means[c]
                out[c+" (std)"] = stds[c]
            return out

        out_tbl = interleave_mean_std(mean_tbl, std_tbl, cols_to_show)
        st.dataframe(out_tbl, use_container_width=True)

        cexp1, cexp2 = st.columns(2)
        with cexp1:
            if st.button("Download CSV (mean+std)"):
                path = os.path.join(EXPORT_DIR, "summary_mean_std.csv")
                out_tbl.to_csv(path, index=False)
                st.success(f"Saved to {path}")
        with cexp2:
            if st.button("Download per-run CSV"):
                path2 = os.path.join(EXPORT_DIR, "per_run_table.csv")
                per_run_table.to_csv(path2, index=False)
                st.success(f"Saved to {path2}")


# -----------------------------
# Footer: tiny help
# -----------------------------
with st.expander("Tips & Notes"):
    st.markdown(
        """
        - **Toggle runs on/off** in the legend (click). Shift+click to isolate a single run.
        - **Downsample** long histories from the sidebar to keep the UI snappy.
        - If your metrics differ from the defaults, the app auto-detects logged metrics.
        - Grouping labels use selected sweep keys joined like: `key1=..., key2=...`.
        """
    )
