import argparse
import os

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import wandb

# Import shared utilities
from wandb_utils import MyRun, load_group_history, setup_cache_dir
from metrics_utils import (
    <PERSON><PERSON><PERSON>ey, METRIC_KEYS, LOSS_KEY,
    smooth_metrics, compute_best_scores_valloss_based, compute_best_scores_max,
    parse_config_str, infer_sweep_keys
)

# Set up cache directory
cache_dir = setup_cache_dir()


# All metric-related functionality is now imported from metrics_utils

def test_plot(run_idx, histories, metrics_df, metric_keys: list[str]):
    """
    Generate test plots for a specific run showing metrics over time.

    Args:
        run_idx: Index of run to plot
        histories: Dictionary mapping runs to their history DataFrames
        metrics_df: DataFrame with computed best metrics
        metric_keys: List of metric keys to plot
    """
    run = list(histories.keys())[run_idx]
    run_id = run.run_id

    # Get history data
    df = histories[run]

    # Get smoothed metrics (computed by compute_best_scores_valloss_based)
    df_smoothed = getattr(run, 'smoothed_metrics', df)

    # Get metrics for this run
    metrics = metrics_df.loc[metrics_df["run_id"] == run_id]

    # Setup plotting
    x = np.arange(len(df))
    x_label = "steps"

    # Create subplots
    n = len(metric_keys)
    fig, axes = plt.subplots(nrows=n, ncols=1, figsize=(8, 3.2 * n), sharex=True)
    if n == 1:
        axes = [axes]

    # Create plots directory if it doesn't exist
    os.makedirs("metrics_plots", exist_ok=True)

    for ax, key in zip(axes, metric_keys):
        plotted_any = False

        # Plot training series if available
        if key in df.columns:
            # Smoothed series
            clean = df_smoothed[key].dropna()
            ax.plot(clean.index, clean.values, label=key+' (smoothed)', linewidth=1.8)

            # Raw series
            clean = df[key].dropna()
            ax.plot(clean.index, clean.values, label=key, linewidth=1.0, alpha=0.4)
            plotted_any = True

            # Plot best metrics if available
            if f"{key}/best_max" in metrics.columns:
                best_max = metrics[f"{key}/best_max"].values[0]
                best_max_idx = metrics[f"{key}/best_max_idx"].values[0]
                ax.axvline(best_max_idx, color="red", linestyle="--",
                          label=f"best_max={best_max:.3f}", alpha=0.4)

            if f"{key}/best_valloss" in metrics.columns:
                best_val = metrics[f"{key}/best_valloss"].values[0]
                best_val_idx = metrics[f"best_valloss_idx"].values[0]
                ax.axvline(best_val_idx, color="blue", linestyle="--",
                          label=f"best_valloss={best_val:.3f}", alpha=0.4)

        ax.set_ylabel(key)
        ax.grid(True, alpha=0.3)
        if plotted_any:
            ax.legend(loc="best", fontsize=9)
        else:
            ax.text(0.5, 0.5, f"No data for '{key}'", ha="center", va="center",
                   transform=ax.transAxes, alpha=0.7)
            ax.set_ylim(0, 1)  # harmless default

    axes[-1].set_xlabel(x_label)
    fig.suptitle(f"Run: {run.name}", y=0.98)
    fig.tight_layout()
    plt.show()
    fig.savefig(f"metrics_plots/{run.name}.png")
    return fig, axes



def load_compute_metrics(group,project="aic_base_uncoached_model", entity="tri"):

    # Load histories using shared utility
    histories = load_group_history(entity, project, group, cache_dir=cache_dir)

    for run_id, df in histories.items():
        print(f"Loaded history for run {run_id}: {len(df)} rows")

    metrics_keys = [k.key for k in METRIC_KEYS]

    # Smooth metrics and compute best scores using shared utilities
    max_loss_scores = compute_best_scores_valloss_based(histories, METRIC_KEYS)
    max_scores = compute_best_scores_max(histories, METRIC_KEYS)

    # Build combined results DataFrame
    best_scores_data = []
    for run in histories.keys():
        max_score = max_scores[run].reset_index().set_index("index")
        max_loss_score = max_loss_scores[run].reset_index()
        max_loss_score.pop("index")
        score = pd.merge(max_score, max_loss_score, left_index=True, right_index=True)
        score_dict = score.squeeze().to_dict()
        row = {"run": run.name, "run_id": run.run_id, **run.config, **score_dict}
        best_scores_data.append(row)

    df = pd.DataFrame(best_scores_data)

    # Infer sweep keys using shared utility
    runs_list = list(histories.keys())
    sweep_keys = set(infer_sweep_keys(runs_list))

    # Remove non-varying columns
    for k in list(df.keys()):
        try:
            if df[k].nunique() == 1:
                df.pop(k)
                sweep_keys.discard(k)
        except:
            pass

    print("sweep_keys", sweep_keys)
    df.to_csv("df.csv", index=True)

    # Generate grouped statistics
    sweep_keys_list = list(sweep_keys)
    group_df = df.groupby(sweep_keys_list)[[k+'/best_valloss' for k in metrics_keys]]
    group_df_valloss_mean = group_df.mean()
    group_df_valloss_mean.to_csv("group_df_valloss_mean.csv", index=True)
    group_df_valloss_std = group_df.std()
    group_df_valloss_std.to_csv("group_df_valloss_std.csv", index=True)

    group_df = df.groupby(sweep_keys_list)[[k + '/best_max' for k in metrics_keys]]
    group_df_max_mean = group_df.mean()
    group_df_max_mean.to_csv("group_df_max_mean.csv", index=True)
    group_df_max_std = group_df.std()
    group_df_max_std.to_csv("group_df_max_std.csv", index=True)

    # Generate test plots
    test_plot(0, histories, df, metrics_keys[:6])
    test_plot(1, histories, df, metrics_keys[:6])
    test_plot(2, histories, df, metrics_keys[:6])


if __name__ == "__main__":
    main()
