"""
Shared utilities for W&B API interactions and data structures.

This module provides common functionality for loading W&B runs and histories,
used by both load_wandb_metrics.py and vis_metrics.py.
"""

import os
from dataclasses import dataclass
from typing import Dict, List, Optional, Union
import pandas as pd
import wandb
from cache_decorator import Cache

# Default cache directory
DEFAULT_CACHE_DIR = os.path.expanduser("~/cache/wandb_histories/")


class MyRun:
    """
    Unified representation of a W&B run with essential metadata.
    
    This class provides a consistent interface for both scripts,
    combining the best features from both implementations.
    """
    def __init__(self, run_id: str, name: str, config: dict, state: str):
        self.run_id = run_id
        self.name = name
        self.config = config
        self.state = state

    @staticmethod
    def from_wandb(run: "wandb.apis.public.Run") -> "MyRun":
        """Create MyRun instance from W&B API run object."""
        return MyRun(
            run_id=run.id,
            name=run.name or run.id,
            config=dict(run.config or {}),
            state=run.state
        )

    @staticmethod
    def from_legacy(run: "wandb.apis.public.Run") -> "MyRun":
        """Create MyRun instance using legacy constructor pattern."""
        return MyRun(
            run_id=run.id,
            name=run.name,
            config=run.config,
            state=run.state
        )


def setup_cache_dir(cache_dir: Optional[str] = None) -> str:
    """Setup and return cache directory path."""
    if cache_dir is None:
        cache_dir = DEFAULT_CACHE_DIR
    os.makedirs(cache_dir, exist_ok=True)
    return cache_dir


@Cache(
    cache_path="{cache_dir}/{function_name}/{_hash}.pkl.gz",
    cache_dir=DEFAULT_CACHE_DIR
)
def list_runs(entity: str, project: str, group: str, cache_dir: Optional[str] = None) -> List[MyRun]:
    """
    Return a list of MyRun objects for all runs in the specified group.
    Results are cached on disk.
    
    Args:
        entity: W&B entity name
        project: W&B project name  
        group: W&B group name to filter runs
        cache_dir: Optional cache directory override
        
    Returns:
        List of MyRun objects
    """
    if cache_dir:
        setup_cache_dir(cache_dir)
        
    api = wandb.Api()
    path = f"{entity}/{project}"
    runs_api = api.runs(path, filters={"group": group})
    runs = [run for run in runs_api]
    return [MyRun.from_wandb(r) for r in runs]


@Cache(
    cache_path="{cache_dir}/{function_name}/{_hash}.pkl.gz",
    enable_cache_arg_name="use_cache",
    cache_dir=DEFAULT_CACHE_DIR,
)
def get_run_history(
    entity: str, 
    project: str, 
    run_id: str, 
    samples: int = 1000, 
    use_cache: bool = True,
    cache_dir: Optional[str] = None
) -> pd.DataFrame:
    """
    Fetch the full history DataFrame for a single run.
    
    Args:
        entity: W&B entity name
        project: W&B project name
        run_id: W&B run ID
        samples: Number of samples to fetch (must be larger than actual log count to avoid subsampling)
        use_cache: Whether to use caching
        cache_dir: Optional cache directory override
        
    Returns:
        DataFrame with run history
    """
    if cache_dir:
        setup_cache_dir(cache_dir)
        
    api = wandb.Api()
    print(f"Loading history for run: {run_id}")
    run = api.run(f"{entity}/{project}/{run_id}")
    df = run.history(samples)
    
    # Enforce numeric dtypes where possible to avoid plotting issues
    df = df.apply(pd.to_numeric, errors="ignore")
    return df


def load_group_history(
    entity: str, 
    project: str, 
    group: str,
    cache_dir: Optional[str] = None
) -> Dict[MyRun, pd.DataFrame]:
    """
    Load history for every run in the given group.
    Uses cached run list and run history where available.
    
    Args:
        entity: W&B entity name
        project: W&B project name
        group: W&B group name
        cache_dir: Optional cache directory override
        
    Returns:
        Dictionary mapping MyRun objects to their history DataFrames
    """
    runs = list_runs(entity, project, group, cache_dir=cache_dir)
    histories = {}
    
    for run in runs:
        # Don't cache histories for runs that are still running
        still_running = run.state == "running"
        histories[run] = get_run_history(
            entity, 
            project, 
            run.run_id, 
            use_cache=not still_running,
            cache_dir=cache_dir
        )
    
    return histories


def detect_step_col(df: pd.DataFrame) -> Optional[str]:
    """
    Heuristically detect a step column in the DataFrame.
    
    Args:
        df: DataFrame to analyze
        
    Returns:
        Name of detected step column, or None if not found
    """
    candidates = [
        "_step", "global_step", "step", "steps", "epoch", "iter", "iteration",
    ]
    for c in candidates:
        if c in df.columns:
            return c
    return None
