#!/bin/bash
#SBATCH --job-name=__JOB_NAME
#SBATCH --nodes=__NODE_COUNT                # node count
#SBATCH --ntasks=__TASK_COUNT               # total number of tasks across all nodes
#SBATCH --cpus-per-task=__CPUS_PER_TASK     # cpu-cores per task (>1 if multi-threaded tasks)
##SBATCH --mem=__MEM                # total memory per node (4 GB per cpu-core is default)
#SBATCH --gres=__GRES               # gpus are configured with 100 mps per gpu, so 25 means 25% of one gpu
##SBATCH --time=__TIME              # limit total run time limit (HH:MM:SS), (here for debugging)
#SBATCH --output=slurm_output/%j
##SBATCH --partition aic
__OTHER_SBATCH_ARGS
__OTHER_RUN_SBATCH_ARGS

#. /opt/conda/etc/profile.d/conda.sh
. ~/micromamba/etc/profile.d/micromamba.sh
set -ex

micromamba activate __CONDA_ENV  # environment build in container image
echo SLURM_NODEID=$SLURM_NODEID

echo SLURM_STEP_GPUS:$SLURM_STEP_GPUS , SLURM_JOB_GPUS:$SLURM_JOB_GPUS
echo SLURMD_NODENAME=$SLURMD_NODENAME
echo CUDA_VISIBLE_DEVICES=$CUDA_VISIBLE_DEVICES
echo CUDA_DEVICE_ORDER=$CUDA_DEVICE_ORDER
echo CUDA_MPS_ACTIVE_THREAD_PERCENTAGE=$CUDA_MPS_ACTIVE_THREAD_PERCENTAGE
python -c "import torch; print(f'{torch.cuda.is_available()=}')"
python -c "import torch; print(f'{torch.cuda.device_count()=}')"


# $WANDB_RUN_GROUP override
export WANDB_RUN_GROUP=__WANDB_RUN_GROUP

if [ -z "$WANDB_RUN_GROUP" ]
then
  # Only set WANDB_RUN_GROUP if it is empty.
  RUN_DATE=$(date +%m-%d_%H-%M)
  WANDB_RUN_GROUP=${RUN_GROUP_NAME}-${RUN_DATE}
fi
echo WANDB_RUN_GROUP=$WANDB_RUN_GROUP

export RUN_NAME=__WANDB_RUN_NAME
#WANDB_CONSOLE=off
WANDB_RUN_GROUP=${WANDB_RUN_GROUP} __PYTHON_COMMAND

RETURN=$?
if [ $RETURN -eq 0 ];
then
  echo "The command was executed successfully"
  exit 0
else
  echo "The command was NOT executed successfully and returned the code $RETURN"
  exit $RETURN
fi
