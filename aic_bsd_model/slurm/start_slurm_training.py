import argparse
import copy
import itertools
import os
import subprocess

from slurm_util import call_slurm, make_slurm_scripts


def str2bool(v):
    if isinstance(v, bool):
        return v
    if v.lower() in ("yes", "true", "t", "y", "1"):
        return True
    elif v.lower() in ("no", "false", "f", "n", "0"):
        return False
    else:
        raise argparse.ArgumentTypeError("Boolean value expected.")


BASE_PARAM = """CUDA_VISIBLE_DEVICES=0 python -u aic_bsd_model/training/train_skill_encoder.py  """


DEFAULT_ARTIFACT_DIR = "~/aic_bsd/artifacts"


def start_training_seeds(
    name,
    param: str,
    additional_args_dict,
    seed_range=range(5),
    wandb_group="WB",
):
    group = name

    cmds = []

    # create artifact folder name according wb group and log name for better organization
    full_artifact_folder = os.path.join(DEFAULT_ARTIFACT_DIR, wandb_group, name)
    param = param.replace("ARTIFACTS_FOLDER", full_artifact_folder)
    orig_param = param

    additional_args = " ".join([f"{k} {v}" for k, v in additional_args_dict.items()])

    for i in seed_range:
        name2 = name + f"-s{i}"
        p = param + f" --run_description {name2} {additional_args} "  # --seed {i}"

        d = {"name": name2, "cmd": p, "group": group}
        cmds.append(d)
    return cmds


def add_cmd(cmds, result):
    for i in range(len(result)):
        result[i].append(cmds[i])


def generate_permutation(in_params: list):
    result = []

    if in_params:
        if len(in_params) == 1:
            result = in_params[0]
        else:
            for i in range(len(in_params[0])):
                past_res = generate_permutation(in_params[1:])
                for p in past_res:
                    result.append(p + in_params[0][i])
    return result


def make_train_cmds(args):
    wandb_group = args["wandb_group"]

    param_base = BASE_PARAM

    param_base = param_base.strip()

    cmds = []
    b_cmds = []
    seeds = range(1, 2)
    # seeds = [4]
    results = [[] for _ in list(seeds)]

    run_description = args["run_description"]

    name = run_description

    for pred_coef in [1]:
        arguments = {
            # "--arg": 10,
        }

        cmds2 = start_training_seeds(
            name,
            param_base,
            arguments,
            seed_range=seeds,
            wandb_group=wandb_group,
        )
        b_cmds.append(cmds2)

    cmds = list(itertools.chain(*b_cmds))
    return cmds


def parse_arguments():
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument(
        "--dry-run",
        type=str2bool,
        help="If True, only write the slurm batch files, won't start slurm runs.",
        default=False,
    )
    parser.add_argument(
        "--run-description",
        type=str,
        default="test",
        help="The run name for the training",
    )
    parser.add_argument(
        "--wandb-group",
        type=str,
        help="The name of this group of runs on Wandb.",
        default="",
    )
    parser.add_argument(
        "--use-ec2",
        action="store_true",
        help="If this run is on ec2 or not.",
        default=False,
    )
    parser.add_argument(
        "--nice",
        type=int,
        default=100,
        help="The nice value for the job. Lower is higher priority.",
    )
    result = parser.parse_args()
    return vars(result)


def main():
    args = parse_arguments()
    wandb_group = args["wandb_group"]
    script_params = {
        "__NODE_COUNT": "1",
        "__TASK_COUNT": "1",
        "__CPUS_PER_TASK": "10",  # 6 runs per machine
        # "__CPUS_PER_TASK": "2",  # used for caching waymo sequence data
        "__MEM": "51GB",  # GB
        "__GRES": "mps:50",
        "__TIME": "172:00:00",
        "__OTHER_SBATCH_ARGS": "#SBATCH --partition h1,h2,h3",
        "__CONDA_ENV": "aic_bsd",
    }
    use_ec2 = args["use_ec2"]

    script_params["__OTHER_SBATCH_ARGS"] += f"\n#SBATCH --nice={args['nice']}"
    cmds = make_train_cmds(args)
    cmd_names = [cmd["name"] for cmd in cmds]
    for cmd in cmds:
        pass
        # script_params["__GRES"] = "mps:50"

    scripts = make_slurm_scripts(cmds, script_params, wandb_group)
    if args["dry_run"]:
        for name in cmd_names:
            print(name)
    else:
        call_slurm(scripts, cmd_names)


if __name__ == "__main__":
    main()
