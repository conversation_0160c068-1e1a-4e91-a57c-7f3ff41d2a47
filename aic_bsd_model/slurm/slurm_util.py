import argparse
import itertools
import os
import subprocess


def make_slurm_scripts(cmds, script_params, wandb_group_orig):
    with open("aic_bsd_model/slurm/slurm_training_job_template.sh") as f:
        orig_script = f.read()
    output_dir = "slurm_tmp/"
    os.makedirs(output_dir, exist_ok=True)
    script = orig_script
    for key, val in script_params.items():
        script = script.replace(key, val)

    scripts_path = []
    for i, cmd in enumerate(cmds):
        if not wandb_group_orig:
            wandb_group = cmd["group"]
        this_script = script.replace("__JOB_NAME", cmd["name"])
        this_script = this_script.replace("__WANDB_RUN_NAME", cmd["name"])
        this_script = this_script.replace("__WANDB_RUN_GROUP", wandb_group)
        this_script = this_script.replace("__PYTHON_COMMAND", cmd["cmd"])
        this_script = this_script.replace(
            "__OTHER_RUN_SBATCH_ARGS", cmd.get("other_run_batch_args", "")
        )
        script_path = output_dir + f"{i}.sh"
        with open(script_path, "w") as f:
            f.write(this_script)
        scripts_path.append(script_path)
    return scripts_path


def call_slurm(slurm_scripts, cmd_names=None):
    for i, script in enumerate(slurm_scripts):
        output = subprocess.check_output(["sbatch", script])
        task_id = int(output[19:])
        if cmd_names:
            print(f"name: {cmd_names[i]}, \t\ttask_id: {task_id}")
        else:
            print(f"task_id: {task_id}")
