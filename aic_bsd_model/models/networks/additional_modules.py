import torch
import torch.nn as nn
import torch.nn.functional as F

# from aic_bsd_model.models.model_utils import convert_str_list_to_ints


def convert_str_list_to_ints(str_list):
    # -------- 1) map each unique string to an integer --------------
    id_to_int = {}  # simple dictionary encoder
    int_labels = []
    for s in str_list:
        if s not in id_to_int:
            id_to_int[s] = len(id_to_int)
        int_labels.append(id_to_int[s])
    return id_to_int, int_labels


class MultiPosInfoNCELoss(nn.Module):
    """
    Multi-positive / multi-label InfoNCE with learnable projections.

    z1 : (B, D1)   — anchors  (e.g. lap-level latent)
    p  : (B, D2)   — paired vectors (e.g. penultimate local latent)
    pos_mask : (B, B) bool  —  pos_mask[i,j] = True  iff  j is *any* positive for i
                              (exclude i==j if you don’t want self-positives)

    Returns
    -------
    loss   — scalar to *minimise*
    mi_lb  — detached scalar, variational lower-bound on  I(z1; p)
    """

    def __init__(
        self,
        dim_z1: int,
        dim_p: int,
        proj_dim: int = 128,
        loss_coeff=1.0,
        temperature: float = 0.1,
    ):
        super().__init__()
        self.tau = temperature

        def head(in_dim):
            return nn.Sequential(
                nn.Linear(in_dim, proj_dim),
                nn.ReLU(inplace=True),
                nn.Linear(proj_dim, proj_dim),
            )

        self.proj_z1 = head(dim_z1)
        self.proj_p = head(dim_p)
        self.infonce_loss_coeff = loss_coeff

    def forward(self, batch):
        assert "side_channel_lap_representation" in batch
        assert "cf_teacher_action_enc" in batch
        _, int_labels = convert_str_list_to_ints(batch["snippet_lap_uid"])

        int_labels = torch.tensor(int_labels, device=batch["device"])
        labels = int_labels.unsqueeze(0) == int_labels.unsqueeze(1)
        labels_no_self = labels.clone()  # (B, B)

        labels_no_self.fill_diagonal_(False)
        # 3) detect rows with *no* off-diagonal positives
        no_pos = labels_no_self.sum(dim=1) == 0

        # 4) final mask: keep off-diagonal positives; if none exist, fall back to self
        # this ensure that if there are no multiple labels bper sample in the batch then the diagonal will be active.
        pos_mask = labels_no_self | torch.diag(no_pos)

        side_channel_lap_representation = batch[
            "side_channel_lap_representation"
        ]  # (B, H)
        cf_action_pred_enc = batch["cf_teacher_action_enc"]  # (B, H')

        device = side_channel_lap_representation.device
        side_channel_lap_representation = F.normalize(
            side_channel_lap_representation, dim=-1
        )
        B = side_channel_lap_representation.size(0)
        if side_channel_lap_representation.shape[0] != cf_action_pred_enc.shape[0]:
            raise ValueError("Batch sizes differ")

        # project & L2-normalise
        z = F.normalize(
            self.proj_z1(side_channel_lap_representation), p=2, dim=-1
        )  # (B, P)
        h = F.normalize(
            self.proj_p(cf_action_pred_enc), p=2, dim=-1
        )  # (B, num_snippets=1, P),
        h = h[:, 0]  # (B, P)

        logits = z @ h.T / self.tau  # (B, B)
        logits = logits - logits.max(dim=1, keepdim=True)[0]
        log_prob = logits - torch.logsumexp(logits, dim=1, keepdim=True)

        # average log-probability over *all* positives per anchor
        pos_log_prob = (pos_mask * log_prob).sum(1) / pos_mask.sum(1).clamp(min=1)
        loss = -pos_log_prob.mean()

        # mutual-information lower bound   log(#neg) − loss
        n_neg = (~pos_mask).sum(1).float()  # (N-1?)
        mi_lb = (torch.log(n_neg).mean() - loss).detach()
        infonce_loss_scaled = loss * self.infonce_loss_coeff
        outputs = {
            "infonce_loss": loss,
            "infonce_loss_scaled": infonce_loss_scaled,
            "mi_lb": mi_lb,
        }
        batch.update(outputs)

        return loss, mi_lb
