import math

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from matplotlib import pyplot as plt

from aic_bsd_model.models.networks.basic_network import (
    PositionalEncoding,
    create_anchor,
)


def get_emb(sin_inp):
    """
    Gets a base embedding for one dimension with sin and cos intertwined
    """
    emb = torch.stack((sin_inp.sin(), sin_inp.cos()), dim=-1)
    return torch.flatten(emb, -2, -1)


class PositionalEncoding2D(nn.Module):
    def __init__(self, channels):
        """
        Implementation based on https://github.com/tatp22/multidim-positional-encoding

        :param channels: The last dimension of the tensor you want to apply pos emb to.
        """
        super(PositionalEncoding2D, self).__init__()
        self.org_channels = channels
        channels = int(np.ceil(channels / 4) * 2)
        self.channels = channels
        inv_freq = 1.0 / (10000 ** (torch.arange(0, channels, 2).float() / channels))
        self.register_buffer("inv_freq", inv_freq)
        self.cached_penc = None

    def forward(self, tensor, x_interval=1, y_interval=1, x_start=0, y_start=0):
        """
        :param tensor: A 4d tensor of size (batch_size, x, y, ch)
        :return: Positional Encoding Matrix of size (batch_size, x, y, ch)
        """
        if len(tensor.shape) != 4:
            raise RuntimeError("The input tensor has to be 4d!")

        if self.cached_penc is not None and self.cached_penc.shape == tensor.shape:
            return self.cached_penc

        self.cached_penc = None
        batch_size, x, y, orig_ch = tensor.shape
        pos_x = (
            torch.arange(x, device=tensor.device).type(self.inv_freq.type())
            * x_interval
            + x_start
        )
        pos_y = (
            torch.arange(y, device=tensor.device).type(self.inv_freq.type())
            * y_interval
            + y_start
        )
        sin_inp_x = torch.einsum("i,j->ij", pos_x, self.inv_freq)
        sin_inp_y = torch.einsum("i,j->ij", pos_y, self.inv_freq)
        emb_x = get_emb(sin_inp_x).unsqueeze(1)
        emb_y = get_emb(sin_inp_y)
        emb = torch.zeros((x, y, self.channels * 2), device=tensor.device).type(
            tensor.type()
        )
        emb[:, :, : self.channels] = emb_x
        emb[:, :, self.channels : 2 * self.channels] = emb_y

        self.cached_penc = emb[None, :, :, :orig_ch].repeat(tensor.shape[0], 1, 1, 1)
        return self.cached_penc


# Function to visualize the positional encoding matrix
def visualize_positional_encoding(
    encoding, title="Positional Embedding", xlabel="Position", ylabel="Dimension"
):
    """
    Visualizes a positional encoding matrix as a heatmap.

    Args:
        encoding (torch.Tensor or numpy.ndarray): The positional encoding matrix of shape (seq_len, d_model).
        title (str): Title of the plot.
        xlabel (str): Label for the x-axis.
        ylabel (str): Label for the y-axis.
    """
    # If the encoding is a PyTorch tensor, convert it to a numpy array.
    if isinstance(encoding, torch.Tensor):
        encoding = encoding.detach().cpu().numpy()

    plt.figure(figsize=(10, 8))
    plt.imshow(encoding, aspect="auto", cmap="viridis")
    plt.title(title)
    plt.xlabel(xlabel)
    plt.ylabel(ylabel)
    plt.colorbar(label="Value")
    plt.show()


class PositionalEncoding1D(nn.Module):
    def __init__(self, d_model: int, dropout: float = 0.0, max_len: int = 5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)

        position = torch.arange(max_len).unsqueeze(1)
        div_term = torch.exp(
            torch.arange(0, d_model, 2) * (-math.log(10000.0) / d_model)
        )
        pe = torch.zeros(max_len, d_model)
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        self.register_buffer("pe", pe)

    def get_encoding(self, length: int):
        return self.pe[:length]

    def forward(self, x):
        """Add positional emb to feature.
        Arguments:
            x: Tensor, shape ``[batch_size, seq_len, embedding_dim]``
        """
        x = x + self.pe[None, : x.size(1)]
        return self.dropout(x)


class TransformerWrapper(nn.Module):
    """This wrapper is used to make adapt to various input tensor dimension
    Accept TransformerBlock, CoTransformerBlock

    For self attention, the -2 dim is used as sequence dimension.
    For cross attention, if squeeze_dim2: will make a sequence dimension of size 1
                        if not squeeze_dim2: will use -2 dim as sequence dimension.
    """

    def __init__(self, transformer_cls, *args, use_position_emb=False, **kwargs):
        super().__init__()

        self.transformer = transformer_cls(*args, **kwargs, batch_first=True)
        self.positional_encoding1 = PositionalEncoding1D(args[0])
        self.use_position_emb = use_position_emb

    @staticmethod
    def to_trans_shape(x):
        orig_shape = list(x.shape)
        trans_shape = [-1] + orig_shape[-2:]
        x = x.view(trans_shape)
        return x, orig_shape

    def forward(self, x, x2=None, squeeze_dim2=True):
        if len(x.shape) > 3:
            x, x_orig_shape = self.to_trans_shape(x)
            if x2 is not None:
                x2, x2_orig_shape = self.to_trans_shape(x2)
                if self.use_position_emb:
                    x = self.positional_encoding1(x)
                    x2 = self.positional_encoding1(x2)

                x_res = self.transformer(x, x2)
                # x_res = x +  x_res
                return x_res.view(x_orig_shape), x2
            else:
                if self.use_position_emb:
                    x = self.positional_encoding1(x)
                x = self.transformer(x)
                # x = orig_x +  x
                return x.view(x_orig_shape)
        else:
            if x2 is not None:
                return self.transformer(x, x2)
            else:
                return self.transformer(x)


class TransformerBlock(nn.Module):
    def __init__(self, hidden, num_attn_heads, feed_forward_hidden, dropout):
        """The Transformer block in the "Attention Is All You Need".

        Parameters
        ----------
        hidden: int
            Hidden size of transformer
        num_attn_heads: int
            Head sizes of multi-head attention
        feed_forward_hidden: int
            Hidden size of the position-wise feed-forward layer, usually 4*hidden_size.
        dropout: float
            Dropout ratio.
        """
        super().__init__()
        self.attention = torch.nn.MultiheadAttention(
            embed_dim=hidden, num_heads=num_attn_heads, batch_first=True
        )

        # Implementation of feedforward block
        self.linear1 = nn.Linear(hidden, feed_forward_hidden)
        self.dropout = nn.Dropout(dropout)
        self.linear2 = nn.Linear(feed_forward_hidden, hidden)

        # Norms and dropouts
        self.norm1 = nn.LayerNorm(hidden)
        self.norm2 = nn.LayerNorm(hidden)
        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)

    def forward(
        self, x, mask=None, key_padding_mask=None, need_attention_weights=False
    ):
        # Attention block
        x_in = self.norm1(x)
        x_attn, attn = self.attention(
            x_in, x_in, x_in, attn_mask=mask, key_padding_mask=key_padding_mask
        )
        x_attn = self.dropout1(x_attn)
        # x = x + x_attn
        x = x_attn
        # Feedforward block
        x_ff = self.dropout(F.relu(self.linear1(self.norm2(x))))
        x = self.dropout2(self.linear2(x_ff))
        if need_attention_weights:
            return x, attn
        else:
            return x


class CoTransformerBlock(TransformerBlock):
    def __init__(self, hidden, num_attn_heads, feed_forward_hidden, dropout):
        """The Transformer block in the "Attention Is All You Need".

        Parameters
        ----------
        hidden: int
            Hidden size of transformer
        num_attn_heads: int
            Head sizes of multi-head attention
        feed_forward_hidden: int
            Hidden size of the position-wise feed-forward layer, usually 4*hidden_size.
        dropout: float
            Dropout ratio.
        """
        super().__init__(hidden, num_attn_heads, feed_forward_hidden, dropout)
        self.norm1_q = nn.LayerNorm(hidden)
        self.norm1_kv = nn.LayerNorm(hidden)

    def forward(
        self, q, kv, mask=None, key_padding_mask=None, need_attention_weights=False
    ):
        # Attention block
        q_in = self.norm1_q(q)
        kv_in = self.norm1_kv(kv)
        x_attn, attn = self.attention(
            q_in, kv_in, kv_in, attn_mask=mask, key_padding_mask=key_padding_mask
        )
        x_attn = self.dropout1(x_attn)
        # x = q_in + x_attn
        x = x_attn
        # Feedforward block
        x_ff = self.dropout(F.relu(self.linear1(self.norm2(x))))
        x = self.dropout2(self.linear2(x_ff))
        if need_attention_weights:
            return x, attn
        else:
            return x


class TransformerBase(nn.Module):
    def __init__(self):
        """The base for the transformer encoder and decoder."""
        super().__init__()

    def create_transformer_block(self, transformer_block, hidden_dim=-1):
        if hidden_dim < 0:
            hidden_dim = self.hidden_dim
        return transformer_block(
            hidden_dim,
            self.num_attn_heads,
            hidden_dim * 4,
            self.dropout_ratio,
        )

    def create_agent_transformer(self, n_blocks=1):
        agent_modules = []
        for _ in range(n_blocks):
            for _ in range(2):
                agent_modules.append(
                    CoTransformerBlock(
                        self.hidden_dim,
                        self.num_attn_heads,
                        self.hidden_dim * 4,
                        self.dropout_ratio,
                    )
                )
        return agent_modules

    def forward_agent(
        self,
        embed,
        embed_q,
        transformer_time,
        transformer_agent,
        batch_size,
        num_agents,
        num_samples=-1,
        skip_agent_att=False,
    ):
        device = embed.device
        if num_samples > 0:  # decoder
            # Original: [batch, agents, samples, timestamps, features]
            time_idx = 3
        else:  # encoder
            # Original: [batch, agents, timestamps, features]
            time_idx = 2
        num_time = embed.shape[time_idx]
        num_latent_time = embed_q.shape[time_idx]

        # Self attention across time: [batch x agents, timestamps, features]
        embed = embed.view(-1, num_time, self.hidden_dim)
        embed_q = embed_q.view(-1, num_latent_time, self.hidden_dim)
        embed_q = transformer_time(embed_q, embed)
        # Self attention across agent: [batch x samples x timestamps, agents, features]
        if num_samples > 0:
            embed = embed.view(batch_size, num_agents, num_samples, num_time, -1)
            embed_q = embed_q.view(
                batch_size, num_agents, num_samples, num_latent_time, -1
            )
        else:
            embed = embed.view(batch_size, num_agents, num_time, -1)
            embed_q = embed_q.view(batch_size, num_agents, num_latent_time, -1)
        if not skip_agent_att:
            embed_q = embed_q.transpose(1, time_idx).reshape(
                -1, num_agents, self.hidden_dim
            )
            # Do not use padding because it causes nan
            embed_q = transformer_agent(embed_q, embed_q)
            if num_samples > 0:
                embed_q = (
                    embed_q.view(
                        batch_size, num_latent_time, num_samples, num_agents, -1
                    )
                    .transpose(1, time_idx)
                    .contiguous()
                )
            else:
                embed_q = (
                    embed_q.view(batch_size, num_latent_time, num_agents, -1)
                    .transpose(1, time_idx)
                    .contiguous()
                )
        return embed_q


class LearnedPositionalEncoding(nn.Module):
    def __init__(self, max_len: int, d_model: int):
        super().__init__()
        self.pe = nn.Embedding(max_len, d_model)

    def forward(self, x):
        # x: (B, K, H)
        B, K, H = x.shape
        pos = torch.arange(K, device=x.device).unsqueeze(0).expand(B, K)  # (B, K)
        return x + self.pe(pos)  # (B, K, H)


def main():
    d_model = 64
    seq_len = 300
    pos_enc = PositionalEncoding1D(d_model=d_model)
    encoding_matrix = pos_enc.get_encoding(seq_len)
    visualize_positional_encoding(encoding_matrix)


if __name__ == "__main__":
    main()
