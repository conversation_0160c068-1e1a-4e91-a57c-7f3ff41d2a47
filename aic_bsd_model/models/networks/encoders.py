import torch
import torch.nn as nn

from aic_bsd_model.models.networks.basic_network import (
    LSTMWrapper,
    PositionalEncoding,
    create_mlp,
)
from aic_bsd_model.models.networks.transformer_util import (
    LearnedPositionalEncoding,
    TransformerWrapper,
)


class TransformerEncoder(nn.Module):
    def __init__(self, input_dim, hidden_dim, num_heads, num_layers, dropout=0.1):
        super(TransformerEncoder, self).__init__()
        self.embedding = nn.Linear(input_dim, hidden_dim)
        # TODO (deepak.gopinath add d_feedforward as an arg
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim, nhead=num_heads, dropout=dropout, batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, num_layers=num_layers
        )
        self.layer_norm = nn.LayerNorm(hidden_dim)

    def forward(self, x):
        # x = (B, T, F)
        # (B, T, hidden)
        x = self.embedding(x)
        # (B, T, hidden)
        x = self.transformer_encoder(x)
        # (B, T, hidden)
        x = self.layer_norm(x)
        return x


class PointNetEncoderImpl(nn.Module):
    def __init__(self, input_dim, hidden_dim, params, seq_dim):
        super().__init__()
        self.input_dim = input_dim
        self.embed_size = hidden_dim
        self.params = params
        self.seq_dim = seq_dim
        dropout = params["dropout"]
        self.transformer_hidden_dim_multiplier = params.get(
            "transformer_hidden_dim_multiplier", 2
        )

        self.mlp1 = create_mlp(
            input_dim,
            [hidden_dim, hidden_dim, hidden_dim // 2],
            dropout,
        )
        self.attention1 = TransformerWrapper(
            nn.TransformerEncoderLayer,
            hidden_dim,
            params["num_attention_heads"],
            self.transformer_hidden_dim_multiplier * hidden_dim,
            dropout,
            use_position_emb=True,
        )
        self.mlp2 = create_mlp(
            hidden_dim,
            [hidden_dim, hidden_dim, hidden_dim],
            dropout,
        )
        self.attention2 = TransformerWrapper(
            nn.TransformerEncoderLayer,
            hidden_dim * 2,
            params["num_attention_heads"],
            self.transformer_hidden_dim_multiplier * hidden_dim,
            dropout,
            use_position_emb=True,
        )

        self.mlp3_linear = nn.Linear(hidden_dim * 2, hidden_dim)

    def forward(self, points):
        p1 = self.mlp1(points)
        max_repeat_shape = [1] * p1.dim()
        max_repeat_shape[self.seq_dim] = p1.shape[self.seq_dim]
        # PointNet operation: Add the max to the sequence
        p1_max = torch.max(p1, dim=self.seq_dim, keepdim=True)[0].repeat(
            max_repeat_shape
        )
        p1_cat = torch.cat((p1, p1_max), dim=-1)
        p1_cat = self.attention1(p1_cat)

        p2 = self.mlp2(p1_cat)
        p2_max = torch.max(p2, dim=self.seq_dim, keepdim=True)[0].repeat(
            max_repeat_shape
        )

        p2_cat = torch.cat((p2, p2_max), dim=-1)
        p2_cat = self.attention2(p2_cat)

        # p3 = self.mlp3(p2_cat)
        p3 = self.mlp3_linear(p2_cat)
        # p3 = p2_cat + p3
        out = torch.max(p3, dim=self.seq_dim)[0]

        return out, p3


class PointNetEncoder(PointNetEncoderImpl):
    def forward(self, points):
        ret = super(PointNetEncoder, self).forward(points)
        # Discard p3 which is the intermediate tensor
        return ret[0]


class TrajLSTMNet(nn.Module):
    def __init__(
        self,
        config,
        input_dim,
        hidden_dim,
    ):
        super().__init__()
        self.config = config
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.dropout_ratio = self.config["dropout"]

        self.layers1 = create_mlp(
            self.input_dim,
            [32, hidden_dim, hidden_dim],
            self.dropout_ratio,
            norm_func=None,
        )
        self.lstm = LSTMWrapper(hidden_dim, hidden_dim)
        self.lstm2 = LSTMWrapper(hidden_dim, hidden_dim)

    def forward(self, x):
        # Use mlp first
        # [batch, num_segments, timesteps, hidden_state_dim]
        x1 = self.layers1(x)

        # [batch, num_segments, timesteps, hidden_state_dim]
        x_lstm = self.lstm(x1)[0]
        # Use the last state as traj embedding
        # [batch, num_segments, hidden_state_dim]
        x_lstm2 = self.lstm2(x_lstm)[0][..., -1, :]

        return x_lstm2


class TrajEncoderNet_Transformer(nn.Module):
    def __init__(self, input_dim, hidden_dim, params, seq_dim):
        super().__init__()
        self.traj_input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.config = params
        self.seq_dim = seq_dim
        self.dropout_ratio = self.config["dropout"]
        self.num_encoder_transformer_blocks = self.config["num_attention_layers"]
        self.transformer_hidden_dim_multiplier = self.config.get(
            "transformer_hidden_dim_multiplier", 2
        )

        layers1_dim = [int(self.hidden_dim * k) for k in [1, 1, 2, 1]]

        self.layers1 = create_mlp(
            self.traj_input_dim,
            layers1_dim,
            self.dropout_ratio,
        )

        self.transformer_blocks = []
        print(f"Using {self.num_encoder_transformer_blocks} transformer blocks")
        for i in range(self.num_encoder_transformer_blocks):
            use_pos_emb = i < self.num_encoder_transformer_blocks - 1
            self.transformer_blocks.append(
                TransformerWrapper(
                    nn.TransformerEncoderLayer,
                    self.hidden_dim,
                    self.config["num_attention_heads"],
                    self.transformer_hidden_dim_multiplier * self.hidden_dim,
                    self.dropout_ratio,
                    use_position_emb=use_pos_emb,
                )
            )

        self.transformer_blocks = nn.Sequential(*self.transformer_blocks)

    def forward(self, x):
        # Use mlp first
        # [batch, num_segments, timesteps, hidden_state_dim]
        attn = self.layers1(x)
        attn = self.transformer_blocks(attn)

        # [batch, num_segments, hidden_state_dim]
        ret = torch.max(attn, dim=self.seq_dim)[0]

        return ret


class CrossAttentionFusion(nn.Module):
    def __init__(self, hidden_dim, num_heads, dropout=0.1):
        super(CrossAttentionFusion, self).__init__()
        # Cross-attention where trajectory acts as the query and map as the key and value
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim, num_heads=num_heads, dropout=dropout, batch_first=True
        )
        self.layer_norm = nn.LayerNorm(hidden_dim)

    def forward(self, trajectory_sequence, map_sequence):
        # Apply cross-attention: trajectory attends to the map
        attended_output, _ = self.cross_attention(
            query=trajectory_sequence, key=map_sequence, value=map_sequence
        )
        # Add residual connection and normalize
        output = self.layer_norm(attended_output + trajectory_sequence)
        return output


class MetricsEncoderMLP(nn.Module):
    def __init__(self, metrics_input_dim, hidden_dim, dropout_prob=0.1):
        super(MetricsEncoderMLP, self).__init__()
        self.metrics_input_dim = metrics_input_dim
        self.hidden_dim = hidden_dim
        self.dropout_prob = dropout_prob

        layers1_dim = [int(self.hidden_dim * k) for k in [1, 2, 1]]

        self.mlp = create_mlp(
            self.metrics_input_dim,
            layers1_dim,
            self.dropout_prob,
        )  # input dim = metrics_input_dim, outputdim = hidden_dim

        # # Input layer
        # layers.append(nn.Linear(self.input_dim, hidden_dims[0]))
        # layers.append(nn.ReLU())
        # layers.append(nn.Dropout(dropout_prob))

        # # Hidden layers
        # for i in range(len(hidden_dims) - 1):
        #     layers.append(nn.Linear(hidden_dims[i], hidden_dims[i + 1]))
        #     layers.append(nn.ReLU())
        #     layers.append(nn.Dropout(dropout_prob))

        # # Output layer
        # layers.append(nn.Linear(hidden_dims[-1], hidden_dim))

        # # Combine layers into a Sequential module
        # self.mlp = nn.Sequential(*layers)

    def forward(self, x):
        return self.mlp(x)


class MaskedTransformer(nn.Module):
    def __init__(
        self,
        dim_model,
        num_heads,
        num_layers,
        dim_feedforward=2048,
        dropout=0.1,
        use_pos_emb=True,
        max_len=10,
    ):
        super().__init__()
        self.use_pos_emb = use_pos_emb

        encoder_layer = nn.TransformerEncoderLayer(
            d_model=dim_model,
            nhead=num_heads,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            batch_first=True,  # <<< Avoids the need to transpose
        )

        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        if self.use_pos_emb:
            self.pe = LearnedPositionalEncoding(max_len, dim_model)

    def forward(self, x, mask):
        """
        x:     Tensor of shape (B, K, H)
        mask:  Tensor of shape (B, K), with 1 for valid, 0 for padded
        """
        B, K, H = x.shape
        # Create padding mask: True where padding, False where valid
        padding_mask = mask == 0  # shape: (B, K)
        if self.use_pos_emb:
            x = self.pe(x)

        # Causal mask: shape (K, K). True means "do not attend".
        causal_mask = torch.triu(
            torch.ones(K, K, dtype=torch.bool, device=x.device), diagonal=1
        )

        # src_mask -> causal; src_key_padding_mask -> padding (per batch)
        out = self.transformer(
            x,  # (B, K, H)
            mask=causal_mask,  # (K, K)
            src_key_padding_mask=padding_mask,  # (B, K)
        )  # -> (B, K, H)

        lengths = mask.sum(dim=1)  # (B,)
        has_any = lengths > 0  # (B,)

        # Clamp to avoid negative when an item has 0 valid steps
        last_idx = torch.clamp(lengths - 1, min=0)  # (B,)

        # Gather last valid hidden state for each batch item
        # Build indices of shape (B, 1, H) to gather along dim=1
        idx = last_idx.view(B, 1, 1).expand(-1, 1, H).to(torch.int64)  # (B, 1, H)
        last_valid_outputs = torch.gather(out, dim=1, index=idx).squeeze(1)  # (B, H)

        # Zero-out rows that had no valid steps
        last_valid_outputs = last_valid_outputs * has_any.unsqueeze(-1)  # (B, H)
        return out, last_valid_outputs
