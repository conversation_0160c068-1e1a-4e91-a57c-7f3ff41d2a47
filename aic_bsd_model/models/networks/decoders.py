import torch
import torch.nn as nn

from aic_bsd_model.models.networks.basic_network import (
    LSTMWrapper,
    PositionalEncoding,
    create_anchor,
    create_mlp,
)
from aic_bsd_model.models.networks.transformer_util import TransformerWrapper
from aic_bsd_model.utils.training_utils import get_future_mask, parse_snippet_batch


class SegmentTrajectoryMLPDecoder(nn.Module):
    def __init__(
        self,
        encoder_hidden_dim,
        traj_output_dim,
        decoder_hidden_dim,
        max_segment_length,
        predict_delta=False,
        dropout=0.1,
    ):
        """
        encoder_hidden_dim: Dimension of the segment-level fused encoding.
        input_dim: Number of features in the trajectory (e.g., x, y, speed).
        decoder_hidden_dim: Dimension of the hidden layers in the decoder MLP.
        dropout: Dropout rate.
        """
        super(SegmentTrajectoryMLPDecoder, self).__init__()

        # Positional encoding for timestep-specific information
        self.decoder_output_dim = traj_output_dim
        # Similar idea to anchor embeddings. Consider different initialization?
        self.positional_encoding = nn.Embedding(
            max_segment_length, encoder_hidden_dim
        )  # Maximum of max_segment_length timesteps per segment
        self.predict_delta = predict_delta
        self.rnn_decoder = nn.GRU(
            encoder_hidden_dim, encoder_hidden_dim, num_layers=2, batch_first=True
        )
        # MLP for decoding
        # TODO (deepak.gopinath. PAss MLP layers dims as arg)
        self.mlp = nn.Sequential(
            nn.Linear(encoder_hidden_dim, decoder_hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(decoder_hidden_dim, decoder_hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(
                decoder_hidden_dim // 2, self.decoder_output_dim
            ),  # Final layer outputs trajectory features
        )
        # if self.predict_delta:
        #     self.trajectory_smoother = torch.nn.RNN(self.decoder_output_dim, self.decoder_output_dim, 1, batch_first=True)

    def forward(self, segment_encoding, original_lengths, map_encoding=None):
        """
        segment_encoding: Tensor of shape (batch_size, num_segments, embed_dim).
        original_lengths: Tensor of shape (batch_size, num_segments), where each entry corresponds to the length of each segment in the input
        Returns:
            - reconstructed_trajectories: Tensor of shape (batch_size, num_segments, max_length, input_dim)
            - masks: Tensor of shape (batch_size, num_segments, max_length), where 1 indicates valid data.
        """
        batch_size, num_segments, encoder_hidden_dim = segment_encoding.shape
        # Maximum segment trajectory length in the batch
        max_length = original_lengths.max().item()
        # Prepare positional encodings for all possible timesteps
        # Shape: (1, max_length)
        positions = torch.arange(max_length, device=segment_encoding.device).unsqueeze(
            0
        )

        try:
            # Shape: (1, max_length, embed_dim)
            positional_embeddings = self.positional_encoding(positions)
            # we assume that max_length is less than the max_segment_length with which the positional encodings were initialized with
        except:
            import IPython

            IPython.embed()

        # Tensor to store reconstructed trajectories and masks
        reconstructed_trajectories = torch.zeros(
            batch_size,
            num_segments,
            max_length,
            self.decoder_output_dim,
            device=segment_encoding.device,
        )
        masks = torch.zeros(
            batch_size, num_segments, max_length, device=segment_encoding.device
        )

        for seg_idx in range(num_segments):
            # Get the encoding for the current segment
            # Shape: (batch_size, encoder_embed_dim)
            current_segment_encoding = segment_encoding[:, seg_idx, :]

            # Expand segment encoding and add positional embeddings
            # Shape: (batch_size, max_length, encoder_embed_dim)
            expanded_encoding = current_segment_encoding.unsqueeze(1).repeat(
                1, max_length, 1
            )
            # Shape: (batch_size, max_length, encoder_embed_dim)
            decoder_input = expanded_encoding + positional_embeddings

            decoder_input, _ = self.rnn_decoder(decoder_input)
            # Decode trajectory
            # Shape: (batch_size, max_length, traj_output_dim)
            decoded_trajectory = self.mlp(decoder_input)

            if self.predict_delta:
                # we are assuming that the trajectories are normalized with respect to last time point. so last time point will be 0,0,0
                # therefore we do 'reverse cumsum' from end to start. In RAD, we do regular cumsum, because we are not reconstructting but
                # predicting the future trajectory. The starting point of the future trajectory is the last time point of the past trajectory (which also happens to be the 0,0,0)
                decoded_trajectory = (
                    decoded_trajectory
                    + torch.sum(decoded_trajectory, dim=1, keepdims=True)
                    - torch.cumsum(decoded_trajectory, dim=1)
                )
                # decoded_trajectory, _ = self.trajectory_smoother(decoded_trajectory) #(B, maxL, F)

            # Mask to slice reconstructed trajectories to original lengths
            masks_for_segment = (
                torch.arange(max_length, device=segment_encoding.device)
                .unsqueeze(0)
                .expand(batch_size, -1)
            )
            # left padded
            # Shape: (batch_size, max_length)
            masks_for_segment = masks_for_segment >= (
                max_length - original_lengths[:, seg_idx]
            ).unsqueeze(1)

            # Apply mask to retain only valid timesteps
            reconstructed_trajectories[:, seg_idx, :, :] = decoded_trajectory
            masks[:, seg_idx, :] = masks_for_segment.float()

        return reconstructed_trajectories, masks


class TransformerDecoder(nn.Module):
    def __init__(
        self,
        hidden_dim,
        traj_output_dim,
        max_segment_length,
        predict_delta=False,
        dropout=0.1,
    ):
        """
        encoder_hidden_dim: Dimension of the segment-level fused encoding.
        input_dim: Number of features in the trajectory (e.g., x, y, speed).
        decoder_hidden_dim: Dimension of the hidden layers in the decoder MLP.
        dropout: Dropout rate.
        """
        super(TransformerDecoder, self).__init__()

        # Positional encoding for timestep-specific information
        self.decoder_output_dim = traj_output_dim
        # Similar idea to anchor embeddings. Consider different initialization?
        self.positional_encoding = nn.Embedding(
            max_segment_length, hidden_dim
        )  # Maximum of max_segment_lentgh timesteps per segment
        self.positional_encoding = PositionalEncoding(
            hidden_dim, max_len=max_segment_length
        )
        self.predict_delta = predict_delta
        self.future_mlp = create_mlp(
            hidden_dim, [hidden_dim, hidden_dim // 2, hidden_dim], 0
        )

        self.future_anchor = create_anchor([1, 1, 1, hidden_dim])
        self.traj_map_attention = nn.Sequential(
            TransformerWrapper(
                nn.TransformerEncoderLayer, hidden_dim, 8, 2 * hidden_dim, 0
            ),
            # TransformerWrapper(
            #     TransformerBlock, encoder_hidden_dim, 8, 4 * encoder_hidden_dim, 0
            # ),
        )
        self.map_future_attention1 = TransformerWrapper(
            nn.TransformerDecoderLayer,
            hidden_dim,
            4,
            4 * hidden_dim,
            0,
        )
        self.map_future_attention2 = TransformerWrapper(
            nn.TransformerDecoderLayer, hidden_dim, 4, 4 * hidden_dim, 0
        )
        self.future_attention1 = TransformerWrapper(
            nn.TransformerEncoderLayer,
            hidden_dim,
            4,
            4 * hidden_dim,
            0,
            use_position_emb=True,
        )
        self.future_attention2 = TransformerWrapper(
            nn.TransformerEncoderLayer, hidden_dim, 4, 4 * hidden_dim, 0
        )
        self.future_attention3 = TransformerWrapper(
            nn.TransformerEncoderLayer, hidden_dim, 4, 4 * hidden_dim, 0
        )
        self.coord_decoder = nn.Sequential(
            create_mlp(
                hidden_dim,
                [hidden_dim, hidden_dim // 2, hidden_dim],
                dropout,
                norm_func=None,
            ),
            # create_mlp(32, [32, 16], dropout, norm_func=None),
            nn.Linear(hidden_dim, self.decoder_output_dim),
        )
        # self.coord_decoder = nn.Linear(hidden_dim, self.decoder_output_dim)

        self.traj_anchor = create_anchor([1, 1, max_segment_length, 3])

    def forward(self, batch, segment_encoding, original_lengths, map_encoding):
        """
        batch: dict
        segment_encoding: Tensor of shape (batch_size, num_segments, embed_dim).
        original_lengths: Tensor of shape (batch_size, num_segments), where each entry corresponds to the length of each segment in the input
        map_encoding: Tensor of shape (batch_size, num_segments, map_dim).
        Returns:
            - reconstructed_trajectories: Tensor of shape (batch_size, num_segments, max_length, input_dim)
            - masks: Tensor of shape (batch_size, num_segments, max_length), where 1 indicates valid data.
        """
        batch_size, num_segments, encoder_hidden_dim = segment_encoding.shape
        max_length = (
            original_lengths.max().item()
        )  # Maximum segment trajectory length in the batch

        # Prepare positional encodings for all possible timesteps
        positions = torch.arange(max_length, device=segment_encoding.device)[None, None]
        # Shape: (1, 1, max_length, embed_dim)
        # positional_embeddings = self.positional_encoding(positions)
        positional_embeddings = self.positional_encoding.get_encoding(max_length)
        # we assume that max_length is less than the max_segment_length with which the positional encodings were initialized with

        # [b, seg, 5(in_traj, map*3, out_traj), feat]
        traj_map_encoding = torch.cat(
            [segment_encoding[:, :, None], map_encoding], dim=2
        )
        traj_map_encoding = self.traj_map_attention(traj_map_encoding)
        # Multiply anchor by the time embed
        # positional_embeddings = positional_embeddings * traj_map_encoding[:,:,-1:]
        # future_emb = positional_embeddings * segment_encoding[:, :, None]
        future_emb = positional_embeddings[None, None].repeat(
            batch_size, num_segments, 1, 1
        )
        future_emb = self.future_mlp(future_emb)
        future_emb, _ = self.map_future_attention1(future_emb, traj_map_encoding)
        future_emb = self.future_attention1(future_emb)
        future_emb, _ = self.map_future_attention2(future_emb, traj_map_encoding)
        future_emb = self.future_attention2(future_emb)
        future_emb = self.future_attention3(future_emb)

        # [b, seg, max_len, feat]
        reconstructed_trajectories = self.coord_decoder(future_emb)

        # fut_encoding = positional_embeddings * segment_encoding[:, :, None]
        # reconstructed_trajectories = self.coord_decoder(fut_encoding)

        masks = torch.zeros(
            batch_size, num_segments, max_length, device=segment_encoding.device
        )

        for seg_idx in range(num_segments):
            # Mask to slice reconstructed trajectories to original lengths
            masks_for_segment = (
                torch.arange(max_length, device=segment_encoding.device)
                .unsqueeze(0)
                .expand(batch_size, -1)
            )
            # left padded
            masks_for_segment = masks_for_segment >= (
                max_length - original_lengths[:, seg_idx]
            ).unsqueeze(
                1
            )  # Shape: (batch_size, max_length)

            # Apply mask to retain only valid timesteps
            # TODO (deepak.gopinath) consider adding a cumsum so that the mlp is outputting delta x, deltay, delat z.
            # TODO (deepak.gopinath) consider adding a "smoother RNN" like in RAD
            masks[:, seg_idx, :] = masks_for_segment.float()

        return reconstructed_trajectories, masks


class DecoderModule(nn.Module):
    def __init__(self, params, hidden_dim, dropout, num_future_timesteps):
        super().__init__()
        self.decoder_predict_traj_delta = False

        self.num_future_timesteps = num_future_timesteps

        self.map_attention = TransformerWrapper(
            nn.TransformerDecoderLayer, hidden_dim, 4, 3 * hidden_dim, 0
        )

        self.traj_attention = TransformerWrapper(
            nn.TransformerEncoderLayer, hidden_dim, 4, 3 * hidden_dim, 0
        )

        self.coordinate_decoder_delta = nn.Sequential(
            create_mlp(
                hidden_dim,
                layers_dim=[
                    hidden_dim,
                    hidden_dim,
                    hidden_dim,
                ],
                dropout_ratio=dropout,
                norm_func=None,
                activation_func=nn.Mish,
            ),
            nn.Linear(hidden_dim, 3),
        )
        self.coordinate_decoder = nn.Sequential(
            create_mlp(
                hidden_dim,
                layers_dim=[
                    hidden_dim // 2,
                    hidden_dim,
                    hidden_dim,
                ],
                dropout_ratio=dropout,
                norm_func=None,
                activation_func=nn.Mish,
            ),
            nn.Linear(hidden_dim, 3),
        )
        self.smoother = nn.RNN(3, 3, 1, batch_first=True)

    def forward(self, traj_emb, map_emb, coordinate_anchor=None):
        # cross attentions between map sequence and future traj encoding (made by duplicating past traj encoding). This is where the fusion between trajectory and map happens
        # if traj_emb.dim() == 4:
        #     traj_emb = traj_emb[..., None, :]
        traj_encoding, map_emb = self.map_attention(
            traj_emb, map_emb, squeeze_dim2=False
        )
        traj_encoding = self.traj_attention(traj_encoding)
        if self.decoder_predict_traj_delta:
            result = self.coordinate_decoder_delta(traj_encoding)
            if coordinate_anchor is not None:
                result = result * coordinate_anchor
                result = torch.cumsum(result, dim=-2)

        else:
            result = self.coordinate_decoder(traj_encoding)
            # result = result.view(*result.shape[:-1], self.future_timestep, 2)
        result_shape = result.shape
        smooth_result, _ = self.smoother(result.view(-1, *result_shape[-2:]))
        result = smooth_result.reshape(result_shape)
        return (
            result,
            traj_encoding,
            map_emb,
        )


class MLPDecoder(nn.Module):
    """
    A neural network to decode a tensor into trajectories with MLP.

    Parameters
    ----------
    intermediate_dim : int
        Intermediate dimension.
    coordinate_dim : int
        Coordinate dimension.
    cumulative_decoding : bool
        Whether to decode cumulative trajectories.
    truncated_steps : int
        Number of steps to truncate.
    params : dict
        Parameter dictionary.
    """

    def __init__(
        self,
        config,
        hidden_dim,
        traj_output_dim,
        max_segment_length=350,
    ):
        super().__init__()
        self.params = config
        self.hidden_dim = hidden_dim
        self.decoder_dropout = config["dropout"]
        self.max_future_steps = config["max_future_steps"]
        self.traj_output_dim = traj_output_dim
        self.use_reconstruction = config["use_trajectory_reconstruction"]

        output_dim = traj_output_dim * self.max_future_steps
        self.coordinate_decoder = nn.Sequential(
            create_mlp(
                self.hidden_dim,
                layers_dim=[self.hidden_dim, self.hidden_dim, self.hidden_dim * 2],
                dropout_ratio=self.decoder_dropout,
                activation_func=nn.Mish,
            ),
            nn.Linear(self.hidden_dim * 2, output_dim),
        )

        self.learned_anchor_embeddings = create_anchor(
            (1, 1, self.max_future_steps, hidden_dim)
        )
        self.state_mlp = create_mlp(
            self.hidden_dim,
            [hidden_dim * 2, hidden_dim],
            self.decoder_dropout,
            None,
            activation_func=nn.Mish,
        )
        self.anchor_layer = create_mlp(
            hidden_dim * 2,
            [hidden_dim * 2, hidden_dim],
            self.decoder_dropout,
            None,
            activation_func=nn.Mish,
        )
        self.coordinate_anchor_embeddings = create_anchor(
            (1, 1, self.max_future_steps, traj_output_dim)
        )

        self.decoder_module1 = DecoderModule(
            config, hidden_dim, self.decoder_dropout, self.max_future_steps
        )

    def forward(self, batch, segment_encoding, original_lengths, map_encoding):
        # # Use mlp first
        batch_size, num_segments, encoder_hidden_dim = segment_encoding.shape
        lap_segments_data_valid = batch["lap_segments_data_valid"]
        #
        max_length = original_lengths.max().item()
        # positional_embeddings = self.positional_encoding.get_encoding(max_length)
        # positional_embeddings = positional_embeddings * segment_encoding[:, :, None]
        # traj_map_encoding = torch.cat(
        #     [segment_encoding[:, :, None], map_encoding], dim=2
        # )
        # future_emb, _ = self.map_future_attention1(
        #     positional_embeddings, traj_map_encoding
        # )

        # encoder_state = self.state_mlp(segment_encoding)
        map_state = map_encoding

        # add anchors to the past traj + map embedding encoder state. after repetition
        # anchor_expanded = torch.cat(
        #     [
        #         self.learned_anchor_embeddings.repeat(batch_size, num_segments, 1, 1),
        #         encoder_state[..., None, :].repeat(1, 1, self.max_future_steps, 1),
        #     ],
        #     dim=-1,
        # )
        # traj_encoding = self.anchor_layer(anchor_expanded)

        # # traj_encodin is (B, num_seg, future_timesteps, hidden_state_dim)
        # traj_pred, traj_encoding, map_encoding = self.decoder_module1(
        #     traj_encoding, map_encoding, self.coordinate_anchor_embeddings
        # )
        traj_pred = self.coordinate_decoder(segment_encoding).view(
            batch_size, num_segments, self.max_future_steps, self.traj_output_dim
        )
        output = traj_pred
        if max_length <= self.max_future_steps:
            output = output[..., :max_length, :]
        else:
            output_padding = torch.zeros(
                [
                    batch_size,
                    num_segments,
                    max_length - self.max_future_steps,
                    self.traj_output_dim,
                ],
                device=segment_encoding.device,
            )
            output = torch.cat([traj_pred, output_padding], dim=-2)
        if self.use_reconstruction:
            pred_mask = lap_segments_data_valid
        else:
            pred_mask = get_future_mask(original_lengths).unsqueeze(-1)
        return output, pred_mask


class MetricsTransformerDecoder(nn.Module):
    def __init__(self, input_dim, target_metric_keys, dropout_prob=0.5):
        """
        input_dim: Dimension of the input layer of this decoder. Typically matches the hidden_dim of the encoder that feeds into this decoder.
        target_metric_keys: list of metrics to be decoded. The length of this list is the output_dimensionality of this networ
        dropout_prob: Dropout probability applied after each hidden layer.
        """
        super(MetricsTransformerDecoder, self).__init__()
        self.target_metric_keys = target_metric_keys
        self.output_dim = len(self.target_metric_keys)

        self.metrics_anchor = create_anchor([1, self.output_dim, input_dim])

        self.seg_metrics_attention1 = TransformerWrapper(
            nn.TransformerDecoderLayer, input_dim, 4, 4 * input_dim, 0
        )
        self.metrics_attention = TransformerWrapper(
            nn.TransformerEncoderLayer, input_dim, 4, 4 * input_dim, 0
        )
        # Metrics are independent of each other at this point
        self.output_mlp = create_mlp(
            input_dim,
            [input_dim // 2, input_dim, 1],
            dropout_prob,
            norm_func=None,
            last_relu=False,
        )

    def forward(self, lap_encoding, seg_encoding):
        # probably needs map_encoding and not seg encodings.
        # lap_encoding
        metrics_emb = self.metrics_anchor * lap_encoding[:, None]
        metrics_emb = self.seg_metrics_attention1(metrics_emb, seg_encoding)
        metrics_emb = self.metrics_attention(metrics_emb)
        metrics = self.output_mlp(metrics_emb)
        out = {}
        for i, key in enumerate(self.target_metric_keys):
            out[key] = metrics[:, i, 0]
        return out


class MetricsDecoderSimpleMLP(nn.Module):
    def __init__(self, hidden_dim, target_metric_keys, dropout_prob=0.5, append_key=""):
        """
        input_dim: Dimension of the input layer.
        hidden_dims: List of integers specifying the dimensions of each hidden layer.
        output_dim: Dimension of the output layer.
        dropout_prob: Dropout probability applied after each hidden layer.
        """
        super(MetricsDecoderSimpleMLP, self).__init__()
        self.target_metric_keys = target_metric_keys
        self.output_dim = len(self.target_metric_keys)
        self.append_key = append_key

        hidden_dims = [hidden_dim, hidden_dim // 2, hidden_dim]
        layers = []

        # Input layer
        layers.append(nn.Linear(hidden_dim, hidden_dims[0]))
        layers.append(nn.ReLU())
        layers.append(nn.Dropout(dropout_prob))

        # Hidden layers
        for i in range(len(hidden_dims) - 1):
            layers.append(nn.Linear(hidden_dims[i], hidden_dims[i + 1]))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout_prob))

        # Output layer
        layers.append(nn.Linear(hidden_dims[-1], self.output_dim))

        # Combine layers into a Sequential module
        self.mlp = nn.Sequential(*layers)

    def forward(self, lap_encoding=None, seg_encoding=None):
        if lap_encoding is not None:
            metrics = self.mlp(lap_encoding)
        elif seg_encoding is not None:
            metrics = self.mlp(seg_encoding)

        out = {}
        for i, key in enumerate(self.target_metric_keys):
            out[key + f"-pred{self.append_key}"] = metrics[:, i]
        return out


########################### LSTM Based Trajectory Decoder
class LSTMDecoderNet(nn.Module):
    def __init__(
        self,
        config,
        hidden_dim,
        traj_output_dim,
        max_segment_length=350,
    ):
        super().__init__()
        self.config = config
        self.dropout_ratio = self.config["dropout"]
        self.lstm_dropout_ratio = self.config["transformer_dropout"]
        self.encoder_hidden_dim = hidden_dim
        self.traj_output_dim = traj_output_dim
        self.max_segment_length = max_segment_length
        self.transformer_hidden_dim_multiplier = self.config.get(
            "transformer_hidden_dim_multiplier", 2
        )

        self.positional_encoding = PositionalEncoding(
            hidden_dim, max_len=max_segment_length
        )

        self.map_future_attention1 = TransformerWrapper(
            nn.TransformerDecoderLayer,
            hidden_dim,
            self.config["num_attention_heads"],
            self.transformer_hidden_dim_multiplier * hidden_dim,
            dropout=self.lstm_dropout_ratio,
        )
        self.lstm = LSTMWrapper(hidden_dim, hidden_dim, dropout=self.lstm_dropout_ratio)
        self.lstm2 = LSTMWrapper(
            hidden_dim, hidden_dim, dropout=self.lstm_dropout_ratio
        )
        self.out_traj = nn.Linear(hidden_dim, traj_output_dim)

    def forward(self, batch, segment_encoding, original_lengths, map_encoding):
        # Use mlp first
        # TODO (xiongyi.cui) Add tensor dimensions to each line
        batch_size, num_segments, encoder_hidden_dim = segment_encoding.shape
        max_length = original_lengths.max().item()
        positional_embeddings = self.positional_encoding.get_encoding(max_length)
        positional_embeddings = (
            positional_embeddings[None, None] * segment_encoding[:, :, None]
        )
        traj_map_encoding = torch.cat(
            [segment_encoding[:, :, None], map_encoding], dim=2
        )
        future_emb, _ = self.map_future_attention1(
            positional_embeddings, traj_map_encoding
        )

        x_lstm = self.lstm(future_emb)[0]
        x_lstm2 = self.lstm2(x_lstm)[0]
        traj = self.out_traj(x_lstm2)

        return traj, None
