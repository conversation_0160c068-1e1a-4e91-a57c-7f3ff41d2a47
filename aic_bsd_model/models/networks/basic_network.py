import math

import torch
from torch import nn as nn


def create_anchor(shape, value=None):
    learned_anchor_embeddings = torch.empty(shape)
    if value is not None:
        learned_anchor_embeddings[:] = value
    else:
        stdv = 1.0 / math.sqrt(shape[-1])
        learned_anchor_embeddings.uniform_(-stdv, stdv)
    learned_anchor_embeddings.requires_grad_(True)
    learned_anchor_embeddings = nn.Parameter(learned_anchor_embeddings)
    return learned_anchor_embeddings


class BatchNorm1D(nn.Module):
    def __init__(self, feat_dim):
        super().__init__()
        self.norm = nn.BatchNorm1d(feat_dim)

    def forward(self, x):
        orig_shape = x.shape
        x_norm = self.norm(x.view(-1, orig_shape[-1]))
        return x_norm.view(orig_shape)


class LayerNorm(nn.Module):
    def __init__(self, feat_dim):
        super().__init__()
        self.norm = nn.LayerNorm(normalized_shape=(feat_dim,))

    def forward(self, x):
        orig_shape = x.shape
        x_norm = self.norm(x.view(-1, orig_shape[-1]))
        return x_norm.view(orig_shape)


class LinearLayer(nn.Module):
    def __init__(
        self,
        i_layer,
        input_dim,
        output_dim,
        use_norm,
        activation_func,
        dropout_ratio,
        norm_func,
        use_rezero=False,
        use_init=True,
    ):
        super().__init__()
        self.use_rezero = use_rezero and (input_dim == output_dim)
        # ReZero is All You Need: https://proceedings.mlr.press/v161/bachlechner21a/bachlechner21a.pdf
        self.a = create_anchor((1,), 0)

        layers = []
        new_linear = nn.Linear(input_dim, output_dim, bias=norm_func is None)
        layers.append(new_linear)
        # if activation_func is not None:
        layers.append(activation_func())
        if use_norm:
            layers.append(norm_func(output_dim))
        layers.append(nn.Dropout(p=dropout_ratio))
        self.layers = nn.Sequential(*layers)

        if use_init:
            nn.init.xavier_normal_(new_linear.weight)
        if new_linear.bias is not None:
            nn.init.zeros_(new_linear.bias)

    def forward(self, x):
        out = self.layers(x)
        if self.use_rezero:
            out = x + self.a * out
        return out


class MyResidual(nn.Module):
    def __init__(self, layers, input_dim, output_dim):
        super().__init__()
        self.layers = layers
        if input_dim != output_dim:
            self.proj_in = nn.Linear(input_dim, output_dim)
            self.proj_in.weight.data.zero_()
            self.proj_in.bias.data.zero_()
        else:
            self.proj_in = None

    def forward(self, x):
        if self.proj_in is not None:
            x_residual = self.proj_in(x)
        else:
            x_residual = x
        return self.layers(x) + x_residual
        # return self.layers(x)


def create_mlp(
    input_dim: int,
    layers_dim: list,
    dropout_ratio: float,
    norm_func=BatchNorm1D,
    activation_func=nn.ReLU,
    last_relu: bool = True,
    special_init=False,
):
    """This is a different impl of MLP.
    It adds the Kaiming initialization and ReZero skip connection.
    """
    output_dim = layers_dim[-1]

    if len(layers_dim) == 1:
        layers = [nn.Linear(input_dim, output_dim)]
        if last_relu:
            layers.append(activation_func())
        if norm_func is not None:
            layers.append(norm_func(output_dim))

    else:
        layers_dim = [input_dim] + layers_dim
        layers = []
        for l_i, (ld, ld_p1) in enumerate(zip(layers_dim[:-1], layers_dim[1:])):
            use_norm = norm_func is not None and (
                l_i < (len(layers_dim) - 1) or last_relu
            )
            is_last_layer = l_i == len(layers_dim) - 2
            layers.append(nn.Linear(ld, ld_p1, bias=not use_norm))
            if use_norm:
                layers.append(norm_func(ld_p1))
            if not is_last_layer or last_relu:
                layers.append(activation_func())
            if dropout_ratio != 0:
                layers.append(nn.Dropout(dropout_ratio))
    layers = nn.Sequential(*layers)
    layers = MyResidual(layers, input_dim, layers_dim[-1])
    return layers


class LSTMWrapper(nn.Module):
    def __init__(self, input_size, hidden_size, batch_first=True, **kwargs):
        super(LSTMWrapper, self).__init__()
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=batch_first, **kwargs)

    def forward(self, x):
        """Make sure all shape of x works for LSTM
        LSTM will use -2 dim as sequence
        inputs:
            x [..., seq, feat]

        """
        orig_shape = x.shape
        x2, hidden = self.lstm(x.view(-1, *orig_shape[-2:]))
        x2 = x2.view(*orig_shape[:-1], x2.shape[-1])

        return x2, hidden


class PositionalEncoding(nn.Module):
    def __init__(self, d_model: int, dropout: float = 0.1, max_len: int = 5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)

        position = torch.arange(max_len).unsqueeze(1)
        div_term = torch.exp(
            torch.arange(0, d_model, 2) * (-math.log(10000.0) / d_model)
        )
        pe = torch.zeros(max_len, d_model)
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        self.register_buffer("pe", pe)

    def get_encoding(self, length: int):
        return self.pe[:length]

    def forward(self, x):
        """
        Arguments:
            x: Tensor, shape ``[seq_len, batch_size, embedding_dim]``
        """
        x = x + self.pe[: x.size(0)]
        return self.dropout(x)


class GaussianDropout(nn.Module):
    """
    Code borrowed from https://www.kaggle.com/code/cepheidq/gaussian-dropout-for-pytorch
    """

    def __init__(self, p: float = 0.5):
        """
        Multiplicative Gaussian Noise dropout with N(1, p/(1-p))
        It is NOT (1-p)/p like in the paper, because here the
        noise actually increases with p. (It can create the same
        noise as the paper, but with reversed p values)

        Source:
        Dropout: A Simple Way to Prevent Neural Networks from Overfitting
        https://www.cs.toronto.edu/~rsalakhu/papers/srivastava14a.pdf

        :param p: float - determines the the standard deviation of the
        gaussian noise, where sigma = p/(1-p).
        """
        super().__init__()
        assert 0 <= p < 1
        self.t_mean = torch.ones((0,))
        self.shape = ()
        self.p = p
        self.t_std = self.compute_std()

    def compute_std(self):
        return self.p / (1 - self.p)

    def forward(self, t_hidden):
        if self.training and self.p > 0.0:
            if self.t_mean.shape != t_hidden.shape:
                self.t_mean = torch.ones_like(
                    input=t_hidden, dtype=t_hidden.dtype, device=t_hidden.device
                )
            elif self.t_mean.device != t_hidden.device:
                self.t_mean = self.t_mean.to(
                    device=t_hidden.device, dtype=t_hidden.dtype
                )

            t_gaussian_noise = torch.normal(self.t_mean, self.t_std)
            t_hidden = t_hidden.mul(t_gaussian_noise)
        return t_hidden
