import torch
import torch.nn as nn

from aic_bsd_model.models.networks.basic_network import create_anchor
from aic_bsd_model.models.networks.encoders import (
    CrossAttentionFusion,
    PointNetEncoder,
    PointNetEncoderImpl,
    TrajEncoderNet_Transformer,
    TrajLSTMNet,
    TransformerEncoder,
)
from aic_bsd_model.models.networks.transformer_util import TransformerWrapper


# Lap encoder with self-attention-based fusion for trajectory and map segments
class LapEncoderWithSelfAttentionFusion(nn.Module):
    def __init__(
        self,
        traj_input_dim,
        map_input_dim,
        hidden_dim,
        num_attention_heads,
        num_layers,
        dropout=0.1,
    ):
        super(LapEncoderWithSelfAttentionFusion, self).__init__()
        self.local_trajectory_encoder = TransformerEncoder(
            traj_input_dim, hidden_dim, num_attention_heads, num_layers, dropout
        )
        self.map_encoder = TransformerEncoder(
            map_input_dim, hidden_dim, num_attention_heads, num_layers, dropout
        )

        # Self-attention fusion layer across combined sequence of trajectory and map segments
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim * 2,
            nhead=num_attention_heads,
            dropout=dropout,
            batch_first=True,
        )
        self.fusion_encoder = nn.Sequential(
            nn.TransformerEncoder(encoder_layer, num_layers=num_layers),
            nn.Linear(hidden_dim * 2, hidden_dim),
        )
        self.layer_norm = nn.LayerNorm(hidden_dim)

    def forward(
        self, batch, lap_data, map_data, trajectory_attention_masks, map_attention_masks
    ):
        """
        lap_data: Tensor of shape (batch_size, num_segments, max_length_traj, input_dim_trajectory)
        map_data: Tensor of shape (batch_size, num_segments, max_length_map, input_dim_map)
        trajectory_attention_masks: Tensor of shape (batch_size, num_segments, max_length_traj)
        map_attention_masks: Tensor of shape (batch_size, num_segments, max_length_map)
        """
        batch_size, num_segments, max_length_traj, input_dim_trajectory = lap_data.shape
        _, _, max_length_map, input_dim_map = map_data.shape

        # Encode each trajectory and map segment separately with attention masking
        trajectory_segment_encodings = []
        map_segment_encodings = []

        for i in range(num_segments):
            # Extract and mask out padding for each segment
            # Shape: (batch_size, max_length_traj, input_dim_trajectory)
            trajectory_segment = lap_data[:, i, :, :]
            # Shape: (batch_size, max_length_map, input_dim_map)
            map_segment = map_data[:, i, :, :]
            # Shape: (batch_size, max_length_traj)
            traj_mask = trajectory_attention_masks[:, i, :]
            # Shape: (batch_size, max_length_map)
            map_mask = map_attention_masks[:, i, :]

            # Apply the masks to the segments
            masked_trajectory_segment = trajectory_segment * traj_mask.unsqueeze(-1)
            masked_map_segment = map_segment * map_mask.unsqueeze(-1)

            # Local encodings with separate attention masking
            # Shape: (batch_size, max_length_traj, embed_dim)
            trajectory_i_encoding = self.local_trajectory_encoder(
                masked_trajectory_segment
            )
            # Shape: (batch_size, max_length_map, embed_dim)
            map_i_encoding = self.map_encoder(masked_map_segment)

            # Pool to get segment-level representations, ignoring padding
            trajectory_representation = (
                trajectory_i_encoding * traj_mask.unsqueeze(-1)
            ).sum(dim=1) / (traj_mask.sum(dim=1, keepdim=True) + 1e-8)
            map_representation = (map_i_encoding * map_mask.unsqueeze(-1)).sum(
                dim=1
            ) / (map_mask.sum(dim=1, keepdim=True) + 1e-8)

            # Store encoded segment representations
            trajectory_segment_encodings.append(trajectory_representation)
            map_segment_encodings.append(map_representation)

        # Stack segment representations to form separate sequences
        # Shape: (batch_size, num_segments, embed_dim)
        trajectory_sequence = torch.stack(trajectory_segment_encodings, dim=1)
        # Shape: (batch_size, num_segments, embed_dim)
        map_embedding = torch.stack(map_segment_encodings, dim=1)
        # To make this compatible with TransformerEncoder
        # (batch_size, num_segments, 1, embed_dim)
        map_sequence = map_embedding[..., None, :]

        # Concatenate trajectory and map sequences along the sequence dimension
        # Shape: (batch_size, 2 * num_segments, embed_dim)
        fused_sequence = torch.cat((trajectory_sequence, map_embedding), dim=2)

        # Apply self-attention fusion on the combined sequence
        # Shape: (batch_size, 2 * num_segments, embed_dim)
        fused_output = self.fusion_encoder(fused_sequence)

        # Final lap representation with mean pooling over the sequence dimension.
        # Shape: (batch_size, embed_dim)
        lap_fused_traj_and_map_representation = self.layer_norm(
            fused_output.mean(dim=1)
        )
        ret = {
            "lap_encoding": lap_fused_traj_and_map_representation,
            "seg_encoding": fused_output,
            "map_encoding": map_sequence,
        }
        return ret


class LapEncoderWithCrossAttentionFusion(nn.Module):
    def __init__(
        self,
        traj_input_dim,
        map_input_dim,
        hidden_dim,
        num_attention_heads,
        num_layers,
        dropout=0.1,
        segment_dropout_ratio=0.0,
        lap_emb_use_cls_global_token=False,
    ):
        super(LapEncoderWithCrossAttentionFusion, self).__init__()
        self.local_trajectory_encoder = TransformerEncoder(
            traj_input_dim, hidden_dim, num_attention_heads, num_layers, dropout
        )
        self.map_encoder = TransformerEncoder(
            map_input_dim, hidden_dim, num_attention_heads, num_layers, dropout
        )
        self.cross_attention_fusion = CrossAttentionFusion(
            hidden_dim, num_attention_heads, dropout
        )

        # Optional: another global transformer encoder after cross-attention

        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_attention_heads,
            dropout=dropout,
            batch_first=True,
        )
        self.global_transformer = nn.TransformerEncoder(
            encoder_layer, num_layers=num_layers
        )
        # self.cls_token = nn.Embedding()
        self.lap_emb_use_cls_global_token = lap_emb_use_cls_global_token
        if self.lap_emb_use_cls_global_token:
            # Learnable [CLS] token
            self.cls_token = nn.Parameter(torch.randn(1, 1, hidden_dim))

        self.layer_norm = nn.LayerNorm(hidden_dim)
        self.segment_dropout_ratio = segment_dropout_ratio

    def forward(
        self,
        batch,
        lap_data,
        map_data,
        trajectory_attention_masks,
        map_attention_masks,
    ):
        """
        lap_data: Tensor of shape (batch_size, num_segments, max_length_traj, input_dim_trajectory)
        map_data: Tensor of shape (batch_size, num_segments, max_length_map, input_dim_map)
        trajectory_attention_masks: Tensor of shape (batch_size, num_segments, max_length_traj)
        map_attention_masks: Tensor of shape (batch_size, num_segments, max_length_map)
        """
        batch_size, num_segments, max_length_traj, input_dim_trajectory = lap_data.shape
        _, _, max_length_map, input_dim_map = map_data.shape

        # Encode each trajectory and map segment separately with attention masking
        trajectory_segment_encodings = []
        map_segment_encodings = []

        for i in range(num_segments):
            # Mask out padding for each segment during local encoding
            # Shape: (batch_size, max_length_traj, input_dim_trajectory)
            trajectory_segment = lap_data[:, i, :]
            # Shape: (batch_size, max_length_map, input_dim_map)
            map_segment = map_data[:, i, :, :]
            # Shape: (batch_size, max_length_traj)
            traj_mask = trajectory_attention_masks[:, i, :]
            # Shape: (batch_size, max_length_map)
            map_mask = map_attention_masks[:, i, :]

            # Apply the trajectory attention mask to the trajectory segment. Add last dim with unsqueeze so that multiplication works
            masked_trajectory_segment = trajectory_segment * traj_mask.unsqueeze(-1)
            masked_map_segment = map_segment * map_mask.unsqueeze(-1)

            # Local encodings with separate attention masking
            # Shape: (batch_size, max_length_traj, hidden_dim)
            traj_i_encoding = self.local_trajectory_encoder(masked_trajectory_segment)
            # Shape: (batch_size, max_length_traj, hidden_dim)
            map_i_encoding = self.map_encoder(masked_map_segment)

            # Local encodings with separate attention masking

            # Pool to get segment-level representations, ignoring padded tokens
            # sum along the sequence dimension
            trajectory_i_representation = (
                traj_i_encoding * traj_mask.unsqueeze(-1)
            ).sum(dim=1) / (traj_mask.sum(dim=1, keepdim=True) + 1e-8)
            map_i_representation = (map_i_encoding * map_mask.unsqueeze(-1)).sum(
                dim=1
            ) / (map_mask.sum(dim=1, keepdim=True) + 1e-8)

            trajectory_segment_encodings.append(trajectory_i_representation)
            map_segment_encodings.append(map_i_representation)

        # Stack segment representations to create separate sequences
        # Shape: (batch_size, num_segments, hidden_dim)
        trajectory_sequence = torch.stack(trajectory_segment_encodings, dim=1)
        # Shape: (batch_size, num_segments, hidden_dim)
        map_sequence = torch.stack(map_segment_encodings, dim=1)

        # Apply cross-attention where trajectory attends to the map
        # Shape: (batch_size, num_segments, hidden_dim)
        seg_level_fused_traj_map_encodings = self.cross_attention_fusion(
            trajectory_sequence, map_sequence
        )

        # dropout segments
        if self.segment_dropout_ratio > 0.0:
            N = seg_level_fused_traj_map_encodings.shape[1]
            num_zero_out = int(N * min(1.0, self.segment_dropout_ratio))
            segment_mask = torch.ones(
                (seg_level_fused_traj_map_encodings.shape[0], N),
                device=seg_level_fused_traj_map_encodings.device,
            )  # Start with all ones
            # TODO (deepak.gopinath) look into removing this for loop
            for b in range(seg_level_fused_traj_map_encodings.shape[0]):
                # Randomly pick indices
                zero_indices = torch.randperm(
                    N, device=seg_level_fused_traj_map_encodings.device
                )[:num_zero_out]
                segment_mask[b, zero_indices] = 0  # Set selected indices to zero

            # Expand mask for feature dimension and apply to the tensor
            segment_mask = segment_mask.unsqueeze(-1)  # Shape: (B, N, 1)
            seg_level_fused_traj_map_encodings = (
                seg_level_fused_traj_map_encodings * segment_mask
            )

        if self.lap_emb_use_cls_global_token:
            # Prepend the [CLS] token
            # Shape: (batch_size, 1, hidden_dim)
            cls_tokens = self.cls_token.expand(batch_size, -1, -1)
            # Shape: (batch_size, num_segments + 1, hidden_dim)
            cls_prepended_cross_attention_output = torch.cat(
                (cls_tokens, seg_level_fused_traj_map_encodings), dim=1
            )
            # Global transformer layer with combined segments
            # Shape: (batch_size, num_segments+1, hidden_dim)
            global_encoding = self.global_transformer(
                cls_prepended_cross_attention_output
            )
            # Shape: (batch_size, hidden_dim)
            lap_fused_traj_and_map_representation = self.layer_norm(
                global_encoding[:, 0, :]
            )
        else:
            global_encoding = self.global_transformer(
                seg_level_fused_traj_map_encodings
            )
            # Final lap representation with mean pooling over num segments
            # Shape: (batch_size, embed_dim)
            lap_fused_traj_and_map_representation = self.layer_norm(
                global_encoding.mean(dim=1)
            )

        map_sequence = map_sequence[..., None, :]
        ret = {
            "lap_encoding": lap_fused_traj_and_map_representation,
            "seg_encoding": seg_level_fused_traj_map_encodings,
            "map_encoding": map_sequence,
        }

        return ret


def add_diff_feat(feat, diff_feat, valid):
    """
    Compute the xy diff and concat to the feature.
    feat: [..., sequence, feat]
    diff_feat: [..., sequence, 2]
    valid: [..., sequence, 1]

    output: [..., sequence, feat+2]
    """
    # [..., sequence-1, 2]
    diff = diff_feat[..., 1:, :] - diff_feat[..., :-1, :]
    diff = diff * (valid[..., 1:, :] * valid[..., :-1, :])
    # [..., sequence, 2]
    diff = torch.cat([diff, torch.zeros_like(diff[..., :1, :])], dim=-2)
    # [..., sequence, feat+2]
    feat = torch.cat([feat, diff], dim=-1)
    return feat


def compute_diff_feat(
    lap_segments_data, lap_segments_data_valid, map_data, map_data_valid
):
    # [batch, seg, time, feat+2]
    lap_segments_data = add_diff_feat(
        lap_segments_data, lap_segments_data[..., :2], lap_segments_data_valid
    )

    # [batch, seg, points, lane, xy]
    map_data2 = map_data.view(*map_data.shape[:-1], 3, 2)

    # [batch, seg, lane, points, 2(xy)]
    map_data2 = map_data2.permute(0, 1, 3, 2, 4)
    # [batch, seg, lane, points, 4(xy,xy_diff)]
    map_data2 = add_diff_feat(
        map_data2, map_data2[..., :2], map_data_valid[..., None, :, :]
    )
    return lap_segments_data, map_data2


class TransformerBaselineEncoder(nn.Module):
    """
    This is a baseline encoder that uses a transformer encoder for the trajectory and a pointnet encoder for the map.
    """

    def __init__(
        self,
        config,
        input_dim,
        map_input_dim,
        hidden_dim,
        is_generate_lap_embedding=True,
        map_channel_dropout=0.0,
    ):
        """
        :param config: config dictionary
        :param input_dim: dimensionality of the main input (positions).
        :param map_input_dim: map input dimensionality.
        :param hidden_dim: dimensionality of model and output.
        """
        super().__init__()
        self.config = config
        self.dropout_ratio = config["dropout"]
        self.transformer_dropout_ratio = config["transformer_dropout"]
        self.is_generate_lap_embedding = is_generate_lap_embedding
        self.lap_emb_use_cls_global_token = config["lap_emb_use_cls_global_token"]
        self.transformer_hidden_dim_multiplier = config.get(
            "transformer_hidden_dim_multiplier", 2
        )

        self.hidden_dim = hidden_dim
        input_dim += 2  # +2 to add the xy_diff feature

        if self.config["traj_encoder_type"] == "TransformerTrajEncoder":
            self.traj_encoder = TrajEncoderNet_Transformer(
                input_dim, hidden_dim=hidden_dim, params=self.config, seq_dim=-2
            )
        elif self.config["traj_encoder_type"] == "PointNetEncoder":
            self.traj_encoder = PointNetEncoder(
                input_dim, hidden_dim=hidden_dim, params=self.config, seq_dim=-2
            )
        elif self.config["traj_encoder_type"] == "TrajLSTMNet":
            self.traj_encoder = TrajLSTMNet(config, input_dim, hidden_dim=hidden_dim)
        else:
            raise NotImplementedError("")

        # Map input dim (x, y, x_diff, y_diff)
        num_lanes = 3
        self.map_encoder = PointNetEncoder(
            input_dim=2 * (map_input_dim // num_lanes),
            hidden_dim=hidden_dim,
            params=self.config,
            seq_dim=-2,
        )
        self.map_channel_dropout = map_channel_dropout
        # cross attention between map embedding and last step of past trajectory embedding.
        self.map_attention1 = TransformerWrapper(
            nn.TransformerEncoderLayer,
            hidden_dim,
            self.config["num_attention_heads"],
            self.transformer_hidden_dim_multiplier * hidden_dim,
            self.transformer_dropout_ratio,
        )
        self.seg_map_attention1 = TransformerWrapper(
            nn.TransformerDecoderLayer,
            hidden_dim,
            self.config["num_attention_heads"],
            self.transformer_hidden_dim_multiplier * hidden_dim,
            self.transformer_dropout_ratio,
        )

        self.seg_map_attention2 = TransformerWrapper(
            nn.TransformerDecoderLayer,
            hidden_dim,
            self.config["num_attention_heads"],
            self.transformer_hidden_dim_multiplier * hidden_dim,
            self.transformer_dropout_ratio,
        )
        if self.is_generate_lap_embedding:
            if self.lap_emb_use_cls_global_token:
                # Learnable [CLS] token
                self.cls_token = create_anchor([1, 1, hidden_dim])
                self.layer_norm = nn.LayerNorm(hidden_dim)
                self.cls_global_transformer = TransformerWrapper(
                    nn.TransformerEncoderLayer,
                    hidden_dim,
                    self.config["num_attention_heads"],
                    self.transformer_hidden_dim_multiplier * hidden_dim,
                    self.transformer_dropout_ratio,
                    use_position_emb=True,
                )

    def forward(
        self,
        batch,
        lap_segments_data,
        map_data,
        lap_segments_data_valid=None,
        map_data_valid=None,
    ):
        """
        Runs over the graph, and creates an output from each node.
        :param batch: batch dictionary
        :param lap_segments_data: lap segments data
        :param map_data: map data
        :return: result_tensor, node_states
            lap_encoding: [batch_size, encoding_dim]
            seg_encoding: [batch_size, num_segments, encoding_dim]
            map_encoding: [batch_size, num_segments, num_lanes, encoding_dim]
        """
        if lap_segments_data_valid is None:
            lap_segments_data_valid = batch["lap_segments_data_valid"]
        if map_data_valid is None:
            map_data_valid = batch["map_data_valid"]

        # lap_segments_data [batch, segment, time, xy_feat]
        # map_data2         [batch, segment, num_lanes, num_points, xy_feat]
        lap_segments_data, map_data2 = compute_diff_feat(
            lap_segments_data, lap_segments_data_valid, map_data, map_data_valid
        )

        # [batch, segment, feat]
        seg_traj_emb = self.traj_encoder(lap_segments_data * lap_segments_data_valid)

        # [batch, segment, num_lanes, feat]
        seg_map_emb = self.map_encoder(map_data2)
        if self.map_channel_dropout > 0.0:
            if self.map_channel_dropout < 1.0:
                if self.training:
                    B = seg_map_emb.shape[0]
                    # (B, 1)
                    mask = (
                        torch.rand(B, 1, device=seg_map_emb.device)
                        > self.map_channel_dropout
                    ).float()
                    # unsqueeze makes mask into (B, 1, 1, 1) and matches the dimensionality of the seg_map_emb
                    seg_map_emb = seg_map_emb * mask.unsqueeze(-1).unsqueeze(-1)
            elif self.map_channel_dropout == 1.0:
                B = seg_map_emb.shape[0]
                mask = (
                    torch.rand(B, 1, device=seg_map_emb.device)
                    > self.map_channel_dropout
                ).float()
                seg_map_emb = seg_map_emb * mask.unsqueeze(-1).unsqueeze(-1)

        # [batch, segment, 1, feat]
        seg_traj_emb, _ = self.seg_map_attention1(
            seg_traj_emb[..., None, :], seg_map_emb
        )
        seg_map_emb = self.map_attention1(seg_map_emb)
        # [batch, num_seg, 1, feat]
        seg_traj_emb, _ = self.seg_map_attention2(seg_traj_emb, seg_map_emb)

        # pull the fused traj and map encoding and remove the "lane" dimension
        # [batch, segment, feat]
        seg_traj_emb = seg_traj_emb[..., 0, :]

        batch_size, num_segments = seg_traj_emb.shape[:2]

        lap_emb = None
        if self.is_generate_lap_embedding:
            # Squash the segment dim to get lap embedding
            if not self.lap_emb_use_cls_global_token:
                # [batch, 1, feat]
                lap_emb = torch.max(seg_traj_emb, dim=1)[0]
            else:
                # Prepend the [CLS] token
                # Shape: (batch_size, 1, hidden_dim)
                cls_tokens = self.cls_token.expand(batch_size, -1, -1)
                # Shape: (batch_size, num_segments + 1, hidden_dim)
                all_tokens = torch.cat((cls_tokens, seg_traj_emb), dim=1)
                # Global transformer layer with combined segments
                # Shape: (batch_size, num_segments+1, hidden_dim)
                global_encoding = self.cls_global_transformer(all_tokens)
                # Shape: (batch_size, hidden_dim)
                lap_emb = self.layer_norm(global_encoding[:, 0])

        ret = {
            "lap_encoding": lap_emb,
            "seg_encoding": seg_traj_emb,
            "map_encoding": seg_map_emb,
        }
        return ret


class PointTransformerEncoder(nn.Module):
    """
    This is a baseline encoder that uses a PointNet encoder for the trajectory and map,
    then cross attention between trajectory time sequence and map.
    """

    def __init__(
        self,
        config,
        input_dim,
        map_input_dim,
        hidden_dim,
    ):
        """
        :param config: config dictionary
        :param input_dim: dimensionality of the main input (positions).
        :param map_input_dim: map input dimensionality.
        :param hidden_dim: dimensionality of model and output.
        """
        super().__init__()
        self.config = config
        self.dropout_ratio = config["dropout"]
        input_dim += 2  # +2 to add the xy_diff feature

        self.hidden_dim = hidden_dim

        self.traj_encoder = PointNetEncoderImpl(
            input_dim, hidden_dim=hidden_dim, params=self.config, seq_dim=-2
        )
        # Map input dim (x, y, x_diff, y_diff)
        num_lanes = 3
        self.map_encoder = PointNetEncoderImpl(
            input_dim=2 * (map_input_dim // num_lanes),
            hidden_dim=hidden_dim,
            params=self.config,
            seq_dim=-2,
        )
        # cross attention between map embedding and last step of past trajectory embedding.
        self.map_attention1 = TransformerWrapper(
            nn.TransformerEncoderLayer,
            hidden_dim,
            8,
            4 * hidden_dim,
            self.dropout_ratio,
            use_position_emb=True,
        )
        self.seg_map_attention1 = TransformerWrapper(
            nn.TransformerDecoderLayer,
            hidden_dim,
            8,
            4 * hidden_dim,
            self.dropout_ratio,
            use_position_emb=True,
        )

        self.seg_map_attention2 = TransformerWrapper(
            nn.TransformerDecoderLayer,
            hidden_dim,
            8,
            4 * hidden_dim,
            self.dropout_ratio,
        )

    def forward(
        self,
        batch,
        lap_segments_data,
        map_data,
    ):
        """
        Runs over the graph, and creates an output from each node.
        :param batch: batch dictionary
        :param lap_segments_data: lap segments data
        :param map_data: map data
        :return: result_tensor, node_states
            lap_encoding: [batch_size, encoding_dim]
            seg_encoding: [batch_size, num_segments, encoding_dim]
            map_encoding: [batch_size, num_segments, num_lanes, encoding_dim]
        """
        lap_segments_data_valid = batch["lap_segments_data_valid"]
        map_data_valid = batch["map_data_valid"]
        lap_segments_data, map_data2 = compute_diff_feat(
            lap_segments_data, lap_segments_data_valid, map_data, map_data_valid
        )
        traj_embedding, traj_embedding_seq = self.traj_encoder(lap_segments_data)
        # seg_map_emb [batch, segment, num_lanes, feat]
        # map_embedding_seq [batch, segment, num_lanes, num_points, feat]
        seg_map_emb, map_embedding_seq = self.map_encoder(map_data2)
        map_embedding_mean = torch.mean(map_embedding_seq, dim=-2)
        seg_map_emb = torch.cat([map_embedding_mean, seg_map_emb], dim=-2)

        seg_traj_emb, _ = self.seg_map_attention1(traj_embedding_seq, seg_map_emb)
        seg_map_emb = self.map_attention1(seg_map_emb)
        seg_traj_emb, _ = self.seg_map_attention2(seg_traj_emb, seg_map_emb)
        seg_traj_emb = torch.max(seg_traj_emb, dim=-2)[0]

        # TODO (deepak.gopinath) doesn't make sense to do just a max over the segment encoding to get a lap encoding.
        # Squash the segment dim to get lap embedding
        lap_emb = torch.max(seg_traj_emb, dim=1)[0]
        ret = {
            "lap_encoding": lap_emb,
            "seg_encoding": seg_traj_emb,
            "map_encoding": seg_map_emb,
        }
        return ret
