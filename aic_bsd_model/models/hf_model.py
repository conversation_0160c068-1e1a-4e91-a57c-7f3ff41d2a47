import collections
import weakref
from collections import defaultdict
from pathlib import Path
from typing import Any, Dict, List, Union

import numpy as np
import torch
import wandb
from torch import nn
from torch.utils.data import DataLoader
from transformers import (
    Trainer,
    TrainerCallback,
    TrainerControl,
    TrainerState,
    TrainingArguments,
)

from aic_bsd_model.utils.shell import delete_tree, move_tree

TRAINING_EPOCH = "train"
EVAL_EPOCH = "val"


def move_to_device(batch, device):
    if device is None:
        return
    if isinstance(batch, dict):
        for k, v in batch.items():
            if isinstance(v, dict):
                move_to_device(v, device)
            elif isinstance(v, torch.Tensor):
                batch[k] = v.to(device)
    elif isinstance(batch, list):
        for v in batch:
            move_to_device(v, device)
    return


def compute_param_change(optimized_parameters, prev_params):
    norm_changes = []
    for p, prev_p in zip(optimized_parameters, prev_params):
        norm_changes.append(torch.norm(p.data - prev_p))
    if not norm_changes:
        return torch.zeros([1])
    return torch.norm(torch.stack(norm_changes))


class HuggingFaceModel(nn.Module):
    def __init__(self, config, train_dataset):
        super().__init__()
        self.config = config
        self.epoch = 0
        self.epoch_type = None
        self.batch_idx = 0
        self.vis_interval = config.get("vis_interval", 10)
        self.vis_num_instances = config.get("vis_num_instances", 3)
        self.vis_dataloader = DataLoader(
            train_dataset,
            batch_size=self.vis_num_instances,
            collate_fn=train_dataset.get_collator(config),
        )
        self._keys_to_ignore_on_save = None

        self.metric_keys = config.get("target_metric_keys", [])
        self.num_train_vis_batch = config.get("num_train_vis_batch", 1)
        self.num_eval_vis_batch = config.get("num_eval_vis_batch", 1)
        # This is used to keep all items to be logged at the end of epoch
        self.logs = {}
        # This is used to keep metrics per batch,
        # which will be reduced to a single value for logging.
        self.metrics = defaultdict(lambda: list())
        # self.loss_func_dict = config["loss_func_dict"]

        self.reset_epoch()

    def my_forward(self, batch):
        raise NotImplementedError()

    def get_log_dict(self):
        """
        Return the dict to be logged, which will be logged by the trainer.
        Do not call wandb.log() in this function.

        By default this is called depending on epoch end.
        Or logging_steps setting in hf trainer, can be epoch or num_steps.
        """
        logs = self.logs
        # Reset logs
        return logs

    def forward(self, batch):
        if "device" not in batch:
            if torch.cuda.is_available():
                batch["device"] = torch.device("cuda")
            else:
                batch["device"] = torch.device("cpu")
            move_to_device(batch, batch["device"])

        # This run vis for training epochs
        self._run_training_vis(batch["device"], self.logs)

        batch["epoch"] = self.epoch
        batch["epoch_type"] = self.epoch_type
        batch["batch_idx"] = self.batch_idx

        outputs = self.my_forward(batch)
        loss, extra_loss = self.compute_loss(batch, outputs, self.metrics, {})

        self._run_val_vis(batch, loss, extra_loss, self.logs)
        self.batch_idx += 1
        return {"loss": loss, "outputs": outputs, "extra_loss": extra_loss}

    def on_training_epoch_begin(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        **kwargs,
    ):
        """This is called on start of training epoch"""
        self.epoch_type = TRAINING_EPOCH
        self.epoch = state.epoch
        self.reset_epoch()

    def on_eval_epoch_begin(self):
        """
        There's only training epoch in HF trainer, eval is done at the end of training epoch.
        Or set `eval_strategy` for trainer
        """
        self.epoch_type = EVAL_EPOCH
        self.reset_epoch()

    def visualize_batch(self, batch, loss, extra_loss, count=3):
        """Visualize the batch.
        return:
            vis_logs: dict
                a dict that wandb can log at epoch end.
        """
        raise NotImplementedError()

    def compute_loss(self, batch, outputs, out_metrics, additional_args=None):
        raise NotImplementedError()

    def reset_epoch(self):
        """Reset internal state on the start of a new epoch"""
        self.batch_idx = 0
        # This is the metrics for the entire epoch.
        # Should be a list of numbers
        self.metrics = defaultdict(lambda: list())

        # Don't reset self.logs, HF may call get_log_dict() based on global_step, etc.

    def should_vis_batch(self):
        """This is called every batch, to determine if to visualize.
        By default, we only visualize on the first batch in vis epoch.
        """
        if int(self.epoch) % self.vis_interval == 0:
            if self.epoch_type == TRAINING_EPOCH:
                if self.batch_idx <= self.num_train_vis_batch:
                    return True
            elif self.epoch_type == EVAL_EPOCH:
                if self.batch_idx <= self.num_eval_vis_batch:
                    return True
        return False

    def should_vis_epoch(self):
        """This is called every batch, to determine if to visualize.
        By default, we only visualize on the first batch in vis epoch.
        """
        if int(self.epoch) % self.vis_interval == 0:
            return True
        return False

    def _run_training_vis(self, device, logs):
        """
        This is called to visualize training batches on self.vis_dataset.
        And this function will always visualize the first few batches in the dataset, no shuffle.
        """
        target_epoch = TRAINING_EPOCH
        if self.vis_dataloader is None or self.epoch_type != target_epoch:
            return
        if not self.should_vis_batch():
            return
        with torch.no_grad():
            # This ensures that the visualization samples are note added to the batch metrics list for classification score computation
            additional_args = {"append_cf_metrics": False}
            train_loader = iter(self.vis_dataloader)
            for i in range(self.num_train_vis_batch):
                batch = next(train_loader)
                batch = {
                    k: v.to(device) if isinstance(v, torch.Tensor) else v
                    for k, v in batch.items()
                }
                self.prep_batch(batch)
                batch["epoch"] = self.epoch
                batch["epoch_type"] = target_epoch
                batch["batch_idx"] = 0
                batch["device"] = device
                outputs = self.my_forward(batch)
                loss, extra_loss = self.compute_loss(
                    batch, outputs, self.metrics, additional_args
                )
                vis_logs = self.visualize_batch(
                    batch, loss, extra_loss, count=self.vis_num_instances
                )

                vis_logs = {f"vis/{target_epoch}/" + k: v for k, v in vis_logs.items()}
                logs.update(vis_logs)

    def _run_val_vis(self, batch, loss, extra_loss, logs):
        """This is called on every forward step (train/val).
        By default, should_vis is True for the 1st batch after vis_interval epochs.
        """
        if not self.should_vis_batch():
            return
        if batch["epoch_type"] == TRAINING_EPOCH:
            # train epoch vis is handled in forward function
            return
        vis_logs = self.visualize_batch(
            batch, loss, extra_loss, count=self.vis_num_instances
        )
        vis_logs = {f"vis/{self.epoch_type}/" + k: v for k, v in vis_logs.items()}
        logs.update(vis_logs)

    def _run_epoch_end_vis(self):
        """This is called at the end of epoch to visualize the metrics."""
        if not self.should_vis_epoch():
            return {}
        vis_logs = self.visualize_epoch()
        vis_logs = {f"vis/{self.epoch_type}/" + k: v for k, v in vis_logs.items()}
        return vis_logs

    def on_training_epoch_end(self):
        """This is called at the end of training epoch.
        Any plotting/logging should use self.logs
        """
        vis_logs = self._run_epoch_end_vis()
        self.logs.update(vis_logs)

    def on_eval_epoch_end(self):
        """This is called at the end of eval epoch.
        Any plotting/logging should use self.logs
        """
        vis_logs = self._run_epoch_end_vis()
        self.logs.update(vis_logs)

    def aggregate_metrics(self):
        """This is called at the end of epoch to
        aggregate all mestrics into float values before logging.
        """
        m = {k: np.mean(v).item() for k, v in self.metrics.items()}
        return m

    def prep_batch(self, batch):
        """Modify batch in place before forward pass"""
        pass

    def visualize_epoch(self):
        return {}

    def on_training_batch_end(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        step: bool,
        **kwargs,
    ):
        """This is called at the end of training batch.
        step: bool
            True if optimizer step is called, False if not, meaning gradient accumulation is done.
        """
        pass

    def reset_log(self):
        """This is called after logging."""
        self.logs = {}


class CustomLoggingCallback(TrainerCallback):
    def __init__(self, model, config_dict):
        self.config_dict = config_dict
        self.prev_params = None
        self._model = weakref.ref(model)
        # If you want to accumulate epoch losses, you can initialize an accumulator here.
        self.epoch_losses = collections.defaultdict(float)
        self.batch_counts = collections.defaultdict(int)
        self.vis_interval = config_dict.get("vis_interval", 40)
        self.save_interval = config_dict.get("save_interval", 40)
        self._trainer = None

    def should_vis(self, state):
        if int(state.epoch) % self.vis_interval == 0:
            if self.model.batch_idx == 0:
                return True
        return False

    @property
    def model(self) -> HuggingFaceModel:
        # This is a weak ref to model.
        # If model is deleted, it will throw.
        return self._model()

    @property
    def trainer(self):
        return self._trainer()

    @trainer.setter
    def trainer(self, trainer):
        self._trainer = weakref.ref(trainer)

    def on_train_begin(
        self, args, state: TrainerState, control: TrainerControl, **kwargs
    ):
        # Initialize previous parameters (only trainable ones)
        self.prev_params = self.get_model_params(kwargs["model"])

    @staticmethod
    def get_model_params(model):
        return [p.cpu().detach() for p in model.parameters() if p.requires_grad]

    def on_epoch_begin(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        **kwargs,
    ):
        # There's only training epoch in HF trainer, eval is done at the end of training epoch.
        self.model.on_training_epoch_begin(args, state, control, **kwargs)

    def on_step_end(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        **kwargs,
    ):
        self.model.on_training_batch_end(args, state, control, step=True, **kwargs)

    def on_substep_end(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        **kwargs,
    ):
        self.model.on_training_batch_end(args, state, control, step=False, **kwargs)

    def on_epoch_end(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        **kwargs,
    ):
        self.model.on_training_epoch_end()

    def on_evaluate(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        **kwargs,
    ):
        self.model.on_eval_epoch_end()

    def on_log(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        **kwargs,
    ):
        """Log everything. Do not call wandb.log() elsewhere.
        This is called depending on logging_steps setting, can be epoch or num_steps.
        Default: log onces per epoch
        """
        epoch_type = self.model.epoch_type
        current_params = self.get_model_params(kwargs["model"])
        param_change = compute_param_change(current_params, self.prev_params)
        self.prev_params = current_params
        logs = kwargs["logs"]
        logs = {f"{epoch_type}/" + k: v for k, v in logs.items()}
        logs[f"{epoch_type}/norm_diff"] = param_change
        model_logs = self.model.get_log_dict()
        if model_logs:
            dup_keys = {k for k in model_logs.keys()} & {k for k in logs.keys()}
            assert (
                not dup_keys
            ), f"Duplicate keys detected, double check to avoid overwrite: {dup_keys}"
            logs.update(model_logs)
        metrics = self.model.aggregate_metrics()
        metrics = {f"{epoch_type}/" + k: v for k, v in metrics.items()}
        # This metrics is used to determine the best model
        logs.update(metrics)

        # logging step must monotonically increase
        # Since global_step doesn't increase during val epoch, we log val epoch as the next step
        # Assume we don't log immediately after 1 step in training
        epoch_idx = 0 if epoch_type == TRAINING_EPOCH else 1
        wandb.log(logs, step=state.global_step + epoch_idx)

        self.model.reset_log()
        return control

    def on_epoch_end(
        self, args, state: TrainerState, control: TrainerControl, **kwargs
    ):
        # At the end of the epoch, print/log the average losses for the epoch.
        # Reset accumulators for the next epoch.
        self.epoch_losses = collections.defaultdict(float)
        self.batch_counts = collections.defaultdict(int)
        self.save_latest_model(args, state, control, **kwargs)
        return control

    def save_latest_model(self, args, state, control, **kwargs):
        """Save the latest model at the end of the epoch."""
        if int(state.epoch) % self.save_interval != 0:
            return

        # Model could be wrapped by deepspeed or other wrapper.
        model = self.trainer.model_wrapped
        orig_output_dir = self.trainer.args.output_dir
        self.trainer.args.output_dir = latest_dir = (
            self.trainer.args.output_dir + "/latest"
        )
        latest_dir_bak = latest_dir + "-bak"
        move_tree(latest_dir, latest_dir_bak)

        self.trainer._save_checkpoint(model, None)
        self.trainer.args.output_dir = orig_output_dir
        delete_tree(latest_dir_bak)


class HFTrainer(Trainer):
    def __init__(self, **kwargs):
        self.vis_epoch = kwargs.pop("vis_epoch", 10)
        super().__init__(**kwargs)
        self.should_vis = False

    def _prepare_inputs(
        self, inputs: Dict[str, Union[torch.Tensor, Any]]
    ) -> Dict[str, Union[torch.Tensor, Any]]:
        """
        This function is called by HF trainer before forward pass.
        """
        inputs["device"] = self.args.device
        self.model.prep_batch(inputs)
        # This conforms to HF trainer interface
        return {"batch": inputs}

    def _evaluate(self, trial, ignore_keys_for_eval, skip_scheduler=False):
        # This is called on eval.
        self.model.on_eval_epoch_begin()
        ret = super()._evaluate(trial, ignore_keys_for_eval, skip_scheduler)
        metrics = self.model.aggregate_metrics()
        metrics = {"eval_" + k: v for k, v in metrics.items()}
        ret.update(metrics)
        return ret
