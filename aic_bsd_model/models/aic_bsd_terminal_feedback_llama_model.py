# the module that takes in some representation of trajectory (either as semantic states or expressed as language) + past teaching (probably necessary and not optional)
# and predicts language that comes after the trial. This is the terminal feedback module. The internal representations extracted
# from this module could be used as additional input to the concurrent feedback module

import copy
import logging
import warnings
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple, Union

import torch
import torch.distributed as dist
from torch import nn
from torch.nn import CrossEntropyLoss, MSELoss
from transformers import LlamaModel, LlamaPreTrainedModel
from transformers.cache_utils import Cache
from transformers.generation.configuration_utils import GenerationConfig
from transformers.generation.logits_process import LogitsProcessorList
from transformers.generation.stopping_criteria import (
    StoppingCriteriaList,
    validate_stopping_criteria,
)
from transformers.modeling_outputs import CausalLMOutputWithPast


class AIC_BSD_Terminal(LlamaPreTrainedModel):
    _tied_weights_keys = ["lm_head.weight"]
    """
    Driving Trajectory-Language Model
    Consumes trajectory data, language data, and a sequence-map. The sequence map tells the model which token goes
     where in the input sequence.
    """

    def __init__(self, config):
        super().__init__(config)
        self.model = LlamaModel(config)
        # TODO (andrewsilva) 5/28/24 -- dynamically accept trajectory encoder network parameters, like traj_input_dim.
        # self.trajectory_encoder = TrajEncoderNet(
        #     traj_input_dim=11, hidden_dim=128, output_dim=config.hidden_size, num_layers=2, dropout_ratio=0.1
        # )
        # Single layer to map from the LLM hidden state back to the trajectory -- Maybe extend to be an MLP?
        # self.trajectory_decoder = nn.Linear(config.hidden_size, 11)
        # self.hidden_size = config.hidden_size
        # self.vocab_size = config.vocab_size

        # prepend cls token to the sequence.
        # self.cls_embedding = nn.Embedding((num_laps, config.hidden_size))
        self.lm_head = nn.Linear(config.hidden_size, config.vocab_size, bias=False)

        # self.transition_mdoel = nn.Linear(config.hidden_size)

        # Initialize weights and apply final processing
        self.post_init()

    def forward(
        self,
        combined_input: torch.FloatTensor = None,
        input_ids: torch.LongTensor = None,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_values: Optional[Union[Cache, List[torch.FloatTensor]]] = None,
        inputs_embeds: Optional[torch.FloatTensor] = None,
        labels: Optional[torch.LongTensor] = None,
        use_cache: Optional[bool] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
        cache_position: Optional[torch.LongTensor] = None,
        # lap_ids: Optional[torch.LongTensor] = None
    ) -> Union[Tuple, CausalLMOutputWithPast]:
        pass
        output_attentions = (
            output_attentions
            if output_attentions is not None
            else self.config.output_attentions
        )  # Should we output attention data?
        output_hidden_states = (
            output_hidden_states
            if output_hidden_states is not None
            else self.config.output_hidden_states
        )  # Should we output hidden states?
        return_dict = (
            return_dict if return_dict is not None else self.config.use_return_dict
        )  # Do we return a dict?
        # Create our input embedding map, with one empty embedding for each token in the input sequence
        inputs_embeds = torch.zeros((*combined_input.size(), self.hidden_size)).to(
            self.device
        )

        if input_ids is not None:
            # Get the word embeddings for each input_id in the input text data that corresponds to the bsd text
            inputs_embeds = self.model.get_input_embeddings()(input_ids)
            # create a multimodal prompt. We can have access to the segment level fused encodings as well as the
            # lap level encoding. Project each of the embeddings into the language space using adapters.
            # # make the multimodal soup.

            # OLD IDEA
            # prepend cls embed from correct lap into this.
            # similar to how cls token is added to ViT
            # index cls embedding matrix using some lap_id list of length B for each batch item? Each of the CLS token
            # can be up projected into the language space. That is this could be n(um_laps, 64). Then 64 to 4096..

        # Give Llama the input embedding sequence as inputs_embeds
        outputs = self.model(
            # input_ids=input_ids,
            attention_mask=attention_mask,
            position_ids=position_ids,
            past_key_values=past_key_values,
            inputs_embeds=inputs_embeds,
            use_cache=use_cache,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
            cache_position=cache_position,
        )

        # Take the final hidden state:
        hidden_states = outputs.last_hidden_state

        # NEW IDEA:
        # The hiddne state can be used an additional side channel information in addition to the posterior skill estimate to the concurrent
        # feedback decoder
        # OLD IDEA
        # grab the hidden state associated with the first token.
        # send it through some layers to go from 4096 to 64. call this 64d z
        # Use that 64d as input the transition MLP to emit 64D next lap z estimate. Get an estimate
        # of the next lap cls, The target of this would be cls_token(numlap+1, :).detach()

        # Additional shaping of z:  Partition z into smaller subsapce and have each subspace regress different lap level metrics.
        # This z vector can be returned from this model, so that it can be conditioning information for the concurrent model.
        return hidden_state
