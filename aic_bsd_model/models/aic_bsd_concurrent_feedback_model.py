# create asimilar model to the IL model from phase 1
# import TrajEncoderNet, MLPDecoder, MapEncoderMarginal, and any other relevant modules necessary for concurrent feedback
import torch
from torch import nn

from aic_bsd_model.models.networks.basic_network import GaussianDropout, create_mlp
from aic_bsd_model.models.networks.decoders import (
    LSTMDecoderNet,
    MetricsDecoderSimpleMLP,
    MetricsTransformerDecoder,
    MLPDecoder,
)
from aic_bsd_model.models.networks.fusion_encoders import TransformerBaselineEncoder
from aic_bsd_model.utils.training_utils import get_future_mask, parse_snippet_batch


class AIC_BSD_Concurrent(nn.Module):
    def __init__(self, config_dict):
        super(AIC_BSD_Concurrent, self).__init__()
        self.config_dict = config_dict
        self.traj_input_dim = config_dict.get("traj_input_dim", None)
        self.map_input_dim = config_dict.get("map_input_dim", None)
        self.hidden_dim = config_dict.get("hidden_dim", None)
        self.z_dim = config_dict.get("z_dim", 32)
        self.padding_value = config_dict.get("padding_value", -2000)
        self.cf_use_trajectory_prediction = config_dict.get(
            "cf_use_trajectory_prediction", False
        )
        self.cf_use_metrics_prediction = config_dict.get(
            "cf_use_metrics_prediction", False
        )
        self.cf_metrics_decoder_type = config_dict.get(
            "cf_metrics_decoder_type", "MetricsDecoderSimpleMLP"
        )
        self.cf_target_metric_keys = config_dict.get("cf_target_metric_keys", None)
        self.cf_map_channel_dropout = config_dict.get("cf_map_channel_dropout", 0.0)
        self.cf_use_teacher_action_encoding = config_dict.get(
            "cf_use_teacher_action_encoding", False
        )
        self.teacher_action_encoding_type = config_dict.get(
            "teacher_action_encoding_type", "existence"
        )

        self.traj_decoder_output_dim = config_dict.get("traj_decoder_output_dim", None)
        self.num_attention_heads = config_dict.get("num_attention_heads", None)
        self.num_layers = config_dict.get("num_layers", None)
        self.predict_delta = config_dict.get("predict_delta", False)
        self.dropout = config_dict.get("dropout", 0.1)
        self.side_channel_dropout = config_dict.get("side_channel_dropout", 0.0)
        self.snippet_past_encoding_dropout = config_dict.get(
            "snippet_past_encoding_dropout", 0.0
        )
        self.use_gaussian_dropout = config_dict.get("use_gaussian_dropout", False)
        self.use_random_side_channel = config_dict.get("use_random_side_channel", False)
        self.use_multi_side_channel = config_dict.get("use_multi_side_channel", False)
        self.gaussian_dropout_ratio = config_dict.get("gaussian_dropout_ratio", 0.5)
        self.is_adaptive = config_dict.get("is_adaptive", False)
        if self.is_adaptive:
            print("Using adaptive CF model")
        else:
            print("Using non-adaptive CF model")
        self.teacher_action_loss_type = config_dict.get(
            "teacher_action_loss_type", "existence"
        )
        self.teacher_action_num_categories = config_dict.get(
            "teacher_action_num_categories", 12
        )

        self.cf_target_sampling_frequency = config_dict.get(
            "cf_target_sampling_frequency", 5
        )
        self.snippet_length_in_sec = config_dict.get("snippet_length_in_sec", 10)
        self.prediction_timestep_in_sec = config_dict.get(
            "prediction_timestep_in_sec", 5
        )
        self.decoded_traj_len_in_sec = config_dict.get("decoded_traj_len_in_sec", 5)
        # the prediction timestep should match the prediction timestep used in the dataset.
        # TODO (deepak.gopinath) ensure that prediction_timestep is retrieved from the dataset and passed in instead of being recomputed
        # to avoid any errors.
        self.prediction_timestep = (
            self.prediction_timestep_in_sec * self.cf_target_sampling_frequency
            if self.prediction_timestep_in_sec is not None
            else None
        )

        self.decoded_traj_len = (
            self.decoded_traj_len_in_sec * self.cf_target_sampling_frequency
        )

        self.cf_encoder_type = config_dict.get(
            "cf_encoder_type", "TransformerTrajEncoder"
        )
        self.cf_traj_decoder_type = config_dict.get(
            "cf_traj_decoder_type", "LSTMDecoder"
        )

        if self.cf_encoder_type == "TransformerTrajEncoder":
            config_dict["traj_encoder_type"] = "TransformerTrajEncoder"
            self.cf_encoder = TransformerBaselineEncoder(
                config_dict,
                self.traj_input_dim,
                self.map_input_dim,
                self.hidden_dim,
                is_generate_lap_embedding=False,
                map_channel_dropout=self.cf_map_channel_dropout,
            )
        else:
            raise ValueError(f"Invalid lap_encoder_type: {self.cf_encoder_type}")

        if self.use_gaussian_dropout:
            self.gaussian_dropout_layer = GaussianDropout(p=self.gaussian_dropout_ratio)
        if self.cf_use_trajectory_prediction:
            if self.cf_traj_decoder_type == "LSTMDecoder":
                self.cf_traj_decoder = LSTMDecoderNet(
                    config_dict,
                    hidden_dim=self.hidden_dim,
                    traj_output_dim=self.traj_decoder_output_dim,
                    max_segment_length=self.decoded_traj_len,  # TODO look at all stats and find the upper bound to minimize model size.
                    # max_segment_length is known exactly for cf model, because we are always predictin the same number of steps
                )
            elif self.cf_traj_decoder_type == "MLPDecoder":
                self.cf_traj_decoder = MLPDecoder(
                    config_dict,
                    hidden_dim=self.hidden_dim,
                    traj_output_dim=self.traj_decoder_output_dim,
                )

            else:
                raise ValueError(
                    f"Invalid cf_traj_decoder_type: {self.cf_traj_decoder_type}"
                )
        else:
            self.cf_traj_decoder = None

        if self.cf_use_metrics_prediction:
            if self.cf_metrics_decoder_type == "MetricsDecoderSimpleMLP":
                self.cf_metrics_decoder = MetricsDecoderSimpleMLP(
                    self.hidden_dim,
                    self.cf_target_metric_keys,
                    dropout_prob=self.dropout,
                    append_key="_cf",
                )
            elif self.cf_metrics_decoder_type == "MetricsTransformerDecoder":
                self.cf_metrics_decoder = MetricsTransformerDecoder(
                    self.hidden_dim,
                    self.cf_target_metric_keys,
                    dropout_prob=self.dropout,
                )
            else:
                raise ValueError(
                    f"Invalid metrics_decoder_type: {self.cf_metrics_decoder_type}"
                )

        if self.cf_use_teacher_action_encoding:
            if self.teacher_action_encoding_type == "existence":
                self.cf_teacher_action_encoder = nn.Sequential(
                    create_mlp(
                        self.teacher_action_num_categories,
                        # TODO (deepak.gopinath) update the hidden dimension stuff.
                        [self.hidden_dim, self.hidden_dim],
                        self.dropout,
                        activation_func=nn.Mish,
                    )
                )
            self.traj_map_teaching_encoding_fusion = nn.Sequential(
                create_mlp(
                    self.hidden_dim + self.hidden_dim,
                    # TODO (deepak.gopinath) update how the combined hidden_state is calculated
                    [self.hidden_dim, self.hidden_dim],
                    self.dropout,
                    activation_func=nn.Mish,
                )
            )
        # TODO (deepak.gopinath) verify what the self.cf_action_decoder_input_dim should be when doing adaptive. Verify the dimension
        # of the lap_level representation (is it just z_dim or z_dim + lstm_hidden_dim) that comes in as side channel information
        # self.cf_action_decoder_input_dim = (
        #     self.hidden_dim if not self.is_adaptive else self.hidden_dim + self.z_dim
        # )
        self.cf_action_decoder_input_dim = self.hidden_dim
        self.teacher_decoder_dim_multiplier = 2 if self.use_multi_side_channel else 1

        if self.is_adaptive:
            self.side_channel_net = nn.Sequential(
                create_mlp(
                    self.hidden_dim + self.hidden_dim,
                    [
                        self.hidden_dim,
                    ],
                    self.dropout,
                    activation_func=nn.Mish,
                ),
                nn.Linear(self.hidden_dim, self.cf_action_decoder_input_dim),
            )

        self.cf_teacher_action_decoder_mlp_in = nn.Sequential(
            create_mlp(
                # TODO (deepak.gopinath) replace the first layer dimensionality to a more meaninful variable so that it matches the
                # dimensionality of the traj_encoding (output traj_attention in DecoderModule in MLPDecoder)
                # the dimensionality should match the hidden size of traj_attention multi-head attention module
                # inside DecoderModule in MLPDecoder
                self.cf_action_decoder_input_dim * self.teacher_decoder_dim_multiplier,
                [
                    # self.cf_action_decoder_input_dim * 2,
                    self.cf_action_decoder_input_dim,
                ],
                self.dropout,
                activation_func=nn.Mish,
            )
        )
        if (
            self.config_dict["teacher_action_loss_type"] == "existence"
            or self.config_dict["teacher_action_loss_type"] == "full_sequence"
        ):
            subtitle_mlp_output_dim = self.config_dict["teacher_action_num_categories"]

        # TODO currently following the same structure as in ICRA IL paper.
        # self.cf_teacher_action_decoder_mlp_existence_net = nn.Sequential(
        #     create_mlp(
        #         self.cf_action_decoder_input_dim,
        #         [
        #             self.cf_action_decoder_input_dim,
        #             # self.cf_action_decoder_input_dim,
        #         ],
        #         self.dropout,
        #         activation_func=nn.Mish,
        #     )
        # )
        self.cf_teacher_action_decoder_mlp_out = nn.Sequential(
            create_mlp(
                self.cf_action_decoder_input_dim * self.teacher_decoder_dim_multiplier,
                [
                    self.cf_action_decoder_input_dim,
                ],
                self.dropout,
                activation_func=nn.Mish,
                # norm_func=None,
            ),
            nn.Linear(self.cf_action_decoder_input_dim, subtitle_mlp_output_dim),
        )

        self.racing_line_prediction_decoder = None

    def forward(self, batch):
        (
            snippet_trajectories,
            snippet_map_segments,
            snippet_full_trajectory_lengths,
            snippet_cf_action_series,
        ) = parse_snippet_batch(batch, return_cf_series=True)
        # check if the dimensionality of snipet_trajectories is 3. This means that this is cf only training, B, max_len, F.
        # If so, unsqueeze in dimension 1, B, 1, max_len, F
        if snippet_trajectories.ndim == 3:
            # (B, 1, :prediction_L, F)
            snippet_past_trajectories = snippet_trajectories[
                ..., : self.prediction_timestep, :
            ].unsqueeze(1)
            snippet_map_segments = snippet_map_segments.unsqueeze(1)
            snippet_full_trajectory_lengths = snippet_full_trajectory_lengths.unsqueeze(
                1
            )
            snippet_predicted_trajectory_lengths = (
                self.decoded_traj_len * torch.ones_like(snippet_full_trajectory_lengths)
            )
        elif snippet_trajectories.ndim == 4:  # (B, num_snippts, :prediction_L, F)
            snippet_past_trajectories = snippet_trajectories[
                ..., : self.prediction_timestep, :
            ]
            snippet_full_trajectory_lengths = snippet_full_trajectory_lengths.unsqueeze(
                1
            )
            snippet_predicted_trajectory_lengths = (
                self.decoded_traj_len * torch.ones_like(snippet_full_trajectory_lengths)
            )

        batch["cf_snippet_past_traj_valid"] = (
            snippet_past_trajectories[..., :1] != self.padding_value
        )
        batch["cf_snippet_map_data_valid"] = (
            snippet_map_segments[..., :1] != self.padding_value
        )
        # Add dimension if needed extract only up to prediction point along the time step dimension
        snippet_past_traj_data = (
            snippet_past_trajectories * batch["cf_snippet_past_traj_valid"]
        )
        # map can be full map
        snippet_map_data = snippet_map_segments * batch["cf_snippet_map_data_valid"]
        snippet_past_traj_valid = batch["cf_snippet_past_traj_valid"]
        snippet_map_data_valid = batch["cf_snippet_map_data_valid"]
        ret = self.cf_encoder(
            batch,
            snippet_past_traj_data,
            snippet_map_data,
            snippet_past_traj_valid,
            snippet_map_data_valid,
        )

        # (B, num_snippets, hidden_sim) #fused traj and map encoding from encoder
        snippet_past_encoding = ret["seg_encoding"]
        if self.cf_use_teacher_action_encoding:
            if self.teacher_action_encoding_type == "existence":
                # extract the teacher action multilabel for the past time horizon
                past_teacher_action_sequence = snippet_cf_action_series[
                    :, : self.prediction_timestep
                ]
                if (
                    past_teacher_action_sequence.ndim == 2
                ):  # else it will be 3 (batch_size, num_snippet, seq_len)
                    past_teacher_action_sequence = (
                        past_teacher_action_sequence.unsqueeze(1)
                    )
                batch_size, num_snippets, seq_len = past_teacher_action_sequence.shape

                # (B, num_snippets, num_classes) each batch item contains number of counts of each label
                cf_past_teacher_action_existence_gt = torch.cat(
                    [
                        torch.bincount(
                            item,
                            minlength=self.config_dict["teacher_action_num_categories"],
                        ).unsqueeze(0)
                        for item in past_teacher_action_sequence.view(-1, seq_len)
                    ],
                    dim=0,
                )
                cf_past_teacher_action_existence_gt = (
                    cf_past_teacher_action_existence_gt.view(
                        batch_size, num_snippets, -1
                    )
                )
                cf_past_teacher_action_existence_gt = (
                    cf_past_teacher_action_existence_gt != 0
                ).float()

                past_teacher_action_encoding = self.cf_teacher_action_encoder(
                    cf_past_teacher_action_existence_gt
                )
                # (B, num_snippets, hidden_dim + hidden_dim)
                snippet_past_encoding = torch.cat(
                    [snippet_past_encoding, past_teacher_action_encoding], dim=-1
                )
                # (B, num_snippets, hidden_dim)
                snippet_past_encoding = self.traj_map_teaching_encoding_fusion(
                    snippet_past_encoding
                )
            elif self.teacher_action_encoding_type == "sequence":
                pass
            # concatenate
        # (B, num_snippets, num_lanes, hidden_dim)
        map_encoding = ret["map_encoding"]
        if self.cf_use_trajectory_prediction:
            (
                predicted_future_trajectories,
                cf_future_pred_mask,
            ) = self.cf_traj_decoder(
                batch,
                snippet_past_encoding,
                snippet_predicted_trajectory_lengths,
                map_encoding,
            )

            if cf_future_pred_mask is None:
                # [B, S, T, 1]
                cf_future_pred_mask = get_future_mask(
                    snippet_predicted_trajectory_lengths
                ).unsqueeze(-1)

        if self.cf_use_metrics_prediction:
            B, N, H = snippet_past_encoding.shape
            cf_metrics_prediction = self.cf_metrics_decoder(
                lap_encoding=None, seg_encoding=snippet_past_encoding.view(B * N, -1)
            )

        if self.is_adaptive:
            # (B, hidden_size)
            side_channel_lap_representation = batch["side_channel_lap_representation"]
            if self.use_gaussian_dropout:
                snippet_past_encoding = self.gaussian_dropout_layer(
                    snippet_past_encoding
                )
            # if self.snippet_past_encoding_dropout > 0.0 and self.training:
            #     B = snippet_past_encoding.shape[0]
            #     mask = (
            #         torch.rand(B, 1, 1, device=snippet_past_encoding.device)
            #         > self.snippet_past_encoding_dropout
            #     ).float()
            #     snippet_past_encoding = snippet_past_encoding * mask
            if self.side_channel_dropout > 0.0:  # and self.training:
                if self.side_channel_dropout < 1.0:
                    if self.training:
                        B = side_channel_lap_representation.shape[0]
                        mask = (
                            torch.rand(
                                B, 1, device=side_channel_lap_representation.device
                            )
                            > self.side_channel_dropout
                        ).float()
                        side_channel_lap_representation = (
                            side_channel_lap_representation * mask
                        )
                elif self.side_channel_dropout == 1.0:
                    B = side_channel_lap_representation.shape[0]
                    mask = (
                        torch.rand(B, 1, device=side_channel_lap_representation.device)
                        > self.side_channel_dropout
                    ).float()
                    side_channel_lap_representation = (
                        side_channel_lap_representation * mask
                    )
            # (B, snippet_num=1, hidden_size+side_channel_size)
            if self.use_random_side_channel:
                side_channel_lap_representation = torch.randn(
                    *side_channel_lap_representation.shape,
                    device=side_channel_lap_representation.device,
                )

            snippet_past_encoding = torch.cat(
                [snippet_past_encoding, side_channel_lap_representation.unsqueeze(1)],
                dim=-1,
            )  #
            # (B, num_snippet=1, hidden_size) # 2*hidden_dim --> hidden_dim
            snippet_past_encoding = self.side_channel_net(snippet_past_encoding)
            # have a small network to compress the side channel information
            # if adaptive, then need to integrate the side channel information into the decoder.
            # can be extracted from bacth
            # pass
            if self.use_multi_side_channel:
                # B, num_snippets, hidden_dim
                cf_teacher_action_enc = self.cf_teacher_action_decoder_mlp_in(
                    torch.cat(
                        [
                            snippet_past_encoding,
                            side_channel_lap_representation.unsqueeze(1),
                        ],
                        dim=-1,
                    )
                )
                # cf_teacher_action_enc = self.cf_teacher_action_decoder_mlp_existence_net(
                #     cf_teacher_action_enc
                # )
                cf_teacher_action_pred = self.cf_teacher_action_decoder_mlp_out(
                    torch.cat(
                        [
                            cf_teacher_action_enc,
                            side_channel_lap_representation.unsqueeze(1),
                        ],
                        dim=-1,
                    )
                )
            else:
                cf_teacher_action_enc = self.cf_teacher_action_decoder_mlp_in(
                    snippet_past_encoding
                )
                # cf_teacher_action_enc = self.cf_teacher_action_decoder_mlp_existence_net(
                #     cf_teacher_action_enc
                # )
                cf_teacher_action_pred = self.cf_teacher_action_decoder_mlp_out(
                    cf_teacher_action_enc
                )
        else:
            # B, num_snippets, hidden_dim
            cf_teacher_action_enc = self.cf_teacher_action_decoder_mlp_in(
                snippet_past_encoding
            )
            # cf_teacher_action_enc = self.cf_teacher_action_decoder_mlp_existence_net(
            #     cf_teacher_action_enc
            # )
            cf_teacher_action_pred = self.cf_teacher_action_decoder_mlp_out(
                cf_teacher_action_enc
            )
        cf_teacher_action_sig = torch.sigmoid(cf_teacher_action_pred)
        outputs = {
            "cf_teacher_action_pred": cf_teacher_action_pred,
            "cf_teacher_action_sig": cf_teacher_action_sig,
            "cf_teacher_action_enc": cf_teacher_action_enc,
        }
        if self.cf_use_trajectory_prediction:
            outputs.update(
                {
                    "cf_predicted_future_trajectories": predicted_future_trajectories,
                    "cf_future_pred_mask": cf_future_pred_mask,
                }
            )

        batch.update(outputs)
        if self.cf_use_metrics_prediction:
            batch["cf_decoded_metrics"] = cf_metrics_prediction
            batch["cf_metrics"].update(cf_metrics_prediction)

        return outputs
