from transformers import AutoTokenizer

from .aic_base_model import LapModelNonTemporal
from .aic_bsd_full_model import AICBSDFullModel
from .model_configs import ModelConfig


def get_aic_bsd_llama_full_model_and_tokenizer(cfg: ModelConfig):
    """
    Return a AICBSDFullModel and Tokenizer for a given ModelConfig
    """
    model = AICBSDFullModel(cfg)
    model.train()
    tokenizer = AutoTokenizer.from_pretrained(cfg.llm_backbone_id)
    if tokenizer.pad_token_id is None:
        tokenizer.pad_token_id = tokenizer.eos_token_id
    return model, tokenizer


def get_base_skill_encoder_1_model(cfg: ModelConfig):
    model = LapModelNonTemporal(cfg)
    model.train()
    return model, None


def get_model_and_tokenizer(cfg: ModelConfig):
    if cfg.model_id == "aic-bsd-llama-2-7b":
        return get_aic_bsd_llama_full_model_and_tokenizer(cfg)
    elif cfg.model_id == "base-skill-encoder-1":
        return get_base_skill_encoder_1_model(cfg)
