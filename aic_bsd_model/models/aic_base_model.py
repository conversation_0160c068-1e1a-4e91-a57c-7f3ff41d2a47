import torch
import torch.nn as nn

from aic_bsd_model.models.networks.basic_network import create_mlp
from aic_bsd_model.models.networks.decoders import (
    LSTMDecoderNet,
    MetricsDecoderSimpleMLP,
    MetricsTransformerDecoder,
    MLPDecoder,
    SegmentTrajectoryMLPDecoder,
    TransformerDecoder,
)
from aic_bsd_model.models.networks.encoders import (
    MaskedTransformer,
    MetricsEncoderMLP,
    TrajLSTMNet,
)
from aic_bsd_model.models.networks.fusion_encoders import (
    LapEncoderWithCrossAttentionFusion,
    LapEncoderWithSelfAttentionFusion,
    PointTransformerEncoder,
    TransformerBaselineEncoder,
)
from aic_bsd_model.utils.training_utils import parse_batch, parse_batch_metrics


class LapModelNonTemporal(nn.Module):
    def __init__(self, config_dict):
        # super(LapModelNonTemporal, self).__init__(config_dict, vis_dataloader)
        super(LapModelNonTemporal, self).__init__()
        self.config_dict = config_dict
        self.traj_input_dim = config_dict.get("traj_input_dim", None)
        self.map_input_dim = config_dict.get("map_input_dim", None)
        self.hidden_dim = config_dict.get("hidden_dim", None)
        self.target_metrics_keys = config_dict.get("target_metric_keys", None)
        self.metrics_input_dim = len(config_dict.get("target_metric_keys", []))
        self.metrics_output_dim = len(config_dict.get("target_metric_keys", []))
        self.target_metric_keys = config_dict.get("target_metric_keys", [])
        self.traj_decoder_output_dim = config_dict.get("traj_decoder_output_dim", None)
        self.num_attention_heads = config_dict.get("num_attention_heads", None)
        self.num_layers = config_dict.get("num_layers", None)
        self.predict_delta = config_dict.get("predict_delta", False)
        self.dropout = config_dict.get("dropout", 0.1)
        self.segment_dropout_ratio = config_dict.get("segment_dropout_ratio", 0.0)
        self.padding_value = config_dict.get("padding_value", -2000)
        self.lap_emb_use_cls_global_token = config_dict.get(
            "lap_emb_use_cls_global_token", False
        )
        self.lap_encoder_type = config_dict.get(
            "lap_encoder_type", "LapEncoderWithSelfAttentionFusion"
        )
        self.lap_metrics_encoder_type = config_dict.get(
            "lap_metrics_encoder_type", None
        )
        self.lap_decoder_type = config_dict.get(
            "lap_decoder_type", "TransformerDecoder"
        )
        self.predict_next_lap_metrics = config_dict.get(
            "predict_next_lap_metrics", False
        )
        self.metrics_decoder_type = config_dict.get(
            "metrics_decoder_type", "MetricsDecoderSimpleMLP"
        )

        if self.lap_encoder_type == "LapEncoderWithCrossAttentionFusion":
            self.lap_encoder = LapEncoderWithCrossAttentionFusion(
                self.traj_input_dim,
                self.map_input_dim,
                self.hidden_dim,
                self.num_attention_heads,
                self.num_layers,
                self.dropout,
                self.segment_dropout_ratio,
                self.lap_emb_use_cls_global_token,
            )
        elif self.lap_encoder_type == "LapEncoderWithSelfAttentionFusion":
            self.lap_encoder = LapEncoderWithSelfAttentionFusion(
                self.traj_input_dim,
                self.map_input_dim,
                self.hidden_dim,
                self.num_attention_heads,
                self.num_layers,
                self.dropout,
            )
        elif self.lap_encoder_type == "PointNetEncoder":
            config_dict["traj_encoder_type"] = "PointNetEncoder"
            self.lap_encoder = TransformerBaselineEncoder(
                config_dict,
                self.traj_input_dim,
                self.map_input_dim,
                self.hidden_dim,
            )
        elif self.lap_encoder_type == "TransformerTrajEncoder":
            config_dict["traj_encoder_type"] = "TransformerTrajEncoder"
            self.lap_encoder = TransformerBaselineEncoder(
                config_dict,
                self.traj_input_dim,
                self.map_input_dim,
                self.hidden_dim,
            )
        elif self.lap_encoder_type == "LSTM":
            config_dict["traj_encoder_type"] = "TrajLSTMNet"
            self.lap_encoder = TransformerBaselineEncoder(
                config_dict,
                self.traj_input_dim,
                self.map_input_dim,
                self.hidden_dim,
            )
        elif self.lap_encoder_type == "PointTransformer":
            self.lap_encoder = PointTransformerEncoder(
                config_dict,
                self.traj_input_dim,
                self.map_input_dim,
                self.hidden_dim,
            )
        else:
            raise ValueError(f"Invalid lap_encoder_type: {self.lap_encoder_type}")

        if self.lap_metrics_encoder_type == "MetricsEncoderMLP":
            print("Using lap metrics encoding")
            self.lap_metrics_encoder = MetricsEncoderMLP(
                self.metrics_input_dim, self.hidden_dim, self.dropout
            )
            self.traj_metrics_fusion_net = nn.Sequential(
                create_mlp(
                    self.hidden_dim + self.hidden_dim,
                    [self.hidden_dim, self.hidden_dim],
                    self.dropout,
                    activation_func=nn.Mish,
                )
            )

        if self.lap_decoder_type == "SegmentTrajectoryDecoder":
            self.segment_trajectory_decoder = SegmentTrajectoryMLPDecoder(
                encoder_hidden_dim=self.hidden_dim,
                traj_output_dim=self.traj_decoder_output_dim,
                decoder_hidden_dim=self.hidden_dim,
                max_segment_length=350,  # TODO look at all stats and find the upper bound to minimize model size.
                predict_delta=self.predict_delta,
            )
        elif self.lap_decoder_type == "TransformerDecoder":
            self.segment_trajectory_decoder = TransformerDecoder(
                hidden_dim=self.hidden_dim,
                traj_output_dim=self.traj_decoder_output_dim,
                max_segment_length=350,  # TODO look at all stats and find the upper bound to minimize model size.
                predict_delta=self.predict_delta,
            )
        elif self.lap_decoder_type == "LSTMDecoder":
            self.segment_trajectory_decoder = LSTMDecoderNet(
                config_dict,
                hidden_dim=self.hidden_dim,
                traj_output_dim=self.traj_decoder_output_dim,
                max_segment_length=350,  # TODO look at all stats and find the upper bound to minimize model size.
            )
        elif self.lap_decoder_type == "MLPDecoder":
            self.segment_trajectory_decoder = MLPDecoder(
                config_dict,
                hidden_dim=self.hidden_dim,
                traj_output_dim=self.traj_decoder_output_dim,
                max_segment_length=350,  # TODO look at all stats and find the upper bound to minimize model size.
            )
        else:
            raise ValueError(f"Invalid lap_decoder_type: {self.lap_decoder_type}")

        # TODO (deepak.gopinath) change layer dims list to be passed in as an arg or compute it fro/output dim
        if self.metrics_decoder_type == "MetricsDecoderSimpleMLP":
            self.metrics_decoder = MetricsDecoderSimpleMLP(
                self.hidden_dim,
                self.target_metric_keys,
                dropout_prob=self.dropout,
            )
        elif self.metrics_decoder_type == "MetricsTransformerDecoder":
            self.metrics_decoder = MetricsTransformerDecoder(
                self.hidden_dim,
                self.target_metric_keys,
                dropout_prob=self.dropout,
            )
        else:
            raise ValueError(
                f"Invalid metrics_decoder_type: {self.metrics_decoder_type}"
            )

        print(
            f"Using lap encoder {self.lap_encoder_type}, decoder type {self.lap_decoder_type}, metrics decoder type {self.metrics_decoder_type} "
        )

    def run_lap_encoder(self, batch, lap_segments_data, map_data):
        ret = self.lap_encoder(batch, lap_segments_data, map_data)
        lap_encoding = ret["lap_encoding"]
        seg_encoding = ret["seg_encoding"]
        map_encoding = ret["map_encoding"]
        return lap_encoding, seg_encoding, map_encoding

    def forward(self, batch):
        """
        Forward pass of the model.
        Args:
            Batch: dict
                lap_segments_data (Tensor): Input data for lap segments.
                map_data (Tensor): Input map data.
                segment_lengths (Tensor): Lengths of each segment.
        Returns:
            outputs: dict
                lap_encoding (Tensor): This is the fused representation of trajectory and map for the entire lap. Fusion of traj and map happens in the fusion encoders.
                decoded_metrics (Tensor): Decoded metrics from the fused representation.
                decoded_segment_trajectories (Tensor): Decoded trajectories for each segment.
                decode_segment_trajectories_mask (Tensor): Mask for the decoded segment trajectories.
        """
        (
            lap_segments_data,
            map_data,
            segment_lengths,
        ) = parse_batch(batch, batch["device"])
        original_lap_dim = lap_segments_data.ndim

        if original_lap_dim == 5:  # (B, K=1, N=10, max_T, F) -> (B, N, max_T, F)
            lap_segments_data = lap_segments_data[:, 0]
            map_data = map_data[:, 0]
            segment_lengths = segment_lengths[:, 0]

        # TODO (deepak.gopinath) Remove redundancy. Both *_data_valid and *_attention_masks have the same information.
        batch["lap_segments_data_valid"] = (
            lap_segments_data[..., :1] != self.padding_value
        )
        batch["map_data_valid"] = map_data[..., :1] != self.padding_value
        lap_segments_data = lap_segments_data * batch["lap_segments_data_valid"]
        map_data = map_data * batch["map_data_valid"]

        # lap_encoding = (B, H), map_encoding (B, N=10, num_lanes, F), seg_encoding = (B, N=10,  F)
        lap_encoding, seg_encoding, map_encoding = self.run_lap_encoder(
            batch, lap_segments_data, map_data
        )
        # do any decoding using metrics_decoder or any other decoder.
        if self.lap_metrics_encoder_type is not None:
            metrics_input = parse_batch_metrics(
                batch, self.target_metric_keys, batch["device"]
            )  # (B, len(self.target_metric_keys))
            # (B, hidden_dim)
            metrics_encoding = self.lap_metrics_encoder(metrics_input)
            lap_encoding = torch.cat(
                [lap_encoding, metrics_encoding], dim=-1
            )  # (B, 2*hidden_dim)
            lap_encoding = self.traj_metrics_fusion_net(lap_encoding)  # (B, hidden)

        (
            reconstructed_segment_trajectories,  # (B, N, T, F)
            reconstructed_pred_mask,  # (B, N, T, 1)
        ) = self.segment_trajectory_decoder(
            batch, seg_encoding, segment_lengths, map_encoding
        )
        if reconstructed_pred_mask is None:
            # [B, N, T, 1]
            reconstructed_pred_mask = batch["lap_segments_data_valid"]

        # (B) for each key in metrics dict
        lap_decoded_metrics = self.metrics_decoder(lap_encoding, seg_encoding)
        batch["metrics"].update(lap_decoded_metrics)

        if original_lap_dim == 5:
            # re-add the removed K dimension
            # (B, K=1, N=10, T, F)
            reconstructed_segment_trajectories = (
                reconstructed_segment_trajectories.unsqueeze(1)
            )
            # (B, K=1, N=10, T, 1)
            reconstructed_pred_mask = reconstructed_pred_mask.unsqueeze(1)
            # (B, K=1, H)
            lap_representation = lap_encoding.unsqueeze(1)
            # (B, K=1, N=10, T, 1)
            batch["lap_segments_data_valid"] = batch[
                "lap_segments_data_valid"
            ].unsqueeze(1)
            # (B, K=1, N=10, max_map_L, 1)
            batch["map_data_valid"] = batch["map_data_valid"].unsqueeze(1)
        else:
            lap_representation = lap_encoding

        outputs = {
            "side_channel_lap_representation": lap_encoding,  # (B, H)is is same as lap_representation for the time being.
            "lap_representation": lap_representation,  # (B, K=1, H)
            "lap_decoded_metrics": lap_decoded_metrics,  # (B) for each key
            "reconstructed_segment_trajectories": reconstructed_segment_trajectories,  # # (B, K=1, N=10, T, F)
            "reconstructed_pred_mask": reconstructed_pred_mask,  # (B, K=1, N=10, T, 1)
        }
        # Output is stored back to the batch
        batch.update(outputs)

        return outputs


class LapLSTM(nn.Module):
    def __init__(
        self,
        input_dim,
        hidden_dim,
        num_lstm_layers,
        batch_first=True,
        use_noise_in_hc=False,
    ):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.use_noise_in_hc = use_noise_in_hc
        if self.use_noise_in_hc:
            # assuming that the noise dimension is also same as hidden_dim
            self.hidden_dim += self.hidden_dim
            self.noise_linear_layer = nn.Linear(self.hidden_dim, self.hidden_dim)

        self.num_lstm_layers = num_lstm_layers
        self.lstm = nn.LSTM(
            input_size=self.input_dim,
            hidden_size=self.hidden_dim,
            num_layers=self.num_lstm_layers,
            batch_first=batch_first,
        )

        self.n_samples = 1

    def forward(self, lap_reps, lap_mask):  # lap_reps: (B, K, D), lap_mask: (B, K)
        # Run through full LSTM
        output, _ = self.lstm(lap_reps)  # (B, K, D)

        # Use mask to extract last valid output for each batch
        lengths = lap_mask.sum(dim=1).clamp(min=1)  # (B,)
        # (B, 1, D)
        last_indices = (
            (lengths - 1).unsqueeze(1).unsqueeze(2).expand(-1, 1, output.size(-1))
        ).to(torch.int64)
        # (B, D)
        last_valid_output = output.gather(dim=1, index=last_indices).squeeze(1)

        return output, last_valid_output

    def run_lstm_step_wise_with_additional_decoding(
        self, lap_reps, lap_mask, additional_info_dict
    ):
        # lap reps = (B, K, D)
        B, K, D = lap_reps.shape
        metrics_dim = additional_info_dict["metrics_input_dim"]
        metrics_decoder = additional_info_dict["metrics_decoder"]

        metrics_output_list = []
        lstm_outputs = []
        last_valid_outputs = []
        for n in range(self.n_samples):
            h = torch.zeros(
                self.num_lstm_layers, B, self.hidden_dim, device=lap_reps.device
            )
            c = torch.zeros_like(h)

            if self.use_noise_in_hc:
                sampled_noise = torch.randn((self.num_lstm_layers, B, self.hidden_dim))
                sampled_noise = self.noise_linear_layer(sampled_noise)
                h = h + sampled_noise  # () add in feature_dim
                # h = torch.cat([h, sampled_noise], -1)
                # c = torch.cat([c, torch.zeros_like(sampled_noise)], -1)

            # (B, 1, target_metrics_dim)
            prev_metrics_out = torch.zeros((B, 1, metrics_dim)).to(lap_reps.device)
            metrics_output_list_n = []
            lstm_outputs_n = []
            for t in range(K):
                # (B, 1, D)
                lap_rep_t = lap_reps[:, t : t + 1, :]
                # (B, 1, D+metrics_dim)
                lstm_input = torch.cat([lap_rep_t, prev_metrics_out], dim=-1)
                # lstm_output = [B, 1, D]
                lstm_output_t, (h, c) = self.lstm(lstm_input, (h, c))
                metrics_output_t_dict = metrics_decoder(
                    lstm_output_t.reshape(B, -1), None
                )

                # reshaping the decoded metrics
                for mk_pred in metrics_output_t_dict:
                    # (B, 1) for each metric
                    metrics_output_t_dict[mk_pred] = metrics_output_t_dict[
                        mk_pred
                    ].view(B, -1)

                lstm_outputs_n.append(lstm_output_t)
                metrics_output_list_n.append(metrics_output_t_dict)
                metrics_combined = []
                for key in metrics_output_t_dict:
                    metrics_combined.append(metrics_output_t_dict[key].unsqueeze(-1))

                prev_metrics_out = torch.cat(metrics_combined, dim=-1).detach()
                prev_metric_out = self.metrics_output_encoder(prev_metric_out)

                # dropout

            # Use mask to extract last valid output for each batch
            # (B, K, D)
            lstm_outputs_n = torch.cat(lstm_outputs_n, dim=1)
            lengths = lap_mask.sum(dim=1).clamp(min=1)  # (B,)
            # (B, 1, D)
            last_indices = (
                (lengths - 1)
                .unsqueeze(1)
                .unsqueeze(2)
                .expand(-1, 1, lstm_outputs_n.size(-1))
            ).to(torch.int64)
            # (B, D)
            last_valid_output_n = lstm_outputs_n.gather(
                dim=1, index=last_indices
            ).squeeze(1)

            metrics_combined_dict = {}
            for key in metrics_output_list_n[0].keys():
                metrics_combined_dict[key] = torch.cat(
                    [v[key] for v in metrics_output_list_n], dim=-1
                )

            metrics_output_list.append(metrics_combined_dict)
            lstm_outputs.append(lstm_outputs_n)
            last_valid_outputs.append(last_valid_output_n)

        if self.n_samples == 1:
            # (B, K, H), (B, H), (dict containing metrics_keys each of which is (B, K))
            return lstm_outputs[0], last_valid_outputs[0], metrics_output_list[0]
        else:
            # (B, num_samples, K, H)
            lstm_outputs_combined = torch.cat(
                [l.unsqueeze(1) for l in lstm_outputs], dim=1
            )
            last_valid_outputs_combined = torch.cat(
                [l.unsqueeze(1) for l in last_valid_outputs], dim=1
            )
            # list of n metrics dict
            metrics_output_combined = {}
            for k in metrics_output_list[0]:
                metrics_output_combined[key] = torch.cat(
                    [v[key].unsqueeze(1) for v in metrics_output_list], dim=1
                )

            return (
                lstm_outputs_combined,
                last_valid_outputs_combined,
                metrics_output_combined,
            )


class LapModelTemporal(LapModelNonTemporal):
    def __init__(self, config_dict):
        super(LapModelTemporal, self).__init__(config_dict)
        # add an LSTM to operate on ret['lap_encoding']
        # The metrics and reconstruction decoder still operates as is.
        # The hidden state pulled fomr the LSTM is to be used as sode channel information for the concurrent model.

    def generate_lap_encodings(self, batch):
        (
            lap_segments_data,
            map_data,
            segment_lengths,
            lap_masks,  # (B, K)
        ) = parse_batch(batch, batch["device"], return_lap_masks=True)
        original_shape = lap_segments_data.shape

        # (B*K, ...)
        lap_segments_data = lap_segments_data.view(-1, *lap_segments_data.shape[2:])
        map_data = map_data.view(-1, *map_data.shape[2:])
        segment_lengths = segment_lengths.reshape(-1, *segment_lengths.shape[2:])

        batch["lap_segments_data_valid"] = (
            lap_segments_data[..., :1] != self.padding_value
        )
        batch["map_data_valid"] = map_data[..., :1] != self.padding_value
        lap_segments_data = lap_segments_data * batch["lap_segments_data_valid"]
        map_data = map_data * batch["map_data_valid"]

        # lap_encoding = (B*K, H), map_encoding (B*K, N, num_lanes, F), seg_encoding = (B*K, 10,  F)
        lap_encoding, seg_encoding, map_encoding = self.run_lap_encoder(
            batch, lap_segments_data, map_data
        )

        if self.lap_metrics_encoder_type is not None:
            metrics_input = parse_batch_metrics(
                batch, self.target_metric_keys, batch["device"]
            )  # (B,K, len(self.target_metric_keys))
            # (B*K, len(metric_keys))
            metrics_input = metrics_input.view(-1, *metrics_input.shape[2:])
            metrics_encoding = self.lap_metrics_encoder(
                metrics_input
            )  # (B*K, hidden_dim)
            lap_encoding = torch.cat(
                [lap_encoding, metrics_encoding], dim=-1
            )  # (B*K, 2*hidden_dim)
            lap_encoding = self.traj_metrics_fusion_net(lap_encoding)  # (B*, hidden)

        # do any decoding using metrics_decoder or any other decoder.
        (
            reconstructed_segment_trajectories,  # (B*K, N, T, F)
            reconstructed_pred_mask,  # (B*K, N, T, 1)
        ) = self.segment_trajectory_decoder(
            batch, seg_encoding, segment_lengths, map_encoding
        )

        if reconstructed_pred_mask is None:
            # [B*K, N, T, 1]
            reconstructed_pred_mask = batch["lap_segments_data_valid"]

        # lap_decoded_metrics = self.metrics_decoder(lap_encoding, seg_encoding)
        # for mk_pred in lap_decoded_metrics:
        #     lap_decoded_metrics[mk_pred] = lap_decoded_metrics[mk_pred].view(
        #         original_shape[0], -1
        #     )
        # # dict containing (B, K) for each metrics
        # batch["metrics"].update(lap_decoded_metrics)

        # lap_encoding is (B*K, H) to (B, K, H)
        lap_encoding = lap_encoding.view(
            original_shape[0], original_shape[1], *lap_encoding.shape[1:]
        )
        # seg_encoding is (B*K, N, H) -> (B,K,N,H)
        seg_encoding = seg_encoding.view(
            original_shape[0], original_shape[1], *seg_encoding.shape[1:]
        )
        # map encoding is (B*K, N, num_lanes, H) -> (B, K, N, num_lanes, H)
        map_encoding = map_encoding.view(
            original_shape[0], original_shape[1], *map_encoding.shape[1:]
        )
        # reconstructed_segment_trajectories is (B*K, N=10, maxT, decoder_traj_dim) -> (B, K, N=10, maxT, decoder_traj_dim)
        reconstructed_segment_trajectories = reconstructed_segment_trajectories.view(
            original_shape[0],
            original_shape[1],
            *reconstructed_segment_trajectories.shape[1:],
        )
        # reconstructed_segment_trajectories is (B*K, N=10, maxT, 1) -> (B, K, N=10, maxT, 1)
        reconstructed_pred_mask = reconstructed_pred_mask.view(
            original_shape[0], original_shape[1], *reconstructed_pred_mask.shape[1:]
        )

        batch["lap_segments_data_valid"] = batch["lap_segments_data_valid"].view(
            original_shape[0],
            original_shape[1],
            *batch["lap_segments_data_valid"].shape[1:],
        )
        batch["map_data_valid"] = batch["map_data_valid"].view(
            original_shape[0], original_shape[1], *batch["map_data_valid"].shape[1:]
        )

        outputs = {
            "lap_representation": lap_encoding,  # (B, K, H) this will be lap encoding for each of the K laps
            # "lap_decoded_metrics": lap_decoded_metrics,  # dict containing (B, K) for each metrics
            "reconstructed_segment_trajectories": reconstructed_segment_trajectories,  # (B, K, N=10, maxT, decoder_traj_dim))
            "reconstructed_pred_mask": reconstructed_pred_mask,  # ((B, K, N=10, maxT, 1))
            "lap_segment_encodings": seg_encoding,  # (B, K, N=10, H)
            "lap_segment_map_encodings": map_encoding,  # (B, K, N, num_lanes, H)
        }

        return batch, outputs, lap_encoding, lap_masks

    def run_lap_recurrent_model(self, lap_encoding, lap_masks):
        pass

    def forward(self, batch):
        pass


class LapModelTemporalLSTM(LapModelTemporal):
    def __init__(self, config_dict):
        super(LapModelTemporalLSTM, self).__init__(config_dict)
        # add an LSTM to operate on ret['lap_encoding']
        # The metrics and reconstruction decoder still operates as is.
        # The hidden state pulled fomr the LSTM is to be used as sode channel information for the concurrent model.
        self.num_lstm_layers = self.config_dict.get("num_lstm_layers", 1)
        self.run_lstm_step_wise = self.config_dict.get("run_lstm_step_wise", False)

        self.lap_lstm = LapLSTM(
            self.hidden_dim + self.metrics_input_dim
            if self.run_lstm_step_wise
            else self.hidden_dim,
            self.hidden_dim,
            num_lstm_layers=self.num_lstm_layers,
        )

        # simple n by n linear matrix
        # self.A = nn.Parameter(torch.randn(self.hidden_dim, self.hidden_dim) * 0.01)
        self.A = nn.Linear(self.hidden_dim, self.hidden_dim, bias=False)
        self.compute_weight_penalty = self.config_dict.get("use_weight_penalty", False)
        self.weight_penalty_type = self.config_dict.get("weight_penalty_type", "l2")
        self.weight_penalty_coeff = self.config_dict.get("weight_penalty_coeff", 1.0)

    def run_lap_recurrent_model(self, lap_encoding, lap_masks):
        # lstm_outputs = (B, K, H), last_valid_output = (B, H)

        if not self.run_lstm_step_wise:
            lstm_outputs, last_valid_output = self.lap_lstm(lap_encoding, lap_masks)
            B, K, H = lstm_outputs.shape
            lap_decoded_metrics = self.metrics_decoder(
                lstm_outputs.reshape(B * K, -1), None
            )
            for mk_pred in lap_decoded_metrics:
                # (B, K) for each metric
                lap_decoded_metrics[mk_pred] = lap_decoded_metrics[mk_pred].view(B, -1)

            return lstm_outputs, last_valid_output, lap_decoded_metrics
        else:
            additional_info_dict = {}
            additional_info_dict["metrics_input_dim"] = self.metrics_input_dim
            additional_info_dict["metrics_decoder"] = self.metrics_decoder
            (
                lstm_outputs,
                last_valid_output,
                lap_decoded_metrics,
            ) = self.lap_lstm.run_lstm_step_wise_with_additional_decoding(
                lap_encoding, lap_masks, additional_info_dict
            )

    def forward(self, batch):
        # we are assuming that segment level trajectories etc are not affected by long term context.
        batch, outputs, lap_encoding, lap_masks = self.generate_lap_encodings(batch)

        # lstm_outputs - (B, K, H), lstm_accumulated_output = (B, H)
        # lstm_outputs - (B, N, K, H), lstm_accumulated_output = (B, N, H), additional_decoded_outputs (B, N, K, metrics_dim)
        (
            lstm_outputs,
            lstm_accumulated_representation,
            additional_decoded_outputs,
        ) = self.run_lap_recurrent_model(lap_encoding, lap_masks)

        batch["metrics"].update(additional_decoded_outputs)
        outputs["lap_decoded_metrics"] = additional_decoded_outputs
        # (B, H), (B, N, H) if MoN
        outputs["side_channel_lap_representation"] = lstm_accumulated_representation
        # (B, K, H)
        outputs["latent_z_sequence"] = lstm_outputs
        # (B, K, H)
        outputs["updated_latent_z_sequence"] = lstm_outputs + self.A(lstm_outputs)
        # # Output is stored back to the batch
        if self.compute_weight_penalty:
            assert self.weight_penalty_type is not None
            assert self.weight_penalty_coeff >= 0.0
            if "weight_penalty" not in batch:
                batch["weight_penalty"] = {}
            if self.weight_penalty_type == "l2":
                # do mean over l2
                l2_loss = (self.A.weight**2).mean()
                batch["weight_penalty"]["A_l2_wp_loss"] = l2_loss
                batch["weight_penalty"]["A_l2_wp_loss_scaled"] = (
                    self.weight_penalty_coeff * l2_loss
                )
            elif self.weight_penalty_type == "nuclear":
                nuclear_norm = torch.norm(self.A.weight, p="nuc") / self.A.weight.size(
                    0
                )  # divide by number of diaogonal elements
                batch["weight_penalty"]["A_nuc_wp_loss"] = nuclear_norm
                batch["weight_penalty"]["A_nuc_wp_loss_scaled"] = (
                    self.weight_penalty_coeff * nuclear_norm
                )

        batch.update(outputs)

        return outputs


class MetricsEncoderDecoderLSTM(nn.Module):
    def __init__(self, config_dict):
        super(MetricsEncoderDecoderLSTM, self).__init__()
        self.config_dict = config_dict
        self.num_lstm_layers = self.config_dict.get("num_lstm_layers", 1)
        self.target_metrics_keys = config_dict.get("target_metric_keys", None)
        self.metrics_input_dim = len(config_dict.get("target_metric_keys", []))
        self.metrics_output_dim = len(config_dict.get("target_metric_keys", []))
        self.target_metric_keys = config_dict.get("target_metric_keys", [])
        self.dropout = config_dict.get("dropout", 0.1)
        self.hidden_dim = config_dict.get("hidden_dim", None)
        self.lap_lstm = LapLSTM(
            self.hidden_dim, self.hidden_dim, num_lstm_layers=self.num_lstm_layers
        )
        self.lap_metrics_encoder = MetricsEncoderMLP(
            self.metrics_input_dim, self.hidden_dim, self.dropout
        )
        self.lap_metrics_decoder = MetricsDecoderSimpleMLP(
            self.hidden_dim,
            self.target_metric_keys,
            dropout_prob=self.dropout,
        )

    def forward(self, batch):
        # (B, max_L, x_dim)
        (
            _,
            _,
            _,
            lap_masks,  # (B, K)
        ) = parse_batch(batch, batch["device"], return_lap_masks=True)

        metrics_input = parse_batch_metrics(
            batch, self.target_metric_keys, batch["device"]
        )  # (B,K, len(self.target_metric_keys))
        B, K = metrics_input.shape[0], metrics_input.shape[1]
        batch_size, num_laps = metrics_input.shape[0], metrics_input.shape[1]
        # (B*K, len(metric_keys))
        metrics_input = metrics_input.view(-1, *metrics_input.shape[2:])
        # (B*K, hidden_dim)
        metrics_encoding = self.lap_metrics_encoder(metrics_input)
        # (B, K, F)
        metrics_encoding = metrics_encoding.view(batch_size, num_laps, -1)
        # lstm_outputs - (B, K, H), lstm_accumulated_output = (B, H)
        lstm_outputs, lstm_accumulated_output = self.lap_lstm(
            metrics_encoding, lap_masks
        )
        # lap_decoded_metrics is dict containing (B, K)

        # needed to use reshape instead of view due to non-contiugous memoery
        lstm_outputs = lstm_outputs.reshape(-1, *lstm_outputs.shape[2:])
        lap_decoded_metrics = self.lap_metrics_decoder(lstm_outputs, None)
        for mk_pred in lap_decoded_metrics:
            lap_decoded_metrics[mk_pred] = lap_decoded_metrics[mk_pred].view(B, K)
        # dict containing (B, K) for each metrics
        batch["metrics"].update(lap_decoded_metrics)

        outputs = {
            "side_channel_lap_representation": lstm_accumulated_output,  # (B, H)
            "lap_representation": lstm_outputs,  # (B, K, H)
            "lap_decoded_metrics": lap_decoded_metrics,  # (B, K) for each key
        }
        # Output is stored back to the batch
        batch.update(outputs)

        return outputs


class LapModelTemporalTransformer(LapModelTemporal):
    def __init__(self, config_dict):
        super(LapModelTemporalTransformer, self).__init__(config_dict)
        self.num_lap_transformer_layers = self.config_dict.get(
            "num_lap_transformer_layers", 1
        )
        self.transformer_hidden_dim_multiplier = self.config_dict.get(
            "transformer_hidden_dim_multiplier", 1
        )
        self.use_pos_emb = self.config_dict.get("use_pos_emb", True)
        # the second to last is the feedforward im. Could 2*hidden
        self.lap_sequence_transformer = MaskedTransformer(
            self.hidden_dim,
            self.num_attention_heads,
            self.num_lap_transformer_layers,
            self.transformer_hidden_dim_multiplier * self.hidden_dim,
            self.dropout,
            self.use_pos_emb,
        )
        self.A = nn.Linear(self.hidden_dim, self.hidden_dim, bias=False)
        self.compute_weight_penalty = self.config_dict.get("use_weight_penalty", False)
        self.weight_penalty_type = self.config_dict.get("weight_penalty_type", "l2")
        self.weight_penalty_coeff = self.config_dict.get("weight_penalty_coeff", 1.0)

    def run_lap_recurrent_model(self, lap_encoding, lap_masks):
        # causal_lap_seq_encodings - (B, K, H), last_valid_output = (B, H)
        causal_lap_seq_encodings, last_valid_output = self.lap_sequence_transformer(
            lap_encoding, lap_masks
        )
        B, K, H = causal_lap_seq_encodings.shape
        lap_decoded_metrics = self.metrics_decoder(
            causal_lap_seq_encodings.reshape(B * K, -1), None
        )
        for mk_pred in lap_decoded_metrics:
            # (B, K) for each metric
            lap_decoded_metrics[mk_pred] = lap_decoded_metrics[mk_pred].view(B, -1)

        return causal_lap_seq_encodings, last_valid_output, lap_decoded_metrics

    def forward(self, batch):
        # TODO update
        # lap_encoding = (B, K, H)
        batch, outputs, lap_encoding, lap_masks = self.generate_lap_encodings(batch)
        #  lap_transformer_outputs = (B, K,  H)
        (
            causal_lap_seq_encodings,
            last_valid_output,
            additional_decoded_outputs,
        ) = self.run_lap_recurrent_model(lap_encoding, lap_masks)

        batch["metrics"].update(additional_decoded_outputs)
        outputs["lap_decoded_metrics"] = additional_decoded_outputs
        # (B, H)
        outputs["side_channel_lap_representation"] = last_valid_output
        # (B, K, H)
        outputs["latent_z_sequence"] = causal_lap_seq_encodings
        # (B, K, H)
        outputs["updated_latent_z_sequence"] = causal_lap_seq_encodings + self.A(
            causal_lap_seq_encodings
        )
        # # Output is stored back to the batch
        if self.compute_weight_penalty:
            assert self.weight_penalty_type is not None
            assert self.weight_penalty_coeff >= 0.0
            if "weight_penalty" not in batch:
                batch["weight_penalty"] = {}
            if self.weight_penalty_type == "l2":
                # do mean over l2
                l2_loss = (self.A.weight**2).mean()
                batch["weight_penalty"]["A_l2_wp_loss"] = l2_loss
                batch["weight_penalty"]["A_l2_wp_loss_scaled"] = (
                    self.weight_penalty_coeff * l2_loss
                )
            elif self.weight_penalty_type == "nuclear":
                nuclear_norm = torch.norm(self.A.weight, p="nuc") / self.A.weight.size(
                    0
                )  # divide by number of diaogonal elements
                batch["weight_penalty"]["A_nuc_wp_loss"] = nuclear_norm
                batch["weight_penalty"]["A_nuc_wp_loss_scaled"] = (
                    self.weight_penalty_coeff * nuclear_norm
                )

        # # Output is stored back to the batch
        batch.update(outputs)

        return outputs


class MetricsEncoderDecoderTransformer(nn.Module):
    pass


class VRNN(nn.Module):
    def __init__(self, config_dict):
        super(VRNN, self).__init__()
        self.hidden_dim = config_dict.get("hidden_dim", None)
        self.z_dim = config_dict.get("z_dim", None)
        self.num_lstm_layers = config_dict.get("num_lstm_layers", 1)
        if self.z_dim is None:
            self.z_dim = self.hidden_dim

        self.reduce_latent_layer = nn.Linear(
            self.hidden_dim + self.hidden_dim, self.hidden_dim
        )
        self.phi_z = nn.Sequential(nn.Linear(self.z_dim, self.hidden_dim))
        # encoder q(z|enc(x), h)
        self.enc = nn.Sequential(
            nn.Linear(self.hidden_dim + self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim),
        )
        self.enc_mean = nn.Linear(self.hidden_dim, self.z_dim)
        self.enc_std = nn.Sequential(
            nn.Linear(self.hidden_dim, self.z_dim), nn.Softplus()
        )
        self.prior = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim), nn.ReLU()
        )
        self.prior_mean = nn.Linear(self.hidden_dim, self.z_dim)
        self.prior_std = nn.Sequential(
            nn.Linear(self.hidden_dim, self.z_dim), nn.Softplus()
        )
        self.rnn = nn.GRU(
            self.hidden_dim + self.hidden_dim,
            self.hidden_dim,
            self.num_lstm_layers,
            batch_first=True,
        )
        # To fuse z and h for final side channel representation
        self.fusion_net = nn.Sequential(
            nn.Linear(self.hidden_dim + self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim),
        )

    def decode(self, phi_z_t, h_t):
        pass

    def generation_z(self, h_t):
        # prior
        prior_t = self.prior(h_t)
        # prior_mean_t, prior_std_t = torch.chunk(prior_t, 2, dim=-1)
        prior_mean_t = self.prior_mean(prior_t)
        prior_std_t = self.prior_std(prior_t)
        return prior_mean_t, prior_std_t

    def inference(self, phi_x_t, h_t):
        enc_t = self.enc(torch.cat([phi_x_t, h_t], 1))
        # enc_mean_t, enc_std_t = torch.chunk(enc_t, 2, dim=-1)
        enc_mean_t = self.enc_mean(enc_t)
        enc_std_t = self.enc_std(enc_t)
        return enc_mean_t, enc_std_t

    def recurrence(self, phi_x_t, phi_z_t, h_t, c_t=None):
        _, h_valid_next = self.rnn(torch.cat([phi_x_t, phi_z_t], 1).unsqueeze(1), h_t)
        return h_valid_next

    def _reparameterized_sample(self, mean, std, device):
        """using std to sample"""
        eps = torch.empty(size=std.size(), device=device, dtype=torch.float).normal_()
        return eps.mul(std).add_(mean)

    def forward(self, batch):
        pass


class MetricsEncoderDecoderVRNN(VRNN):
    def __init__(self, config_dict):
        super(MetricsEncoderDecoderVRNN, self).__init__(config_dict)
        self.target_metric_keys = config_dict.get("target_metric_keys", [])
        self.dropout = config_dict.get("dropout", 0.1)
        self.metrics_input_dim = len(config_dict.get("target_metric_keys", []))
        self.metrics_output_dim = len(config_dict.get("target_metric_keys", []))
        self.A = nn.Linear(self.hidden_dim, self.hidden_dim, bias=False)
        self.compute_weight_penalty = config_dict.get("use_weight_penalty", False)
        self.weight_penalty_type = config_dict.get("weight_penalty_type", "l2")
        self.weight_penalty_coeff = config_dict.get("weight_penalty_coeff", 1.0)

        # replace all of this with create_mlp functions
        # feature extractor for the input
        self.lap_metrics_encoder = nn.Sequential(
            nn.Linear(self.metrics_input_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim),
        )

        # decoder
        self.metrics_decoder = MetricsDecoderSimpleMLP(
            self.hidden_dim + self.hidden_dim,
            self.target_metric_keys,
            dropout_prob=self.dropout,
        )

    def decode(self, phi_z_t, h_t):
        # deterministic decoder for lap metrics decoder
        dec_mean_t = self.metrics_decoder(torch.cat([phi_z_t, h_t], 1), None)
        return dec_mean_t

    def forward(self, batch):
        # (B, max_L, x_dim)
        (
            _,
            _,
            _,
            lap_masks,  # (B, K)
        ) = parse_batch(batch, batch["device"], return_lap_masks=True)

        metrics_input = parse_batch_metrics(
            batch, self.target_metric_keys, batch["device"]
        )  # (B, K, len(self.target_metric_keys))

        # (B, K)
        batch_size, max_seq_len = metrics_input.size(0), metrics_input.size(1)

        all_enc_mean, all_enc_std = [], []
        all_lap_decoded_metrics_dict = []
        all_prior_mean, all_prior_std = [], []
        all_hidden_states, all_phi_z_t = [], []

        h = torch.zeros(
            self.num_lstm_layers,
            batch_size,
            self.hidden_dim,
            device=metrics_input.device,
        )
        # (B, K, H)
        all_phi_x = self.lap_metrics_encoder(metrics_input)

        for t in range(max_seq_len):
            # encode metrics at time t, (B, hidden_dim)
            # phi_x_t = self.lap_metrics_encoder(metrics_input[:, t, :])
            phi_x_t = all_phi_x[:, t, :]

            # (B, hidden_dim), (B, hidden_dim). h_valid[-1] is the lstm output after the last layer
            enc_mean_t, enc_std_t = self.inference(phi_x_t, h[-1])

            # (B, hidden_dim), (B, hidden_dim). only used for loss function
            prior_mean_t, prior_std_t = self.generation_z(h[-1])

            # sampling and reparameterization (B, hidden_dim)
            z_t = self._reparameterized_sample(
                enc_mean_t, enc_std_t, metrics_input.device
            )
            # extract z features. (B, hidden_dim)
            phi_z_t = self.phi_z(z_t)
            # dict containing metrics number of keys. each of which is (B)
            lap_decoded_metrics_dict = self.decode(phi_z_t, h[-1])

            # (num_lstm_layers, B, hidden_dim)
            h_next = self.recurrence(phi_x_t, phi_z_t, h)

            # update hidden state of RNN
            h = h_next  # (num_lstm_layers, B, hidden_dim)

            # each element would be (num_valid_indices_t, z_dim)
            all_enc_std.append(enc_std_t)
            all_enc_mean.append(enc_mean_t)
            # each element would be (num_valid_indices_t, obs_dim). # list of dicts
            all_lap_decoded_metrics_dict.append(lap_decoded_metrics_dict)

            all_prior_mean.append(prior_mean_t)
            all_prior_std.append(prior_std_t)
            all_hidden_states.append(h_next)
            all_phi_z_t.append(phi_z_t)

        # (list of (B, 1, hidden_dim))
        all_hidden_states_last = [h[-1].unsqueeze(1) for h in all_hidden_states]
        # (B, K, H)
        all_hidden_states_last = torch.cat(all_hidden_states_last, dim=1)
        # Use mask to extract last valid output for each batch
        lengths = lap_masks.sum(dim=1).clamp(min=1)  # (B,)
        # (B, 1, H)
        last_indices = (
            (lengths - 1)
            .unsqueeze(1)
            .unsqueeze(2)
            .expand(-1, 1, all_hidden_states_last.size(-1))
        ).to(torch.int64)
        # (B, H)
        last_valid_rnn_hs = all_hidden_states_last.gather(
            dim=1, index=last_indices
        ).squeeze(1)

        # list of (B, 1, hidden_dim)
        all_phi_z_t = [z.unsqueeze(1) for z in all_phi_z_t]
        # (B, K, hidden_dim)
        all_phi_z_t = torch.cat(all_phi_z_t, dim=1)
        # (B, H)
        last_valid_phi_z_t = all_phi_z_t.gather(dim=1, index=last_indices).squeeze(1)

        # (B, H+H) --> (B, H)
        side_channel_lap_representation = self.fusion_net(
            torch.cat([last_valid_rnn_hs, last_valid_phi_z_t], dim=-1)
        )
        # (B, K, H+H)
        latent_representation_seq = torch.cat(
            [all_phi_z_t, all_hidden_states_last], dim=-1
        )
        # (B, K, H)
        latent_representation_seq = self.reduce_latent_layer(latent_representation_seq)
        # deal with the lap decoded metrics.
        metric_keys = all_lap_decoded_metrics_dict[0].keys()

        final_lap_decoded_metrics = {
            metric_key: torch.stack(
                [d[metric_key] for d in all_lap_decoded_metrics_dict], dim=1
            )  # shape: (B, K)
            for metric_key in metric_keys
        }
        batch["metrics"].update(final_lap_decoded_metrics)

        outputs = {
            "side_channel_lap_representation": side_channel_lap_representation,  # (B, H) after fusing z and h at the last time step
            "lap_representation": all_phi_x,  # (B, K, H) - lap input encodings
            "lap_decoded_metrics": final_lap_decoded_metrics,  # (B, K) for each key
            "enc_mean_t": all_enc_mean,
            "enc_std_t": all_enc_std,
            "prior_mean_t": all_prior_mean,
            "prior_std_t": all_prior_std,
        }

        # (B, K, H)
        outputs["latent_z_sequence"] = latent_representation_seq
        # (B, K, H)
        outputs["updated_latent_z_sequence"] = latent_representation_seq + self.A(
            latent_representation_seq
        )
        # # Output is stored back to the batch
        if self.compute_weight_penalty:
            assert self.weight_penalty_type is not None
            assert self.weight_penalty_coeff >= 0.0
            if "weight_penalty" not in batch:
                batch["weight_penalty"] = {}
            if self.weight_penalty_type == "l2":
                # do mean over l2
                l2_loss = (self.A.weight**2).mean()
                batch["weight_penalty"]["A_l2_wp_loss"] = l2_loss
                batch["weight_penalty"]["A_l2_wp_loss_scaled"] = (
                    self.weight_penalty_coeff * l2_loss
                )
            elif self.weight_penalty_type == "nuclear":
                nuclear_norm = torch.norm(self.A.weight, p="nuc") / self.A.weight.size(
                    0
                )  # divide by number of diaogonal elements
                batch["weight_penalty"]["A_nuc_wp_loss"] = nuclear_norm
                batch["weight_penalty"]["A_nuc_wp_loss_scaled"] = (
                    self.weight_penalty_coeff * nuclear_norm
                )
        # Output is stored back to the batch
        batch.update(outputs)

        return outputs


class LapModelTemporalVRNN(LapModelTemporal):
    def __init__(self, config_dict):
        super(LapModelTemporalVRNN, self).__init__(config_dict)
        self.A = nn.Linear(self.hidden_dim, self.hidden_dim, bias=False)
        self.compute_weight_penalty = config_dict.get("use_weight_penalty", False)
        self.weight_penalty_type = config_dict.get("weight_penalty_type", "l2")
        self.weight_penalty_coeff = config_dict.get("weight_penalty_coeff", 1.0)

        self.num_lstm_layers = self.config_dict.get("num_lstm_layers", 1)
        # LapModelTemporal.__init__(self, config_dict)
        # VRNN.__init__(self, config_dict)

        self.z_dim = config_dict.get("z_dim", None)
        self.num_lstm_layers = config_dict.get("num_lstm_layers", 1)
        if self.z_dim is None:
            self.z_dim = self.hidden_dim

        # overwrite metrics decoder to have z + h input dim
        self.metrics_decoder = MetricsDecoderSimpleMLP(
            self.hidden_dim + self.hidden_dim,
            self.target_metric_keys,
            dropout_prob=self.dropout,
        )

        self.reduce_latent_layer = nn.Linear(
            self.hidden_dim + self.hidden_dim, self.hidden_dim
        )
        self.phi_z = nn.Sequential(nn.Linear(self.z_dim, self.hidden_dim))
        # encoder q(z|enc(x), h)
        self.enc = nn.Sequential(
            nn.Linear(self.hidden_dim + self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim),
        )
        self.enc_mean = nn.Linear(self.hidden_dim, self.z_dim)
        self.enc_std = nn.Sequential(
            nn.Linear(self.hidden_dim, self.z_dim), nn.Softplus()
        )
        self.prior = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim), nn.ReLU()
        )
        self.prior_mean = nn.Linear(self.hidden_dim, self.z_dim)
        self.prior_std = nn.Sequential(
            nn.Linear(self.hidden_dim, self.z_dim), nn.Softplus()
        )
        self.rnn = nn.GRU(
            self.hidden_dim + self.hidden_dim,
            self.hidden_dim,
            self.num_lstm_layers,
            batch_first=True,
        )
        # To fuse z and h for final side channel representation
        self.fusion_net = nn.Sequential(
            nn.Linear(self.hidden_dim + self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim),
        )

    def decode(self, phi_z_t, h_t):
        # deterministic decoder for lap metrics decoder
        dec_mean_t = self.metrics_decoder(torch.cat([phi_z_t, h_t], 1), None)
        return dec_mean_t

    def generation_z(self, h_t):
        # prior
        prior_t = self.prior(h_t)
        # prior_mean_t, prior_std_t = torch.chunk(prior_t, 2, dim=-1)
        prior_mean_t = self.prior_mean(prior_t)
        prior_std_t = self.prior_std(prior_t)
        return prior_mean_t, prior_std_t

    def inference(self, phi_x_t, h_t):
        enc_t = self.enc(torch.cat([phi_x_t, h_t], 1))
        # enc_mean_t, enc_std_t = torch.chunk(enc_t, 2, dim=-1)
        enc_mean_t = self.enc_mean(enc_t)
        enc_std_t = self.enc_std(enc_t)
        return enc_mean_t, enc_std_t

    def recurrence(self, phi_x_t, phi_z_t, h_t, c_t=None):
        _, h_valid_next = self.rnn(torch.cat([phi_x_t, phi_z_t], 1).unsqueeze(1), h_t)
        return h_valid_next

    def _reparameterized_sample(self, mean, std, device):
        """using std to sample"""
        eps = torch.empty(size=std.size(), device=device, dtype=torch.float).normal_()
        return eps.mul(std).add_(mean)

    def forward(self, batch):
        # we are assuming that segment level trajectories etc are not affected by long term context.
        batch, outputs, lap_encoding, lap_masks = self.generate_lap_encodings(batch)

        # (B, K)
        batch_size, max_seq_len = lap_masks.size(0), lap_masks.size(1)

        all_enc_mean, all_enc_std = [], []
        all_lap_decoded_metrics_dict = []
        all_prior_mean, all_prior_std = [], []
        all_hidden_states, all_phi_z_t = [], []

        h = torch.zeros(
            self.num_lstm_layers,
            batch_size,
            self.hidden_dim,
            device=lap_masks.device,
        )

        all_phi_x = lap_encoding  # (B, K, H)
        for t in range(max_seq_len):
            # encode trajectory and map data at lap t
            # this is the feature extaction step for the "input" at time t
            # (B, H)
            lap_encoding_t = lap_encoding[
                :, t, :
            ]  # fused traj+map encoding for lap at t

            # (B, hidden_dim), (B, hidden_dim). h_valid[-1] is the lstm output after the last layer
            enc_mean_t, enc_std_t = self.inference(lap_encoding_t, h[-1])
            # (B, hidden_dim), (B, hidden_dim).
            prior_mean_t, prior_std_t = self.generation_z(h[-1])

            # sampling and reparameterization (B, hidden_dim)
            z_t = self._reparameterized_sample(enc_mean_t, enc_std_t, lap_masks.device)
            # extract z features. (B, hidden_dim)
            phi_z_t = self.phi_z(z_t)
            # dict containing metrics number of keys. each of which is (B)
            # TODO think of ways to decode the segment level trajectories? Should this go throughs
            # the z's as well. If we are trying to replicate full Dreamer like structure, then yes,
            # I think its clear to decode lap-level metrics
            # phi_z_t = (B, H), h[-1] = (B, H)
            # dict containing metrics number of keys. each of which is (B)
            lap_decoded_metrics_dict = self.decode(phi_z_t, h[-1])

            # (num_lstm_layers, B, hidden_dim)
            h_next = self.recurrence(lap_encoding_t, phi_z_t, h)
            # update hidden state of RNN
            h = h_next  # (num_lstm_layers, B, hidden_dim)

            # each element would be (num_valid_indices_t, z_dim)
            all_enc_std.append(enc_std_t)
            all_enc_mean.append(enc_mean_t)
            # each element would be (num_valid_indices_t, obs_dim). # list of dicts
            all_lap_decoded_metrics_dict.append(lap_decoded_metrics_dict)
            all_prior_mean.append(prior_mean_t)
            all_prior_std.append(prior_std_t)
            all_hidden_states.append(h_next)
            all_phi_z_t.append(phi_z_t)

        # (list of (B, 1, hidden_dim))
        all_hidden_states_last = [h[-1].unsqueeze(1) for h in all_hidden_states]
        # (B, K, H)
        all_hidden_states_last = torch.cat(all_hidden_states_last, dim=1)
        # list of (B, 1, hidden_dim)
        all_phi_z_t = [z.unsqueeze(1) for z in all_phi_z_t]
        # (B, K, hidden_dim)
        all_phi_z_t = torch.cat(all_phi_z_t, dim=1)

        # Use mask to extract last valid output for each batch
        lengths = lap_masks.sum(dim=1).clamp(min=1)  # (B,)
        # (B, 1, H)
        last_indices = (
            (lengths - 1)
            .unsqueeze(1)
            .unsqueeze(2)
            .expand(-1, 1, all_hidden_states_last.size(-1))
        ).to(torch.int64)
        # (B, H)
        last_valid_rnn_hs = all_hidden_states_last.gather(
            dim=1, index=last_indices
        ).squeeze(1)
        # extract last valid z
        # (B, H)
        last_valid_phi_z_t = all_phi_z_t.gather(dim=1, index=last_indices).squeeze(1)

        # (B, H+H) --> (B, H)
        side_channel_lap_representation = self.fusion_net(
            torch.cat([last_valid_rnn_hs, last_valid_phi_z_t], dim=-1)
        )
        # (B, K, H+H)
        latent_representation_seq = torch.cat(
            [all_phi_z_t, all_hidden_states_last], dim=-1
        )
        # (B, K, H)
        latent_representation_seq = self.reduce_latent_layer(latent_representation_seq)
        # deal with the lap decoded metrics.
        metric_keys = all_lap_decoded_metrics_dict[0].keys()

        final_lap_decoded_metrics = {
            metric_key: torch.stack(
                [d[metric_key] for d in all_lap_decoded_metrics_dict], dim=1
            )  # shape: (B, K)
            for metric_key in metric_keys
        }
        batch["metrics"].update(final_lap_decoded_metrics)

        outputs.update(
            {
                "side_channel_lap_representation": side_channel_lap_representation,  # (B, H) after fusing z and h at the last time step
                "lap_representation": all_phi_x,  # (B, K, H) - lap input encodings
                "lap_decoded_metrics": final_lap_decoded_metrics,  # (B, K) for each key
                "enc_mean_t": all_enc_mean,
                "enc_std_t": all_enc_std,
                "prior_mean_t": all_prior_mean,
                "prior_std_t": all_prior_std,
            }
        )

        # (B, K, H)
        outputs["latent_z_sequence"] = latent_representation_seq
        # (B, K, H)
        outputs["updated_latent_z_sequence"] = latent_representation_seq + self.A(
            latent_representation_seq
        )
        # # Output is stored back to the batch
        if self.compute_weight_penalty:
            assert self.weight_penalty_type is not None
            assert self.weight_penalty_coeff >= 0.0
            if "weight_penalty" not in batch:
                batch["weight_penalty"] = {}
            if self.weight_penalty_type == "l2":
                # do mean over l2
                l2_loss = (self.A.weight**2).mean()
                batch["weight_penalty"]["A_l2_wp_loss"] = l2_loss
                batch["weight_penalty"]["A_l2_wp_loss_scaled"] = (
                    self.weight_penalty_coeff * l2_loss
                )
            elif self.weight_penalty_type == "nuclear":
                nuclear_norm = torch.norm(self.A.weight, p="nuc") / self.A.weight.size(
                    0
                )  # divide by number of diaogonal elements
                batch["weight_penalty"]["A_nuc_wp_loss"] = nuclear_norm
                batch["weight_penalty"]["A_nuc_wp_loss_scaled"] = (
                    self.weight_penalty_coeff * nuclear_norm
                )
        # Output is stored back to the batch
        batch.update(outputs)

        return outputs
