# full model

# self.skill encoder - takes in 'full trajectory from a trial' and encodes it into a low dimensional latent space
# self.skill_proj - takes the output of the skill encoder and projects it into language space

# self.concurrent_feedback
#   self.traj_encoder_concurrent_feedback - Encoder for encoding short trajectory
#   self.map_encoder - borrowed from earlier IL model
#   self.teacher_action_decoder - emits taxonomy based teacher action.
#   Optional
#        self.trajectory_decoder - if we are going with MTL set up

# TerminalLLM
# self.traj_encoder_terminal - should this be different from self.traj_encoder_concurrent_feedback
# self.terminal feedback llm -
import copy
import logging
import warnings
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple, Union

import torch
import torch.distributed as dist
from peft import LoraConfig, TaskType, get_peft_model
from torch import nn
from torch.nn import CrossEntropyLoss, MSELoss
from transformers.cache_utils import Cache
from transformers.generation.configuration_utils import GenerationConfig
from transformers.generation.logits_process import LogitsProcessorList
from transformers.generation.stopping_criteria import (
    Stopping<PERSON><PERSON>riaList,
    validate_stopping_criteria,
)
from transformers.trainer import TrainerControl, TrainerState, TrainingArguments

from aic_bsd_model.models.aic_base_model import (
    LapModelNonTemporal,
    LapModelTemporalLSTM,
    LapModelTemporalTransformer,
    LapModelTemporalVRNN,
    MetricsEncoderDecoderLSTM,
    MetricsEncoderDecoderVRNN,
)
from aic_bsd_model.models.hf_model import HuggingFaceModel
from aic_bsd_model.models.model_configs import ModelConfig
from aic_bsd_model.models.networks.additional_modules import MultiPosInfoNCELoss

from .aic_bsd_concurrent_feedback_model import AIC_BSD_Concurrent
from .aic_bsd_terminal_feedback_llama_model import AIC_BSD_Terminal


class AICBSDFullModel(HuggingFaceModel):
    def __init__(self, config_dict, train_dataset):
        super(AICBSDFullModel, self).__init__(config_dict, train_dataset)
        self.training_mode = config_dict.get("training_mode", "full_model")
        self.lap_model_temporal_structure = config_dict.get(
            "lap_model_temporal_structure", None
        )
        self.use_infonce = config_dict.get("use_infonce", False)

        # self.terminal_feedback_module = AIC_BSD_Terminal.from_pretrained(
        #     cfg.llm_backbone_id
        # )
        # # do we need this .train() here or would the full model.train take care of this as well
        # self.terminal_feedback_module.train()

        # peft_config = LoraConfig(
        #     task_type=TaskType.CAUSAL_LM,
        #     inference_mode=False,
        #     r=16,
        #     lora_alpha=16,
        #     lora_dropout=0.1,
        #     target_modules=[
        #         "q_proj",
        #         "k_proj",
        #         "v_proj",
        #         "o_proj",
        #         "gate_proj",
        #         "up_proj",
        #         "down_proj",
        #         "lm_head",
        #     ],
        # )

        # self.terminal_feedback_module = get_peft_model(
        #     self.terminal_feedback_module, peft_config
        # )

        if self.training_mode == "cf_only" or self.training_mode == "full_model":
            self.concurrent_feedback_module = AIC_BSD_Concurrent(config_dict)

        if self.training_mode == "lap_only" or self.training_mode == "full_model":
            if self.lap_model_temporal_structure == None:
                print(f"Using non temporal lap level model with {self.training_mode}")
                # assert self.config['num_prev_laps'] == 1
                self.lap_encoder_and_performance_model = LapModelNonTemporal(
                    config_dict
                )
            elif self.lap_model_temporal_structure == "LSTM":
                print(
                    f"Using {self.lap_model_temporal_structure} temporal lap level model with {self.training_mode}"
                )
                self.lap_encoder_and_performance_model = LapModelTemporalLSTM(
                    config_dict
                )
            elif self.lap_model_temporal_structure == "Transformer":
                print(
                    f"Using {self.lap_model_temporal_structure} temporal lap level model with {self.training_mode}"
                )
                self.lap_encoder_and_performance_model = LapModelTemporalTransformer(
                    config_dict
                )
            elif self.lap_model_temporal_structure == "LapModelTemporalVRNN":
                print(
                    f"Using {self.lap_model_temporal_structure} temporal lap level model with {self.training_mode}"
                )
                self.lap_encoder_and_performance_model = LapModelTemporalVRNN(
                    config_dict
                )

            elif self.lap_model_temporal_structure == "MetricsEncoderDecoderVRNN":
                print(
                    f"Using {self.lap_model_temporal_structure} temporal lap level model with with {self.training_mode}"
                )

                self.lap_encoder_and_performance_model = MetricsEncoderDecoderVRNN(
                    config_dict
                )
            elif self.lap_model_temporal_structure == "MetricsEncoderDecoderLSTM":
                print(
                    f"Using {self.lap_model_temporal_structure} temporal lap level model with with {self.training_mode}"
                )

                self.lap_encoder_and_performance_model = MetricsEncoderDecoderLSTM(
                    config_dict
                )

        if self.use_infonce:
            z_dim = config_dict.get("hidden_dim", 32)
            p_dim = self.concurrent_feedback_module.cf_action_decoder_input_dim
            self.infonce_module = MultiPosInfoNCELoss(
                z_dim,
                p_dim,
                proj_dim=16,
                loss_coeff=config_dict.get("infonce_loss_coeff", 1.0),
            )

    def my_forward(self, batch):
        if self.training_mode == "cf_only":
            return self.concurrent_feedback_module(batch)
        elif self.training_mode == "lap_only":
            return self.lap_encoder_and_performance_model(batch)
        elif self.training_mode == "full_model":
            lap_model_outputs = self.lap_encoder_and_performance_model(batch)
            # side channel representation is already in the batch variable.
            cf_model_outputs = self.concurrent_feedback_module(batch)
            if self.use_infonce:
                loss, mi_lb = self.infonce_module(batch)
            return (lap_model_outputs, cf_model_outputs)

            # if one lap and next laps cf snippets.
            # run lap encoder. get intermediate representation and pass it as additional input to the concurrent feedback

            # if lap sequence.
            # put a for loop along the lap dimension.
            # step through each lap.
            # run lap module. get z and h. Pass it as intermediate representation to cf model
            # update h.
            # got to next step.
            # repeat.
            pass
            # run lap encoding
            # get intermediate representations to pass into side channel information
            #

    # lap_i (as current traj segments and curr map segments) comes in.
    # goes into the LapModelNonTemporal.
    # output is full lap encoding, segment level encoding, decoded, segment level traj, skill embedding,
    # , metrics vector.

    # The terminal feedback LLM takes in the lap level encoding, segment level encoding, skill embedding, input_ids (target text), combined_input sequence (which would masks for where to put
    # the skill encoding, segment level encoding and lap level encoding. )
    # (the actual filling happens in the forward function of the LLM),
    # output is predicted text, and LLM hidden state (need to decided which one to pull?)

    # the snippet level traj and map goes into the concurrent feedback model encoder. The fused encoded state
    # is combined with the skill embedding output from LapModelNonTemporal, downproject LLM hidden sttae)
    # the concurrent deocder decodes future trajectory, future teahcer action prediction. similar to IL model.
    # In the ablated version, the side channel information from the skill embedding output and downprojected LLM hidden
    # would be zeroed out, essentially making it a non-adaptive concurrent feedback model.
