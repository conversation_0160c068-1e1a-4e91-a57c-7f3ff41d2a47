import numpy as np
from scipy.spatial.transform import Rotation as R


class CoordinateNormalizationUtil:
    @staticmethod
    def compute_yaw_from_trajectory(trajectory, valid, time_dim):
        return 0.0

    @staticmethod
    def normalize_map_wrt_frame(map, T_bg):
        transformed_map = {}
        for map_key in map.keys():
            # (N, 4)
            map_pose = map[map_key][:, :2] if map_key == "race_line" else map[map_key]
            aug_map_positions = np.hstack(
                (map_pose, np.array([[0, 1]] * map_pose.shape[0]))
            )
            transformed_aug_map_positions = (T_bg @ aug_map_positions.T).T  # (N, 4)
            if map_key == "race_line":
                transformed_map[map_key] = transformed_aug_map_positions[
                    :, :2
                ]  # (N, 2)
                transformed_map[map_key] = np.hstack(
                    (transformed_map[map_key], map[map_key][:, -1, None])
                )  # (N, 3)
            else:
                transformed_map[map_key] = transformed_aug_map_positions[
                    :, :2
                ]  # (N, 2)

            # convert np.matrix to np.array for ease of downstream usage
            transformed_map[map_key] = np.array(transformed_map[map_key])

        return transformed_map

    @staticmethod
    def normalize_3d(trajectory, normalization_frame_timestep=-1, with_accel=False):
        # assumes that the trajectory has the quaternion information in them.

        assert trajectory.shape[0] > 2
        # # grab the normalization timestamp's quaternion. last 4 indices are quaternion
        quat_normalization_frame = trajectory[normalization_frame_timestep][-4:]

        # # convention is R_gb means rotation b (body frame) with respect to g (global frame_).
        # np.matrix (3,3)
        R_gb = R.from_quat(quat_normalization_frame).as_matrix()
        # # rotation matrix of global frame with respect to normalization timestep body frame
        R_bg = np.linalg.inv(R_gb)  # np.matrix (3,3)

        # # grab the position from normalization timestamp first three indices
        p_gb = trajectory[normalization_frame_timestep][:3]

        # # create transformation matrix. body frame of normalization time step with respect to global
        T_gb = np.asarray(
            [
                np.concatenate((np.array(R_gb[0, :]).flatten(), p_gb[0:1])),
                np.concatenate((np.array(R_gb[1, :]).flatten(), p_gb[1:2])),
                np.concatenate((np.array(R_gb[2, :]).flatten(), p_gb[2:])),
                np.array([0, 0, 0, 1]),
            ]
        )  # np.matrix (4,4)

        # # transformation matrix of global with respect to body frame
        T_bg = np.linalg.inv(T_gb)  # np.matrix (4,4)

        # #TODO (deepak.gopinath) index the positions, velocities, quaternoins using the Enum index
        # # transform positions
        transformed_trajectory = np.zeros_like(trajectory)

        positions = trajectory[:, :3]  # (T, 3)
        T = positions.shape[0]
        aug_positions = np.hstack(
            (trajectory[:, :3], np.expand_dims(np.array([1] * T), 1))
        )  # (T, 4)

        transformed_aug_positions = (T_bg @ aug_positions.T).T  # (T, 4)
        transformed_trajectory[:, :3] = transformed_aug_positions[:, :3]

        # # # transform velocities
        velocities = trajectory[:, 3:6]  # (T, 3)
        transformed_velocities = (R_bg @ velocities.T).T
        transformed_trajectory[:, 3:6] = transformed_velocities

        # # transform acceleration
        if with_accel:
            accelerations = trajectory[:, 6:9]  # (T, 3)
            transformed_accelerations = (R_bg @ accelerations.T).T
            transformed_trajectory[:, 6:9] = transformed_accelerations
            # copy steering and throttle:
            transformed_trajectory[:, 9:12] = trajectory[:, 9:12]
        else:
            transformed_trajectory[:, 6:9] = trajectory[:, 6:9]

        # # transform orientation
        R_bg_all = (
            R.from_quat(trajectory[:, -4:]).inv().as_matrix()
        )  # (T, 3, 3) np.array
        # R_gb is a np.matrix. Need to do np.array for broadcasting
        transformed_R = np.matmul(R_bg_all, np.array(R_gb))  # (T, 3, 3)
        transformed_quat = R.from_matrix(transformed_R).as_quat()  # (T, 4)
        transformed_trajectory[:, -4:] = transformed_quat

        return transformed_trajectory, T_bg
