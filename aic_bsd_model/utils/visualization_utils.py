import math
from typing import List

import imageio
import imageio.v3 as iio
import matplotlib
import matplotlib.patches as patches
import numpy as np
import torch
import umap
from matplotlib import pyplot as plt
from matplotlib.gridspec import GridSpec
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE

matplotlib.use("Agg")

from torch.utils.data import Dataset

import wandb
from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_DEFAULT,
    CategoryDictVizIndex,
    TrajFeatureIndexNoAccel,
)
from aic_bsd_model.utils.training_utils import parse_batch, parse_snippet_batch


class VideoVisCFDataset(Dataset):
    """This dataset is used to visualize the reconstructed trajectories in a video format.
    It takes a dataset and a list of dataset's indices to visualize.
    """

    def __init__(self, dataset, config, trial_idxs):
        self.dataset = dataset
        self.config = config
        self.trial_idxs = trial_idxs

    def __len__(self):
        return len(self.trial_idxs)

    def __getitem__(self, idx):
        item = self.dataset[self.trial_idxs[idx]]
        return item


def vis_traj(
    batch,
    batch_i,
    xy,
    xy_pred,
    map_xy,
    cones_xy,
    xy_valid,
    pred_valid,
    curr_lap_uid,
    dataloader_name,
    segment_i,
    pred_label="Reconstructed",
    fix_ax=None,
    additional_title_tag=None,
):
    x, y = xy[:, 0], xy[:, 1]
    x_pred, y_pred = xy_pred[:, 0], xy_pred[:, 1]
    handle = {}
    alpha = 0.5
    if fix_ax is None:
        fig, ax = plt.subplots()
        handle["plot"] = fig, ax
    else:
        fig, ax = fix_ax

    x_valid = x[xy_valid]
    y_valid = y[xy_valid]
    x_pred_valid = x_pred[pred_valid]
    y_pred_valid = y_pred[pred_valid]
    ax.plot(x_valid, y_valid, "g", label="Ground Truth Segment", alpha=alpha)
    ax.annotate(
        "",
        xy=(x_valid[-1], y_valid[-1]),  # Arrow tip
        xytext=(
            x_valid[-2],
            y_valid[-2],
        ),
        arrowprops=dict(arrowstyle="->", color="g", lw=2),
        alpha=alpha,
    )
    ax.plot(x_pred_valid, y_pred_valid, "b", label=pred_label, alpha=0.7)
    ax.annotate(
        "",
        xy=(x_pred_valid[-1], y_pred_valid[-1]),  # Arrow tip
        xytext=(x_pred_valid[-2], y_pred_valid[-2]),
        arrowprops=dict(arrowstyle="->", color="b", lw=2),
        alpha=alpha,
    )
    plot_map(ax, map_xy, cones_xy, alpha=alpha)
    if additional_title_tag is None:
        ax.set_title(f"BEV Trajectory - UID {(batch_i, segment_i, curr_lap_uid)}")
    else:
        ax.set_title(
            f"BEV Trajectory - UID {(batch_i, segment_i, curr_lap_uid)} - {additional_title_tag}"
        )
    ax.legend(fontsize="small", framealpha=0.5)
    ax.axis("equal")

    return handle


def plot_map(ax, map_xy, cones_xy, alpha):
    left_lane, right_lane, racing_line = map_xy[:, :2], map_xy[:, 2:4], map_xy[:, -2:]
    left_lane_mask = left_lane.abs().max(dim=-1)[0] < 1000
    right_lane_mask = right_lane.abs().max(dim=-1)[0] < 1000
    racing_line_mask = racing_line.abs().max(dim=-1)[0] < 1000
    ax.plot(
        left_lane[left_lane_mask, 0],
        left_lane[left_lane_mask, 1],
        "k",
        label="lane",
        alpha=alpha,
    )
    ax.plot(
        right_lane[right_lane_mask, 0],
        right_lane[right_lane_mask, 1],
        "k",
        label="lane",
        alpha=alpha,
    )
    ax.plot(
        racing_line[racing_line_mask, 0],
        racing_line[racing_line_mask, 1],
        "r--",
        label="racing line",
        alpha=alpha,
    )
    cones_mask = cones_xy.abs().sum(dim=-1) != 0
    # Do not change the XY limit
    xlim = ax.get_xlim()
    ylim = ax.get_ylim()
    ax.scatter(
        cones_xy[cones_mask, 0],
        cones_xy[cones_mask, 1],
        c="r",
        s=100,
        label="cones",
        alpha=alpha,
    )
    ax.set_xlim(xlim)
    ax.set_ylim(ylim)


def plot_metric(estimated_metric_dict, gt_metric_dict):
    ret = {}
    for key in estimated_metric_dict:
        gt = gt_metric_dict[key]
        est = estimated_metric_dict[key]

        fig, ax = plt.subplots()

        # Calculate min and max ground truth values
        min_val = torch.min(gt).item()
        max_val = torch.max(gt).item()
        # Create a rectangle (bounding box) spanning the ground truth limits
        width = max_val - min_val
        height = max_val - min_val
        rect = patches.Rectangle(
            (min_val, min_val),
            width,
            height,
            linewidth=2,
            edgecolor="r",
            facecolor="none",
            label="Ground Truth Bounds",
        )
        ax.add_patch(rect)

        ax.scatter(gt, est, alpha=0.5)
        ax.set_xlabel("Ground Truth")
        ax.set_ylabel("Estimated")
        ax.set_title(f"Metrics scatter: {key}")

        ax.legend()

        ret[f"{key}_scatter"] = wandb.Image(fig)

        plt.close(fig)
    return ret


def visualize_confusion_matrix(conf_matrices, config_dict, wandb_key):
    """
    confusion matrix is N, 2, 2
    """

    conf_matrices = np.array(conf_matrices)
    if config_dict["valid_annotation_categories_index_dict_type"] == "default":
        valid_annotation_categories_index_viz_dict = CategoryDictVizIndex.DEFAULT.value
    elif config_dict["valid_annotation_categories_index_dict_type"] == "no_steering":
        valid_annotation_categories_index_viz_dict = (
            CategoryDictVizIndex.NO_STEERING.value
        )
    elif (
        config_dict["valid_annotation_categories_index_dict_type"]
        == "no_steering_no_turn"
    ):
        valid_annotation_categories_index_viz_dict = (
            CategoryDictVizIndex.NO_STEERING_NO_TURN.value
        )
    elif (
        config_dict["valid_annotation_categories_index_dict_type"]
        == "no_steering_no_turn_no_brake"
    ):
        valid_annotation_categories_index_viz_dict = (
            CategoryDictVizIndex.NO_STEERING_NO_TURN_NO_BRAKE.value
        )
    elif config_dict["valid_annotation_categories_index_dict_type"] == "basic":
        valid_annotation_categories_index_viz_dict = CategoryDictVizIndex.BASIC.value

    class_names = list(valid_annotation_categories_index_viz_dict.keys())

    # TP/TN/FP/FN position labels
    label_map = [["TN", "FP"], ["FN", "TP"]]
    # Grid layout
    N = len(conf_matrices)
    cols = math.ceil(math.sqrt(N))
    rows = math.ceil(N / cols)
    fig, axes = plt.subplots(rows, cols, figsize=(cols * 4, rows * 4))
    axes = np.array(axes).reshape(rows, cols)  # Make 2D for uniform indexing

    for i, matrix in enumerate(conf_matrices):
        row, col = divmod(i, cols)
        ax = axes[row][col]

        im = ax.imshow(matrix, cmap="Blues", vmin=0, vmax=np.max(conf_matrices))

        # Add TP/TN/FP/FN + value to each cell
        for r in range(2):
            for c in range(2):
                val = matrix[r, c]
                label = f"{label_map[r][c]}\n{val:.3f}"  # or use a variable for the precision
                ax.text(
                    c, r, label, va="center", ha="center", fontsize=10, color="black"
                )

        ax.set_xticks([0, 1])
        ax.set_yticks([0, 1])
        ax.set_xticklabels(["Pred 0", "Pred 1"])
        ax.set_yticklabels(["True 0", "True 1"])
        ax.set_title(f"{class_names[i]}")
        ax.set_xlabel("Predicted")
        ax.set_ylabel("True")
    # Hide unused axes
    for j in range(i + 1, rows * cols):
        row, col = divmod(j, cols)
        axes[row][col].axis("off")

    plt.tight_layout()
    ret = {}
    ret[wandb_key] = wandb.Image(fig)
    plt.close(fig)
    return ret


def visualize_metrics_lineplot(
    values, x_values, counts_per_trial, x_label, plot_title, wandb_key
):
    ret = {}
    fig, ax = plt.subplots()
    ax.plot(x_values, values)
    ax.set_xticks(ticks=x_values, labels=x_values)
    # Annotate each point with sample count
    for x, y, count in zip(x_values, values, counts_per_trial):
        ax.annotate(
            f"n={count}",
            xy=(x, y),
            xytext=(0, 8),
            textcoords="offset points",
            ha="center",
            fontsize=7,
            color="gray",
        )
    ax.set_xlabel(x_label)
    ax.set_title(plot_title)
    ax.set_ylim([0.55, 0.8])
    ret[wandb_key] = wandb.Image(fig)
    plt.close(fig)
    return ret


def visualize_histograms(values, x_label, hist_title, wandb_key, bins=20):
    ret = {}
    fig, ax = plt.subplots()
    ax.hist(values, bins=bins)
    ax.set_xlabel(x_label)
    ax.set_title(hist_title)
    ax.set_xlim([0, 1])
    # ax.axis("equal")
    ret[wandb_key] = wandb.Image(fig)
    plt.close(fig)
    return ret


def visualize_batch_reconstructed_lap_segments(
    batch, dataloader_name, config_dict, count, plot_callback=None
):
    if "reconstructed_segment_trajectories" not in batch:
        return {}

    curr_lap_reconstructed_segment_trajectories = batch[
        "reconstructed_segment_trajectories"
    ]
    curr_map_cones = batch["curr_map_cones"]
    reconstructed_pred_mask = batch["reconstructed_pred_mask"]

    (
        curr_trajectory_segments,
        curr_map_segments,
        curr_traj_segments_lengths,
    ) = parse_batch(batch, None)

    if curr_trajectory_segments.ndim == 5:
        curr_trajectory_segments = curr_trajectory_segments[:, 0]
        curr_map_segments = curr_map_segments[:, 0]
        curr_traj_segments_lengths = curr_traj_segments_lengths[:, 0]
        curr_lap_reconstructed_segment_trajectories = (
            curr_lap_reconstructed_segment_trajectories[:, 0]
        )
        reconstructed_pred_mask = reconstructed_pred_mask[:, 0]
        curr_map_cones = curr_map_cones[:, 0]

    ret = {}
    for batch_i in range(min(count, curr_trajectory_segments.shape[0])):
        for segment_i in range(curr_trajectory_segments.shape[1]):
            xy = (
                curr_trajectory_segments[
                    batch_i,
                    segment_i,
                    :,
                    TrajFeatureIndexNoAccel.TRAJ_X : TrajFeatureIndexNoAccel.TRAJ_X + 2,
                ]
                .detach()
                .cpu()
            )

            xy_pred = (
                curr_lap_reconstructed_segment_trajectories[
                    batch_i,
                    segment_i,
                    :,
                    TrajFeatureIndexNoAccel.TRAJ_X : TrajFeatureIndexNoAccel.TRAJ_X + 2,
                ]
                .detach()
                .cpu()
            )
            xy_valid = ~(xy[:, 0] == config_dict["padding_value"])
            pred_valid = (
                reconstructed_pred_mask[batch_i, segment_i, :, 0].detach().cpu()
            )
            # if multiple laps, update [0]
            curr_lap_uid = batch["curr_lap_uid"][batch_i][0]

            map_xy = curr_map_segments[batch_i, segment_i, :].cpu()
            cones_xy = curr_map_cones[batch_i, segment_i, :].cpu()
            handle = vis_traj(
                batch,
                batch_i,
                xy,
                xy_pred,
                map_xy,
                cones_xy,
                xy_valid,
                pred_valid,
                curr_lap_uid,
                dataloader_name,
                segment_i,
            )
            if plot_callback is not None:
                plots, handle = plot_callback(
                    handle,
                    batch,
                    batch_i,
                    xy,
                    xy_pred,
                    xy_valid,
                    curr_lap_uid,
                    dataloader_name,
                    segment_i,
                )
            ret[f"Lap Seg Trajectory-b{batch_i}-s{segment_i}"] = wandb.Image(
                handle["plot"][0]
            )
            plt.close(handle["plot"][0])

    return ret


def visualize_teacher_action_prediction(
    handle,
    batch,
    batch_i,
    xy,
    xy_pred,
    valid,
    curr_lap_uid,
    dataloader_name,
    segment_i,
    valid_annotation_categories_index_dict_type,
):
    fig, ax = handle["plot"]
    _, table_ax = handle["table"]
    # fig.set_size_inches(6, 8)
    # ax.set_position([0.1, 0.5, 0.85, 0.35])
    action_pred = batch["cf_teacher_action_sig"][batch_i, segment_i, :].detach().cpu()
    # action_pred = action_pred.argmax(dim=-1)
    action_gt = (
        batch["cf_teacher_action_existence_gt"][batch_i, segment_i, :].detach().cpu()
    )
    light_blue = [d / 255 for d in (0, 153, 255)]
    light_green = [d / 255 for d in (102, 255, 153)]
    light_red = [d / 255 for d in (255, 77, 77)]

    if valid_annotation_categories_index_dict_type == "default":
        valid_annotation_categories_index_viz_dict = CategoryDictVizIndex.DEFAULT.value
    elif valid_annotation_categories_index_dict_type == "no_steering":
        valid_annotation_categories_index_viz_dict = (
            CategoryDictVizIndex.NO_STEERING.value
        )
    elif valid_annotation_categories_index_dict_type == "no_steering_no_turn":
        valid_annotation_categories_index_viz_dict = (
            CategoryDictVizIndex.NO_STEERING_NO_TURN.value
        )
    elif valid_annotation_categories_index_dict_type == "no_steering_no_turn_no_brake":
        valid_annotation_categories_index_viz_dict = (
            CategoryDictVizIndex.NO_STEERING_NO_TURN_NO_BRAKE.value
        )
    elif valid_annotation_categories_index_dict_type == "basic":
        valid_annotation_categories_index_viz_dict = CategoryDictVizIndex.BASIC.value

    if len(list(valid_annotation_categories_index_viz_dict.keys())) % 2 == 0:
        # if there are odd number of categories, just don't visualize the no_op category anyway.
        # so that the table works fine.
        colLabels = list(valid_annotation_categories_index_viz_dict.keys())
        action_pred = action_pred[1:]
        action_gt = action_gt[1:]
    else:
        colLabels = ["no_op"] + list(valid_annotation_categories_index_viz_dict.keys())

    gt_int = action_gt.int().tolist()
    cell_text = [
        [str(x) for x in gt_int],
        ["{:0.2f}".format(d) for d in action_pred],
    ]
    round_pred = torch.round(action_pred).tolist()
    cell_colors = [
        [light_blue if x == 1 else "w" for x in gt_int],
        [
            light_green if round_pred[i] == gt_int[i] else light_red
            for i in range(len(gt_int))
        ],
    ]
    # Split the labels into two halves

    split_idx = len(colLabels) // 2
    new_cell_text = []
    new_cell_colors = []
    new_cell_text.append(cell_text[0][:split_idx])
    new_cell_text.append(cell_text[1][:split_idx])
    new_cell_text.append(colLabels[split_idx:])
    new_cell_text.append(cell_text[0][split_idx:])
    new_cell_text.append(cell_text[1][split_idx:])

    new_cell_colors.append(cell_colors[0][:split_idx])
    new_cell_colors.append(cell_colors[1][:split_idx])
    new_cell_colors.append([None] * split_idx)
    new_cell_colors.append(cell_colors[0][split_idx:])
    new_cell_colors.append(cell_colors[1][split_idx:])
    cell_text = new_cell_text
    cell_colors = new_cell_colors

    x_l, x_r = ax.get_xlim()
    y_l, y_r = ax.get_ylim()
    x_len, y_len = x_r - x_l, y_r - y_l
    len_diff = abs(y_len - x_len) / 2

    if y_len > x_len:
        x_l -= len_diff
        x_r += len_diff
        ax.set_xlim(x_l, x_r)
    else:
        y_l -= len_diff
        y_r += len_diff
        ax.set_ylim(y_l, y_r)

    table_ax.axis("off")  # Hide this axes frame.

    tb = table_ax.table(
        cellText=cell_text,
        cellColours=cell_colors,
        rowLabels=["truth", "pred", "label", "truth", "pred"],
        # rowColours=["g", "r"],
        colLabels=colLabels[:split_idx],
        loc="center",
        # bbox=[0.0, 0.0, 1, 0.3],
        fontsize=6,
        alpha=0.5,
    )
    tb.auto_set_font_size(True)
    tb.auto_set_column_width(col=list(range(len(colLabels))))
    # fig.subplots_adjust(bottom=0.5)
    fig.tight_layout()
    return None


def visualize_batch_predicted_cf_future_trajectories(
    batch, dataloader_name, config_dict, count, plot_callback=None
):
    if "cf_predicted_future_trajectories" not in batch:
        return {}
    snippet_i = 0

    cf_predicted_future_trajectories = batch["cf_predicted_future_trajectories"]
    valid_annotation_categories_index_dict_type = config_dict[
        "valid_annotation_categories_index_dict_type"
    ]

    (
        snippet_trajectories,
        snippet_map_segments,
        snippet_full_trajectory_lengths,
        snippet_cf_action_series,
    ) = parse_snippet_batch(batch, return_cf_series=True)
    ret = {}
    for batch_i in range(min(count, snippet_trajectories.shape[0])):
        xy = (
            snippet_trajectories[
                batch_i,
                :,
                TrajFeatureIndexNoAccel.TRAJ_X : TrajFeatureIndexNoAccel.TRAJ_X + 2,
            ]
            .detach()
            .cpu()
        )

        xy_valid = ~(xy[:, 0] == config_dict["padding_value"])
        pred_valid = (
            batch["cf_future_pred_mask"][batch_i, snippet_i, :, 0].detach().cpu()
        )
        xy_pred = (
            cf_predicted_future_trajectories[
                batch_i,
                snippet_i,
                :,
                TrajFeatureIndexNoAccel.TRAJ_X : TrajFeatureIndexNoAccel.TRAJ_X + 2,
            ]
            .detach()
            .cpu()
        )
        curr_lap_uid = batch["snippet_lap_uid"][batch_i]

        map_xy = batch["snippet_normalized_map_subsampled"][batch_i, :].cpu()
        cones_xy = batch["snippet_cones"][batch_i, :].cpu()

        fig = plt.figure(figsize=(6, 8))
        gs = GridSpec(2, 1, figure=fig, height_ratios=[5, 1], hspace=0.1)

        ax = fig.add_subplot(gs[0, 0])  # big plot
        table_ax = fig.add_subplot(gs[1, 0])  # table underneath
        handle = {}
        handle["plot"] = (fig, ax)
        handle["table"] = (fig, table_ax)

        vis_traj(
            batch,
            batch_i,
            xy,
            xy_pred,
            map_xy,
            cones_xy,
            xy_valid,
            pred_valid,
            curr_lap_uid,
            dataloader_name,
            snippet_i,
            pred_label="Predicted",
            fix_ax=(fig, ax),
        )
        if plot_callback is not None:
            plot_callback(
                handle,
                batch,
                batch_i,
                xy,
                xy_pred,
                xy_valid,
                curr_lap_uid,
                dataloader_name,
                snippet_i,
                valid_annotation_categories_index_dict_type,
            )

        ret[f"Snippet Trajectory-b{batch_i}-s{snippet_i}"] = wandb.Image(fig)

        plt.close(fig)

    return ret


def visualize_latent_comparison(
    batch,
    dataloader_name,
    config_dict,
    count,
    latent_tensor,
    dim=2,
    labels=None,
    random_state=42,
):
    """
    Visualize a latent tensor using PCA, t-SNE, and UMAP side by side.

    Args:
        latent_tensor (torch.Tensor): shape (B, H)
        dim (int): 2 or 3
        labels (array-like): optional, shape (B,) for coloring
        random_state (int): for reproducibility
    """

    dim = config_dict.get("latent_viz_dim", 2)
    assert dim in [2, 3], "dim must be 2 or 3"

    assert "side_channel_representation" in batch
    algos = ["pca", "tsne", "umap"]
    reducers = {
        "pca": PCA(n_components=dim, random_state=config_dict.get("seed", 42)),
        "tsne": TSNE(n_components=dim, random_state=config_dict.get("seed", 42)),
        "umap": umap.UMAP(n_components=dim, random_state=config_dict.get("seed", 42)),
    }

    # Setup figure
    fig = plt.figure(figsize=(6 * len(algos), 6))

    for i, algo in enumerate(algos):
        X_reduced = reducers[algo].fit_transform(X)

        if dim == 2:
            ax = fig.add_subplot(1, len(algos), i + 1)
            if labels is not None:
                sc = ax.scatter(
                    X_reduced[:, 0], X_reduced[:, 1], c=labels, cmap="viridis", s=20
                )
            else:
                ax.scatter(X_reduced[:, 0], X_reduced[:, 1], s=20)
            ax.set_xlabel("Dim 1")
            ax.set_ylabel("Dim 2")
            ax.set_title(f"{algo.upper()} (2D)")
        else:
            ax = fig.add_subplot(1, len(algos), i + 1, projection="3d")
            if labels is not None:
                sc = ax.scatter(
                    X_reduced[:, 0],
                    X_reduced[:, 1],
                    X_reduced[:, 2],
                    c=labels,
                    cmap="viridis",
                    s=20,
                )
                plt.colorbar(sc, ax=ax, shrink=0.5)
            else:
                ax.scatter(X_reduced[:, 0], X_reduced[:, 1], X_reduced[:, 2], s=20)
            ax.set_xlabel("Dim 1")
            ax.set_ylabel("Dim 2")
            ax.set_zlabel("Dim 3")
            ax.set_title(f"{algo.upper()} (3D)")

    plt.tight_layout()
    plt.show()


def image_to_video(
    wandb_images: List["wandb.Image"],
    output_path: str = "wandb_images_video.mp4",
    fps=1,
):
    frames = []
    for wb_img in wandb_images:
        arr = iio.imread(wb_img._path)
        frames.append(arr)
    imageio.mimsave(output_path, frames, fps=fps)
