import argparse
import hashlib
import random
import time
import uuid

import numpy as np
import torch


def str2bool(v):
    if isinstance(v, bool):
        return v
    if v.lower() in ("yes", "true", "t", "y", "1"):
        return True
    elif v.lower() in ("no", "false", "f", "n", "0"):
        return False
    else:
        raise argparse.ArgumentTypeError("Boolean value expected.")


def parse_snippet_batch(batch, device=None, return_cf_series=False):
    if (
        device is not None
        and batch["snippet_trajectory_inputs_subsampled"].device != device
    ):
        snippet_trajectories = batch["snippet_trajectory_inputs_subsampled"] = batch[
            "snippet_trajectory_inputs_subsampled"
        ].to(
            device
        )  # Shape: (batch_size, max_length_traj, feature_dim)
        snippet_map_segments = batch["snippet_normalized_map_subsampled"] = batch[
            "snippet_normalized_map_subsampled"
        ].to(
            device
        )  # Shape: (batch_size, num_segments, max_length_map, feature_dim)
        snippet_trajectory_lengths = batch[
            "snippet_trajectory_inputs_subsampled_length"
        ] = batch["snippet_trajectory_inputs_subsampled_length"].to(
            device
        )  # Shape :(batch_size, num_segments)
    else:
        snippet_trajectories = batch["snippet_trajectory_inputs_subsampled"]
        snippet_map_segments = batch["snippet_normalized_map_subsampled"]
        snippet_trajectory_lengths = batch[
            "snippet_trajectory_inputs_subsampled_length"
        ]
    if not return_cf_series:
        return (
            snippet_trajectories,
            snippet_map_segments,
            snippet_trajectory_lengths,
        )
    else:
        # (batch_size, max_len)
        if (
            device is not None
            and batch["snippet_trajectory_inputs_subsampled"].device != device
        ):
            snippet_cf_action_series = batch[
                "snippet_cf_instruction_class_series_subsampled"
            ] = batch["snippet_cf_instruction_class_series_subsampled"].to(device)
        else:
            snippet_cf_action_series = batch[
                "snippet_cf_instruction_class_series_subsampled"
            ]

        return (
            snippet_trajectories,
            snippet_map_segments,
            snippet_trajectory_lengths,
            snippet_cf_action_series,
        )


def parse_batch_metrics(batch, target_metric_keys, device=None):
    metrics_input = torch.cat(
        [batch[f"curr_{mk}"].unsqueeze(-1) for mk in target_metric_keys], dim=-1
    )
    if device is not None and metrics_input.device != device:
        # TODO (deepak.gopinath) update batc[curr_mk] to be on correct device as well if needed
        metrics_input = metrics_input.to(device)
    return metrics_input  # (B, 1, targetmetrics_dim) or (B, K, targetmetrics_dim)


def parse_batch(batch, device=None, return_lap_masks=False, return_lap_metrics=True):
    if device is not None and batch["curr_trajectory_segments"].device != device:
        curr_trajectory_segments = batch["curr_trajectory_segments"] = batch[
            "curr_trajectory_segments"
        ].to(
            device
        )  # Shape: (batch_size, num_segments, max_length_traj, feature_dim)
        curr_map_segments = batch["curr_map_segments"] = batch["curr_map_segments"].to(
            device
        )  # Shape: (batch_size, num_segments, max_length_map, feature_dim)
        curr_traj_segments_lengths = batch["curr_trajectory_segments_length"] = batch[
            "curr_trajectory_segments_length"
        ].to(
            device
        )  # Shape :(batch_size, num_segments)
    else:
        curr_trajectory_segments = batch["curr_trajectory_segments"]
        curr_map_segments = batch["curr_map_segments"]
        curr_traj_segments_lengths = batch["curr_trajectory_segments_length"]

    if not return_lap_masks:
        return (
            curr_trajectory_segments,
            curr_map_segments,
            curr_traj_segments_lengths,
        )

    else:
        if device is not None and batch["curr_lap_masks"].device != device:
            curr_lap_masks = batch["curr_lap_masks"] = batch["curr_lap_masks"].to(
                device
            )
        else:
            curr_lap_masks = batch["curr_lap_masks"]

        return (
            curr_trajectory_segments,
            curr_map_segments,
            curr_traj_segments_lengths,
            curr_lap_masks,
        )


def generate_session_id(session_name: str) -> str:
    """Generates a session id.
    Returns
    -------
    str
        A session id of the format: TIME-UUID[-SESSION_NAME].
        An example id is 03-01T11:23:24-d507c-Baseline

        Reasoning
            For time, for example '03-31T11:23:24' is generally a ISO format, year is removed for conciseness.
    """
    cur_hash = hashlib.md5(str(uuid.uuid1()).encode()).hexdigest()[:5]
    timestamp = time.strftime("%m-%dT%H_%M_%S")

    session_id = f"{timestamp}-{cur_hash}"

    if session_name:
        session_id += f"-{session_name}"
    return session_id


def get_future_mask(snippet_predicted_trajectory_lengths):
    traj_index = torch.arange(
        snippet_predicted_trajectory_lengths.max(),
        device=snippet_predicted_trajectory_lengths.device,
    )
    valid = traj_index < snippet_predicted_trajectory_lengths.unsqueeze(1)
    return valid


def set_seed(seed):
    print(f"Setting seed {seed}")
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    # torch.use_deterministic_algorithms(True)


def parse_arguments(args=None):
    parser = argparse.ArgumentParser(
        description="Training script using HF Trainer with Callback that logs parameter change and loss components."
    )
    parser.add_argument(
        "--run-description", type=str, required=False, help="Description of the run."
    )
    parser.add_argument(
        "--category-index-type",
        type=str,
        default="default",
        help="Number of categories used for training",
    )
    parser.add_argument(
        "--data-dir",
        type=str,
        default="~/Data/24-D-05",
        help="Folder in which the trajectory segments are stored",
    )
    parser.add_argument(
        "--use-train-as-test",
        action="store_true",
        help="Use the training dataset as the test dataset.",
    )
    parser.add_argument(
        "--use-one-subject-to-train",
        action="store_true",
        help="Use only one subject to train",
    )
    parser.add_argument(
        "--debug-with-one-trial", action="store_true", help="Use only one trial."
    )
    parser.add_argument(
        "--test-size", type=int, default=1, help="Size of the test dataset."
    )
    parser.add_argument(
        "--freeze-cf-model",
        action="store_true",
        help="freeze cf model params",
    )
    parser.add_argument(
        "--use-lap-dataset",
        action="store_true",
        help="use lap only dataset in adaptive script for testing",
    )
    parser.add_argument(
        "--config-str",
        type=str,
        default="",
        help="The config string that will be merged into config dict.",
    )
    parser.add_argument("--checkpoint", type=str, default=None)
    parser.add_argument("--inference", type=str2bool, default=False)
    parser.add_argument("--online-inference", type=str2bool, default=False)
    return parser.parse_args(args)


def parse_config(config_str):
    """
    Parse a configuration string into a nested dictionary.

    The string should have entries separated by commas, and each entry
    is in the format key=value. Nested keys can be represented by a dot.

    Example:
        'server.port=82,log=log2'
        -> {'server': {'port': 82}, 'log': 'log2'}
    """
    result = {}
    if not config_str:
        return result
    # Split the string by commas to process each entry.
    for item in config_str.split(","):
        if "=" not in item:
            continue  # Skip if the format is not key=value.
        key_part, value_part = item.split("=", 1)
        if value_part.lower() == "true":
            value = True
        elif value_part.lower() == "false":
            value = False
        else:
            # Attempt to convert the value to an integer if possible.
            try:
                value = int(value_part)
            except ValueError:
                try:
                    value = float(value_part)
                except ValueError:
                    value = value_part

        # Split key by '.' to determine nested keys.
        keys = key_part.split(".")
        d = result
        # Traverse or create nested dictionaries for all but the last key.
        for k in keys[:-1]:
            if k not in d:
                d[k] = {}
            d = d[k]
        # Assign the value to the final key.
        d[keys[-1]] = value

    return result
