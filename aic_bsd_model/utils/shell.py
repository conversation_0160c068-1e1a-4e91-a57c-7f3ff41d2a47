import os
import shutil


def move_tree(source_dir, destination_dir):
    # Check if the source directory exists
    if not os.path.exists(source_dir):
        print(f"Source directory '{source_dir}' does not exist.")
        return

    # Create destination directory if it doesn't exist
    os.makedirs(destination_dir, exist_ok=True)

    try:
        shutil.move(source_dir, destination_dir)
    except Exception as e:
        print(f"Error moving {source_dir} to {destination_dir}: {e}")


def delete_tree(source_dir):
    try:
        shutil.rmtree(source_dir)
    except Exception as e:
        print(f"Error deleting directory {source_dir}: {e}")


if __name__ == "__main__":
    source = "delete_dir"
    destination = "to_delete"
    move_tree(source, destination)
