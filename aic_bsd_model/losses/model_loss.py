import torch
import torch.nn as nn
import torch.nn.functional as F
import torchmetrics
import torchmetrics.classification
import torchmetrics.functional.classification

from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_DEFAULT,
    CategoryDictVizIndex,
)

EPS = torch.finfo(torch.float).eps


def compute_model_loss(
    loss_computation_dict, config_dict, dataset_type, loss_component_name_key_prefix=""
):
    loss_components = {}
    scaled_loss_components = {}
    total_loss = 0.0
    for loss_component_name in config_dict[
        f"{loss_component_name_key_prefix}loss_component_names"
    ]:
        loss = loss_computation_dict["loss_func_dict"][loss_component_name](
            loss_computation_dict, config_dict, dataset_type
        )
        if not isinstance(loss, dict):
            loss = {loss_component_name: loss}
        for k, v in loss.items():
            loss_components[k] = v
            scaled_loss_components[k] = config_dict[f"{loss_component_name}_coeff"] * v
            total_loss += scaled_loss_components[k]

    return total_loss, loss_components, scaled_loss_components


def compute_vicreg_loss(loss_computation_dict, config_dict, dataset_type):
    """
    Computes VICReg loss: variance + covariance regularization over z_s

    Returns:
        dict: {"vicreg_loss": scalar Tensor}
    """
    z_s = loss_computation_dict["skill_representation"]  # [B, D]
    device = z_s.device

    # Hyperparameters
    var_weight = config_dict.get("vicreg_var_weight", 10.0)
    cov_weight = config_dict.get("vicreg_cov_weight", 2.0)
    eps = 1e-4

    B, D = z_s.shape

    # Center embeddings (zero mean)
    z_s_centered = z_s - z_s.mean(dim=0, keepdim=True)

    # ---------- Variance Loss ----------
    std = torch.sqrt(z_s_centered.var(dim=0, unbiased=False) + eps)

    var_loss = torch.mean(F.relu(1 - std))

    # ---------- Covariance Loss ----------

    if B > 1:
        cov = (z_s_centered.T @ z_s_centered) / (B - 1)
        cov_off_diag = cov - torch.diag(torch.diag(cov))  # remove diagonal
        cov_loss = (cov_off_diag**2).sum() / D
    else:
        cov_loss = torch.tensor(0.0, device=device)

    total_loss = var_weight * var_loss + cov_weight * cov_loss

    return {"vicreg_loss": total_loss}


def compute_contrastive_loss(loss_computation_dict, config_dict, dataset_type):
    zs = loss_computation_dict["skill_representation"]  # (B, skill_dim)
    driver_ids = loss_computation_dict["driver_ids"]  # (B,)
    temperature = config_dict.get("contrastive_temperature", 0.1)
    # temperature = loss_computation_dict["contrastive_temperature"]

    device = zs.device
    zs = F.normalize(zs, dim=-1)
    B = zs.size(0)
    if B <= 1:
        return {"contrastive_loss": torch.tensor(0.0, device=device)}

    sim_matrix = torch.matmul(zs, zs.T) / temperature
    identity = torch.eye(zs.size(0), device=device).bool()

    labels = driver_ids.unsqueeze(0) == driver_ids.unsqueeze(1)
    labels = labels & ~identity  # (B, B)

    logits = sim_matrix - sim_matrix.max(dim=1, keepdim=True)[0]
    exp_logits = torch.exp(logits) * (~identity)
    log_prob = logits - torch.log(exp_logits.sum(dim=1, keepdim=True) + 1e-8)

    valid = labels.sum(dim=1) > 0
    mean_log_prob_pos = (log_prob * labels).sum(dim=1)[valid] / labels.sum(dim=1)[valid]
    loss = -mean_log_prob_pos.mean()

    return {"contrastive_loss": loss}


def compute_zn_kl_loss(loss_computation_dict, config_dict, dataset_type):
    """
    Computes KL divergence loss between zₙ ∼ N(μ, σ²) and standard normal N(0, I).
    This penalizes `zₙ` for deviating too far from a normal distribution.
    """
    mu = loss_computation_dict["noise_representation_mu"]  # shape: [B, num_segments, D]
    logvar = loss_computation_dict["noise_representation_logvar"]  # same shape

    # KL divergence per element: 0.5 * (μ² + σ² - log(σ²) - 1)
    kl = 0.5 * (mu**2 + torch.exp(logvar) - 1.0 - logvar)
    kl = kl.sum(dim=-1).mean()  # sum over latent dim, mean over batch and segments

    return {"zn_kl_loss": kl}


def compute_subskill_orthogonality_loss(
    loss_computation_dict, config_dict, dataset_type
):
    subskill_latent_dim_map = config_dict["subskill_latent_dim_map"]
    zs = loss_computation_dict["skill_representation"]
    slices = []
    start = 0
    for subskill in subskill_latent_dim_map.keys():
        dim = subskill_latent_dim_map[subskill]
        slices.append(zs[:, start : start + dim])
        start += dim

    loss = 0
    for i in range(len(slices)):
        for j in range(i + 1, len(slices)):
            zi = F.normalize(slices[i], dim=1)
            zj = F.normalize(slices[j], dim=1)
            sim = torch.sum(zi * zj, dim=1)  # cosine similarity
            loss += sim.abs().mean()
    return loss


def compute_metrics_decoding_loss(loss_computation_dict, config_dict, dataset_type):
    for k in ["metrics", "batch_item_masks", "metrics_decoding_loss_type"]:
        assert k in loss_computation_dict
    metrics = loss_computation_dict["metrics"]
    batch_item_masks = loss_computation_dict["batch_item_masks"]
    metrics_loss_dict = {}
    for key in metrics:
        if key.endswith("-pred"):
            continue

        if loss_computation_dict["metrics_decoding_loss_type"] == "l2":
            diff_sq = (metrics[f"{key}-pred"] - metrics[key]) ** 2  # (B, )
            diff_sq = diff_sq * batch_item_masks  # (B, )
            diff_sq_mean = torch.sum(diff_sq) / torch.sum(batch_item_masks)  # ()
            metrics_loss_dict[key] = diff_sq_mean  # ()
        elif loss_computation_dict["metrics_decoding_loss_type"] == "l1":
            abs_diff = torch.abs(metrics[f"{key}-pred"] - metrics[key])  # (B,)
            abs_diff = abs_diff * batch_item_masks  # (B,)
            abs_diff_mean = torch.sum(abs_diff) / torch.sum(batch_item_masks)  # ()
            metrics_loss_dict[key] = abs_diff_mean  # ()
        # metrics_loss_dict[key] = nn.MSELoss()(metrics[f"{key}-pred"], metrics[key])
    return metrics_loss_dict


def compute_sparsity_loss(loss_computation_dict, config_dict, dataset_type):
    assert "sparsity_loss_key" in loss_computation_dict
    assert "sparsity_dim" in loss_computation_dict
    assert loss_computation_dict["sparsity_loss_key"] in loss_computation_dict

    sparsity_loss_key = loss_computation_dict["sparsity_loss_key"]
    # (B, H)
    tensor_to_sparsify = loss_computation_dict[sparsity_loss_key]
    # L1 norm along the specified dimension. Assumes that the shape is (B, H) and we are computing norm for H dimensional tensors
    sparsity_loss = torch.mean(
        torch.norm(tensor_to_sparsify, p=1, dim=loss_computation_dict["sparsity_dim"])
    )
    return sparsity_loss


def compute_lap_representation_smoothness_loss(
    loss_computation_dict, config_dict, dataset_type
):
    assert "latent_z_sequence" in loss_computation_dict
    assert "updated_latent_z_sequence" in loss_computation_dict
    assert "smoothness_loss_type" in loss_computation_dict
    assert "batch_item_masks_non_flattened" in loss_computation_dict
    # (B, K-1), it is possible that lap masks for 1: are all 0's. This happens when there is only one valid previous lap.
    # Later we will deal with this with a nanmean operation (when computing mean over laps) instead of regular mean.
    relevant_batch_lap_masks = loss_computation_dict["batch_item_masks_non_flattened"][
        :, 1:
    ]
    # (B, K-1, H)
    valid_ztp1 = loss_computation_dict["latent_z_sequence"][:, 1:, ...]
    # (B, K-1, H)
    valid_ztp1_estimated = loss_computation_dict["updated_latent_z_sequence"][
        :, :-1, ...
    ]
    if loss_computation_dict["smoothness_loss_type"] == "l2":
        # (B, K-1, H)
        diff_sq = (valid_ztp1 - valid_ztp1_estimated) ** 2
        # (B, K-1, H)
        diff_sq_valid = diff_sq * relevant_batch_lap_masks.unsqueeze(-1)
        # (B, K-1) #sum of squares over all dimensions
        diff_sq_valid_sum_over_feature = diff_sq_valid.sum(dim=-1)
        # (B)
        nonzero_mask = ~(relevant_batch_lap_masks.sum(-1) == 0)
        diff_sq_valid_mean_over_laps = diff_sq_valid_sum_over_feature.sum(-1)[
            nonzero_mask
        ]
        diff_sq_valid_mean_over_batch = diff_sq_valid_mean_over_laps.mean()
        # ()
        # we have to do nanmean because since relevant_batch_lap_masks could have all zero's for some batch items
        # diff_sq_valid_mean_over_batch = diff_sq_valid_mean_over_laps.nanmean(-1)
        return diff_sq_valid_mean_over_batch

    elif loss_computation_dict["smoothness_loss_type"] == "l1":
        # (B, K-1, H)
        diff_abs = torch.abs(valid_ztp1 - valid_ztp1_estimated)
        # (B, K-1, H)
        diff_abs_valid = diff_abs * relevant_batch_lap_masks.unsqueeze(-1)
        # (B, K-1) #sum of abs values over all dimensions
        diff_abs_valid_sum_over_feature = diff_abs_valid.sum(dim=-1)
        # (B)
        nonzero_mask = ~(relevant_batch_lap_masks.sum(-1) == 0)
        diff_abs_valid_mean_over_laps = diff_abs_valid_sum_over_feature.sum(-1)[
            nonzero_mask
        ]
        # ()
        diff_abs_valid_mean_over_batch = diff_abs_valid_mean_over_laps.mean(-1)
        return diff_abs_valid_mean_over_batch

    # torch.norm(lstm_outputs[:, 1:, :] - update_lstm_outputs[:, :-1, :], p=2)


def compute_trajectory_prediction_loss(loss_dict, config, dataset_type, type="mse"):
    """
    type: str, one of "mse" or "ade"
    """
    # 1) validate inputs
    for key in (
        "predicted_future_trajectories",
        "target_segment_trajs",
        "future_pred_mask",
    ):
        assert key in loss_dict, f"Missing '{key}' in loss_dict"

    # [B, S, T, D]
    decoded = loss_dict["predicted_future_trajectories"]
    # [B, S, T, D′>=D]
    target_full = loss_dict["target_segment_trajs"]
    # [B, S, T, 1]
    mask = loss_dict["future_pred_mask"]

    # if decoded is None:
    #     return torch.tensor(
    #         0.0, device=target_full.device if target_full is not None else None
    #     )

    # crop to the decoder’s output dimension D
    D = config["traj_decoder_output_dim"]
    # [B, S, T, D]
    target = target_full[..., :D]

    # zero out invalid timesteps
    # if mask.dim() == decoded.dim() - 1:
    #     mask = mask.unsqueeze(-1)
    # [B, S, T, D]
    diff = (decoded - target) * mask

    # per-step error
    loss_type = type.lower()
    if loss_type == "mse":
        # squared error sum per step
        # [B, S, T]
        step_err = (diff * diff).sum(dim=-1)
    elif loss_type == "ade":
        # l2 norm per step
        step_err = torch.norm(diff, p=2, dim=-1)
    else:
        raise ValueError(f"Unknown loss type '{loss_type}'")

    # count valid timesteps per segment
    # [B, S]
    valid_steps = mask.sum(dim=-2).clamp(min=1.0)[..., 0]

    # average over time, segments, batch
    per_segment = step_err.sum(dim=-1) / valid_steps
    per_sample = per_segment.mean(dim=-1)
    return per_sample.mean()


def compute_trajectory_prediction_loss_mse(
    loss_computation_dict, config_dict, dataset_type
):
    return compute_trajectory_prediction_loss(
        loss_computation_dict, config_dict, dataset_type, type="mse"
    )


def compute_trajectory_prediction_loss_ade(
    loss_computation_dict, config_dict, dataset_type
):
    return compute_trajectory_prediction_loss(
        loss_computation_dict, config_dict, dataset_type, type="ade"
    )


def compute_trajectory_reconstruction_loss(
    loss_computation_dict, config_dict, dataset_type
):
    assert (
        "reconstructed_segment_trajectories"
        and "target_segment_trajs"
        and "reconstructed_pred_mask"
        and "batch_item_masks" in loss_computation_dict
    )
    if loss_computation_dict["reconstructed_segment_trajectories"] is None:
        return torch.tensor(0.0)

    reconstructed_segment_trajs = loss_computation_dict[
        "reconstructed_segment_trajectories"
    ]
    target_segment_trajs = loss_computation_dict["target_segment_trajs"]
    reconstructed_pred_mask = loss_computation_dict["reconstructed_pred_mask"]
    batch_item_masks = loss_computation_dict["batch_item_masks"]
    valid_mask = reconstructed_pred_mask
    # (B, S)
    segments_length = torch.sum(valid_mask, dim=-2)[..., 0]
    # (B, S, T, traj_decoder_output_dim)
    diff = (
        reconstructed_segment_trajs
        - target_segment_trajs[..., : config_dict["traj_decoder_output_dim"]]
    )
    diff = diff * valid_mask
    diff_norm_sq = torch.sum(diff * diff, dim=-1)  # (B, S, T)
    diff_norm_sq = diff_norm_sq[batch_item_masks.bool()]  # (B', S, T )
    segments_length = segments_length[batch_item_masks.bool()]
    diff_norm_sq_mean_over_time = (
        torch.sum(diff_norm_sq, axis=-1) / segments_length
    )  # (B', S)
    diff_norm_sq_mean_over_segments = torch.mean(
        diff_norm_sq_mean_over_time, axis=-1
    )  # (B')
    segment_traj_reconstruction_loss = torch.mean(diff_norm_sq_mean_over_segments)
    return segment_traj_reconstruction_loss


def compute_trajectory_reconstruction_norm_loss(
    loss_computation_dict, config_dict, dataset_type
):
    assert (
        "reconstructed_segment_trajectories"
        and "target_segment_trajs"
        and "reconstructed_pred_mask"
        and "batch_item_masks" in loss_computation_dict
    )
    if loss_computation_dict["reconstructed_segment_trajectories"] is None:
        return torch.tensor(0.0)

    reconstructed_segment_trajs = loss_computation_dict[
        "reconstructed_segment_trajectories"
    ]
    target_segment_trajs = loss_computation_dict["target_segment_trajs"]
    reconstructed_pred_mask = loss_computation_dict["reconstructed_pred_mask"]
    batch_item_masks = loss_computation_dict["batch_item_masks"]
    valid_mask = reconstructed_pred_mask
    # (B, S)
    segments_length = torch.sum(valid_mask, dim=-2)[..., 0]
    # (B, S, T, 3)
    diff = (
        reconstructed_segment_trajs
        - target_segment_trajs[..., : config_dict["traj_decoder_output_dim"]]
    )
    diff = diff * valid_mask
    diff_norm = torch.norm(diff, dim=-1)  # (B, S, T)
    diff_norm = diff_norm[batch_item_masks.bool()]  # (B', S, T )
    segments_length = segments_length[batch_item_masks.bool()]
    diff_norm_mean_over_time = (
        torch.sum(diff_norm, axis=-1) / segments_length
    )  # (B', S)
    diff_norm_mean_over_segments = torch.mean(diff_norm_mean_over_time, axis=-1)  # (B')
    # () mean over batch items
    segment_traj_reconstruction_loss = torch.mean(diff_norm_mean_over_segments)
    return segment_traj_reconstruction_loss


def compute_trajectory_reconstruction_smoothness_loss(
    loss_computation_dict, config_dict, dataset_type
):
    # TODO (deepak.gopinath/xiongyi) update with usage of lap_segments_data_dvalid instead of segment_trajs_mask for consistency
    assert (
        "reconstructed_segment_trajectories"
        and "segment_trajs_mask" in loss_computation_dict
    )
    decoded_segment_trajs = loss_computation_dict["reconstructed_segment_trajectories"]
    segment_trajs_mask = loss_computation_dict["segment_trajs_mask"]

    delta_decoded_traj = torch.diff(decoded_segment_trajs, dim=-2)  # (B, S, T-1, 3)
    delta_decoded_traj_diff = (
        delta_decoded_traj[:, :, 1:, :] - delta_decoded_traj[:, :, :-1, :]
    )  # (B, S, T-2, 3)
    delta_decoded_traj_diff_norm = torch.norm(
        delta_decoded_traj_diff * segment_trajs_mask[:, :, :-2].unsqueeze(-1), dim=-1
    )  # (B, S, T-2)
    delta_decoded_traj_diff_norm_mean_over_time = torch.sum(
        delta_decoded_traj_diff_norm, dim=-1
    ) / torch.sum(
        segment_trajs_mask[:, :, :-2], dim=-1
    )  # (B, S)
    delta_decoded_traj_diff_norm_over_time_over_segments = torch.mean(
        delta_decoded_traj_diff_norm_mean_over_time, dim=-1
    )  # (B)
    smoothness_loss = torch.mean(
        delta_decoded_traj_diff_norm_over_time_over_segments
    )  # ()
    return smoothness_loss


def compute_snippet_metrics_prediction_loss(
    loss_computation_dict, config_dict, dataset_type
):
    for k in ["cf_metrics", "cf_metrics_decoding_loss_type"]:
        assert k in loss_computation_dict

    cf_metrics = loss_computation_dict["cf_metrics"]
    metrics_loss_dict = {}
    for key in cf_metrics:
        if key.endswith("-pred_cf"):
            continue
        if loss_computation_dict["cf_metrics_decoding_loss_type"] == "l2":
            diff_sq = (cf_metrics[f"{key}-pred_cf"] - cf_metrics[key]) ** 2  # (B, )
            metrics_loss_dict[f"{key}_cf"] = torch.mean(diff_sq)  # ()
        elif loss_computation_dict["cf_metrics_decoding_loss_type"] == "l1":
            abs_diff = torch.abs(cf_metrics[f"{key}-pred"] - cf_metrics[key])  # (B,)
            metrics_loss_dict[f"{key}_cf"] = torch.mean(abs_diff)  # ()
        # metrics_loss_dict[key] = nn.MSELoss()(metrics[f"{key}-pred"], metrics[key])
    return metrics_loss_dict


def compute_teacher_action_prediction_loss(
    loss_computation_dict, config_dict, dataset_type
):
    if config_dict["teacher_action_loss_type"] == "existence":
        return compute_teacher_action_prediction_loss_existence(
            loss_computation_dict, config_dict, dataset_type
        )


def compute_teacher_action_prediction_loss_existence(
    loss_computation_dict, config_dict, dataset_type
):
    assert (
        "cf_teacher_action_pred"
        and "cf_teacher_action_existence_gt"
        and "pos_weights_for_classes" in loss_computation_dict
    )
    num_classes = config_dict["teacher_action_num_categories"]
    use_soft_label = config_dict.get("teacher_action_use_soft_label", False)
    soft_label_beta = config_dict.get("teacher_action_use_soft_label_beta", None)
    soft_label_start_epoch = config_dict.get("teacher_action_use_soft_start_epoch", 20)
    pos_weights = loss_computation_dict["pos_weights_for_classes"]
    # TODO (deepak.gopinath). Make it general enough so that any subset of classes can be ignored.
    pos_weights[0] = 0.0  # don't train class 0 (the null categories)
    bce = torch.nn.BCEWithLogitsLoss(
        pos_weight=torch.as_tensor(pos_weights), reduction="none"
    )

    # (B*num_snippets, num_categories), these are the logits
    cf_teacher_action_pred = loss_computation_dict["cf_teacher_action_pred"].view(
        -1, loss_computation_dict["cf_teacher_action_pred"].shape[-1]
    )
    # (B*num_snippets, future_len)
    target_teacher_action_sequence = loss_computation_dict[
        "target_teacher_action_sequence"
    ].view(-1, loss_computation_dict["target_teacher_action_sequence"].shape[-1])
    # (B*num_snippets, num_classes) each batch item contains number of counts of each label
    regression_targets = loss_computation_dict["cf_teacher_action_existence_gt"]
    regression_targets = regression_targets.view(-1, regression_targets.shape[-1])
    if use_soft_label and soft_label_start_epoch <= loss_computation_dict["epoch"]:
        # Soft Bootstrapping
        p = (
            loss_computation_dict["cf_teacher_action_sig"]
            .view(-1, loss_computation_dict["cf_teacher_action_sig"].shape[-1])
            .detach()
        )
        regression_targets = (
            soft_label_beta * regression_targets + (1 - soft_label_beta) * p
        )

    bce = bce.to(regression_targets.device)
    # (B*num_snippets, num_classes)
    teacher_action_prediction_loss = bce(cf_teacher_action_pred, regression_targets)
    # (B*num_snippets) - average across classes
    teacher_action_prediction_loss = torch.mean(teacher_action_prediction_loss, dim=-1)
    # () - mean over batches
    teacher_action_prediction_loss = torch.mean(teacher_action_prediction_loss)

    return teacher_action_prediction_loss


def _kld_gauss(mean_1, std_1, mean_2, std_2):
    kld_element = (
        2 * torch.log(std_2 + EPS)
        - 2 * torch.log(std_1 + EPS)
        + (std_1.pow(2) + (mean_1 - mean_2).pow(2)) / std_2.pow(2)
        - 1
    )
    return 0.5 * torch.sum(kld_element)


def compute_kld_loss(loss_computation_dict, config_dict, dataset_type):
    assert (
        "enc_mean_t"
        and "enc_std_t"
        and "prior_mean_t"
        and "prior_std_t" in loss_computation_dict
    )
    assert "batch_item_masks_non_flattened" in loss_computation_dict

    # (B, K)
    batch_item_masks_non_flattened = loss_computation_dict[
        "batch_item_masks_non_flattened"
    ]
    enc_mean_t = loss_computation_dict["enc_mean_t"]
    enc_std_t = loss_computation_dict["enc_std_t"]
    prior_mean_t = loss_computation_dict["prior_mean_t"]
    prior_std_t = loss_computation_dict["prior_std_t"]
    assert (
        len(enc_mean_t)
        == len(enc_std_t)
        == len(prior_mean_t)
        == len(prior_std_t)
        == batch_item_masks_non_flattened.shape[1]
    )
    kld_loss = 0.0
    # each of em, es, pm, ps are (B, H) to start of with
    for t, (em, es, pm, ps) in enumerate(
        zip(enc_mean_t, enc_std_t, prior_mean_t, prior_std_t)
    ):
        # (B', H)
        batch_item_mask_t = batch_item_masks_non_flattened[:, t].to(torch.bool)
        em = em[batch_item_mask_t]
        es = es[batch_item_mask_t]
        pm = pm[batch_item_mask_t]
        ps = ps[batch_item_mask_t]
        kld_loss += _kld_gauss(em, es, pm, ps)

    return kld_loss


def compute_classification_metrics(
    loss_computation_dict,
    config_dict,
    dataset_type,
    is_weighted=False,
    skip_confusion_matrix=False,
):
    assert (
        "cf_teacher_action_pred"
        and "cf_teacher_action_sig"
        and "target_teacher_action_sequence" in loss_computation_dict
    )
    metrics = {}
    num_total_classes = config_dict["teacher_action_num_categories"]
    if config_dict["valid_annotation_categories_index_dict_type"] == "default":
        valid_annotation_categories_index_viz_dict = CategoryDictVizIndex.DEFAULT.value
    elif config_dict["valid_annotation_categories_index_dict_type"] == "no_steering":
        valid_annotation_categories_index_viz_dict = (
            CategoryDictVizIndex.NO_STEERING.value
        )
    elif (
        config_dict["valid_annotation_categories_index_dict_type"]
        == "no_steering_no_turn"
    ):
        valid_annotation_categories_index_viz_dict = (
            CategoryDictVizIndex.NO_STEERING_NO_TURN.value
        )
    elif (
        config_dict["valid_annotation_categories_index_dict_type"]
        == "no_steering_no_turn_no_brake"
    ):
        valid_annotation_categories_index_viz_dict = (
            CategoryDictVizIndex.NO_STEERING_NO_TURN_NO_BRAKE.value
        )
    elif config_dict["valid_annotation_categories_index_dict_type"] == "basic":
        valid_annotation_categories_index_viz_dict = CategoryDictVizIndex.BASIC.value

    class_indices_considered_for_metrics = list(
        valid_annotation_categories_index_viz_dict.values()
    )
    num_classes = len(class_indices_considered_for_metrics)
    # (B*num_snippets, num_categories), these are the prediction probabilities
    cf_teacher_action_sig = loss_computation_dict["cf_teacher_action_sig"].view(
        -1, loss_computation_dict["cf_teacher_action_sig"].shape[-1]
    )
    # (B*num_snippets, future_len)
    target_teacher_action_sequence = loss_computation_dict[
        "target_teacher_action_sequence"
    ].view(-1, loss_computation_dict["target_teacher_action_sequence"].shape[-1])
    # (B*num_snippets, num_classes) each batch item contains number of counts of each label
    regression_targets = torch.cat(
        [
            torch.bincount(item, minlength=num_total_classes).unsqueeze(0)
            for item in target_teacher_action_sequence
        ],
        dim=0,
    )
    regression_targets = (regression_targets != 0).float()

    average = "weighted" if is_weighted else None
    suffix = "weighted" if is_weighted else "unweighted"

    # if weighted = the individual label scores are weighted according to support
    # if unweighted, we do a standard mean of individual labels scores. This is 'macro' averaging

    prediction = cf_teacher_action_sig[:, class_indices_considered_for_metrics]
    gt = regression_targets[:, class_indices_considered_for_metrics]

    per_label_multilabel_accuracy = (
        torchmetrics.functional.classification.multilabel_accuracy(
            prediction, gt.int(), num_labels=num_classes, average=average
        )
    )
    # array([0.17788841, 0.09535193, 0.27560627, 0.14759956, 0.05311021,
    #    0.06484129, 0.03588328, 0.00904476, 0.01355481, 0.10883281,
    #    0.01828667], dtype=float32)
    metrics["pred_accuracy_" + suffix] = torch.mean(per_label_multilabel_accuracy)

    per_label_multilabel_precision = (
        torchmetrics.functional.classification.multilabel_precision(
            prediction, gt.int(), num_labels=num_classes, average=average
        )
    )
    metrics["precision_" + suffix] = torch.mean(per_label_multilabel_precision)

    # recall
    per_label_multilabel_recall = (
        torchmetrics.functional.classification.multilabel_recall(
            prediction, gt.int(), num_labels=num_classes, average=average
        )
    )
    metrics["recall_" + suffix] = torch.mean(per_label_multilabel_recall)

    # f1 score
    per_label_multilabel_f1score = (
        torchmetrics.functional.classification.multilabel_f1_score(
            prediction, gt.int(), num_labels=num_classes, average=average
        )
    )
    metrics["f1score_" + suffix] = torch.mean(per_label_multilabel_f1score)

    # febta score precision higher
    per_label_multilabel_fbetascore_precision_higher = (
        torchmetrics.functional.classification.multilabel_fbeta_score(
            prediction, gt.int(), beta=0.5, num_labels=num_classes, average=average
        )
    )
    metrics["fbetascore_precision_higher_" + suffix] = torch.mean(
        per_label_multilabel_fbetascore_precision_higher
    )

    # fbeta recall higher
    per_label_multilabel_fbetascore_recall_higher = (
        torchmetrics.functional.classification.multilabel_fbeta_score(
            prediction, gt.int(), beta=2.0, num_labels=num_classes, average=average
        )
    )
    metrics["fbetascore_recall_higher_" + suffix] = torch.mean(
        per_label_multilabel_fbetascore_recall_higher
    )

    # multilabel auroc
    per_label_multilabel_auroc = (
        torchmetrics.functional.classification.multilabel_auroc(
            prediction, gt.int(), num_labels=num_classes, average=average, thresholds=50
        )
    )
    metrics["auroc_" + suffix] = torch.mean(per_label_multilabel_auroc)

    if not skip_confusion_matrix:
        metrics[
            "confusion_matrix"
        ] = torchmetrics.functional.classification.multilabel_confusion_matrix(
            prediction, gt.int(), num_labels=num_classes, normalize="true"
        )

    # hamming distance
    per_label_multilabel_hamming_distance = (
        torchmetrics.functional.classification.multilabel_hamming_distance(
            prediction, gt.int(), num_labels=num_classes, average=average
        )
    )
    metrics["hamming_distance_" + suffix] = torch.mean(
        per_label_multilabel_hamming_distance
    )

    if not is_weighted:
        for (
            teacher_action,
            action_idx,
        ) in valid_annotation_categories_index_viz_dict.items():  # skips 0th class
            if action_idx not in class_indices_considered_for_metrics:
                continue

            class_idx = class_indices_considered_for_metrics.index(action_idx)
            metrics[
                "pred_accuracy_" + suffix + "_" + teacher_action
            ] = per_label_multilabel_accuracy[class_idx]
            metrics[
                "precision_" + suffix + "_" + teacher_action
            ] = per_label_multilabel_precision[class_idx]
            metrics[
                "recall_" + suffix + "_" + teacher_action
            ] = per_label_multilabel_recall[class_idx]
            metrics[
                "f1score_" + suffix + "_" + teacher_action
            ] = per_label_multilabel_f1score[class_idx]
            metrics[
                "fbetascore_precision_higher_" + suffix + "_" + teacher_action
            ] = per_label_multilabel_fbetascore_precision_higher[class_idx]
            metrics[
                "fbetascore_recall_higher_" + suffix + "_" + teacher_action
            ] = per_label_multilabel_fbetascore_recall_higher[class_idx]
            metrics[
                "hamming_distance_" + suffix + "_" + teacher_action
            ] = per_label_multilabel_hamming_distance[class_idx]
            metrics[
                "auroc_" + suffix + "_" + teacher_action
            ] = per_label_multilabel_auroc[class_idx]

    return metrics
