# load in a subjects trials in chronological order.
# dataset to be used for learning skill representation.
import collections
import os
import pickle
from enum import IntEnum
from pathlib import Path
from typing import List

import numpy as np
import pandas as pd
import yaml
from aic_bsd_model.datasets.aic_24d16_lap_dataset import AIC24D16LapDataset
from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    CONES_COORDINATES, FEATURE_TO_DF_KEY_NO_ACCEL, METRICS_LINEAR_SCALE_24D18,
    SUBJ_LEVEL_METRICS_LINEAR_SCALE, TRAJ_FEATURE_LINEAR_SCALE,
    TrajFeatureIndexNoAccel, add_map, calculate_sampling_freq,
    extract_uid_from_path, get_pid_and_trial_num_from_uid,
    get_uid_from_pid_and_trial_num, open_pkl, parse_arguments)
from aic_bsd_model.utils.math_utils import CoordinateNormalizationUtil
from shapely import Point, geometry
from torch.utils.data import Dataset
from transformers import AutoTokenizer, logging


class AIC24D18Dataset(AIC24D16LapDataset):
    def __init__(
        self,
        config_dict,
        dataset_type,
    ):

        # Call parent class init to initialize shared logic
        super().__init__(config_dict, dataset_type)

        # Set subclass-specific flags/attributes
        self.segmented_laps = True
        self.subj_level_metric_keys = {
            "trial_time": ["trial_time"],
            "occlusion_image": ["additional_dict.occlusion_image_overall_accuracy"],
            "occlusion_video": ["additional_dict.occlusion_video_accuracy"],
            "qualtrics": [
                "additional_dict.knowledge_score",
                "additional_dict.tops_score",
            ],
            "fitts": [
                "additional_dict.fitts_hand_eye_coordination",
                "additional_dict.fitts_motor_skill",
            ],
            "grip_strength": ["additional_dict.grip_strength"],
        }

        self.config_dict = config_dict
        self.trials_directory = config_dict.get("trials_directory", None)
        self.full_trial_dirs = [
            d for d in list(self.trials_directory.iterdir()) if not d.is_file()
        ]



        # Post-super init: override/extend parent class behavior or add new subclass logic
        self.subject_level_scenarios_to_be_considered = [
            "skidpadcircleracingline",
            "prestudy",
            "skidpadslalomracingline",
        ]

        self.metric_keys = {
            "trial_time": [None],
            "oob": ["percentage"],
            "steering_score": ["abs_mean"],
            "racing_line_score": ["abs_mean"],
            "braking_score": ["abs_mean"],
            "throttle_score": ["abs_mean"],
            "smoothness_score": ["mean"],
            "gaze_dispersion": [None],
        }

        self.subj_metrics_by_uid = collections.defaultdict(dict)
        self.trial_metrics_by_uid = collections.defaultdict(dict)

        self._prepare_trial_level_metrics()
        self._prepare_subject_level_metrics()


        self.valid_lap_list = []
        self._prepare_valid_laps_list()

        if config_dict.get("debug_with_one_trial", False):
            self.valid_lap_list = [self.valid_lap_list[0]]

        self.feature_linear_scale = np.array(
            [TRAJ_FEATURE_LINEAR_SCALE[e.name] for e in TrajFeatureIndexNoAccel]
        ).reshape(1, -1)

    def _prepare_trajectory_segment_data_for_each_lap(self):
        for d in self.trajectory_segments_dir.iterdir():
            uid = d.stem
            if "track" in d.stem:
                continue

            segment_pkls = list(d.iterdir())
            if uid not in self.trajectory_segments_pkl_paths_according_to_uid:
                self.trajectory_segments_pkl_paths_according_to_uid[
                    uid
                ] = collections.OrderedDict()
                for seg_id in range(1, 11):
                    self.trajectory_segments_pkl_paths_according_to_uid[uid][
                        f"segment_{seg_id}"
                    ] = None
            for sp in segment_pkls:
                if "metrics" in sp.stem:
                    continue
                self.trajectory_segments_pkl_paths_according_to_uid[uid][sp.stem] = sp

    def _prepare_subject_level_metrics(self):
        # load in subject level metrics in dictionary

        def get_nested_value(d, dotted_key):
            # get value from yaml
            keys = dotted_key.split(".")
            for k in keys:
                if not isinstance(d, dict) or k not in d:
                    return None  # or raise or log
                d = d[k]
            return d

        for subject_scenario_dir in Path(self.trials_directory).iterdir():
            parts = subject_scenario_dir.stem.split("-")
            uid = parts[0]
            scenario_name = parts[-1].split("_")[0]

            if (
                uid not in self.pids_to_be_considered
                or scenario_name not in self.subject_level_scenarios_to_be_considered
            ):
                continue

            for metric_path in subject_scenario_dir.glob("*.yaml"):
                metric_name = metric_path.stem

                for yaml_tag, fields_to_extract in self.subj_level_metric_keys.items():
                    if yaml_tag in metric_name:
                        with open(metric_path, "r") as f:
                            try:
                                data = yaml.load(f, Loader=yaml.UnsafeLoader)
                            except Exception as e:
                                print(f"Failed to load {metric_path}: {e}")
                                continue
                        for dotted_field in fields_to_extract:
                            value = get_nested_value(data, dotted_field)
                            key = f"{scenario_name}_{dotted_field.split('.')[-1]}"
                            norm_value = (
                                float(value) * SUBJ_LEVEL_METRICS_LINEAR_SCALE[key]
                            )

                            self.subj_metrics_by_uid[uid][key] = norm_value

    def get_subj_level_metrics(self, pid):
        # gets subject level metrics associated with subject, pid
        return self.subj_metrics_by_uid[pid]

    def _prepare_trial_level_metrics(self):
        def get_metrics_from_pkl(metrics_dict_for_uid, key_prefix=""):
            selected_metrics_dict = {}
            for mk, sub_key_list in self.metric_keys.items():
                for subkey in sub_key_list:
                    if subkey is None:
                        prefix_mk_key = f"{key_prefix}{mk}"
                        selected_metrics_dict[prefix_mk_key] = metrics_dict_for_uid[mk]
                    else:
                        prefix_mk_key = f"{key_prefix}{mk}_{subkey}"
                        selected_metrics_dict[prefix_mk_key] = metrics_dict_for_uid[
                            f"{mk}_{subkey}"
                        ]

            # scale normalize lap-level metrics
            for mk, subkey_dict in METRICS_LINEAR_SCALE_24D18.items():
                for subkey, subkey_ls in subkey_dict.items():
                    dict_key = (
                        f"{key_prefix}{mk}"
                        if subkey is None
                        else f"{key_prefix}{mk}_{subkey}"
                    )
                    selected_metrics_dict[dict_key] = (
                        selected_metrics_dict[dict_key] - subkey_ls["min"]
                    ) / (subkey_ls["max"] - subkey_ls["min"])

            return selected_metrics_dict

        for (
            uid,
            metric_path_dict,
        ) in self.trajectory_metrics_pkl_paths_according_to_uid.items():
            pid, _ = get_pid_and_trial_num_from_uid(uid)
            if pid not in self.pids_to_be_considered:
                continue
            if "metrics_dict" not in metric_path_dict:
                continue
            metric_path = metric_path_dict["metrics_dict"]
            try:
                metrics_dict = open_pkl(metric_path)
            except Exception as e:
                print(f"Failed to load {metric_path}: {e}")
                continue

            # preload with no prefix; we'll add the prefix during access
            self.trial_metrics_by_uid[uid] = get_metrics_from_pkl(
                metrics_dict, key_prefix=""
            )

    def get_metrics_from_trial(self, uid, key_prefix=""):
        # cached lookup from preloaded trial metrics
        if uid not in self.trial_metrics_by_uid:
            raise ValueError(f"UID {uid} not found in preloaded trial metrics.")

        raw_metrics = self.trial_metrics_by_uid[uid]
        # Add key prefix at access time
        return {f"{key_prefix}{k}": v for k, v in raw_metrics.items()}

    def __getitem__(self, idx):
        """
        Return:
            traj: normalized to traj[-1]
            map: normalized to traj[-1]
                local:  only map segment within local_map_distance_threshold
                        variable length, collator should pad
                global: the entire map
                        constant length
                cones:  only cones within local_map_distance_threshold
                        constant length, invalid cones(too far) will be set to 0
        """

        valid_lap_uid = self.valid_lap_list[idx]
        pid, trial_num = get_pid_and_trial_num_from_uid(valid_lap_uid)
        data_dict = {}
        # print(valid_lap_uid)
        (
            current_trial_trajectory_and_map_segments_dict,
            _,
            valid_next_lap_trial_num,
        ) = self.get_traj_and_map_segments_from_trial(
            uid=valid_lap_uid, key_prefix="curr_", return_prev_next_lap_trial_num=True
        )
        data_dict.update(current_trial_trajectory_and_map_segments_dict)

        current_trial_metrics = self.get_metrics_from_trial(
            valid_lap_uid, key_prefix="curr_"
        )

        data_dict.update(current_trial_metrics)
        subj_skill_metrics = self.get_subj_level_metrics(pid)
        data_dict.update(subj_skill_metrics)
        data_dict["curr_lap_uid"] = valid_lap_uid

        # next_valid_lap_uid = get_uid_from_pid_and_trial_num(pid, valid_next_lap_trial_num)
        # next_trial_trajectory_segments_dict = self.get_traj_segments_from_trial(uid=next_valid_lap_uid, key_prefix='next_', return_pre_next_lap_trial_num=False)

        return data_dict
        #
