from typing import Any, Dict, List

import torch
from torch.utils.data._utils.collate import default_collate

from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    NUM_SEGMENTS,
    coerce_tensor_dtype_bits,
    lap_level_padding,
    pad_cf_snippet_map,
    padding,
)


class TrackSegmentCollator:
    """
    This collator is to used with AIC24D16LapDataset, AIC24D05LapDataset and AIC24D05FullDataset.
    This collator class also takes care of padding and collation of different laps in FullSequence version of the datasets
    also with take care of the collation of num_cf_snippets_per_lap cf_snippets associated with the lap.
    """

    def __init__(
        self,
        padding_value=0,
        padding_side="right",
        lap_has_cf_series=False,
        with_cf_snippets=False,
        num_cf_snippets_per_lap=0,
    ):
        self.padding_value = padding_value
        self.padding_side = padding_side
        self.with_cf_snippets = with_cf_snippets
        self.num_cf_snippets_per_lap = num_cf_snippets_per_lap
        self.lap_has_cf_series = lap_has_cf_series
        if self.with_cf_snippets:
            assert self.num_cf_snippets_per_lap != 0
            self.cf_collator = CFSnippetCollator(self.padding_value, self.padding_side)

    def __call__(self, batch: List[Dict[str, Any]]) -> Dict[str, Any]:
        # all keys related laps that will be collated using default collate
        non_traj_map_segment_lap_keys = []
        for k in batch[0].keys():
            # ignore any cf_snippet related keys (as they are handed separately) or lap level traj and cones
            if "snippet" in k or "subsampled" in k:
                continue
            non_traj_map_segment_lap_keys.append(k)

        non_traj_map_segment_lap_keys = sorted(list(set(non_traj_map_segment_lap_keys)))

        # deal with collation of lap level keys (those that require padding)
        padded_curr_trajectory_segments = []
        padded_curr_map_segments = []
        padded_curr_map_cones = []

        max_length_traj = 0
        max_length_map = 0

        for i in range(1, NUM_SEGMENTS + 1):
            traj_i_key = f"curr_segment_{i}_trajectory_inputs_subsampled"
            map_i_key = f"curr_segment_{i}_normalized_map_subsampled"
            curr_trajectory_segments = [
                torch.as_tensor(item[traj_i_key]) for item in batch
            ]
            max_length_traj = max(
                max_length_traj, max(seg.size(0) for seg in curr_trajectory_segments)
            )
            curr_map_segments = [torch.as_tensor(item[map_i_key]) for item in batch]
            max_length_map = max(
                max_length_map, max(seg.size(0) for seg in curr_map_segments)
            )

        for i in range(1, NUM_SEGMENTS + 1):
            traj_i_key = f"curr_segment_{i}_trajectory_inputs_subsampled"
            map_i_key = f"curr_segment_{i}_normalized_map_subsampled"
            cone_i_key = f"curr_segment_{i}_cones"

            curr_trajectory_segments = [
                torch.as_tensor(item[traj_i_key]) for item in batch
            ]  # list of len batch_size
            curr_map_segments = [torch.as_tensor(item[map_i_key]) for item in batch]
            curr_map_cones = [torch.as_tensor(item[cone_i_key]) for item in batch]

            # Pad each segment to the maximum length for this segment
            # (B, max_len_traj, traj_feature_dim)

            padded_curr_trajectory_segment = padding(
                curr_trajectory_segments,
                self.padding_value,
                max_length_traj,
                left=self.padding_side == "left",
            )
            padded_curr_map_segment = padding(
                curr_map_segments,
                self.padding_value,
                max_length_map,
                left=self.padding_side == "left",
            )

            curr_map_cones = torch.stack(curr_map_cones, dim=0)
            padded_curr_map_cones.append(curr_map_cones)
            # Append padded segments and masks
            # Shape: list of (batch_size, max_length_traj, feature_dim)
            padded_curr_trajectory_segments.append(padded_curr_trajectory_segment)
            # Shape: list of (batch_size, max_length_map, feature_dim)
            padded_curr_map_segments.append(padded_curr_map_segment)

        # Step 2: Stack padded segments along a new dimension for each track segment
        # Final shape: (batch_size, num_segments, max_length_i, feature_dim)

        padded_curr_trajectory_segments = torch.stack(
            padded_curr_trajectory_segments, dim=1
        )
        padded_curr_map_cones = torch.stack(padded_curr_map_cones, dim=1)
        padded_curr_map_segments = torch.stack(padded_curr_map_segments, dim=1)

        # collate scalar valued batch items using default_collate
        collated_batch = default_collate(
            [
                {key: d[key] for key in non_traj_map_segment_lap_keys if key in d}
                for d in batch
            ]
        )

        # collate all segment lengths
        all_curr_trajectory_segment_lengths = []
        for i in range(1, NUM_SEGMENTS + 1):
            traj_i_length_key = f"curr_segment_{i}_trajectory_inputs_subsampled_length"
            curr_trajectory_segment_lengths = [
                item[traj_i_length_key] for item in batch
            ]
            all_curr_trajectory_segment_lengths.append(
                torch.tensor(curr_trajectory_segment_lengths)
            )

        # add the collated current trajectory and map tensors separately.
        collated_batch["curr_trajectory_segments"] = padded_curr_trajectory_segments
        collated_batch["curr_map_segments"] = padded_curr_map_segments
        collated_batch["curr_map_cones"] = padded_curr_map_cones

        # (batch_size, num_segments)
        collated_batch["curr_trajectory_segments_length"] = torch.vstack(
            all_curr_trajectory_segment_lengths
        ).T

        if self.lap_has_cf_series:
            padded_curr_cf_series_segments = []
            max_length_cf_series = 0
            for i in range(1, NUM_SEGMENTS + 1):
                cf_i_key = f"curr_segment_{i}_cf_instruction_class_series_subsampled"
                curr_cf_series_segments = [
                    torch.as_tensor(item[cf_i_key]) for item in batch
                ]
                max_length_cf_series = max(
                    max_length_cf_series,
                    max(seg.size(0) for seg in curr_cf_series_segments),
                )
            for i in range(1, NUM_SEGMENTS + 1):
                cf_i_key = f"curr_segment_{i}_cf_instruction_class_series_subsampled"
                curr_cf_series_segments = [
                    torch.as_tensor(item[cf_i_key]) for item in batch
                ]
                padded_curr_cf_series_segment = padding(
                    curr_cf_series_segments,
                    self.padding_value,
                    max_length_cf_series,
                    left=self.padding_side == "left",
                )
                # Shape: list of (batch_size, max_length_cf_series, feature_dim)
                padded_curr_cf_series_segments.append(padded_curr_cf_series_segment)

            padded_curr_cf_series_segments = torch.stack(
                padded_curr_cf_series_segments, dim=1
            )
            collated_batch["curr_cf_series_segments"] = padded_curr_cf_series_segments

        if self.with_cf_snippets:
            all_snippet_keys = [k for k in batch[0].keys() if "snippet" in k]
            # b[map_key] = (num_snippets_per_lap, max_map_L_for_b, F)
            # flattened_list_of_map_dicts is a list of length batch_size*num_snippets_per_lap with each element being a dict with all keys of a single cf dataset item
            flattened_list_of_map_dicts = [
                {k: b[k][i] for k in all_snippet_keys}
                for b in batch
                for i in range(self.num_cf_snippets_per_lap)
            ]
            # collated flattened snippet keys using the snippet collator
            collated_snippet_dict = self.cf_collator(flattened_list_of_map_dicts)
            # reshape tensors back into (batch_size, num_snippets,....)
            for k in collated_snippet_dict:
                if type(collated_snippet_dict[k]) is not list:
                    collated_snippet_dict[k] = collated_snippet_dict[k].view(
                        len(batch),
                        self.num_cf_snippets_per_lap,
                        *collated_snippet_dict[k].shape[1:],
                    )
                else:
                    collated_snippet_dict[k] = [
                        collated_snippet_dict[k][i : i + self.num_cf_snippets_per_lap]
                        for i in range(
                            0,
                            len(collated_snippet_dict[k]),
                            self.num_cf_snippets_per_lap,
                        )
                    ]
            collated_batch.update(collated_snippet_dict)

        # convert all 64 to 32 bit floats.
        collated_batch = coerce_tensor_dtype_bits(collated_batch)

        return collated_batch


class TrackSegmentCollatorFullSequence:
    """
    This collator is to used with AIC24D16FullSequenceLapDataset, AIC24D05FullSequenceLapDataset and AIC24D05CombinedFullSequenceDataset
    also with take care of the collation of num_cf_snippets_per_lap cf_snippets associated with the lap.

    All keys require padding along the lap dimensions. Only some keys require padding along the other dimension (the feature length). Depending on the shape of the tensor to be
    padded, the data item keys are grouped into different category and dealt with separately.
    """

    def __init__(
        self,
        padding_value=-2000,
        padding_side="right",
        lap_has_cf_series=False,
        with_cf_snippets=False,
        num_cf_snippets_per_lap=0,
    ):
        self.padding_value = padding_value
        self.padding_side = padding_side
        self.lap_has_cf_series = lap_has_cf_series
        self.with_cf_snippets = with_cf_snippets
        self.num_cf_snippets_per_lap = num_cf_snippets_per_lap

    def __call__(self, batch: List[Dict[str, Any]]) -> Dict[str, Any]:
        non_traj_map_keys = []

        # traj and map segments in a lap need special handling due to padding
        traj_map_keys = [
            "curr_trajectory_segments",
            "curr_map_segments",
        ]
        if self.lap_has_cf_series:
            traj_map_keys.append("curr_cf_series_segments")

        if self.with_cf_snippets:
            # if snippets are present map for snippets need special handling.
            traj_map_keys.append("snippet_normalized_map_subsampled")

        # create list of keys that require only padding on lap dimension
        exclude_list = (
            traj_map_keys + ["curr_lap_uid"]
            if not self.with_cf_snippets
            else traj_map_keys + ["curr_lap_uid"] + ["snippet_lap_uid"]
        )
        non_traj_map_keys = [k for k in batch[0].keys() if k not in exclude_list]

        # max number of laps in the lap dimnesions
        max_num_laps_in_batch = max(
            [b["curr_trajectory_segments"].shape[0] for b in batch]
        )
        # for different features, compute the max feature len required for padding
        max_feature_len = {}
        for k in traj_map_keys:
            max_feature_len[k] = max([b[k].shape[-2] for b in batch])

        collated_batch = {}
        for k in batch[0].keys():
            list_k = []
            for b in batch:
                if k in traj_map_keys:
                    # collate trajectories and maps. Needs padding along
                    # lap dimension as well as the feature length dimension
                    # L, num_segments/num_cf_snippets, feature, F
                    padded_tensor = lap_level_padding(
                        b[k],
                        b[k].shape[1],  # num_segments/num_cf_snippets
                        max_num_laps_in_batch,
                        max_feature_len[k],
                        self.padding_value,
                    )
                    # list of [max_L, num_segments/num_cf_snippets, max_feature_len, F]
                    list_k.append(padded_tensor)

                if k in non_traj_map_keys:
                    # collate with padding only along the lap dimension
                    padded_tensor = lap_level_padding(
                        b[k],
                        None,  # unused
                        max_num_laps_in_batch,
                        None,  # unused
                        self.padding_value,
                        pad_lap_dim_only=True,
                    )
                    list_k.append(padded_tensor)

                if k == "curr_lap_uid" or k == "snippet_lap_uid":
                    # b['curr_lap_uid] and b['snippet_lap_uid'] is a list and not a tensor
                    # so simply append them
                    list_k.append(b[k])

            if k == "curr_lap_uid" or k == "snippet_lap_uid":
                collated_batch[k] = list_k
            else:
                collated_batch[k] = torch.stack(list_k, dim=0)

        # (B, max_L) - use one of the metrics to compute the lap-level masks.
        collated_batch["curr_lap_masks"] = (
            collated_batch["curr_trial_time"] != self.padding_value
        ).float()

        collated_batch = coerce_tensor_dtype_bits(collated_batch)
        return collated_batch


class TrackSegmentCollatorPrevKSequence:
    """
    This collator is to used with AIC24D05PrevKCombinedDataset and deals with lap keys and snippet keys separately

    Uses a cf collator on the snippet keys

    For the lap keys, they are first divided into traj_map_keys and others and then lap_level_padding is approrpriately
    applied for individual lap keys
    """

    def __init__(
        self,
        padding_value=-2000,
        padding_side="right",
        lap_has_cf_series=False,
        with_cf_snippets=False,
        num_cf_snippets_per_lap=0,
    ):
        self.padding_value = padding_value
        self.padding_side = padding_side
        self.lap_has_cf_series = lap_has_cf_series
        self.with_cf_snippets = with_cf_snippets
        self.num_cf_snippets_per_lap = num_cf_snippets_per_lap
        if self.with_cf_snippets:
            assert self.num_cf_snippets_per_lap != 0
            self.cf_collator = CFSnippetCollator(self.padding_value, self.padding_side)

    def __call__(self, batch: List[Dict[str, Any]]) -> Dict[str, Any]:
        # traj and map segments in a lap need special handling due to padding
        traj_map_keys = [
            "curr_trajectory_segments",
            "curr_map_segments",
        ]
        if self.lap_has_cf_series:
            traj_map_keys.append("curr_cf_series_segments")

        # create list of keys that require only padding on lap dimension
        exclude_list = (
            traj_map_keys + ["curr_lap_uid"]
            if not self.with_cf_snippets
            else traj_map_keys + ["curr_lap_uid"] + ["snippet_lap_uid"]
        )
        non_traj_map_keys = [
            k for k in batch[0].keys() if k not in exclude_list and "snippet" not in k
        ]
        # lap level data keys
        all_lap_keys = traj_map_keys + non_traj_map_keys + ["curr_lap_uid"]
        # max number of laps in the lap dimnesions
        max_num_laps_in_batch = max(
            [b["curr_trajectory_segments"].shape[0] for b in batch]
        )
        # for different features, compute the max feature len required for padding
        max_feature_len = {}
        for k in traj_map_keys:
            max_feature_len[k] = max([b[k].shape[-2] for b in batch])

        collated_batch = {}
        for k in batch[0].keys():
            # only proess lap level keys, snippet level keys are dealt with later.
            if k not in all_lap_keys:
                continue
            list_k = []
            for b in batch:
                if k in traj_map_keys:
                    # collate trajectories and maps. Needs padding along
                    # lap dimension as well as the feature length dimension
                    # L, num_segments/num_cf_snippets, feature, F
                    padded_tensor = lap_level_padding(
                        b[k],
                        b[k].shape[1],  # num_segments/num_cf_snippets
                        max_num_laps_in_batch,
                        max_feature_len[k],
                        self.padding_value,
                    )
                    # list of [max_L, num_segments/num_cf_snippets, max_feature_len, F]
                    list_k.append(padded_tensor)

                if k in non_traj_map_keys:
                    # collate with padding only along the lap dimension
                    padded_tensor = lap_level_padding(
                        b[k],
                        None,  # unused
                        max_num_laps_in_batch,
                        None,  # unused
                        self.padding_value,
                        pad_lap_dim_only=True,
                    )
                    list_k.append(padded_tensor)

                if k == "curr_lap_uid":
                    # b['curr_lap_uid] and b['snippet_lap_uid'] is a list and not a tensor
                    # so simply append them
                    list_k.append(b[k])

            if k == "curr_lap_uid":
                collated_batch[k] = list_k
            else:
                collated_batch[k] = torch.stack(list_k, dim=0)

        # (B, max_L) - use one of the metrics to compute the lap-level masks.
        collated_batch["curr_lap_masks"] = (
            collated_batch["curr_trial_time"] != self.padding_value
        ).float()
        # deal with the cf snippets
        if self.with_cf_snippets:
            all_snippet_keys = [k for k in batch[0].keys() if "snippet" in k]
            list_of_snippet_dicts = [{k: b[k] for k in all_snippet_keys} for b in batch]
            collated_snippet_dict = self.cf_collator(list_of_snippet_dicts)
            collated_batch.update(collated_snippet_dict)

        collated_batch = coerce_tensor_dtype_bits(collated_batch)

        return collated_batch


class CFSnippetCollator:
    """
    This collator is used by the AIC24D05CFDataset.
    The dataset returns a single snippet for concurrent feedback model training
    Only the local map for the snippet needs specialized handling as the lengths would be different.
    All other data items are collated using the default collate.
    """

    def __init__(self, padding_value=-2000, padding_side="right"):
        self.padding_value = padding_value
        self.padding_side = padding_side

    def __call__(self, batch: List[Dict[str, Any]]) -> Dict[str, Any]:
        non_map_keys = [k for k in batch[0].keys() if not "map" in k]

        # collate all other keys (which don't require padding) normally
        collated_batch = default_collate(
            [{key: d[key] for key in non_map_keys if key in d} for d in batch]
        )
        collated_batch.update(
            pad_cf_snippet_map(batch, self.padding_value, self.padding_side)
        )

        # convert all 64 to 32 bit floats.
        collated_batch = coerce_tensor_dtype_bits(collated_batch)

        return collated_batch
