import argparse
import collections
import os
import pickle
from collections.abc import MutableMapping, MutableSequence
from enum import Enum, IntEnum
from itertools import islice
from pathlib import Path, PosixPath
from typing import Callable, Iterator, Union

import numpy as np
import pandas as pd
import torch
import yaml
from torch.utils.data import Dataset

# General size of a local map for an approximately 10s long snippet. Used for creating empty data points.
FIXED_LOCAL_MAP_LEN = 203
# frequency at which carla data is extracted
CARLA_HZ = 120
# number of map segments in Thunderhill West
NUM_SEGMENTS = 10
# annotator ids for 24D05 crest instruction category annotations.
ANNOTATOR_IDS_24_D_05 = [
    "private.us-east-1.311daa9188d174fb",
    "private.us-east-1.54d1c2e40e03d4df",
    "private.us-east-1.aa1918156fc0e326",
    "private.us-east-1.cc76526182a05c1d",
    "private.us-east-1.ecc34e76087853d4",
]
INVALID_INSTRUCTION_CATEGORIES = ["lookingahead"]

# mapping from categories that we will consider for training and an integer indicating class id. 0 is reserved for null categories.
VALID_ANNOTATION_CATEGORIES_INDEX_DICT_DEFAULT = {
    ("instruction", "right", "stay"): 1,  # treat move and stay as the same category
    ("instruction", "right", "move"): 1,
    ("instruction", "left", "stay"): 2,  # treat move and stay as the same category
    ("instruction", "left", "move"): 2,
    ("instruction", "throttle", "on"): 3,
    ("instruction", "throttle", "off"): 4,
    ("instruction", "throttle", "stay"): 5,
    ("instruction", "brake", "on"): 6,
    ("instruction", "brake", "off"): 7,
    ("instruction", "turn", "left"): 8,
    ("instruction", "turn", "right"): 9,
    ("instruction", "steering", "landmark"): 10,
    ("instruction", "steering", "less"): 11,
    ("instruction", "steering", "little"): 11,
}

VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING = {
    ("instruction", "right", "stay"): 1,  # treat move and stay as the same category
    ("instruction", "right", "move"): 1,
    ("instruction", "left", "stay"): 2,  # treat move and stay as the same category
    ("instruction", "left", "move"): 2,
    ("instruction", "throttle", "on"): 3,
    ("instruction", "throttle", "off"): 4,
    ("instruction", "throttle", "stay"): 5,
    ("instruction", "brake", "on"): 6,
    ("instruction", "brake", "off"): 7,
    ("instruction", "turn", "left"): 8,
    ("instruction", "turn", "right"): 8,
    # ("instruction", "steering", "landmark"): 10,
    # ("instruction", "steering", "less"): 11,
    # ("instruction", "steering", "little"): 11,
}
VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN = {
    ("instruction", "right", "stay"): 1,  # treat move and stay as the same category
    ("instruction", "right", "move"): 1,
    ("instruction", "left", "stay"): 2,  # treat move and stay as the same category
    ("instruction", "left", "move"): 2,
    ("instruction", "throttle", "on"): 3,
    ("instruction", "throttle", "off"): 4,
    ("instruction", "throttle", "stay"): 5,
    ("instruction", "brake", "on"): 6,
    ("instruction", "brake", "off"): 7,
}

VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN_NO_BRAKE = {
    ("instruction", "right", "stay"): 1,  # treat move and stay as the same category
    ("instruction", "right", "move"): 1,
    ("instruction", "left", "stay"): 2,  # treat move and stay as the same category
    ("instruction", "left", "move"): 2,
    ("instruction", "throttle", "on"): 3,
    ("instruction", "throttle", "off"): 4,
    ("instruction", "throttle", "stay"): 5,
    # ("instruction", "brake", "on"): 6,
    # ("instruction", "brake", "off"): 7,
}

VALID_ANNOTATION_CATEGORIES_INDEX_DICT_BASIC = {
    ("instruction", "right", "stay"): 1,  # treat move and stay as the same category
    ("instruction", "right", "move"): 1,
    ("instruction", "left", "stay"): 2,  # treat move and stay as the same category
    ("instruction", "left", "move"): 2,
    ("instruction", "throttle", "on"): 3,
    ("instruction", "throttle", "stay"): 3,
    ("instruction", "brake", "off"): 3,
    ("instruction", "throttle", "off"): 4,
    ("instruction", "brake", "on"): 4,
}

######
COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_DEFAULT = {
    "right - stay/move": 1,  # treat move and stay as the same category
    "left - stay/move": 2,  # treat move and stay as the same category
    "throttle - on": 3,
    "throttle - off": 4,
    "throttle - stay": 5,
    "brake - on": 6,
    "brake - off": 7,
    "turn - left": 8,
    "turn - right": 9,
    "steering - landmark": 10,
    "steering - less/little": 11,
}

COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING = {
    "right - stay/move": 1,  # treat move and stay as the same category
    "left - stay/move": 2,  # treat move and stay as the same category
    "throttle - on": 3,
    "throttle - off": 4,
    "throttle - stay": 5,
    "brake - on": 6,
    "brake - off": 7,
    "turn - left/right": 8,
}

COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN = {
    "right - stay/move": 1,  # treat move and stay as the same category
    "left - stay/move": 2,  # treat move and stay as the same category
    "throttle - on": 3,
    "throttle - off": 4,
    "throttle - stay": 5,
    "brake - on": 6,
    "brake - off": 7,
}

COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN_NO_BRAKE = {
    "right - stay/move": 1,  # treat move and stay as the same category
    "left - stay/move": 2,  # treat move and stay as the same category
    "throttle - on": 3,
    "throttle - off": 4,
    "throttle - stay": 5,
}

COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_BASIC = {
    "right - stay/move": 1,  # treat move and stay as the same category
    "left - stay/move": 2,  # treat move and stay as the same category
    "speed_up; t/on, b/off": 3,
    "slow down; t/off, b/on": 4,
}


class CategoryDictIndex(Enum):
    DEFAULT = VALID_ANNOTATION_CATEGORIES_INDEX_DICT_DEFAULT
    NO_STEERING = VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING
    NO_STEERING_NO_TURN = VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN
    NO_STEERING_NO_TURN_NO_BRAKE = (
        VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN_NO_BRAKE
    )
    BASIC = VALID_ANNOTATION_CATEGORIES_INDEX_DICT_BASIC


class CategoryDictVizIndex(Enum):
    DEFAULT = COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_DEFAULT
    NO_STEERING = COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING
    NO_STEERING_NO_TURN = (
        COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN
    )
    NO_STEERING_NO_TURN_NO_BRAKE = (
        COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN_NO_BRAKE
    )
    BASIC = COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_BASIC


# information extracted from 24-D-05: Testing Schedule & High level notes spreadsheet, column titled (demos)
# in the spreadsheet the information is entered 1-index. Here we need it as 0-indexed. Hence there will be a difference of 1
# between the data in the spreadsheet and here.
DEMO_TRIAL_PER_SUBJECT_24_D_05 = {
    "P601": 19,
    "P602": 9,
    "P603": 13,
    "P604": 10,
    "P605": 11,
    "P606": 12,
    "P607": 16,
    "P608": 16,
    "P609": None,
    "P610": 16,
    "P611": 11,
    "P612": 12,
    "P613": 13,
    "P614": 11,
    "P615": None,
}

INVALID_TRIAL_IDX_PER_SUBJECT_24_D_05 = {
    "P601": [],
    "P602": [],
    "P603": [],
    "P604": [],
    "P605": [],
    "P606": [],
    "P607": [],
    "P608": [],
    "P609": [],
    "P610": [],
    "P611": [],
    "P612": [],
    "P613": [],
    "P614": [],
    "P615": [],
}

INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16 = {
    "P1601": [16, 20, 28],
    "P1602": [],
    "P1603": [13, 36],
    "P1604": [],
    "P1605": [],
    "P1606": [3, 13, 14, 16, 20, 25, 32, 33, 34, 39],  # too many invalid trials.
    "P1607": [],
    "P1608": [1, 21, 35],
    "P1609": [0, 1, 6, 9, 13, 17, 19, 20, 24],
    "P1610": [6, 19, 20],
    "P1611": [],  # the trials are valid. but subject is not to be included
    "P1612": [4, 5, 17, 18, 21, 25],
    "P1613": [13],
    "P1614": [0, 3, 21, 23, 24, 25, 28, 35],
    "P1615": [31, 34],
}


INVALID_TRIAL_IDX_PER_SUBJECT_24_D_18 = {
    "P1804": [],
    "P1805": [],
    "P1806": [],
    "P1807": [],
    "P1808": [],
    "P1809": [],
    "P1810": [],
    "P1811": [],
    "P1812": [],
    "P1813": [],
    "P1814": [],
    "P1815": [],
    "P1816": [],
    "P1817": [],
    "P1818": [],
    "P1819": [],
    "P1820": [],
    "P1821": [],
    "P1822": [],
    "P1823": [],
    "P1824": [],
    "P1825": [],
    "P1826": [],
    "P1827": [],
    "P1828": [],
    "P1829": [],
    "P1830": [],
    "P1831": [],
    "P1832": [],
    "P1833": [],
    "P1834": [],
    "P1835": [],
    "P1836": [],
    "P1837": [],
    "P1838": [],
    "P1839": [],
    "P1840": [],
}

_CONES_COORDINATES = [
    [-682, -2, 1.7],  # end of straightaway
    [-700, -144, 4.4],  # turn 1 right
    [-615, -239, 12.0],  # turn 2 left
    [-912, -117, -1.48],  # turn 3 left
    [-938, -72, -1.65],  # turn 3 right
    [-974, 80, -3.9],  # turn 4 left
    [-935, 109, -2.6],  # turn 4 right
    [-990, 2, -4.1],  # turn 4 brake zone
    [-991.5, 6, -4.2],  # turn 4 brake zone
    [-855, 171, -2.5],  # turn 5 right
    [-835, 256, -5.5],  # turn 5 left
    [-834, 330, -7.6],  # turn 5 right 2
    [-849, 518, -10.7],  # turn 6 left
    [-767, 569, -10.6],  # turn 6 right
    [-615, 587, -11.8],  # turn 7 left
    [-682, 611.5, -13.0],  # turn 7 brake zone
    [-684, 611.5, -13.0],  # turn 7 brake zone
    [-673, 535, -10.67],  # turn 8 right straight
    [-773, 420, -5.3],  # turn 8 right apex
    [-747, 289, -5.4],  # turn 9 right
    [-779, 335, -6.7],  # turn 9 brake zone
    [-780, 338, -6.7],  # turn 9 brake zone
    [-645, 365, -4.45],  # turn 10 left
]
CONES_COORDINATES = [(i, -j, k) for (i, j, k) in _CONES_COORDINATES]

GRAVITATION_CONST = -9.81


# Potential move this to a general common file in utils folder
class TrajFeatureIndex(IntEnum):
    TRAJ_X = 0
    TRAJ_Y = 1
    TRAJ_Z = 2
    TRAJ_VX = 3
    TRAJ_VY = 4
    TRAJ_VZ = 5
    TRAJ_AX = 6
    TRAJ_AY = 7
    TRAJ_AZ = 8
    STEERING_ANGLE = 9
    THROTTLE = 10
    BRAKE = 11
    ORIENT_X = 12
    ORIENT_Y = 13
    ORIENT_Z = 14
    ORIENT_W = 15


class TrajFeatureIndexNoAccel(IntEnum):
    TRAJ_X = 0
    TRAJ_Y = 1
    TRAJ_Z = 2
    TRAJ_VX = 3
    TRAJ_VY = 4
    TRAJ_VZ = 5
    STEERING_ANGLE = 6
    THROTTLE = 7
    BRAKE = 8
    ORIENT_X = 9
    ORIENT_Y = 10
    ORIENT_Z = 11
    ORIENT_W = 12


class FeatureNormalizationScheme(IntEnum):
    MEAN_STD_SCALING = 0  # mean 0, unit std, aka standardization
    MIN_MAX_SCALING = 1
    MAX_SCALING = 2
    MEDIAN_IQR_SCALING = 3
    LINEAR_SCALE = 4


TRAJ_FEATURE_LINEAR_SCALE = {
    TrajFeatureIndex.TRAJ_X.name: 1.0 / 300.0,
    TrajFeatureIndex.TRAJ_Y.name: 1.0 / 300.0,
    TrajFeatureIndex.TRAJ_Z.name: 1.0 / 40.0,
    TrajFeatureIndex.TRAJ_VX.name: 1.0 / 40.0,
    TrajFeatureIndex.TRAJ_VY.name: 1.0 / 40.0,
    TrajFeatureIndex.TRAJ_VZ.name: 1.0 / 8.0,
    TrajFeatureIndex.TRAJ_AX.name: 1.0,
    TrajFeatureIndex.TRAJ_AY.name: 1.0,
    TrajFeatureIndex.TRAJ_AZ.name: 1.0,
    TrajFeatureIndex.STEERING_ANGLE.name: 1.0,
    TrajFeatureIndex.THROTTLE.name: 1.0,
    TrajFeatureIndex.BRAKE.name: 1.0,
    TrajFeatureIndex.ORIENT_X.name: 1.0,
    TrajFeatureIndex.ORIENT_Y.name: 1.0,
    TrajFeatureIndex.ORIENT_Z.name: 1.0,
    TrajFeatureIndex.ORIENT_W.name: 1.0,
}

# TODO (deepak.gopinath Modify to have key and subkey. similar structure as self.metric_keys)
# The metrics scale was determined based on 24D05 lap level dataset
METRICS_LINEAR_SCALE = {
    "trial_time": {None: 1.0 / 120.0},
    "oob": {"percentage": 1.0 / 100.0},
    "steering_score": {"abs_mean": 1000.0},
    "racing_line_score": {"abs_mean": 1.0 / 5.0},
    "braking_score": {"abs_mean": 1000.0},
    "throttle_score": {"abs_mean": 100.0},
    "smoothness_score": {"mean": 1.0 / 20.0},
}

CF_METRICS_LINEAR_SCALE = {
    "oob_score": {"percentage": 1.0 / 100.0},
    "steering_score": {"abs_mean": 10.0},
    "racing_line_score": {"abs_mean": 1.0 / 5.0},
    "braking_score": {"abs_mean": 5.0},  # only positive
    "throttle_score": {"abs_mean": 5.0},  # only positive
    "smoothness_score": {"mean": 1.0 / 20.0},  # could be 10.0 if 0-1 is what's needed
    "friction_utilization_score": {
        "mean": 1.0 / 2.0
    },  # could be 10.0 if 0-1 is what's needed
}

METRICS_LINEAR_SCALE_24D18 = {
    "trial_time": {None: {"max": 120.0, "min": 0.0}},
    "oob": {"percentage": {"max": 1.0, "min": 0.0}},
    "steering_score": {"abs_mean": {"max": 1.0, "min": 0.0}},
    "racing_line_score": {"abs_mean": {"max": 1.0, "min": 0.0}},
    "braking_score": {"abs_mean": {"max": 1.0, "min": 0.0}},
    "throttle_score": {"abs_mean": {"max": 1.0, "min": 0.0}},
    "smoothness_score": {"mean": {"max": 20.0, "min": 0.0}},
    "gaze_dispersion": {None: {"max": 0.2, "min": 0.02}},
}

SUBJ_LEVEL_METRICS_LINEAR_SCALE = {
    "skidpadcircleracingline_g_force": 1.0 / 1000,
    "prestudy_grip_strength": 1.0 / 60,
    "prestudy_knowledge_score": 1.0 / 100,
    "prestudy_tops_score": 1.0 / 160,
    "prestudy_occlusion_video_accuracy": 1.0,
    "prestudy_occlusion_image_overall_accuracy": 1.0,
    "prestudy_fitts_hand_eye_coordination": 1.0,
    "prestudy_fitts_motor_skill": 1.0 / 350,
    "skidpadslalomracingline_g_force": 1.0 / 15,
    "skidpadcircleracingline_trial_time": 1.0 / 25,
    "skidpadslalomracingline_trial_time": 1.0 / 18,
}

FEATURE_TO_DF_KEY = {
    TrajFeatureIndex.TRAJ_X: "ego_x",
    TrajFeatureIndex.TRAJ_Y: "ego_y",
    TrajFeatureIndex.TRAJ_Z: "ego_z",
    TrajFeatureIndex.TRAJ_VX: "ego_vx",
    TrajFeatureIndex.TRAJ_VY: "ego_vy",
    TrajFeatureIndex.TRAJ_VZ: "ego_vz",
    TrajFeatureIndex.TRAJ_AX: None,
    TrajFeatureIndex.TRAJ_AY: None,
    TrajFeatureIndex.TRAJ_AZ: None,
    TrajFeatureIndex.STEERING_ANGLE: "steering",
    TrajFeatureIndex.THROTTLE: "throttle",
    TrajFeatureIndex.BRAKE: "brake",
    TrajFeatureIndex.ORIENT_X: "ego_orientation_x",
    TrajFeatureIndex.ORIENT_Y: "ego_orientation_y",
    TrajFeatureIndex.ORIENT_Z: "ego_orientation_z",
    TrajFeatureIndex.ORIENT_W: "ego_orientation_w",
}
FEATURE_TO_DF_KEY_NO_ACCEL = {
    TrajFeatureIndexNoAccel.TRAJ_X: "ego_x",
    TrajFeatureIndexNoAccel.TRAJ_Y: "ego_y",
    TrajFeatureIndexNoAccel.TRAJ_Z: "ego_z",
    TrajFeatureIndexNoAccel.TRAJ_VX: "ego_vx",
    TrajFeatureIndexNoAccel.TRAJ_VY: "ego_vy",
    TrajFeatureIndexNoAccel.TRAJ_VZ: "ego_vz",
    TrajFeatureIndexNoAccel.STEERING_ANGLE: "steering",
    TrajFeatureIndexNoAccel.THROTTLE: "throttle",
    TrajFeatureIndexNoAccel.BRAKE: "brake",
    TrajFeatureIndexNoAccel.ORIENT_X: "ego_orientation_x",
    TrajFeatureIndexNoAccel.ORIENT_Y: "ego_orientation_y",
    TrajFeatureIndexNoAccel.ORIENT_Z: "ego_orientation_z",
    TrajFeatureIndexNoAccel.ORIENT_W: "ego_orientation_w",
}

FEATURE_NORMALIZATION_BIT = {
    TrajFeatureIndex.TRAJ_X: True,
    TrajFeatureIndex.TRAJ_Y: True,
    TrajFeatureIndex.TRAJ_Z: True,
    TrajFeatureIndex.TRAJ_VX: True,
    TrajFeatureIndex.TRAJ_VY: True,
    TrajFeatureIndex.TRAJ_VZ: True,
    TrajFeatureIndex.TRAJ_AX: None,
    TrajFeatureIndex.TRAJ_AY: None,
    TrajFeatureIndex.TRAJ_AZ: None,
    TrajFeatureIndex.STEERING_ANGLE: True,
    TrajFeatureIndex.THROTTLE: True,
    TrajFeatureIndex.BRAKE: True,
    TrajFeatureIndex.ORIENT_X: False,
    TrajFeatureIndex.ORIENT_Y: False,
    TrajFeatureIndex.ORIENT_Z: False,
    TrajFeatureIndex.ORIENT_W: False,
}


def parse_arguments(args=None):
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument(
        "--track-map-csv",
        type=str,
        help="Input .csv file to read for the track map.",
        default="~/Data/24-D-05/Rosbags/track.csv",
    )
    parser.add_argument(
        "--trials-dir",
        type=str,
        help="Folder containing the trials directory",
        default="~/Data/24-D-05/trials_final",
    )
    parser.add_argument(
        "--trajectory-snippets-dir",
        type=str,
        help="Folder in which the snipped trajectory are to be stored",
        default="~/Data/24-D-05/trajectory_snippets",
    )
    parser.add_argument(
        "--segment-level-trajectory-snippets-dir",
        type=str,
        help="Folder in which the segment level trajectory",
        default="~/Data/24-D-05/trajectory_segments_map_seg_ids",
    )
    parser.add_argument(
        "--llm-model-name",
        type=str,
        default="meta-llama/Llama-2-7b-chat-hf",
        help="Model name to initialize. From huggingface or a path.",
    )
    parser.add_argument(
        "--trajectory-snippet-len-s",
        type=int,
        default=10,
        help="How far back should the context window stretch? Int, in seconds.",
    )
    parser.add_argument(
        "--hop-size-s",
        type=int,
        default=1,
        help="How much to hop in seconds when snipping ",
    )
    parser.add_argument(
        "--make-up-missing-segments",
        type=bool,
        default=False,
        help="Create dummy al 0s segment pkls for missing segments",
    )

    result = parser.parse_args(args)
    return result


def add_map(map_data):
    left_lane_keys = ["inner_edge/x", "inner_edge/y"]
    right_lane_keys = ["outer_edge/x", "outer_edge/y"]
    racing_line_keys = ["refline/x", "refline/y", "refline/v"]
    # Add map features.
    data_len = len(map_data)

    def get_polyline(df, keys):
        x = df[keys[0]]
        y = df[keys[1]]
        segments = []

        for s_i in range(data_len - 1):
            x0 = x[s_i]
            y0 = y[s_i]
            segment = [x0, y0]  # , x1, y1]
            segments.append(segment)
        return segments

    # list of  4-element lists, where each 4-element list is the start (first 2) and end (last 2) value of the 3D segment
    left_lane_segments = get_polyline(map_data, left_lane_keys)
    right_lane_segments = get_polyline(map_data, right_lane_keys)

    def convert_vector(df, key):
        return df[key].tolist()

    race_line_x_pb = convert_vector(map_data, racing_line_keys[0])
    race_line_y_pb = convert_vector(map_data, racing_line_keys[1])
    race_line_vel_pb = convert_vector(map_data, racing_line_keys[2])
    race_line = [
        [x, y, v] for x, y, v in zip(race_line_x_pb, race_line_y_pb, race_line_vel_pb)
    ]  # list of 3-element lists, where each 3-element list is x,y,v of a racing line
    # First and last element are identical, hence remove the last one?
    race_line = race_line[:-1]

    return {
        "left_lane": left_lane_segments,
        "right_lane": right_lane_segments,
        "race_line": race_line,
        "cones": np.asarray(CONES_COORDINATES)[:, :2],
    }


def load_pkl(pkl_path):
    # TODO (deepak.gopinath add checks for whether the pkl path is valid and it exists)
    with open(pkl_path, "rb") as fp:
        a = pickle.load(fp)

    return a


def load_file_from_yaml(filename):
    config = {}
    with open(filename, "r") as file:
        try:
            # unsafe load is ok for our application. If we use safe_load, we will need to write custom handlers for numpy objects
            config = yaml.load(file, Loader=yaml.UnsafeLoader)
        except yaml.YAMLError as exc:
            print(exc)
    return config


def extract_uid_from_path(parquet_path: PosixPath) -> str:
    return parquet_path.parts[-2]


def get_pid_and_trial_num_from_uid(uid):
    hyphen_index = uid.find("-")
    underscore_index = uid.find("_")
    pid = uid[:hyphen_index]
    trial_num = int(uid[underscore_index + 1 :])
    return pid, trial_num


def get_uid_from_pid_and_trial_num(pid, trial_num):
    return pid + "-trial_" + str(trial_num)


def split_pids_laps(dataset: Dataset):
    """
    Splits the dataset into unique participant IDs (pids), unique lap IDs (lap_ids), and unique lap participant labels (lap_uids).
    Args:
        dataset (Dataset): The dataset to be split.
    Returns:
        tuple: A tuple containing the following:
            - pids (list): A sorted list of unique participant IDs.
            - lap_ids (list): A sorted list of unique lap IDs.
            - lap_uids (list): A list of all lap+pid labels.
    """

    pids = set()
    lap_ids = set()
    lap_uids = list()
    for itm in dataset:
        current_lap_uid = itm["curr_lap_uid"]
        pid, trial_num = get_pid_and_trial_num_from_uid(current_lap_uid)
        pids.add(pid)
        lap_uids.append(current_lap_uid)
        lap_ids.add(trial_num)

    pids = sorted(list(pids))
    lap_ids = sorted(list(lap_ids))
    return pids, lap_ids, lap_uids


def get_total_trials_per_subject(all_trial_parquets):
    total_trials_per_subject_dict = collections.Counter()
    for trial_parquet_path in all_trial_parquets:
        uid = extract_uid_from_path(trial_parquet_path)
        pid, _ = get_pid_and_trial_num_from_uid(uid)
        total_trials_per_subject_dict[pid] += 1

    return total_trials_per_subject_dict


def open_pkl(pkl_file_path):
    # TODO (deepak.gopinath) clean up handling of missing file path.
    assert pkl_file_path is not None
    with open(pkl_file_path, "rb") as fp:
        a = pickle.load(fp)

    return a


def open_trial(parquet_file_path):
    trial_df = open_trial_df_parquet(parquet_file_path)
    trial_dict = open_trial_yaml(parquet_file_path)
    trial_dict["dataframe"] = trial_df
    return trial_dict


def open_trial_df_parquet(parquet_file_path):
    assert parquet_file_path is not None
    return pd.read_parquet(parquet_file_path)


def open_trial_yaml(parquet_file_path):
    yaml_path = parquet_file_path.with_suffix(".yaml")
    return load_file_from_yaml(yaml_path)


def load_file_from_yaml(filename):
    config = {}
    with open(filename, "r") as file:
        try:
            # unsafe load is ok for our application. If we use safe_load, we will need to write custom handlers for numpy objects
            config = yaml.load(file, Loader=yaml.UnsafeLoader)
        except yaml.YAMLError as exc:
            print(exc)
    return config


def calculate_sampling_freq(trial_df):
    # compute sampling rate and frequency for a dataframe. Assumes that each dataframe has no breaks.
    ts = trial_df["timestamp"].values
    delta_ts = np.diff(ts)
    delta_ts_mean = np.mean(delta_ts)
    delta_ts_std = np.std(delta_ts)
    sampling_freq = 1.0 / delta_ts_mean

    return delta_ts_mean, delta_ts_std, sampling_freq


def walk_batch(item: Union[MutableSequence, MutableMapping], types, func: Callable):
    """A helper method to recursively walk through nested dicts and lists and modify them in place

    This method will walk through the provided item, recursively checking any lists or dicts it finds, and replace
    objects that match `isinstance(value, types)` with the result of `func(value)`.

    Other collection types besides dict and list, such as tuples, will not be checked recursively, even if
    they contain other dicts and lists.

    :param item: dict or list to walk through
    :param types: the types that `func` should be called on
    :param func: a callable that should expect an object of the specified types and returns a modified version of the
        object, which will replace the original object in the parent collection

    returns the modified item
    """
    if isinstance(item, MutableMapping):
        union_iterable = item.items()
    else:
        union_iterable = enumerate(item)

    for key, value in union_iterable:
        if isinstance(value, types):
            item[key] = func(value)
        elif isinstance(value, (MutableSequence, MutableMapping)):
            item[key] = walk_batch(value, types, func)

    return item


def coerce_numpy_dtype_bits(item: dict):
    # Normalize dtypes to 32 bit
    def coerce_array(array: Union[np.ndarray, np.number]):
        if array.dtype == np.float64:
            return array.astype(np.float32)
        else:
            return array

    return walk_batch(item, (np.ndarray, np.number), coerce_array)


def coerce_tensor_dtype_bits(item: Union[dict, list]):
    # Normalize dtypes to 32 bit
    def coerce_tensor(tensor: torch.Tensor):
        if tensor.dtype == torch.float64:
            return tensor.float()
        else:
            return tensor

    return walk_batch(item, torch.Tensor, coerce_tensor)


def padding(target, pad_val, max_len, left=True):
    out = []
    for seg in target:
        pad_size = max_len - seg.size(0)
        padding = torch.full((pad_size, seg.size(1)), pad_val)
        if left:
            padded_seg = torch.cat(
                [padding, seg],
                dim=0,
            )
        else:
            padded_seg = torch.cat(
                [seg, padding],
                dim=0,
            )

        out.append(padded_seg)
    # Shape: (batch_size, max_length, feature_dim)
    out_stack = torch.stack(out, dim=0)
    return out_stack


def lap_level_padding(
    tensor_to_be_padded,
    num_items,
    max_num_laps_in_batch,
    max_feature_len,
    padding_value,
    lap_padding_side="right",
    feature_padding_side="right",
    pad_lap_dim_only=False,  # cones, lengths
):
    """
    Takes care of padding lap sequences. Also considers the padding requirements along other dimensions if needed,
    Args:
        tensor_to_be_padded: Tensor that need to be padded. Can be of different shapes. First dimension is always laps.
                            Example shapes are (max_laps, num_segments, max_seg_len), (max_laps, max_len), (max_laps, F), (max_laps)
        num_segments: number of segment
        max_num_laps_in_batch: Maximum number of laps in this batch. This is the target lap sequence size after padding
        max_feature_len: Maximum size of other dimension that needs to be padded.
                         Can be None for tensors that don't need padding on any other dimension but the lap dimension
        lap_padding_side: Side in which the lap sequence should be padded. Defaults to "right"
        feature_padding_side: Side in which other dimensions get padded.
        pad_lap_dim_only: boolean that specifies if it is any tensor that has a shape (L,....). Only lap dimension needs padding
    """

    if pad_lap_dim_only:
        # cones, lengths
        # (max_laps, ...), works for tensors, that are just (max_laps)
        full_padded_tensor = padding_value * torch.ones(
            (max_num_laps_in_batch, *tensor_to_be_padded.shape[1:]),
            dtype=tensor_to_be_padded.dtype,
        )
    else:
        # trajectories, maps for segemnt
        # (max_laps, num_items, max_feature_len, feature)
        full_padded_tensor = padding_value * torch.ones(
            (
                max_num_laps_in_batch,
                num_items,
                max_feature_len,
                tensor_to_be_padded.shape[-1],  # traj or map_dim
            ),
            dtype=tensor_to_be_padded.dtype,
        )

    if lap_padding_side == "right":
        lap_end_idx = tensor_to_be_padded.shape[0]
        if feature_padding_side == "left":
            if pad_lap_dim_only:
                # (max_L, ...) or (max_L)
                full_padded_tensor[:lap_end_idx] = tensor_to_be_padded
            else:
                # (max_L, num_segments, max_seg_len, F)
                segment_start_idx = max_feature_len - tensor_to_be_padded.shape[-2]
                full_padded_tensor[
                    :lap_end_idx, :, segment_start_idx:, :
                ] = tensor_to_be_padded
        elif feature_padding_side == "right":
            if pad_lap_dim_only:
                full_padded_tensor[:lap_end_idx] = tensor_to_be_padded
            else:
                # (max_L, num_segments, max_seg_len, F)
                segment_end_idx = tensor_to_be_padded.shape[-2]
                full_padded_tensor[
                    :lap_end_idx, :, :segment_end_idx, :
                ] = tensor_to_be_padded

    return full_padded_tensor


def pad_cf_snippet_map(batch, padding_value, padding_side):
    collated_map = {}

    map_key = f"snippet_normalized_map_subsampled"
    snippet_maps = [torch.as_tensor(item[map_key]) for item in batch]
    max_length_i_map = max(snippet_map.size(0) for snippet_map in snippet_maps)
    # (batch_size, max_length_map, feature_dim)
    padded_snippet_maps = padding(
        snippet_maps,
        padding_value,
        max_length_i_map,
        left=padding_side == "left",
    )
    collated_map[map_key] = padded_snippet_maps

    return collated_map


## COPY PASTE OF METRICS COMPUTATION FROM hid_common
# TODO (deepak.gopinath/xiongyi.cui). Add hid_common math stuff as library or submodule?


def compute_trial_time(df):
    return {
        "trial_time": df["carla_objects log time"].iloc[-1]
        - df["carla_objects log time"].iloc[0],
        "trial_time_series": df["carla_objects log time"].values,
    }


def compute_steering_score(df):
    steering_change = np.diff(df["steering"].values)
    return {
        "steering_score" + "_mean": np.mean(steering_change),
        "steering_score" + "_std": np.std(steering_change),
        "steering_score" + "_max": np.max(steering_change),
        "steering_score" + "_abs_mean": np.mean(np.abs(steering_change)),
        "steering_score" + "_abs_std": np.std(np.abs(steering_change)),
        "steering_score" + "_abs_max": np.max(np.abs(steering_change)),
        "steering_score_series": steering_change,
    }


def compute_racing_line_score(df):
    lateral_distances = df["lateral_distances"].values
    return {
        "racing_line_score" + "_mean": np.mean(lateral_distances),
        "racing_line_score" + "_std": np.std(lateral_distances),
        "racing_line_score" + "_abs_max": np.max(np.abs(lateral_distances)),
        "racing_line_score" + "_abs_mean": np.mean(np.abs(lateral_distances)),
        "racing_line_score" + "_abs_std": np.std(np.abs(lateral_distances)),
        "racing_line_series": lateral_distances,
    }


def compute_oob(df):
    oob_series = df["out_of_bounds"].values
    percentage_oob = 100.0 * sum(oob_series) / len(df)
    # Note: mean and std only makes sense if the distribution is close to normal. Adding it to
    # result dict for bookkeeping
    return {
        "oob" + "_percentage": percentage_oob,
        "oob" + "_max": np.max(df["out_of_bounds_distances"].values),
        "oob" + "_mean": np.mean(df["out_of_bounds_distances"].values),
        "oob" + "_std": np.std(df["out_of_bounds_distances"].values),
        "oob_series": oob_series,
    }


def compute_throttle_score(df):
    throttle_change = np.diff(df["throttle"].values)
    return {
        "throttle_score" + "_mean": np.mean(throttle_change),
        "throttle_score" + "_std": np.std(throttle_change),
        "throttle_score" + "_max": np.max(throttle_change),
        "throttle_score" + "_abs_mean": np.mean(np.abs(throttle_change)),
        "throttle_score" + "_abs_std": np.std(np.abs(throttle_change)),
        "throttle_score" + "_abs_max": np.max(np.abs(throttle_change)),
        "throttle_score_series": throttle_change,
    }


def compute_braking_score(df):
    brake_change = np.diff(df["brake"].values)
    return {
        "braking_score" + "_mean": np.mean(brake_change),
        "braking_score" + "_std": np.std(brake_change),
        "braking_score" + "_max": np.max(brake_change),
        "braking_score" + "_abs_mean": np.mean(np.abs(brake_change)),
        "braking_score" + "_abs_std": np.std(np.abs(brake_change)),
        "braking_score" + "_abs_max": np.max(np.abs(brake_change)),
        "braking_score_series": brake_change,
    }


def compute_smoothness_score(df):
    kernel_size = 10
    kernel = np.ones(kernel_size) / kernel_size

    if len(df) < len(kernel):
        smoothness_score_vec = [0, 0]
    else:
        acc_x = np.diff(df["ego_vx"].values) / np.diff(df.timestamp.values)
        # smoothing is added to deal with the chatter in the signal. To be removed if fixed at the sources
        smoothed_acc_x = np.convolve(acc_x, kernel, mode="same")
        acc_y = np.diff(df["ego_vy"].values) / np.diff(df.timestamp.values)
        smoothed_acc_y = np.convolve(acc_y, kernel, mode="same")
        acc_z = np.diff(df["ego_vz"].values) / np.diff(df.timestamp.values)
        smoothed_acc_z = np.convolve(acc_z, kernel, mode="same")
        # (L, 3)
        smoothed_acc_vec = np.vstack((smoothed_acc_x, smoothed_acc_y, smoothed_acc_z)).T
        # (L-1, 3)
        try:
            smoothed_jerk_vec = np.diff(smoothed_acc_vec, axis=0) / np.expand_dims(
                np.diff(df["carla_objects log time"].values[:-1]), 1
            )
        except ValueError as e:
            # Note: This can arise if something goes wrong with windowed smoothing,
            #   leading to an off-by-one size mismatch
            print(f"Encountered ValueError during Jerk calculation: {e}")
            smoothed_jerk_vec = np.diff(smoothed_acc_vec, axis=0) / np.expand_dims(
                np.diff(df["carla_objects log time"].values[:]), 1
            )
        # (L-1, )
        smoothness_score_vec = np.linalg.norm(smoothed_jerk_vec, axis=1)

    return {
        "smoothness_score" + "_mean": np.mean(smoothness_score_vec),
        "smoothness_score" + "_std": np.std(smoothness_score_vec),
        "smoothness_score" + "_max": np.max(smoothness_score_vec),
        "smoothness_score" + "_min": np.min(smoothness_score_vec),
        "smoothness_score_series": smoothness_score_vec,
    }


def compute_friction_utilization_score(df):
    kernel_size = 10
    kernel = np.ones(kernel_size) / kernel_size
    acc_x = np.diff(df["ego_vx"].values) / np.diff(df.timestamp.values)
    # smoothing is added to deal with the chatter in the signal. To be removed if fixed at the sources
    smoothed_acc_x = np.convolve(acc_x, kernel, mode="same")
    acc_y = np.diff(df["ego_vy"].values) / np.diff(df.timestamp.values)
    smoothed_acc_y = np.convolve(acc_y, kernel, mode="same")
    acc_z = np.diff(df["ego_vz"].values) / np.diff(df.timestamp.values)
    smoothed_acc_z = np.convolve(acc_z, kernel, mode="same")

    # (L, 3)
    smoothed_acc_vec = np.vstack((smoothed_acc_x, smoothed_acc_y, smoothed_acc_z)).T
    g_force_vec = smoothed_acc_vec / abs(GRAVITATION_CONST)
    friction_utilization_vec = np.linalg.norm(g_force_vec, axis=1)

    return {
        "friction_utilization_score" + "_mean": np.mean(friction_utilization_vec),
        "friction_utilization_score" + "_std": np.std(friction_utilization_vec),
        "friction_utilization_score" + "_max": np.max(friction_utilization_vec),
        "friction_utilization_score" + "_min": np.min(friction_utilization_vec),
        "friction_utilization_score_series": friction_utilization_vec,
    }
