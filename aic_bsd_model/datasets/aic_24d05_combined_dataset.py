import collections
import random

import numpy as np
from torch.utils.data import Dataset

from aic_bsd_model.datasets.aic_24d05_cf_dataset import AIC24D05CFDataset
from aic_bsd_model.datasets.aic_24d05_lap_dataset import (
    AIC24D05FullSequenceDataset,
    AIC24D05LapDataset,
)
from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    CARLA_HZ,
    CONES_COORDINATES,
    FIXED_LOCAL_MAP_LEN,
    TrajFeatureIndexNoAccel,
    get_pid_and_trial_num_from_uid,
    get_uid_from_pid_and_trial_num,
    load_file_from_yaml,
)
from aic_bsd_model.datasets.data_collators import (
    CFSnippetCollator,
    TrackSegmentCollator,
    TrackSegmentCollatorFullSequence,
    TrackSegmentCollatorPrevKSequence,
)


# this class is essentially K=1. So can be thought of a base class for AIC24D05combinedPrevKDataset
class AIC24D05CombinedDataset(Dataset):
    class_name = "AIC24D05CombinedDataset"
    """
    This is a dataset class to load up individual laps or full lap sequences along with concurrent feedback snippets for the subsequent valid laps
    Each concurrent feedback snippet (cf snippet) is 10s long. 0-5s will be treated as past and 5-10s will be treated as future.
    This dataset feeds the adaptive concurrent feedback model.
    """

    def __init__(self, config_dict, dataset_type, tokenizer=None):
        # dataset class to load lap sequences
        self.dataset_type = dataset_type
        self.lap_dataset = AIC24D05LapDataset(config_dict, dataset_type)
        self.lap_dataset.cache_dataset()
        # dataset class to load cf snippets.
        self.cf_dataset = AIC24D05CFDataset(config_dict, dataset_type, tokenizer)
        self.cf_dataset.cache_dataset()
        self.config_dict = config_dict
        # tokenizer, if needed, to be used for tokenizing text data
        self.tokenizer = tokenizer
        self.trials_directory = self.config_dict.get("trials_directory", None)
        if self.trials_directory is not None:
            self.full_trial_dirs = [
                d for d in list(self.trials_directory.iterdir()) if not d.is_file()
            ]

        self.num_cf_snippets_per_lap = self.config_dict.get(
            "num_cf_snippets_per_lap", 4
        )
        self.padding_value = self.config_dict["padding_value"]
        self.padding_side = self.config_dict["padding_side"]

        self.cf_collator = CFSnippetCollator(self.padding_value, self.padding_side)

        # BSD related stuff
        # TODO (deepak.gopinath
        # UPDATE THE TASK PROMPT TO REFLECT THAT THE JOB IS TO PROVIDE END OF LAP COACHING
        if self.tokenizer is not None:
            self.task_prompt = """You are an AI Racing Coach. You will be given a clip of a driver on a track, 
            and your job is to help coach that driver to a faster lap time. Clip:"""
            self.segment_encoding_prompts_list = [
                "The segment level fused trajectory and map representations are as follows."
            ] + [f"Segment {i+1}: " for i in range(10)]
            self.lap_encoding_prompt = (
                "The lap level fused trajectory and map representation is as follows."
            )
            self.skill_encoding_prompt = "The individual's current skill encoding is "
            self.coach_utterance_prompt = "Coach: "

            # convert prompts into tokenized input_ids and labels
            self.task_prompt_input_ids = self.tokenizer(
                self.task_prompt, add_special_tokens=True
            ).input_ids
            self.coach_utterance_prompt_input_ids = self.tokenizer(
                self.coach_utterance_prompt, add_special_tokens=False
            ).input_ids

            # TODO (deepak.gopinath). Change -100 to a MACRO
            self.task_prompt_labels = [-100] * len(self.task_prompt_input_ids)
            self.coach_utterance_prompt_labels = [-100] * len(
                self.coach_utterance_prompt_input_ids
            )
        # parse in bsd data from the original trial folders.
        self.bsd_language_data_dict = collections.OrderedDict()
        self._prepare_bsd_language_data()

    def _prepare_bsd_language_data(self):
        for trial_dir in self.full_trial_dirs:
            uid = trial_dir.stem
            pid, trial_num = get_pid_and_trial_num_from_uid(uid)
            if pid not in self.bsd_language_data_dict:
                self.bsd_language_data_dict[pid] = collections.OrderedDict()
            bsd_str = ""
            if trial_num != 0:
                bsd_path = trial_dir / str(uid + "_bsd_dict.yaml")
                bsd_dict = load_file_from_yaml(bsd_path)
                for b in bsd_dict["subject_coach_srts"]:
                    bsd_str += b["subtitle"].content + " "
                bsd_str = bsd_str[:-1]  # skip last space
            self.bsd_language_data_dict[pid][trial_num] = bsd_str

    def __len__(self):
        # the len of this dataset is the same as the lap dataset.
        # if lap dataset uses use_full_sequence, then len is the number of subjects
        # if lap_dataset does not have use_full_sequence then len is the number of valid laps
        # if self.dataset_type == 'train':
        #     return len(self.lap_dataset)
        # else:
        return len(self.cf_dataset)

    def create_empty_cf_dict(self, valid_lap_uid=None):
        empty_cf_dict_list = []
        for i in range(1, self.num_cf_snippets_per_lap + 1):
            empty_cf_dict = {}
            empty_cf_dict[f"snippet_trajectory_inputs_subsampled"] = np.zeros(
                (
                    self.cf_dataset.snippet_length_in_sec
                    * self.cf_dataset.target_sampling_frequency,
                    len(TrajFeatureIndexNoAccel),
                )
            )
            empty_cf_dict[f"snippet_trajectory_inputs_subsampled_length"] = (
                self.cf_dataset.snippet_length_in_sec
                * self.cf_dataset.target_sampling_frequency
            )
            # 6 is the dimensionality of the map feature
            empty_cf_dict[f"snippet_normalized_map_subsampled"] = np.zeros(
                (FIXED_LOCAL_MAP_LEN, 6)
            )
            empty_cf_dict[f"snippet_cones"] = np.zeros((len(CONES_COORDINATES), 2))
            empty_cf_dict[f"snippet_lap_uid"] = (
                valid_lap_uid if valid_lap_uid is not None else "INVALID"
            )
            empty_cf_dict[f"snippet_num"] = -1

            empty_cf_dict[f"snippet_cf_instruction_class_series"] = np.zeros(
                (self.cf_dataset.snippet_length_in_sec * CARLA_HZ,)
            )
            empty_cf_dict[f"snippet_cf_instruction_class_series_subsampled"] = np.zeros(
                (
                    self.cf_dataset.snippet_length_in_sec
                    * self.cf_dataset.target_sampling_frequency,
                )
            )

            # empty metrics dict
            empty_cf_dict[f"snippet_racing_line_score_series_subsampled"] = np.zeros(
                (
                    self.cf_dataset.snippet_length_in_sec
                    * self.cf_dataset.target_sampling_frequency,
                )
            )
            empty_cf_dict[f"snippet_throttle_score_series_subsampled"] = np.zeros(
                (
                    self.cf_dataset.snippet_length_in_sec
                    * self.cf_dataset.target_sampling_frequency,
                )
            )
            empty_cf_dict[f"snippet_braking_score_series_subsampled"] = np.zeros(
                (
                    self.cf_dataset.snippet_length_in_sec
                    * self.cf_dataset.target_sampling_frequency,
                )
            )
            empty_cf_dict[f"snippet_smoothness_score_series_subsampled"] = np.zeros(
                (
                    self.cf_dataset.snippet_length_in_sec
                    * self.cf_dataset.target_sampling_frequency,
                )
            )
            empty_cf_dict[
                f"snippet_friction_utilization_score_series_subsampled"
            ] = np.zeros(
                (
                    self.cf_dataset.snippet_length_in_sec
                    * self.cf_dataset.target_sampling_frequency,
                )
            )
            empty_cf_dict[f"snippet_oob_score_series_subsampled"] = np.zeros(
                (
                    self.cf_dataset.snippet_length_in_sec
                    * self.cf_dataset.target_sampling_frequency,
                )
            )
            empty_cf_dict[f"snippet_steering_score_series_subsampled"] = np.zeros(
                (
                    self.cf_dataset.snippet_length_in_sec
                    * self.cf_dataset.target_sampling_frequency,
                )
            )

            empty_cf_dict[f"snippet_valid"] = False
            empty_cf_dict_list.append(empty_cf_dict)

        # do empty cf dict collate
        empty_cf_snippet_data_dict = self.cf_collator(empty_cf_dict_list)
        return empty_cf_snippet_data_dict

    def get_next_lap_cf_dict(self, lap_uid, data_dict=None):
        if data_dict is None:
            data_dict = {}

        lap_pid, lap_trial_num = get_pid_and_trial_num_from_uid(lap_uid)
        idx_in_ordered_list_for_lap_uid = self.lap_dataset.get_idx_in_ordered_list(
            lap_uid
        )
        idx_in_ordered_list_for_next_valid_lap = idx_in_ordered_list_for_lap_uid + 1
        if idx_in_ordered_list_for_next_valid_lap == len(
            self.lap_dataset.pid_to_ordered_laps[lap_pid]
        ):
            no_valid_next_lap = True
        else:
            no_valid_next_lap = False

        if not no_valid_next_lap:
            # maybe add a getter in the lap dataset class
            next_valid_lap_trial_num = self.lap_dataset.pid_to_ordered_laps[lap_pid][
                idx_in_ordered_list_for_next_valid_lap
            ]
            next_valid_lap_uid = get_uid_from_pid_and_trial_num(
                lap_pid, next_valid_lap_trial_num
            )
            all_idxs_for_snippets_from_next_valid_lap_uid = (
                self.cf_dataset.get_snippet_idxs_for_uid(next_valid_lap_uid)
            )
            if len(all_idxs_for_snippets_from_next_valid_lap_uid) == 0:
                # there was a valid next lap, but there were no cf snippets from that valid next lap.
                # This happens when the valid next lap is a retention lap
                print("Valid next lap, but no cf snippets from the valid next lap")
                data_dict.update(self.create_empty_cf_dict(next_valid_lap_uid))
            else:
                # randomly sample num_cf_snippets_per_lap number of samples from all_idxs_for_snippets_from_next_valid_lap_uid
                # TODO(deepak.gopinath) decide if there needs to be any specific sampling strategy.
                # if sampling with replacement is needed use random.choices.
                cf_snippet_idxs = random.sample(
                    all_idxs_for_snippets_from_next_valid_lap_uid,
                    self.num_cf_snippets_per_lap,
                )
                cf_snippet_data_dict_list = []
                for i, cf_snippet_idx in enumerate(cf_snippet_idxs):
                    snippet_i_dict = self.cf_dataset.__getitem__(cf_snippet_idx)
                    snippet_i_dict["snippet_valid"] = True
                    cf_snippet_data_dict_list.append(snippet_i_dict)

                cf_snippet_data_dict = self.cf_collator(cf_snippet_data_dict_list)
                data_dict.update(cf_snippet_data_dict)

        else:
            # no valid next lap to grab cf snippets from
            print("no valid next lap to grab cf snippets from")
            data_dict.update(self.create_empty_cf_dict())

        return data_dict

    def __getitem__(self, idx):
        data_dict = {}
        # if self.dataset_type == 'train':

        #     lap_data_dict = self.lap_dataset[idx]
        #     data_dict.update(lap_data_dict)
        #     lap_uid = lap_data_dict["curr_lap_uid"]
        #     data_dict = self.get_next_lap_cf_dict(lap_uid, data_dict)
        # elif self.dataset_type == 'test':
        cf_data_dict = self.cf_dataset[idx]
        cf_data_dict = self.cf_collator(
            [cf_data_dict]
        )  # mainly to add num_snippets dimnesion to the beginning
        cf_lap_uid = cf_data_dict["snippet_lap_uid"][0]  # str
        cf_lap_pid, cf_lap_trial_num = get_pid_and_trial_num_from_uid(cf_lap_uid)
        idx_in_ordered_list_for_cf_lap_uid = self.lap_dataset.get_idx_in_ordered_list(
            cf_lap_uid
        )
        idx_in_ordered_list_for_prev_valid_lap = idx_in_ordered_list_for_cf_lap_uid - 1
        assert (
            idx_in_ordered_list_for_prev_valid_lap >= 0
        )  # this will always be the case because the first valid lap is the second baseline lap
        prev_valid_lap_trial_num = self.lap_dataset.pid_to_ordered_laps[cf_lap_pid][
            idx_in_ordered_list_for_prev_valid_lap
        ]
        prev_valid_lap_uid = get_uid_from_pid_and_trial_num(
            cf_lap_pid, prev_valid_lap_trial_num
        )
        prev_lap_ds_idx = self.lap_dataset.get_data_idx_from_trial_uid(
            prev_valid_lap_uid
        )
        lap_data_dict = self.lap_dataset[prev_lap_ds_idx]
        data_dict.update(lap_data_dict)
        data_dict.update(cf_data_dict)

        return data_dict

        ### BSD stuff potentially

        # bsd_between_prev_and_curr_lap = self.bsd_language_data_dict[pid][
        #     current_trial_num
        # ]
        # tokenized_bsd = self.tokenizer(
        #     bsd_between_prev_and_curr_lap, add_special_tokens=False
        # ).input_ids

        # input_ids = (
        #     self.task_prompt_input_ids
        #     + self.coach_utterance_prompt_input_ids
        #     + tokenized_bsd
        # )

        # labels = (
        #     self.task_prompt_labels
        #     + self.coach_utterance_prompt_labels
        #     + tokenized_bsd  # the only thing the LLM should predict
        # )
        # The structure of the lmm input sequence would be
        # TASK_PROMPT + SEGMENT_PROMT[0] + Segment 1: seg_1_token Segment 2: seg_2_token....Segment N: seg_N_token
        # LAP_PROMPT lap_level_token SKILL_PROMPT skill_level_token. COACHPROMPT: target
        # construct lmm_input sequence
        # add combine sequence.

    @staticmethod
    def get_collator(config_dict):
        collator = TrackSegmentCollator(
            padding_value=config_dict["padding_value"],
            padding_side=config_dict["padding_side"],
            lap_has_cf_series=config_dict["return_cf_for_lap"],
            with_cf_snippets=config_dict["with_cf_snippets"],
            # num_cf_snippets_per_lap=config_dict["num_cf_snippets_per_lap"],
            num_cf_snippets_per_lap=1,
        )
        return collator


# most likely this class will not be used.
class AIC24D05CombinedFullSequenceDataset(AIC24D05CombinedDataset):
    class_name = "AIC24D05CombinedFullSequenceDataset"

    def __init__(self, config_dict, dataset_type, tokenizer=None):
        super().__init__(config_dict, dataset_type, tokenizer)
        self.lap_dataset = AIC24D05FullSequenceDataset(config_dict, dataset_type)

    def __getitem__(self, idx):
        data_dict = {}
        # idx now refers to a single subject
        lap_seq_data_dict = self.lap_dataset[idx]
        data_dict.update(lap_seq_data_dict)
        list_of_dicts = []
        for lap_uid in lap_seq_data_dict["curr_lap_uid"]:
            list_of_dicts.append(self.get_next_lap_cf_dict(lap_uid))

        flattened_list_of_map_dicts = [
            {k: b[k][i] for k in b}
            for b in list_of_dicts
            for i in range(self.num_cf_snippets_per_lap)
        ]
        collated_snippet_dict = self.cf_collator(flattened_list_of_map_dicts)
        # reshape tensors back into (batch_size, num_snippets,....)
        for k in collated_snippet_dict:
            if type(collated_snippet_dict[k]) is not list:
                collated_snippet_dict[k] = collated_snippet_dict[k].view(
                    len(list_of_dicts),
                    self.num_cf_snippets_per_lap,
                    *collated_snippet_dict[k].shape[1:],
                )
            else:
                collated_snippet_dict[k] = [
                    collated_snippet_dict[k][i : i + self.num_cf_snippets_per_lap]
                    for i in range(
                        0,
                        len(collated_snippet_dict[k]),
                        self.num_cf_snippets_per_lap,
                    )
                ]
        data_dict.update(collated_snippet_dict)
        return data_dict

    @staticmethod
    def get_collator(config_dict):
        collator = TrackSegmentCollatorFullSequence(
            padding_value=config_dict["padding_value"],
            padding_side=config_dict["padding_side"],
            lap_has_cf_series=config_dict["return_cf_for_lap"],
            with_cf_snippets=config_dict["with_cf_snippets"],
            num_cf_snippets_per_lap=config_dict["num_cf_snippets_per_lap"],
        )
        return collator


class AIC24D05CombinedPrevKDataset(AIC24D05CombinedDataset):
    """
    This is a dataset class that generalize K step look back from the current lap.
    The dataset is indexed according to the cf snippet. For any snippet, the previous valid K laps will be returned.
    """

    class_name = "AIC24D05CombinedPrevKDataset"

    def __init__(self, config_dict, dataset_type, tokenizer=None):
        super().__init__(config_dict, dataset_type, tokenizer)
        self.num_prev_laps = config_dict.get("num_prev_laps", 4)
        self.lap_collator = self.lap_dataset.get_collator(config_dict)

    def __getitem__(self, idx):
        data_dict = {}
        cf_data_dict = self.cf_dataset[idx]
        cf_lap_uid = cf_data_dict["snippet_lap_uid"]
        cf_lap_pid, cf_lap_trial_num = get_pid_and_trial_num_from_uid(cf_lap_uid)
        idx_in_ordered_list_for_cf_lap_uid = self.lap_dataset.get_idx_in_ordered_list(
            cf_lap_uid
        )
        # get previous k valid lap idxs
        min_idx = max(0, idx_in_ordered_list_for_cf_lap_uid - self.num_prev_laps)
        prev_k_valid_idxs = range(min_idx, idx_in_ordered_list_for_cf_lap_uid)
        list_of_lap_dicts = []
        for prev_idx in prev_k_valid_idxs:
            prev_valid_lap_trial_num = self.lap_dataset.pid_to_ordered_laps[cf_lap_pid][
                prev_idx
            ]
            prev_valid_lap_uid = get_uid_from_pid_and_trial_num(
                cf_lap_pid, prev_valid_lap_trial_num
            )
            prev_lap_ds_idx = self.lap_dataset.get_data_idx_from_trial_uid(
                prev_valid_lap_uid
            )
            lap_data_dict = self.lap_dataset[prev_lap_ds_idx]
            list_of_lap_dicts.append(lap_data_dict)
        # collate the list of laps and add it to the data dict
        data_dict.update(self.lap_collator(list_of_lap_dicts))
        data_dict.update(cf_data_dict)
        return data_dict

    @staticmethod
    def get_collator(config_dict):
        collator = TrackSegmentCollatorPrevKSequence(
            padding_value=config_dict["padding_value"],
            padding_side=config_dict["padding_side"],
            lap_has_cf_series=config_dict["return_cf_for_lap"],
            with_cf_snippets=config_dict["with_cf_snippets"],
            num_cf_snippets_per_lap=1,  # since always the number of snippet is 1 as the idxing is with respect to the cf dataset
        )
        return collator
