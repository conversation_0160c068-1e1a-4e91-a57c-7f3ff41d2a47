import collections
import multiprocessing
from functools import partial
from pathlib import Path

import numpy as np
import tqdm
from cache_decorator import <PERSON><PERSON>
from sklearn.preprocessing import MinMaxScaler

from aic_bsd_model.datasets.aic_24d05_lap_dataset import CACHE_PATH, AIC24D05LapDataset
from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    GRAVITATION_CONST,
    get_pid_and_trial_num_from_uid,
    get_uid_from_pid_and_trial_num,
    open_pkl,
)


class AIC24D05CFDataset(AIC24D05LapDataset):
    class_name = "AIC24D05CFDataset"

    def __init__(self, config_dict, dataset_type, tokenizer=None):
        super().__init__(config_dict, dataset_type)

        self.tokenizer = tokenizer  # can be used if concurrent feedback language needs to be extracted and tokenized and returned as a part of the dataste
        self.snippet_filename = self.config_dict.get("snippet_filename", "snippet.pkl")
        self.snippet_length_in_sec = self.config_dict.get("snippet_length_in_sec", 10)
        self.annotation_majority_threshold = self.config_dict.get(
            "annotation_majority_threshold", 0.6
        )
        self.teacher_action_future_seq_len_in_sec = config_dict.get(
            "teacher_action_future_seq_len_in_sec", 5
        )
        if self.config_dict.get("cf_target_sampling_frequency", None) is not None:
            # snippets and laps can have different sampling rates.
            self.target_sampling_frequency = self.config_dict.get(
                "cf_target_sampling_frequency"
            )
            self.prediction_timestep = (
                self.prediction_timestep_in_sec * self.target_sampling_frequency
                if self.prediction_timestep_in_sec is not None
                else None
            )
            self.teacher_action_future_seq_len = (
                self.teacher_action_future_seq_len_in_sec
                * self.target_sampling_frequency
            )
        else:
            self.teacher_action_future_seq_len = (
                self.teacher_action_future_seq_len_in_sec
                * self.target_sampling_frequency
            )

        self.teacher_actions_to_skip = [0]
        self.config_dict = config_dict
        self.include_all_null = config_dict.get("include_all_null", False)
        self.use_end_of_past_trajectory = config_dict.get(
            "use_end_of_past_trajectory", False
        )

    def init(self):
        # TODO (deepak.gopinath) improve variable name. This variable is use dwhen normalizing the trajectory snippet
        # trajectory segments will be normalized wrt 'last' tiestep
        # potentially add "local_normalization_scheme" variable. and then separately, local_normalization_timestep.
        # for local_normalization_scheme = 'last, local_normalization_timestep would be -1, for "first" it would be 0, and
        # for prediction_timestep it would be some int between [0, N+M], typically N
        print("In init of CF Dataset")
        self.trajectory_snippet_uids_to_be_considered = None
        if self.pids_to_be_considered is None:
            self.trajectory_snippet_uids_to_be_considered = self.config_dict.get(
                f"{self.dataset_type}_trajectory_snippets_uids_to_be_considered", None
            )
            assert self.trajectory_snippet_uids_to_be_considered is not None

        self.trajectory_snippets_dir = self.config_dict.get(
            "trajectory_snippets_dir", None
        )
        self.trajectory_snippets_pkl_paths = [
            d / self.snippet_filename for d in self.trajectory_snippets_dir.iterdir()
        ]
        self.valid_snippet_list = []
        self.all_valid_snippets_instruction_category_hist = []
        self.all_snippet_metrics_future_dict_list = []
        self.trajectory_snippets_pkl_paths = [
            str(p) for p in self.trajectory_snippets_pkl_paths
        ]
        results = self._prepare_valid_trajectory_snippets(
            self.trajectory_snippets_pkl_paths, self.pids_to_be_considered
        )
        for pkl_path, instruction_category_hist, snippet_metrics_future_dict in results:
            self.valid_snippet_list.append(pkl_path)
            self.all_valid_snippets_instruction_category_hist.append(
                instruction_category_hist
            )
            self.all_snippet_metrics_future_dict_list.append(
                snippet_metrics_future_dict
            )

        (
            self.positive_label_distribution,
            self.negative_label_distribution,
            self.pos_weights_for_classes,
            self.stats_for_dataset,
        ) = (None, None, None, {})

        self._prepare_dataset_stats()

        self.idx_path_tuples = list(enumerate(self.valid_snippet_list))
        # for each pid and each trial num make list of idx of the snippets that came from that trial
        self.uid_to_snippet_dataset_idx_dict = collections.defaultdict(
            partial(collections.defaultdict, list)
        )
        self.snippet_ds_idx_according_to_uid_snippet_num_dict = {}
        self.all_snippet_lens = []
        results = self._prepare_uid_to_snippet_idx_map(self.idx_path_tuples)
        self._process_uid_to_snippet_idx_map_results(results)

    def process_snippet_valid(self, pkl_path):
        snippet_dict = open_pkl(pkl_path)
        snippet_dict_metadata = snippet_dict["metadata"]
        snippet_type = snippet_dict_metadata["snippet_type"]

        # Replace self._is_snippet_valid with a globally accessible function if needed
        if snippet_type in ["baseline", "demo_lap", "sight_lap", "retention"]:
            return None

        pid = snippet_dict_metadata["pid"]
        trial_num = snippet_dict_metadata["trial_num"]

        is_considered = False
        if self.pids_to_be_considered is not None:
            if pid in self.pids_to_be_considered:
                is_considered = True
        else:
            snippet_uid = get_uid_from_pid_and_trial_num(pid, trial_num)
            if snippet_uid in self.trajectory_snippet_uids_to_be_considered:
                is_considered = True

        if is_considered:
            get_instruction_hist = False
            snippet_df = snippet_dict["df"]
            subsampled_idxs_dict = self._get_subsampling_idxs(snippet_df)

            (
                _,
                cf_instruction_class_series_subsampled,
            ) = self.get_cf_instruction_class_series(snippet_df, subsampled_idxs_dict)

            cf_instruction_class_series_subsampled_future = (
                cf_instruction_class_series_subsampled[
                    self.prediction_timestep : self.prediction_timestep
                    + self.teacher_action_future_seq_len
                ]
            )  # (T_future,)
            if not self.include_all_null:
                unique_labels_in_future_at_data_hz = np.unique(
                    cf_instruction_class_series_subsampled_future
                )
                for teacher_action_to_skip in self.teacher_actions_to_skip:
                    unique_labels_in_future_at_data_hz = np.delete(
                        unique_labels_in_future_at_data_hz,
                        unique_labels_in_future_at_data_hz == teacher_action_to_skip,
                    )
                if (
                    len(unique_labels_in_future_at_data_hz) > 0
                ):  # so that there at least 1 label beyond 0
                    get_instruction_hist = True
                else:
                    return None
            else:
                get_instruction_hist = True

            if get_instruction_hist:
                instruction_category_hist = np.bincount(
                    cf_instruction_class_series_subsampled_future,
                    minlength=self.config_dict["teacher_action_num_categories"],
                )
                snippet_metrics_dict = self.compute_metrics_time_series(
                    snippet_df, subsampled_idxs_dict
                )
                snippet_metrics_future_mean_dict = {}
                # get the future time series for each metric in the metric list
                # compute mean over time.
                # FOr each snippet, return a dict containing the time averaged metric according to self.metrics
                for k in snippet_metrics_dict.keys():
                    for mk in self.metric_keys:
                        if mk in k:
                            for subkey in self.metric_keys[mk]:
                                if subkey is not None:
                                    if subkey == "mean":
                                        snippet_metrics_future_mean_dict[
                                            f"{mk}_{subkey}"
                                        ] = np.mean(
                                            snippet_metrics_dict[k][
                                                self.prediction_timestep :
                                            ]
                                        )
                                    if subkey == "abs_mean":
                                        snippet_metrics_future_mean_dict[
                                            f"{mk}_{subkey}"
                                        ] = np.mean(
                                            np.abs(
                                                snippet_metrics_dict[k][
                                                    self.prediction_timestep :
                                                ]
                                            )
                                        )
                                    if subkey == "percentage":
                                        snippet_metrics_future_mean_dict[
                                            f"{mk}_{subkey}"
                                        ] = (
                                            100.0
                                            * (
                                                np.sum(
                                                    snippet_metrics_dict[k][
                                                        self.prediction_timestep :
                                                    ]
                                                )
                                            )
                                            / len(
                                                snippet_metrics_dict[k][
                                                    self.prediction_timestep :
                                                ]
                                            )
                                        )

                return (
                    pkl_path,
                    instruction_category_hist,
                    snippet_metrics_future_mean_dict,
                )

        return None

    @Cache(  # documentation see AIC24D16LapDataset.consistent_hash()
        cache_path=CACHE_PATH,
        enable_cache_arg_name="self.enable_dataset_cache",
    )
    def _prepare_valid_trajectory_snippets(
        self, trajectory_snippets_pkl_paths, pids_to_be_considered
    ):
        results = self.run_parallel_loop(
            fn=partial(self.process_snippet_valid),
            data=trajectory_snippets_pkl_paths,
            tqdm_desc="Processing valid snippets",
        )
        return results

    def _compute_instruction_category_weights(self):
        all_valid_class_hist = np.vstack(
            self.all_valid_snippets_instruction_category_hist
        )
        # nosteering_noturn 1s no null - array([9468, 2679, 1350, 3658, 1879,  813,  736,  394])
        # no_steering_no_turn 5s = array([21194,  7216,  3867, 11177,  5987,  2153,  2629,  1456])
        self.positive_label_distribution = np.sum(all_valid_class_hist > 0, axis=0)
        with np.errstate(divide="ignore"):
            self.negative_label_distribution = (
                len(self.valid_snippet_list) - self.positive_label_distribution
            )
        self.pos_weights_for_classes = np.divide(
            self.negative_label_distribution, self.positive_label_distribution
        )
        self.pos_weights_for_classes[np.isinf(self.pos_weights_for_classes)] = 0.0

        self.stats_for_dataset[
            "positive_label_distribution"
        ] = self.positive_label_distribution
        self.stats_for_dataset[
            "negative_label_distribution"
        ] = self.negative_label_distribution
        self.stats_for_dataset["pos_weights_for_classes"] = self.pos_weights_for_classes

    def _compute_performance_metrics_scaling_parameters(self):
        self.snippet_metrics_scaler_dict = {}
        for mk in self.metric_keys:
            for subkey in self.metric_keys[mk]:
                mk_subkey_list = []
                if subkey is not None:
                    dict_key = f"{mk}_{subkey}"
                else:
                    continue
                for snippet_dict in self.all_snippet_metrics_future_dict_list:
                    mk_subkey_list.append(snippet_dict[dict_key])  # list of scalars
                mk_subkey_scaler = MinMaxScaler()
                mk_subkey_scaler.fit(np.array(mk_subkey_list).reshape(-1, 1))
                self.snippet_metrics_scaler_dict[dict_key] = mk_subkey_scaler

        self.stats_for_dataset["min_max_scaler_dict"] = self.snippet_metrics_scaler_dict

    def _prepare_dataset_stats(self):
        print("Computing instruction category weights")
        self._compute_instruction_category_weights()
        print("Computing scaling parameters for snippet metrics")
        self._compute_performance_metrics_scaling_parameters()

    def get_dataset_stats(self):
        return self.stats_for_dataset

    def get_snippet_pid_trial_num_data_idx(self, idx_path_tuple):
        ds_idx, path = idx_path_tuple
        trajectory_snippet_dict = open_pkl(path)
        metadata = trajectory_snippet_dict["metadata"]
        full_snippet_length = trajectory_snippet_dict["df"].shape[0]
        pid, trial_num, snippet_num = (
            metadata["pid"],
            metadata["trial_num"],
            metadata["snippet_num"],
        )
        return (
            pid,
            trial_num,
            snippet_num,
            ds_idx,  # the idx corresponding to this snippet in this Dataset class
            full_snippet_length,
        )

    @Cache(  # documentation see AIC24D16LapDataset.consistent_hash()
        cache_path=CACHE_PATH,
        enable_cache_arg_name="self.enable_dataset_cache",
    )
    def _prepare_uid_to_snippet_idx_map(self, idx_path_tuples):
        results = self.run_parallel_loop(
            fn=partial(self.get_snippet_pid_trial_num_data_idx),
            data=idx_path_tuples,
            tqdm_desc="Processing uid to snippet idx map",
        )
        return results

    def _process_uid_to_snippet_idx_map_results(self, results):
        for pid, trial_num, snippet_num, ds_idx, full_snippet_length in results:
            self.uid_to_snippet_dataset_idx_dict[pid][trial_num].append(ds_idx)
            if pid not in self.snippet_ds_idx_according_to_uid_snippet_num_dict:
                self.snippet_ds_idx_according_to_uid_snippet_num_dict[pid] = {}
            if (
                trial_num
                not in self.snippet_ds_idx_according_to_uid_snippet_num_dict[pid]
            ):
                self.snippet_ds_idx_according_to_uid_snippet_num_dict[pid][
                    trial_num
                ] = {}
            self.snippet_ds_idx_according_to_uid_snippet_num_dict[pid][trial_num][
                snippet_num
            ] = ds_idx
            self.all_snippet_lens.append(full_snippet_length)

    def get_snippet_dict_for_pid(self, pid):
        # return a dictionary containing the trials and snippet nums as keys and the dataset_idx as values.
        if pid in self.snippet_ds_idx_according_to_uid_snippet_num_dict:
            return self.snippet_ds_idx_according_to_uid_snippet_num_dict[pid]
        else:
            return None

    def get_snippet_idxs_for_uid(self, uid):
        # returns the data idxs of all snippets that came from uid
        pid, trial_num = get_pid_and_trial_num_from_uid(uid)
        if pid in self.uid_to_snippet_dataset_idx_dict:
            if trial_num in self.uid_to_snippet_dataset_idx_dict[pid]:
                return self.uid_to_snippet_dataset_idx_dict[pid][trial_num]
            else:
                # snippets from the requested trial for this pid was absent
                return []
        else:
            # this pid is not included in the snippet dict. That is
            return []

    def compute_metrics_time_series(self, df, subsampled_idxs_dict):
        # TODO (deepak.gopinath) (can be made more slick by using functions for each metric)
        metrics_dict = {}

        lateral_distances_subsampled_diff = df["lateral_distances"].values[
            subsampled_idxs_dict["subsampled_idxs_int"]
        ]
        metrics_dict["snippet_racing_line_score_series_subsampled"] = np.array(
            list(lateral_distances_subsampled_diff)
        )  # (L, )

        # controls related
        steering_subsampled_diff = np.diff(
            df["steering"].values[subsampled_idxs_dict["subsampled_idxs_int"]]
        )
        metrics_dict["snippet_steering_score_series_subsampled"] = np.array(
            [0] + list(steering_subsampled_diff)
        )  # (L, )
        throttle_subsampled_diff = np.diff(
            df["throttle"].values[subsampled_idxs_dict["subsampled_idxs_int"]]
        )
        metrics_dict["snippet_throttle_score_series_subsampled"] = np.array(
            [0] + list(throttle_subsampled_diff)
        )  # (L, )
        brake_subsampled_diff = np.diff(
            df["brake"].values[subsampled_idxs_dict["subsampled_idxs_int"]]
        )
        metrics_dict["snippet_braking_score_series_subsampled"] = np.array(
            [0] + list(brake_subsampled_diff)
        )  # (L, )

        # smoothness score
        kernel_size = 10
        kernel = np.ones(kernel_size) / kernel_size
        acc_x_subsampled = np.diff(
            df["ego_vx"].values[subsampled_idxs_dict["subsampled_idxs_int"]]
        ) / np.diff(df.timestamp.values[subsampled_idxs_dict["subsampled_idxs_int"]])
        # smoothing is added to deal with the chatter in the signal. To be removed if fixed at the sources
        smoothed_acc_x_subsampled = np.convolve(acc_x_subsampled, kernel, mode="same")
        smoothed_acc_x_subsampled = np.array([0] + list(smoothed_acc_x_subsampled))
        acc_y_subsampled = np.diff(
            df["ego_vy"].values[subsampled_idxs_dict["subsampled_idxs_int"]]
        ) / np.diff(df.timestamp.values[subsampled_idxs_dict["subsampled_idxs_int"]])
        smoothed_acc_y_subsampled = np.convolve(acc_y_subsampled, kernel, mode="same")
        smoothed_acc_y_subsampled = np.array([0] + list(smoothed_acc_y_subsampled))
        acc_z_subsampled = np.diff(
            df["ego_vz"].values[subsampled_idxs_dict["subsampled_idxs_int"]]
        ) / np.diff(df.timestamp.values[subsampled_idxs_dict["subsampled_idxs_int"]])
        smoothed_acc_z_subsampled = np.convolve(acc_z_subsampled, kernel, mode="same")
        smoothed_acc_z_subsampled = np.array([0] + list(smoothed_acc_z_subsampled))
        smoothed_acc_vec_subsampled = np.vstack(
            (
                smoothed_acc_x_subsampled,
                smoothed_acc_y_subsampled,
                smoothed_acc_z_subsampled,
            )
        ).T  # (L, 3)
        smoothed_jerk_vec_subsampled = np.diff(
            smoothed_acc_vec_subsampled, axis=0
        ) / np.expand_dims(
            np.diff(
                df["carla_objects log time"].values[
                    subsampled_idxs_dict["subsampled_idxs_int"]
                ]
            ),
            1,
        )  # (L-1, 3)

        # (L-1, )
        smoothness_score_vec_subsampled = np.linalg.norm(
            smoothed_jerk_vec_subsampled, axis=1
        )
        # (L, )
        smoothness_score_vec_subsampled = np.array(
            [0] + list(smoothness_score_vec_subsampled)
        )
        metrics_dict[
            "snippet_smoothness_score_series_subsampled"
        ] = smoothness_score_vec_subsampled

        # friction utilization score
        g_force_vec_subsampled = smoothed_acc_vec_subsampled / abs(GRAVITATION_CONST)
        friction_utilization_vec_subsampled = np.linalg.norm(
            g_force_vec_subsampled, axis=1
        )
        metrics_dict[
            "snippet_friction_utilization_score_series_subsampled"
        ] = friction_utilization_vec_subsampled

        # out of bounds raw score
        metrics_dict["snippet_oob_score_series_subsampled"] = df[
            "out_of_bounds"
        ].values[subsampled_idxs_dict["subsampled_idxs_int"]]

        return metrics_dict

    def __len__(self):
        return len(self.valid_snippet_list)

    @Cache(  # documentation see AIC24D16LapDataset.consistent_hash()
        cache_path=CACHE_PATH,
        enable_cache_arg_name="self.enable_dataset_cache",
    )
    def _load_snippet(self, snippet_path):
        traj_snippet_dict = open_pkl(snippet_path)
        traj_snippet_metadata = traj_snippet_dict["metadata"]

        traj_snippet_pid = traj_snippet_metadata["pid"]
        traj_snippet_trial_num = traj_snippet_metadata["trial_num"]
        traj_snippet_uid = get_uid_from_pid_and_trial_num(
            traj_snippet_pid, traj_snippet_trial_num
        )
        data_dict = {}
        # parse snippet trajectory
        trajectory_t_dict, T_matrix, subsampled_idxs_dict = self.get_trajectory(
            traj_snippet_dict,
            key_prefix=f"snippet_",
            is_normalized=True,
            is_scaled=True,
            is_return_length=True,
            normalization_timestep="prediction_timestep",
        )
        data_dict.update(trajectory_t_dict)

        # parse snippet map
        trajectory_m_dict = self.get_map(
            traj_snippet_dict,
            T_matrix,
            key_prefix=f"snippet_",
            map_type="local",
            is_scaled=True,
            use_end_of_past_trajectory=self.use_end_of_past_trajectory,
        )
        data_dict.update(trajectory_m_dict)

        # add uid of the lap the snippet came from
        data_dict[f"snippet_lap_uid"] = traj_snippet_uid
        # Each snippet is named {UID}_{snippet_num}
        data_dict[f"snippet_num"] = traj_snippet_metadata["snippet_num"]

        # add concurrent feedback instruction class series. both full and subsampled versions
        # (L,), (subsampled_L,) for cf_instruction_class_series, cf_instruction_class_series_subsampled respectively
        (
            cf_instruction_class_series,
            cf_instruction_class_series_subsampled,
        ) = self.get_cf_instruction_class_series(
            traj_snippet_dict["df"], subsampled_idxs_dict
        )
        data_dict[f"snippet_cf_instruction_class_series"] = cf_instruction_class_series

        data_dict[
            f"snippet_cf_instruction_class_series_subsampled"
        ] = cf_instruction_class_series_subsampled

        # make sure that the subsampled cf series and trajectory have the same length
        assert (
            len(cf_instruction_class_series_subsampled)
            == data_dict[f"snippet_trajectory_inputs_subsampled"].shape[0]
        )

        subsampled_metrics_time_series_dict = self.compute_metrics_time_series(
            traj_snippet_dict["df"], subsampled_idxs_dict
        )
        data_dict.update(subsampled_metrics_time_series_dict)

        # TODO (deepak.gopinath) add parsing of valid category and subcategory as separate series if needed for heirarchical classification
        # TODO (deepak.gopinath) add parsing of full cf text for any future purpose
        return data_dict

    def __getitem__(self, idx):
        """
        This get item return a single snippet for concurrent feedback training.
        The output consists of the following keys:
            'snippet_trajectory_inputs_subsampled' - (subsampled_L, F)
            'snippet_trajectory_inputs_subsampled_length' - int
            'snippet_normalized_map_subsampled' - (map_L, 6)
            'snippet_cones' - (num_cones, 2)
            'snippet_lap_uid' - str
            'snippet_num' - int
            'snippet_cf_instruction_class_series' - (full_L, )
            'snippet_cf_instruction_class_series_subsampled' - (subsampled_L, )
        The additional_prefix arg is used if say P samples are requested and
        each of the samples need to be separated by name
        """

        traj_snippet_path = self.valid_snippet_list[idx]
        return self._load_snippet(traj_snippet_path)

    def cache_variables_for_hash(self) -> dict:
        """This function is used to generate a unique hash for the dataset.
        Put all the variables that determine the __getitem__ return value here.
        """
        ret = super().cache_variables_for_hash()
        if self.include_all_null:
            ret.update(
                {
                    "prediction_timestep": self.prediction_timestep,
                    "teacher_action_future_seq_len": self.teacher_action_future_seq_len,
                    "annotation_majority_threshold": self.annotation_majority_threshold,
                    "teacher_action_num_categories": self.config_dict[
                        "teacher_action_num_categories"
                    ],
                    "teacher_actions_to_skip": self.teacher_actions_to_skip,
                    "include_all_null": self.include_all_null,
                }
            )
        else:
            ret.update(
                {
                    "prediction_timestep": self.prediction_timestep,
                    "teacher_action_future_seq_len": self.teacher_action_future_seq_len,
                    "annotation_majority_threshold": self.annotation_majority_threshold,
                    "teacher_action_num_categories": self.config_dict[
                        "teacher_action_num_categories"
                    ],
                    "teacher_actions_to_skip": self.teacher_actions_to_skip,
                }
            )
        return ret

    @staticmethod
    def get_collator(config_dict):
        from aic_bsd_model.datasets.data_collators import CFSnippetCollator

        return CFSnippetCollator(
            padding_value=config_dict["padding_value"],
            padding_side=config_dict["padding_side"],
        )
