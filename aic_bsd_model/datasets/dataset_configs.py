"""
datasets.py

Draccus Dataclass Definition for a DatasetConfig object, with various registered subclasses for each dataset variant
and processing scheme. A given dataset variant (e.g., `llava-lightning`) configures the following attributes:
    - Dataset Variant (Identifier) --> e.g., "llava-v15"
    - Align Stage Dataset Components (annotations, images)
    - Finetune Stage Dataset Components (annotations, images)
    - Dataset Root Directory (Path)
"""
# TODO(guy.rosman): update the documentation so as to separate prismatic text / assumptions such as (annotations,images)
# TODO(guy.rosman): move to hail-datasets

from dataclasses import dataclass, field
from enum import Enum, unique
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from draccus import ChoiceRegistry


@dataclass
class DatasetConfig(ChoiceRegistry):
    # fmt: off
    dataset_id: str                                 # Unique ID that fully specifies a dataset variant

    # Dataset Components for each Stage in < align | finetune >
    # TODO (andrewsilva): Update the components of each dataset to include shard locations for each sub-dataset?
    # TODO: Each constituent dataset should be created according to the eventual needs of the model,
    #  but that creation process should precede this script
    align_stage_components: Tuple[Path, Path]       # Path to annotation file and images directory for `align` stage
    finetune_stage_components: Tuple[Path, Path]    # Path to annotation file and images directory for `finetune` stage

    # dataset_root_dir: Path                          # Path to dataset root directory; others paths are relative to root


@dataclass
class ThunderHill_AIC_BSD_Config(DatasetConfig):
    dataset_id: str = "thunderhill-aic-bsd"
    align_stage_components: Path = Path(
        "~/Data/coaching/synced_data_frames_new_transcript_labels/"
    )
    finetune_stage_components: Path = Path(
        "~/Data/coaching/synced_data_frames_new_transcript_labels/"
    )
    dataset_root_dir: Path = Path(
        "~/Data/coaching/synced_data_frames_new_transcript_labels/"
    )
    dataset_segments_dir: Path = Path(
        "~/Data/coaching/synced_data_frames_new_transcript_labels/"
    )
    trials_directory: Path = Path("~/Data/coaching/trials_dir/")

    trajectory_normalization_scheme: str = "local"
    global_trajectory_normalization_info: Dict = None
    target_sampling_frequency: int = 5
    local_map_distance_threshold: int = 100


@dataclass
class ThunderHill_AIC_24D16_Config(DatasetConfig):
    dataset_id: str = "thunderhill-aic-24d16"
    align_stage_components: Path = Path(
        "~/Data/coaching/synced_data_frames_new_transcript_labels/"
    )
    finetune_stage_components: Path = Path(
        "~/Data/coaching/synced_data_frames_new_transcript_labels/"
    )
    dataset_segments_dir: Path = Path(
        "~/Data/coaching/synced_data_frames_new_transcript_labels/"
    )
    trials_directory: Path = Path("~/Data/coaching/trials_dir/")

    trajectory_normalization_scheme: str = "local"
    global_trajectory_normalization_info: Dict = None
    target_sampling_frequency: int = 5
    local_map_distance_threshold: int = 100


# === Define a Dataset Registry Enum for Reference & Validation =>> all *new* datasets must be added here! ===
@unique
class DatasetRegistry(Enum):
    # === ThunderHill AIC Mixes ===
    THUNDERHILL_AIC_BSD = ThunderHill_AIC_BSD_Config
    THUNDERHILL_AIC_24D16 = ThunderHill_AIC_24D16_Config

    @property
    def dataset_id(self) -> str:
        return self.value.dataset_id


# Register Datasets in Choice Registry
for dataset_variant in DatasetRegistry:
    DatasetConfig.register_subclass(dataset_variant.dataset_id, dataset_variant.value)
