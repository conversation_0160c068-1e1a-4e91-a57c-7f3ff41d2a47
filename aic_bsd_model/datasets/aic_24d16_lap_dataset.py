# load in a subjects trials in chronological order.
# dataset to be used for learning skill encoding and transition model over it.

import collections
import multiprocessing
import os
import time
from pathlib import Path

import numpy as np
import pandas as pd
from cache_decorator import Cache
from dict_hash import Hashable, sha256
from filelock import FileLock, Timeout
from shapely import Point, geometry
from torch.utils.data import Dataset
from tqdm import tqdm

from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    FEATURE_TO_DF_KEY_NO_ACCEL,
    METRICS_LINEAR_SCALE,
    TRAJ_FEATURE_LINEAR_SCALE,
    TrajFeatureIndexNoAccel,
    add_map,
    calculate_sampling_freq,
    get_pid_and_trial_num_from_uid,
    get_uid_from_pid_and_trial_num,
    open_pkl,
)
from aic_bsd_model.datasets.data_collators import TrackSegmentCollator
from aic_bsd_model.utils.math_utils import CoordinateNormalizationUtil

CACHE_PATH = "{self.cache_dir}/{self.class_name}/{function_name}/{self.dataset_type}_{_hash}.pkl.gz"


class AIC24D16LapDataset(Dataset, Hashable):
    class_name = "AIC24D16LapDataset"

    def __init__(self, config_dict, dataset_type):
        self.config_dict = config_dict
        self.dataset_type = dataset_type
        self.disable_tqdm = config_dict.get("disable_tqdm", False)
        self.cache_dir = config_dict.get("cache_dir", Path.home() / "cache")
        # directory containing segments. Each trial_dir contains 10 pkl files each pkl file corresponding to the trajectory snippet for the map segment
        self.trajectory_segments_dir = config_dict.get("trajectory_segments_dir", None)
        self.enable_dataset_cache = config_dict.get("enable_dataset_cache", False)
        # Parse MAP
        self.map_file = config_dict.get("map_file", None)  # path to THill map files
        self.use_multiprocessing = config_dict.get("use_multiprocessing", True)
        # open map
        if self.map_file is not None:
            with open(self.map_file, "r") as f:
                self.map_df = pd.read_csv(f)
            self.map = add_map(self.map_df)

        # list of subject ids that will be part of this instance of dataset
        self.pids_to_be_considered = config_dict.get(
            f"{dataset_type}_pids_to_be_considered", None
        )

        # Thesampling rate at which the data will be downsampled to
        self.original_sampling_frequency = config_dict.get(
            "original_sampling_frequency", 120
        )
        self.target_sampling_frequency = config_dict.get("target_sampling_frequency", 5)
        self.trajectory_normalization_scheme = config_dict.get(
            "trajectory_normalization_scheme", "local"
        )
        self.normalization_timestep = config_dict.get("normalization_timestep", "first")
        self.global_trajectory_normalization_info = config_dict.get(
            "global_trajectory_normalization_info", None
        )
        assert (
            self.global_trajectory_normalization_info is not None
            if self.trajectory_normalization_scheme == "global"
            else True
        )

        # TODO deicde if z needs to be added
        self.raceline_xy = np.array([r[:2] for r in self.map["race_line"]])
        # add left and right linestrings if necessary
        self.raceline_linestring = geometry.LineString(self.raceline_xy)
        self.distance_along_rl = []
        for i in range(self.raceline_xy.shape[0]):
            self.distance_along_rl.append(
                self.raceline_linestring.line_locate_point(Point(self.raceline_xy[i]))
            )

        self.local_map_distance_threshold = config_dict.get(
            "local_map_distance_threshold", 100
        )
        self.trajectory_segments_pkl_paths_according_to_uid = collections.defaultdict()
        self.valid_lap_list = []
        self.trajectory_metrics_pkl_paths_according_to_uid = collections.defaultdict()
        if self.trajectory_segments_dir is not None:
            self._prepare_trajectory_segment_data_for_each_lap()

            self._prepare_trajectory_metrics_for_each_lap()

            self._prepare_valid_laps_list()

            self.pid_to_ordered_laps = collections.OrderedDict()
            self._prepare_ordered_laps_for_pids()

            if (
                "debug_with_one_trial" in config_dict
                and config_dict["debug_with_one_trial"]
            ):
                self.valid_lap_list = [self.valid_lap_list[0]]

        self.index_resampling_noise = config_dict.get("index_sampling_noise", 0.0)
        self.constant_shift_noise = config_dict.get("constant_shift_noise", 0.0)
        self.constant_scale_noise = config_dict.get("constant_scale_noise", 0.0)
        self.prediction_timestep_in_sec = config_dict.get(
            "prediction_timestep_in_sec", 5
        )
        self.prediction_timestep = (
            self.prediction_timestep_in_sec * self.target_sampling_frequency
            if self.prediction_timestep_in_sec is not None
            else None
        )

        # TODO (deepak.gopinath) Decide on final list of metrics we want to parse in so that
        # the return data dict is as lean as possible
        self.metric_keys = {
            "trial_time": [None],
            "oob": ["percentage"],
            "steering_score": ["abs_mean"],
            "racing_line_score": ["abs_mean"],
            "braking_score": ["abs_mean"],
            "throttle_score": ["abs_mean"],
            "smoothness_score": ["mean"],
        }

        self.return_next_lap_metrics = config_dict.get("return_next_lap_metrics", False)
        self.feature_linear_scale = []
        for e in TrajFeatureIndexNoAccel:
            self.feature_linear_scale.append(TRAJ_FEATURE_LINEAR_SCALE[e.name])
        self.feature_linear_scale = np.array(self.feature_linear_scale).reshape(
            1, -1
        )  # (1, 13)
        # self.self_compute_scale_normalization_params = scale_normalization_params if scale_normalization_params is not None else self._compute_scale_normalization_params()

    def _prepare_trajectory_segment_data_for_each_lap(self):
        for d in self.trajectory_segments_dir.iterdir():
            uid = d.stem
            if "track" in d.stem:
                continue

            segment_pkls = list(d.iterdir())
            if uid not in self.trajectory_segments_pkl_paths_according_to_uid:
                self.trajectory_segments_pkl_paths_according_to_uid[
                    uid
                ] = collections.OrderedDict()
                for seg_id in range(1, 11):
                    self.trajectory_segments_pkl_paths_according_to_uid[uid][
                        f"segment_{seg_id}"
                    ] = None
            for sp in segment_pkls:
                if "metrics" in sp.stem:
                    continue
                self.trajectory_segments_pkl_paths_according_to_uid[uid][sp.stem] = sp

    def _prepare_trajectory_metrics_for_each_lap(self):
        for d in self.trajectory_segments_dir.iterdir():
            uid = d.stem
            if "track" in d.stem:
                continue
            segment_pkls = list(d.iterdir())
            if uid not in self.trajectory_metrics_pkl_paths_according_to_uid:
                self.trajectory_metrics_pkl_paths_according_to_uid[
                    uid
                ] = collections.OrderedDict()
            for sp in segment_pkls:
                if "metrics" in sp.stem:
                    self.trajectory_metrics_pkl_paths_according_to_uid[uid][
                        sp.stem
                    ] = sp

    def _prepare_valid_laps_list(self):
        # Open every folder in self.trajectory_segments_dir. Each folder is a trial.
        for d in self.trajectory_segments_dir.iterdir():
            uid = d.stem
            # TODO (deepak.gopinath) Move track.csv location to elsewhere so that this if statement can be remvoed.
            if "track" in d.stem:
                continue
            pid, trial_num = get_pid_and_trial_num_from_uid(uid)
            if pid not in self.pids_to_be_considered:
                continue

            segment_1_pkl_path = [
                s for s in list(d.iterdir()) if "segment_1.pkl" in str(s)
            ][0]
            segment_1_snippet = open_pkl(segment_1_pkl_path)
            segment_1_metadata = segment_1_snippet["metadata"]
            if (
                segment_1_metadata["is_valid"]
                and segment_1_metadata["valid_next_lap_trial_num"] != -1
            ):
                self.valid_lap_list.append(uid)

    def _prepare_ordered_laps_for_pids(self):
        for valid_lap_uid in self.valid_lap_list:
            pid, trial_num = get_pid_and_trial_num_from_uid(valid_lap_uid)
            if pid not in self.pid_to_ordered_laps:
                self.pid_to_ordered_laps[pid] = []
            self.pid_to_ordered_laps[pid].append(trial_num)
        for pid in self.pid_to_ordered_laps:
            self.pid_to_ordered_laps[pid] = sorted(self.pid_to_ordered_laps[pid])

    def __len__(self):
        return len(self.valid_lap_list)

    def get_df_and_metadata(self, snippet_dict):
        return snippet_dict["df"], snippet_dict["metadata"]

    def extract_full_trajectory_from_df(self, snippet_df):
        start_index, end_index = snippet_df.index[0], snippet_df.index[-1]
        all_indices = list(range(start_index, end_index + 1))

        # consider "ignore_indices" as an option to zero out unnecessary features.
        # the feature len then would be len(TrajFeatureIndexNoAccel) - num_ignore_features
        # Would necessitate a remapping of the indices.
        # (T, F)
        trajectory = np.zeros((len(all_indices), len(TrajFeatureIndexNoAccel)))
        # TODO (deepak.gopinath) if in a dataset I know what the valid_feature_idxs are just only use those.
        valid_feature_idxs = {
            k: v for k, v in FEATURE_TO_DF_KEY_NO_ACCEL.items() if v is not None
        }
        not_available_feat_keys = [
            d for d in FEATURE_TO_DF_KEY_NO_ACCEL.values() if d not in snippet_df.keys()
        ]
        assert (
            len(not_available_feat_keys) == 0
        ), f"Not all required features are present, missing: {not_available_feat_keys}"

        trajectory[:, list(valid_feature_idxs.keys())] = snippet_df[
            list(valid_feature_idxs.values())
        ].values

        return trajectory

    def _get_subsampling_idxs(self, snippet_df):
        """
        Helper function to compute the subsampling idxs for the trajectory. If resampling is to be performed, then will return floating point based subsampling indices along with the
        floor and ceiling values.
        """
        _, _, snippet_orig_sampling_frequency = calculate_sampling_freq(snippet_df)
        num_skip_samples = max(
            1,
            int(
                round(snippet_orig_sampling_frequency) / self.target_sampling_frequency
            ),
        )
        # int subsampling idxs
        subsampled_idxs = list(range(0, len(snippet_df), num_skip_samples))

        if (
            self.index_resampling_noise > 0
            or self.constant_scale_noise > 0
            or self.constant_shift_noise > 0
        ):
            constant_shift = self.constant_shift_noise * np.random.randn()
            constant_dscale = self.constant_scale_noise * np.random.randn()
            subsampled_idxs = [
                i * (1 + constant_dscale)
                + constant_shift
                + np.random.randn() * self.index_resampling_noise
                for i in subsampled_idxs
            ]
            # resampled floating point subsampled idxs
            subsampled_idxs_f = [
                i for i in subsampled_idxs if i >= 0 and i < len(snippet_df)
            ]
            # The floored version of the subsampled indices
            subsampled_idxs_m = np.floor(subsampled_idxs_f).astype(int)
            # (T_sub, )

            # The ceil version of the subsampled indices
            subsampled_idxs_p = np.minimum(
                np.ceil(subsampled_idxs_f).astype(int), len(snippet_df) - 1
            )
            subsampled_idxs_dict = {}
            subsampled_idxs_dict["subsampled_idxs_floating_point"] = subsampled_idxs_f
            subsampled_idxs_dict["subsampled_idxs_floor"] = subsampled_idxs_m
            subsampled_idxs_dict["subsampled_idxs_ceil"] = subsampled_idxs_p
        else:
            subsampled_idxs_dict = {}
            subsampled_idxs_dict["subsampled_idxs_int"] = subsampled_idxs

        return subsampled_idxs_dict

    def extract_trajectory_data(self, snippet_dict):
        snippet_df, snippet_metadata = self.get_df_and_metadata(snippet_dict)
        subsampled_idxs_dict = self._get_subsampling_idxs(snippet_df)
        full_trajectory = self.extract_full_trajectory_from_df(snippet_df)
        if "subsampled_idxs_floor" in subsampled_idxs_dict:
            subsampled_trajectory = self.linear_interpolate_trajectory(
                full_trajectory, subsampled_idxs_dict
            )
        else:
            subsampled_idxs = subsampled_idxs_dict["subsampled_idxs_int"]
            subsampled_trajectory = full_trajectory[subsampled_idxs, :]

        return (full_trajectory, subsampled_trajectory, subsampled_idxs_dict)

    def linear_interpolate_trajectory(self, full_trajectory, subsampled_idxs_dict):
        """
        Perform linear interpolation on a trajectory given subsampled indices.
        Parameters:
        full_trajectory (np.ndarray): The full trajectory array of shape (T, F), where T is the number of time steps and F is the number of features.
        subsampled_idxs_dict (dict) with 3 keys
            subsampled_idxs_floating_point (np.ndarray) - The subsampled floating point indices array of shape (T_sub,), where T_sub is the number of subsampled time steps.
            subsampled_idxs_floor (np.ndarray) - The subsampled indices (floor values) array of shape (T_sub,), where T_sub is the number of subsampled time steps.
            subsampled_idxs_ceil (np.ndarray) - The subsampled indices (ceiling) array of shape (T_sub,), where T_sub is the number of subsampled time steps.

        Returns:
        np.ndarray: The subsampled trajectory array of shape (T_sub, F) obtained by linear interpolation.
        """

        #
        subsampled_idxs_f = subsampled_idxs_dict["subsampled_idxs_floating_point"]
        # (T_sub, ) The floored version of the subsampled indices
        subsampled_idxs_m = subsampled_idxs_dict["subsampled_idxs_floor"]
        # (T_sub, ) -  The ceil version of the subsampled indices
        subsampled_idxs_p = subsampled_idxs_dict["subsampled_idxs_ceil"]

        # A linear interpolation between the two indices' sampled feature vectors
        # (T_sub, 1)
        interp_alpha = np.expand_dims(subsampled_idxs_f - subsampled_idxs_m, 1)
        # (T_sub, F)
        subsampled_trajectory = full_trajectory[
            subsampled_idxs_m, :
        ] * interp_alpha + full_trajectory[subsampled_idxs_p, :] * (1 - interp_alpha)
        # the linearly interpolated subsampled trajectory
        return subsampled_trajectory

    def normalize_trajectory(self, trajectory, normalization_timestep="last"):
        if self.trajectory_normalization_scheme == "local":
            if normalization_timestep == "last":
                # get pose of the last timestep of the ENTIRE trajectory.
                # normalize the whole trajectory with respect to this pose
                (
                    normalized_trajectory,
                    normalization_transformation_matrix,
                ) = CoordinateNormalizationUtil.normalize_3d(
                    trajectory, normalization_frame_timestep=-1
                )
            elif normalization_timestep == "first":
                # get pose of the first timestep of the ENTIRE trajectory.
                # normalize the whole trajectory with respect to this pose
                (
                    normalized_trajectory,
                    normalization_transformation_matrix,
                ) = CoordinateNormalizationUtil.normalize_3d(
                    trajectory, normalization_frame_timestep=0
                )
            elif normalization_timestep == "prediction_timestep":
                # retrieve the pose at the time step corresponding to the
                # define self.prediction_timestep = original_sampling_rate  * past_traj_len
                # prediction_timestep = round(original_sampling_frequency * past_traj_len)
                assert self.prediction_timestep is not None
                (
                    normalized_trajectory,
                    normalization_transformation_matrix,
                ) = CoordinateNormalizationUtil.normalize_3d(
                    trajectory, normalization_frame_timestep=self.prediction_timestep
                )
        else:
            return trajectory, np.eye(4)
        return normalized_trajectory, normalization_transformation_matrix

    def _stack_lanes(self, normalized_thresholded_local_map_subsampled_dict):
        normalized_thresholded_local_map_subsampled = np.hstack(
            (
                normalized_thresholded_local_map_subsampled_dict["left_lane"],
                normalized_thresholded_local_map_subsampled_dict["right_lane"],
                normalized_thresholded_local_map_subsampled_dict["race_line"][
                    :, 0:2
                ],  # only x and y
            )
        )
        # TODO (deepak.gopinath) consider XC like process_map_vectors if GNN type encoders are being used.
        return normalized_thresholded_local_map_subsampled  # (T, N_m)

    def extract_local_map(
        self, snippet_df, T_bg_subsampled, use_end_of_past_trajectory=False
    ):
        num_ts_snippet = snippet_df.shape[0]
        # compute the mid point of the trajectory along the trajectory. Linestring the trajectory. Find the mid point along the trajectroy
        # decide if you want to deal with z dimension

        if not use_end_of_past_trajectory:
            traj_linestring = geometry.LineString(
                snippet_df[
                    [
                        FEATURE_TO_DF_KEY_NO_ACCEL[TrajFeatureIndexNoAccel.TRAJ_X],
                        FEATURE_TO_DF_KEY_NO_ACCEL[TrajFeatureIndexNoAccel.TRAJ_Y],
                    ]
                ].values
            )

            traj_midpoint = traj_linestring.interpolate(traj_linestring.length / 2.0)
        else:
            end_of_past_x_coord = snippet_df[
                FEATURE_TO_DF_KEY_NO_ACCEL[TrajFeatureIndexNoAccel.TRAJ_X]
            ].values[
                int(self.prediction_timestep_in_sec * self.original_sampling_frequency)
                - 1
            ]
            end_of_past_y_coord = snippet_df[
                FEATURE_TO_DF_KEY_NO_ACCEL[TrajFeatureIndexNoAccel.TRAJ_Y]
            ].values[
                int(self.prediction_timestep_in_sec * self.original_sampling_frequency)
                - 1
            ]
            traj_midpoint = Point(end_of_past_x_coord, end_of_past_y_coord)
        # find the map point closest to the mid point of the trajectory. If the person has spunout or whatever it would
        # still be somewhere close to the trajectory. let this be map_idx_closest

        closest_map_idx_to_traj_midpoint = np.argmin(
            (
                np.linalg.norm(
                    self.raceline_xy - np.array(list(traj_midpoint.coords)), axis=1
                )
            )
        )
        # along the raceline how far is this closest point from the start of the raceline
        distance_along_rl_to_closest_map_idx = geometry.LineString(
            self.raceline_xy
        ).line_locate_point(Point(self.raceline_xy[closest_map_idx_to_traj_midpoint]))
        # subtract the longi distance of map_idx_closest from all map linestring points. [-,-,-,0,+++]
        abs_dist_of_map_pts_from_closest_map_pt = np.abs(
            np.array(self.distance_along_rl) - distance_along_rl_to_closest_map_idx
        )
        # grab idx of the map line string which are within distance threshold (absolute value) from map point at map_idx_closest.
        map_idx_within_threshold = np.nonzero(
            abs_dist_of_map_pts_from_closest_map_pt < self.local_map_distance_threshold
        )[0]
        # There would be N points that are within the distance. This N is going to be different for trajectory snippets.
        thresholded_local_map = {}
        for map_key in ["left_lane", "right_lane", "race_line"]:
            thresholded_local_map[map_key] = np.array(self.map[map_key])[
                map_idx_within_threshold
            ]
        cone_mask = (
            np.linalg.norm(
                self.map["cones"][:, :2] - np.array(list(traj_midpoint.coords)), axis=1
            )
            < self.local_map_distance_threshold
        )
        cones_xy = self.map["cones"].copy()
        # Unset the cones that's too far away
        thresholded_local_map["cones"] = cones_xy

        # normalize the individual points of left, right and raceline of map.

        normalized_thresholded_local_map_subsampled_dict = (
            CoordinateNormalizationUtil.normalize_map_wrt_frame(
                thresholded_local_map, T_bg_subsampled
            )
        )
        # Invalid cone xy will be 0
        normalized_thresholded_local_map_subsampled_dict["cones"] *= cone_mask[:, None]

        normalized_thresholded_local_stacked_lanes = self._stack_lanes(
            normalized_thresholded_local_map_subsampled_dict
        )
        normalized_maps = {
            "lane_lines": normalized_thresholded_local_stacked_lanes,
            "cones": normalized_thresholded_local_map_subsampled_dict["cones"],
        }
        return normalized_maps
        # can deal with packing it into same shape in the collate function.
        # package it as [N, F], where F is [lx, ly, rx, ry, cs, cy, cz?, cv?, valid bit?]

        # add post process of maps. to create map vectors or whatever which will take the above and turn into
        # VectorNet like representation or anthing

        # add functor for post processing. cache always the post processed version.

    def extract_global_map(self, T_bg_subsampled):
        unnormalized_global_map = {}
        for map_key in self.map.keys():
            unnormalized_global_map[map_key] = np.array(self.map[map_key])

        if T_bg_subsampled is not None:
            # Subsampled global map and normalized with respect to local frame
            normalized_map = CoordinateNormalizationUtil.normalize_map_wrt_frame(
                unnormalized_global_map, T_bg_subsampled
            )
        else:
            normalized_map = unnormalized_global_map
        ret = {
            "lane_lines": self._stack_lanes(normalized_map),
            "cones": normalized_map["cones"],
        }
        return ret

    def get_trajectory(
        self,
        snippet_dict,
        key_prefix="",
        is_normalized=False,
        is_scaled=False,
        is_return_length=False,
        normalization_timestep="first",
    ):
        trajectory_dict = {}
        # get full previous trial. chopped into 10 segments.
        # extract full and subsampled trajectory snippets
        (
            full_trajectory,  # at full sampling rate
            subsampled_trajectory,  # at subsampled rate
            subsampled_idxs_dict,
        ) = self.extract_trajectory_data(snippet_dict)

        if is_normalized:
            (
                normalized_subsampled_trajectory,
                normalization_transformation_matrix_subsampled,
            ) = self.normalize_trajectory(
                subsampled_trajectory, normalization_timestep=normalization_timestep
            )

            if is_scaled:
                normalized_subsampled_trajectory = (
                    normalized_subsampled_trajectory
                    * self.feature_linear_scale.repeat(
                        normalized_subsampled_trajectory.shape[0], 0
                    )
                )

            trajectory_dict[f"{key_prefix}trajectory_inputs_subsampled"] = np.array(
                normalized_subsampled_trajectory
            )
            if is_return_length:
                trajectory_dict[
                    f"{key_prefix}trajectory_inputs_subsampled_length"
                ] = normalized_subsampled_trajectory.shape[0]
            return (
                trajectory_dict,
                normalization_transformation_matrix_subsampled,
                subsampled_idxs_dict,
            )
        else:
            trajectory_dict[f"{key_prefix}trajectory_inputs_subsampled"] = np.array(
                subsampled_trajectory
            )
            if is_return_length:
                trajectory_dict[
                    f"{key_prefix}trajectory_inputs_subsampled_length"
                ] = subsampled_trajectory.shape[0]

            return trajectory_dict, None, subsampled_idxs_dict

    def get_map(
        self,
        snippet_dict,
        T_matrix,
        key_prefix="",
        map_type="local",
        is_scaled=False,
        use_end_of_past_trajectory=False,
    ):
        map_dict = {}
        if map_type == "local":
            # dict with same keys as self.map, but with only the thresholded points normalized with repsect to the same frame that was used to normalize
            # the corresponding full or subsampled trajectory.
            normalized_maps = self.extract_local_map(
                snippet_dict["df"], T_matrix, use_end_of_past_trajectory
            )
            if is_scaled:
                for key, val in normalized_maps.items():
                    normalized_maps[key] = val * self.feature_linear_scale[
                        :, :2
                    ].repeat(val.shape[0], 0).repeat(
                        val.shape[1] // 2, 1
                    )  # repeat for inner edge xy, outeredge xy and raceline xy

        elif map_type == "global":
            normalized_maps = self.extract_global_map(T_matrix)
        elif map_type == "aligned":
            # For every timestep in the trajectory compute the nearest map point.
            # Result in T x F_m
            raise NotImplementedError()

        map_dict[f"{key_prefix}normalized_map_subsampled"] = normalized_maps[
            "lane_lines"
        ]
        map_dict[f"{key_prefix}cones"] = normalized_maps["cones"]

        return map_dict

    def get_traj_and_map_segments_from_trial(
        self,
        uid,
        key_prefix="",
        return_prev_next_lap_trial_num=False,
        is_normalized=True,
        is_scaled=True,
        is_return_length=True,
    ):
        segments_dict_for_uid = self.trajectory_segments_pkl_paths_according_to_uid[uid]
        seg_traj_map_data_dict = collections.OrderedDict()
        for seg_id, seg_path in segments_dict_for_uid.items():
            if "metrics" in seg_id:
                continue
            seg_snippet_dict = open_pkl(seg_path)

            seg_t_dict, T_matrix, subsampled_idxs_dict = self.get_trajectory(
                seg_snippet_dict,
                key_prefix=f"{key_prefix}{seg_id}_",
                is_normalized=is_normalized,
                is_scaled=is_scaled,
                is_return_length=is_return_length,
                normalization_timestep=self.normalization_timestep,
            )

            seg_traj_map_data_dict.update(seg_t_dict)

            seg_m_dict = self.get_map(
                seg_snippet_dict,
                T_matrix,
                key_prefix=f"{key_prefix}{seg_id}_",
                map_type="local",
                is_scaled=is_scaled,
            )
            seg_traj_map_data_dict.update(seg_m_dict)

            # print(seg_id, uid)

        if return_prev_next_lap_trial_num:
            valid_prev_lap_trial_num = seg_snippet_dict["metadata"][
                "valid_prev_lap_trial_num"
            ]
            valid_next_lap_trial_num = seg_snippet_dict["metadata"][
                "valid_next_lap_trial_num"
            ]
            return (
                seg_traj_map_data_dict,
                valid_prev_lap_trial_num,
                valid_next_lap_trial_num,
            )

        return seg_traj_map_data_dict

    def get_metrics_from_trial(self, uid, key_prefix=""):
        metrics_dict_path_for_uid = self.trajectory_metrics_pkl_paths_according_to_uid[
            uid
        ]["metrics_dict"]
        metrics_dict_for_uid = open_pkl(metrics_dict_path_for_uid)
        selected_metrics_dict = {}
        for mk, sub_key_list in self.metric_keys.items():
            segment_mk_list = metrics_dict_for_uid[f"segment_{mk}"]
            for subkey in sub_key_list:
                if subkey is None:
                    prefix_mk_key = f"{key_prefix}{mk}"
                    selected_metrics_dict[prefix_mk_key] = metrics_dict_for_uid[mk]
                else:
                    prefix_mk_key = f"{key_prefix}{mk}_{subkey}"
                    selected_metrics_dict[prefix_mk_key] = metrics_dict_for_uid[
                        f"{mk}_{subkey}"
                    ]
                seg_metric_list = []
                for segment_i, segment_i_mk_dict in enumerate(segment_mk_list):
                    if subkey is None:
                        prefix_mk_key = f"{key_prefix}segment_{segment_i+1}_{mk}"
                        selected_metrics_dict[prefix_mk_key] = segment_i_mk_dict[
                            f"{mk}"
                        ]
                    else:
                        prefix_mk_key = (
                            f"{key_prefix}segment_{segment_i+1}_{mk}_{subkey}"
                        )
                        selected_metrics_dict[prefix_mk_key] = segment_i_mk_dict[
                            f"{mk}_{subkey}"
                        ]
                    seg_metric_list.append(selected_metrics_dict[prefix_mk_key])

                # create a key with name seg_{METRIC_NAME} and the value to be a (num_segments, 1) array
                # this is the same information stored as separate keys.
                seg_mk_key = f"segment_{mk}"
                if subkey is not None:
                    seg_mk_key += "_" + subkey
                selected_metrics_dict[seg_mk_key] = np.asarray(seg_metric_list).astype(
                    np.float32
                )

        # scale normalize lap level metrics
        for mk, subkey_dict in METRICS_LINEAR_SCALE.items():
            if mk not in self.metric_keys:
                continue
            for subkey, subkey_ls in subkey_dict.items():
                dict_key = (
                    f"{key_prefix}{mk}"
                    if subkey == None
                    else f"{key_prefix}{mk}_{subkey}"
                )
                selected_metrics_dict[dict_key] = (
                    selected_metrics_dict[dict_key] * subkey_ls
                )

        # TODO decideif segment level metrics need to be scale normalized. Potentially, can be expressed as a percentage of the scaled
        # lap level metric.

        return selected_metrics_dict

    def get_data_idx_from_trial_uid(self, uid):
        return self.valid_lap_list.index(uid)

    def get_idx_in_ordered_list(self, uid):
        pid, trial_num = get_pid_and_trial_num_from_uid(uid)
        if pid not in self.pid_to_ordered_laps:
            return None
        else:
            if trial_num in self.pid_to_ordered_laps[pid]:
                return self.pid_to_ordered_laps[pid].index(trial_num)
            else:
                return None

    @Cache(  # documentation see AIC24D16LapDataset.consistent_hash()
        cache_path=CACHE_PATH,
        enable_cache_arg_name="self.enable_dataset_cache",
    )
    def get_lap_data_dict(self, valid_lap_uid):
        data_dict = {}
        (
            current_trial_trajectory_and_map_segments_dict,
            _,
            valid_next_lap_trial_num,
        ) = self.get_traj_and_map_segments_from_trial(
            uid=valid_lap_uid,
            key_prefix="curr_",
            return_prev_next_lap_trial_num=True,
        )
        data_dict.update(current_trial_trajectory_and_map_segments_dict)

        current_trial_metrics = self.get_metrics_from_trial(
            valid_lap_uid, key_prefix="curr_"
        )
        data_dict.update(current_trial_metrics)
        data_dict["curr_lap_uid"] = valid_lap_uid

        if self.return_next_lap_metrics:
            valid_lap_pid, valid_lap_trial_num = get_pid_and_trial_num_from_uid(
                valid_lap_uid
            )
            valid_next_lap_uid = get_uid_from_pid_and_trial_num(
                valid_lap_pid, valid_next_lap_trial_num
            )
            next_trial_metrics = self.get_metrics_from_trial(
                valid_next_lap_uid, key_prefix="next_"
            )
            data_dict.update(next_trial_metrics)

        return data_dict

    def __getitem__(self, idx):
        """
        Return:
            traj: normalized to traj[-1]
            map: normalized to traj[-1]
                local:  only map segment within local_map_distance_threshold
                        variable length, collator should pad
                global: the entire map
                        constant length
                cones:  only cones within local_map_distance_threshold
                        constant length, invalid cones(too far) will be set to 0
        """
        valid_lap_uid = self.valid_lap_list[idx]
        pid, trial_num = get_pid_and_trial_num_from_uid(valid_lap_uid)
        data_dict = self.get_lap_data_dict(valid_lap_uid)

        return data_dict

    @staticmethod
    def get_collator(config_dict):
        from aic_bsd_model.datasets.data_collators import TrackSegmentCollator

        return TrackSegmentCollator(
            config_dict["padding_value"], padding_side=config_dict["padding_side"]
        )

    def run_parallel_loop(self, fn, data, tqdm_desc="", keep_results=True):
        if self.use_multiprocessing:
            # TODO (deepak.gopinath) this was return to speed up the processing. the num_processes might have to be capped
            # as the number of parallel model runs increase.
            # Alternate approach is to create a cache of this. There should be a different cache for each self.pids_to_be_considered.
            num_processes = multiprocessing.cpu_count()
            total = len(data)
            with multiprocessing.Pool(num_processes) as pool:
                results = []
                with tqdm(
                    total=total, desc=tqdm_desc, disable=self.disable_tqdm
                ) as pbar:
                    for result in pool.imap_unordered(fn, data, chunksize=200):
                        if result is not None and keep_results:
                            results.append(result)
                        pbar.update()
            return results
        else:
            results = []
            for d in tqdm(data, desc=tqdm_desc, disable=self.disable_tqdm):
                result = fn(d)
                if result is not None and keep_results:
                    results.append(result)
            return results

    def cache_variables_for_hash(self) -> dict:
        """
        This function is used to generate a unique hash for the dataset,
        so @Cache can use it to check if the dataset is consistent across different runs.
        Return a dict of variables.
        Note:
            Be sure to call super().cache_variables_for_hash() in the subclass.
        """
        # Do this in the subclass.
        # ret = super().cache_variables_for_hash()
        ret = {}
        ret.update(
            {
                "dataset_type": self.dataset_type,
                "target_sampling_frequency": self.target_sampling_frequency,
                "return_next_lap_metrics": self.return_next_lap_metrics,
            }
        )
        return ret

    def consistent_hash(self):
        """Caching mechanism:
        This function is used to generate a unique hash for this class instance,
        so @Cache can use it to check if there's existing cache for this instance and function.

        Do NOT modify this function. Put all the relevant variables in cache_variables_for_hash().

        How @Cache works:
        - Decorate a function (e.g. __getitem__) as @Cache(cache_path=CACHE_PATH, enable_cache_arg_name="self.enable_dataset_cache")
        - At runtime, the decorated function is called,
            1. self.enable_dataset_cache will determine if caching is enabled. If not, @Cache will be ignored.
            2. '{_hash}' in CACHE_PATH will prompt @Cache to generate a hash value based on the hash of the class instance 'self',
                which is determined by the return value of this function.
                so make sure to put all the relevant variables in cache_variables_for_hash().
                And even if the function to be cached doesn't have any function args,
                since 'self' is included, the hash will be different across different instances.
            3. Other than 'self', '{_hash}' includes ['all function args', 'function name', 'args_to_ignore', 'source code of the function']
            4. If {_hash} is found in the cache, the cached result will be returned.
            5. Otherwise, the decorated function will be executed and its return value will be cached.
        """

        hash_dict = {"class_name": self.class_name}
        hash_dict.update(self.cache_variables_for_hash())
        return sha256(hash_dict)

    @Cache(cache_path=CACHE_PATH, enable_cache_arg_name="self.enable_dataset_cache")
    def _has_cache_helper(self):
        return True

    def _cache_path(self):
        """Compute and return the path where the cache file should live."""
        return Cache.compute_path(self._has_cache_helper, self, {})

    def been_cached(self):
        return os.path.exists(self._cache_path())

    def init(self):
        pass

    def cache_dataset(self, lock_timeout=3600):
        cache_path = self._cache_path()
        if not self.enable_dataset_cache:
            print(f"Caching disabled for {self.class_name}; proceeding without cache")
            self.init()
            return
        if self.been_cached():
            print(f"Dataset {self.class_name} at {cache_path} already cached")
            self.init()
            return

        lock = FileLock(cache_path + ".lock")
        try:
            print(
                f"Checking if {self.class_name} at {cache_path} is cached.\n"
                f"Waiting for caching to complete for {lock_timeout} seconds"
            )
            init_called = False

            with lock.acquire(timeout=lock_timeout, poll_interval=2):
                # Inside the lock: decide writer vs reader.
                if self.been_cached():
                    print(f"Dataset {self.class_name} at {cache_path} already cached")
                else:
                    self.init()
                    init_called = True

                    print(
                        f"Start caching dataset {self.class_name} at {cache_path}, length {len(self)}"
                    )
                    t1 = time.monotonic()
                    self.run_parallel_loop(
                        fn=self.__getitem__,
                        data=range(len(self)),
                        tqdm_desc="Caching dataset",
                        keep_results=False,
                    )
                    _ = self._has_cache_helper()  # triggers cache materialization
                    assert self.been_cached()
                    t2 = time.monotonic()
                    print(
                        f"Cached dataset {self.class_name} at {cache_path}, successfully"
                    )
                    print(f"Time taken to cache: {(t2 - t1) / 60} minutes")

            # Outside the lock: let *all* processes proceed concurrently.
            print(f"Cache is ready for {self.class_name} at {cache_path}")
            if not init_called:
                self.init()

        except Timeout:
            if not self.been_cached():
                print(f"Timeout waiting for cache for {self.class_name}: {cache_path}")
                raise RuntimeError(f"Timeout waiting for cache at {cache_path}")
            # Cache appeared even though we timed out; proceed without acquiring the lock.
            print(
                f"Cache became available for {self.class_name} at {cache_path} after timeout; proceeding"
            )
            self.init()


class AIC24D16FullSequenceDataset(AIC24D16LapDataset):
    class_name = "AIC24D16FullSequenceDataset"

    def __init__(self, config_dict, dataset_type):
        super().__init__(config_dict, dataset_type)
        self.lap_collator = TrackSegmentCollator(
            config_dict["padding_value"], padding_side="right"
        )

    def cache_variables_for_hash(self) -> dict:
        ret = super().cache_variables_for_hash()
        return ret

    @staticmethod
    def get_collator(config_dict):
        from aic_bsd_model.datasets.data_collators import (
            TrackSegmentCollatorFullSequence,
        )

        return TrackSegmentCollatorFullSequence(
            config_dict["padding_value"], padding_side=config_dict["padding_side"]
        )

    def __len__(self):
        return len(self.pids_to_be_considered)

    @Cache(  # documentation see AIC24D16LapDataset.consistent_hash()
        cache_path=CACHE_PATH,
        enable_cache_arg_name="self.enable_dataset_cache",
    )
    def _load_sequence_data(self, pid, ordered_trial_nums_for_pid):
        list_of_dicts = []
        for trial_num in ordered_trial_nums_for_pid:
            valid_lap_uid = get_uid_from_pid_and_trial_num(pid, trial_num)
            data_dict = self.get_lap_data_dict(valid_lap_uid)
            list_of_dicts.append(data_dict)

        # do collation for all segments. Use TrackSegmentCollator
        data_dict = self.lap_collator(list_of_dicts)
        return data_dict

    def __getitem__(self, idx):
        """
        Return:
            traj: normalized to traj[-1]
            map: normalized to traj[-1]
                local:  only map segment within local_map_distance_threshold
                        variable length, collator should pad
                global: the entire map
                        constant length
                cones:  only cones within local_map_distance_threshold
                        constant length, invalid cones(too far) will be set to 0
        """
        # each data item is a subject
        pid = self.pids_to_be_considered[idx]
        # returns the correct ordered sequence of valid lap
        ordered_trial_nums_for_pid = self.pid_to_ordered_laps[pid]
        return self._load_sequence_data(pid, ordered_trial_nums_for_pid)
