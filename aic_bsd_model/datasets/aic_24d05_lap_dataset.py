import collections
from hashlib import sha256
from pathlib import Path

import numpy as np
from cache_decorator import Cache

from aic_bsd_model.datasets.aic_24d16_lap_dataset import CACHE_PATH, AIC24D16LapDataset
from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    ANNOTATOR_IDS_24_D_05,
    VALID_ANNOTATION_CATEGORIES_INDEX_DICT_DEFAULT,
    CategoryDictIndex,
    get_pid_and_trial_num_from_uid,
    get_uid_from_pid_and_trial_num,
    open_pkl,
)
from aic_bsd_model.datasets.data_collators import TrackSegmentCollator


class AIC24D05LapDataset(AIC24D16LapDataset):
    class_name = "AIC24D05LapDataset"

    def __init__(self, config_dict, dataset_type):
        super().__init__(config_dict, dataset_type)
        self.return_cf_for_lap = self.config_dict.get("return_cf_for_lap", False)
        self.annotation_majority_threshold = self.config_dict.get(
            "annotation_majority_threshold", 0.6
        )
        self.valid_annotation_categories_index_dict_type = self.config_dict.get(
            "valid_annotation_categories_index_dict_type", "default"
        )
        if self.valid_annotation_categories_index_dict_type == "default":
            self.valid_annotation_categories_index_dict = (
                CategoryDictIndex.DEFAULT.value
            )
        elif self.valid_annotation_categories_index_dict_type == "no_steering":
            self.valid_annotation_categories_index_dict = (
                CategoryDictIndex.NO_STEERING.value
            )
        elif self.valid_annotation_categories_index_dict_type == "no_steering_no_turn":
            self.valid_annotation_categories_index_dict = (
                CategoryDictIndex.NO_STEERING_NO_TURN.value
            )
        elif (
            self.valid_annotation_categories_index_dict_type
            == "no_steering_no_turn_no_brake"
        ):
            self.valid_annotation_categories_index_dict = (
                CategoryDictIndex.NO_STEERING_NO_TURN_NO_BRAKE.value
            )
        elif self.valid_annotation_categories_index_dict_type == "basic":
            self.valid_annotation_categories_index_dict = CategoryDictIndex.BASIC.value

    def _is_lap_valid(self, metadata):
        """
        Each subject study started with the sight lap followed by two baseline laps.
        The coaching laps started right after the base line laps. For all subjects, except one, Coach Jon
        provided an additional demo lap in between the coaching (except for 2 subjects).
        After the last coaching lap, the subjects performed the 2 retention laps.
        There was never a situation in which 1) a demo_lap immediately followed the baseline lap or 2) a demo
        lap immediately preceded the retention laps.

        Current approach to determining which laps are valid (in ()) and invalid (in []):
        # D is the demo lap. C is last coaching lap. R-1 and R are retention laps.
        [0,1], (2,3....D-1), [D], (D+1...C, R-1), [R]
        Alternately, could consider a scheme where both R-1 and R are invalid
        """
        # the 24d05 lap level metadata was populated when the lap level segments
        # generated in aic_24d05_laps_to_map_segments_snipper.py
        if not metadata["is_valid"]:
            # in the 24d05 dataset all laps are valid so this if block will never be executed,
            # but for completeness we will keep the if block.
            return False
        else:
            pid, trial_num = get_pid_and_trial_num_from_uid(metadata["uid"])
            if metadata["source_lap_type"] in ["sight_lap", "demo_lap"]:
                return False
            if metadata["source_lap_type"] == "baseline":
                # skip the first baseline lap, use the second baseline lap as the first lap of the sequence
                return False if trial_num == 1 else True
            if metadata["source_lap_type"] == "retention":
                # if second retention lap, then the next lap flag would be -1 and hence return False
                return False if metadata["valid_next_lap_trial_num"] == -1 else True
            if metadata["source_lap_type"] == "coaching_lap":
                return True

    def _prepare_valid_laps_list(self):
        # decide the valid laps. Maybe skip sight_lap, first baseline, demo lap and second retention.
        for d in sorted(self.trajectory_segments_dir.iterdir()):
            uid = d.stem
            # TODO (deepak.gopinath) Move track.csv location to elsewhere so that this if statement can be remvoed.
            if "track" in d.stem:
                continue
            pid, trial_num = get_pid_and_trial_num_from_uid(uid)
            if pid not in self.pids_to_be_considered:
                continue

            # metadata is the same for all segments of a given lap, excetp for map_seg_id
            # Hence, just grab the metadata of the first segment
            segment_1_pkl_path = [
                s for s in list(d.iterdir()) if "segment_1.pkl" in str(s)
            ][0]
            segment_1_snippet = open_pkl(segment_1_pkl_path)
            segment_1_metadata = segment_1_snippet["metadata"]
            if self._is_lap_valid(segment_1_metadata):
                self.valid_lap_list.append(uid)

    def get_cf_instruction_class_series(self, snippet_df, subsampled_idxs_dict):
        cf_instruction_class_series = []
        for instance_annotations in snippet_df[ANNOTATOR_IDS_24_D_05].values:
            # instance_annotations are the annotations from the 5 annotators
            if tuple(instance_annotations[0]) == ("INVALID", "INVALID", "INVALID"):
                # this was a row in the df that did not have an utterance.
                cf_instruction_class_series.append(0)
            else:
                instance_annotations = [tuple(i) for i in instance_annotations]
                # get the set of unique annotations
                unique_annotations = set(instance_annotations)
                # The 3-tuple that is most common
                most_common_annotation = max(
                    unique_annotations, key=instance_annotations.count
                )
                # percentage of annotators that chose the most common annotation
                percentage_of_annotators_with_most_common_annotation = (
                    instance_annotations.count(most_common_annotation)
                    / len(ANNOTATOR_IDS_24_D_05)
                )
                # if percentage is greater than a predefined threshold consider it...
                if (
                    percentage_of_annotators_with_most_common_annotation
                    >= self.annotation_majority_threshold
                ):
                    # if the "Type" of this most_common_annotation is "instruction" consider it
                    if most_common_annotation[0] == "instruction":
                        if (
                            most_common_annotation
                            in self.valid_annotation_categories_index_dict
                        ):
                            cf_instruction_class_series.append(
                                self.valid_annotation_categories_index_dict[
                                    most_common_annotation
                                ]
                            )
                        else:
                            # if it is an instruction category that we are not interested in treat it as null
                            cf_instruction_class_series.append(0)
                    else:
                        # if it is not instruction treat is as null
                        cf_instruction_class_series.append(0)
                else:
                    # treat any annotation with no majority to be null.
                    cf_instruction_class_series.append(0)

        # the length of the full temporal category series should be same as the length of the df
        assert len(cf_instruction_class_series) == snippet_df.shape[0]
        # convert list into numpy array
        # replace sampling frequency for trial snippet with sampling frequency from full trial
        cf_instruction_class_series = np.array(cf_instruction_class_series)
        if "subsampled_idxs_int" in subsampled_idxs_dict:
            subsampled_idxs = subsampled_idxs_dict["subsampled_idxs_int"]
            cf_instruction_class_series_subsampled = cf_instruction_class_series[
                subsampled_idxs
            ]
        else:
            subsampled_idxs_f = subsampled_idxs_dict["subsampled_idxs_floating_point"]
            subsampled_idxs_m = subsampled_idxs_dict["subsampled_idxs_floor"]
            subsampled_idxs_p = subsampled_idxs_dict["subsampled_idxs_ceil"]

            # TODO (deepak.gopinath)
            # deal with how we want to "interpolate" the concurrent feedback categories.
            # most likely we will just do a round operation on subsampled_idxs_f and use those indices.
            pass

        return cf_instruction_class_series, cf_instruction_class_series_subsampled

    @Cache(  # documentation see AIC24D16LapDataset.consistent_hash()
        cache_path=CACHE_PATH,
        enable_cache_arg_name="self.enable_dataset_cache",
    )
    def get_cf_data_segments_from_trial(self, uid, key_prefix=""):
        cf_data_segments_dict = collections.OrderedDict()
        segments_dict_for_uid = self.trajectory_segments_pkl_paths_according_to_uid[uid]
        for seg_id, seg_path in segments_dict_for_uid.items():
            if "metrics" in seg_id:
                continue
            seg_snippet_dict = open_pkl(seg_path)
            seg_snippet_df, _ = self.get_df_and_metadata(seg_snippet_dict)
            # the same subsampling indices used for the subsampling the segment trajectory
            subsampled_idxs_dict = self._get_subsampling_idxs(seg_snippet_df)
            (
                _,
                cf_instruction_class_series_subsampled,
            ) = self.get_cf_instruction_class_series(
                seg_snippet_df, subsampled_idxs_dict
            )
            cf_data_segments_dict[
                f"{key_prefix}{seg_id}_cf_instruction_class_series_subsampled"
            ] = np.array(cf_instruction_class_series_subsampled).reshape(
                len(cf_instruction_class_series_subsampled), 1
            )

        return cf_data_segments_dict

    def get_cf_data_for_lap(self, valid_lap_uid):
        cf_data_segments_dict = self.get_cf_data_segments_from_trial(
            uid=valid_lap_uid, key_prefix="curr_"
        )
        return cf_data_segments_dict

    def __getitem__(self, idx):
        data_dict = super().__getitem__(idx)
        # consider adding concurrent feedback for each segment
        # [traj_len] with label curr_segment_1_cf_full_series, curr_segment_1_cf_subsampled
        # padding logic will be same as trajectory. collator will need to be notified if the dataset contains the concurrent feedback or not.
        # alterantely could just have a "teaching dataset" bit.
        if self.return_cf_for_lap:
            valid_lap_uid = self.valid_lap_list[idx]
            cf_data_for_lap = self.get_cf_data_for_lap(valid_lap_uid)
            data_dict.update(cf_data_for_lap)

        return data_dict

    def cache_variables_for_hash(self):
        """This function is used to generate a unique hash for the dataset.
        Put all the variables that determine the __getitem__ return value here.
        """
        ret = super().cache_variables_for_hash()
        ret.update(
            {
                "return_cf_for_lap": self.return_cf_for_lap,
                "valid_annotation_categories_index_dict": self.valid_annotation_categories_index_dict,
            }
        )
        return ret

    @staticmethod
    def get_collator(config_dict):
        from aic_bsd_model.datasets.data_collators import TrackSegmentCollator

        return TrackSegmentCollator(
            config_dict["padding_value"],
            padding_side=config_dict["padding_side"],
            lap_has_cf_series=config_dict["return_cf_for_lap"],
        )


class AIC24D05FullSequenceDataset(AIC24D05LapDataset):
    class_name = "AIC24D05FullSequenceDataset"

    def __init__(self, config_dict, dataset_type):
        super().__init__(config_dict, dataset_type)
        self.lap_collator = TrackSegmentCollator(
            config_dict["padding_value"],
            padding_side="right",
            lap_has_cf_series=config_dict["return_cf_for_lap"],
        )

    def cache_variables_for_hash(self) -> dict:
        ret = super().cache_variables_for_hash()
        return ret

    def __len__(self):
        return len(self.pids_to_be_considered)

    @staticmethod
    def get_collator(config_dict):
        from aic_bsd_model.datasets.data_collators import (
            TrackSegmentCollatorFullSequence,
        )

        return TrackSegmentCollatorFullSequence(
            config_dict["padding_value"],
            padding_side=config_dict["padding_side"],
            lap_has_cf_series=config_dict["return_cf_for_lap"],
        )

    @Cache(  # documentation see AIC24D16LapDataset.consistent_hash()
        cache_path=CACHE_PATH,
        enable_cache_arg_name="self.enable_dataset_cache",
    )
    def _load_sequence_data(self, pid, ordered_trial_nums_for_pid):
        list_of_dicts = []
        for trial_num in ordered_trial_nums_for_pid:
            valid_lap_uid = get_uid_from_pid_and_trial_num(pid, trial_num)
            data_dict = self.get_lap_data_dict(valid_lap_uid)
            if self.return_cf_for_lap:
                cf_data_for_lap = self.get_cf_data_for_lap(valid_lap_uid)
                data_dict.update(cf_data_for_lap)
            list_of_dicts.append(data_dict)

        # do collation for all segments. Use TrackSegmentCollator
        data_dict = self.lap_collator(list_of_dicts)
        return data_dict

    def __getitem__(self, idx):
        """
        Return:
            traj: normalized to traj[-1]
            map: normalized to traj[-1]
                local:  only map segment within local_map_distance_threshold
                        variable length, collator should pad
                global: the entire map
                        constant length
                cones:  only cones within local_map_distance_threshold
                        constant length, invalid cones(too far) will be set to 0
        """
        # each data item is a subject
        pid = self.pids_to_be_considered[idx]
        # returns the correct ordered sequence of valid lap
        ordered_trial_nums_for_pid = self.pid_to_ordered_laps[pid]
        return self._load_sequence_data(pid, ordered_trial_nums_for_pid)
