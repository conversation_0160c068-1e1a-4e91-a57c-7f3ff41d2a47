#%%
import pandas as pd
import numpy as np
from pathlib import Path
import pickle
import matplotlib.pyplot as plt
import collections
import yaml
import math
import copy
import torch

from aic_bsd_model.datasets.aic_24d05_cf_dataset import AIC24D05CFDataset
from aic_bsd_model.datasets.aic_24d05_combined_dataset import (
    AIC24D05CombinedPrevKDataset,
    CFSnippetCollator,
)
from aic_bsd_model.datasets.aic_bsd_dataset_utils import open_pkl, TRAJ_FEATURE_LINEAR_SCALE, DEMO_TRIAL_PER_SUBJECT_24_D_05, INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16, get_pid_and_trial_num_from_uid, TrajFeatureIndex, TrajFeatureIndexNoAccel, FEATURE_TO_DF_KEY, FEATURE_TO_DF_KEY_NO_ACCEL
#%%

# add parse args or something so that paths can be passed as args. eventually unify it with draccuss or whatever AS is using for IFM
trajectory_segments_dir = "~/Data/24-D-05/trajectory_segments_map_seg_ids/"
trajectory_snippets_dir = "~/Data/24-D-05/trajectory_snippets/"
trials_directory = "~/Data/24-D-05/trials_final/"
unflipped_trials_directory = "~/Data/24-D-05/unflipped_trials_final/"
trajectory_segments_dir = Path.expanduser(Path(trajectory_segments_dir))
trials_directory = Path.expanduser(Path(trials_directory))
unflipped_trials_directory = Path.expanduser(Path(unflipped_trials_directory))
trajectory_snippets_dir = Path.expanduser(Path(trajectory_snippets_dir))
map_file = trajectory_segments_dir / "track.csv"
with open(map_file, "r") as f:
    map_data = pd.read_csv(f)

csim_data_parent_folder = "~/Data/24-D-05/csim_data_log/"
csim_data_parent_folder = Path.expanduser(Path(csim_data_parent_folder))
csim_data_folder =  "/home/<USER>/Data/24-D-05/csim_data_log/temp_snippet_08-26T19_16_03-7c213-csim_modeltest_nonadaptive_additional28/"
csim_data_folder = Path.expanduser(Path(csim_data_folder))
# full_lap_test_csv_path = "~/Data/24-D-05/csim_data_log/west_lap_ccw_model_test_6s.csv"
# full_lap_test_csv_path = "~/Data/24-D-05/csim_data_log/west_lap_ccw_model_test_unspin.csv"
#%%
CARLA_TIME_KEY = "carla_objects log time"

#%% md
## Retrieve all pre files in orders
#%%
def rename_vehicle_df(df):
    df.rename(
        columns={
            "ego_position_x": "ego_x",
            "ego_position_y": "ego_y",
            "ego_position_z": "ego_z",
            "ego_velocity_x": "ego_vx",
            "ego_velocity_y": "ego_vy",
            "ego_velocity_z": "ego_vz",
            "ego_orientation_x": "ego_orientation_x",
            "ego_orientation_y": "ego_orientation_y",
            "ego_orientation_z": "ego_orientation_z",
            "ego_orientation_w": "ego_orientation_w",
            "steer": "steering",
            "throttle_pedal": "throttle",
            "brake_pedal": "brake",
        },
        inplace=True,
    )
    df[CARLA_TIME_KEY] = df["time"]

def plot_feature_time_series(time_series,  title="test data", feature_name='ego_x'):
    plt.figure()
    plt.plot(time_series)
    plt.title(f'{title}_{feature_name}')
    plt.xlabel(feature_name)
    plt.ylabel('Feature Value')
    plt.show()

def plot_hist_of_features(feature_list, title="test data", feature_name='ego_x'):
    plt.figure()
    plt.hist(feature_list)
    plt.title(f'{title}_{feature_name}')
    plt.xlabel(feature_name)
    plt.ylabel('Count')
    plt.show()
#%%
# retrieve all files with new naming conventions
pre_swap_ordered_list = []
post_swap_ordered_list = []
cf_pkl_ordered_list = []
cf_pkl_prediction_ordered_list = []
sub_folder_names = ['pre_flip', 'post_flip', 'cf_pkl', 'cf_pkl_prediction']

len_f = lambda x: len(list(Path(csim_data_folder/ x ).iterdir()))

num_files_in_subfolders = [len_f(s) for s in sub_folder_names]
assert len(set(num_files_in_subfolders)) == 1

for i in range(num_files_in_subfolders[0]):
    pre_swap_path = csim_data_folder / "pre_flip" / f"P604-trial_{i}" / f"P604-trial_{i}_with_all_annotations_and_metrics_with_instruction_category.parquet"
    pre_swap_ordered_list.append(pd.read_parquet(pre_swap_path))
    post_swap_path = csim_data_folder / "post_flip" / f"P604-trial_{i}" / f"P604-trial_{i}_with_all_annotations_and_metrics_with_instruction_category.parquet"
    post_swap_ordered_list.append(pd.read_parquet(post_swap_path))
    cf_pkl_path = csim_data_folder / "cf_pkl"/ f"cf_data_snippet_{i+1}.pkl"
    with open(cf_pkl_path, 'rb') as fp:
        a = pickle.load(fp)
    cf_pkl_ordered_list.append(a)

    cf_pkl_prediction_path = csim_data_folder / "cf_pkl_prediction"/ f"cf_data_prediction_{i+1}.pkl"
    with open(cf_pkl_prediction_path, 'rb') as fp:
        a = pickle.load(fp)
    cf_pkl_prediction_ordered_list.append(a)
    if i % 40 == 0:
        print(f'Processed snippet {i}')
    
    
    

#%%
pre_swap_ordered_list[0].keys()
#%%
cf_data = cf_pkl_ordered_list[199]
cf_data['snippet_trajectory_inputs_subsampled'][0, :, TrajFeatureIndexNoAccel.BRAKE]
#%%
df = pre_swap_ordered_list[0]
plt.plot(np.diff(df['time'].values)); plt.show()
# np
#%%

time_series_feature_list_csim_data = collections.defaultdict(list)
for df in pre_swap_ordered_list:
    pre_df = copy.deepcopy(df)
    rename_vehicle_df(pre_df)
    for feature_key in FEATURE_TO_DF_KEY_NO_ACCEL.values():
        time_series_feature_list_csim_data[feature_key].extend(pre_df[feature_key][:24].values)
    

#%%
for k in time_series_feature_list_csim_data:
    plot_feature_time_series(time_series_feature_list_csim_data[k], title = "csim_data", feature_name=k)
#%%
# check model predictions logged
[(i, p[0]) for i, p in enumerate(cf_pkl_prediction_ordered_list)]
#%% md
#### Old code for testing
#%%
pre_swap_ordered_list = []
post_swap_ordered_list = []
cf_pkl_ordered_list = []
total_num_files = len(list(csim_data_folder.iterdir()))

idxs = range(1, int(total_num_files/3) + 1)
for i in idxs:
    pre_swap_path = csim_data_folder / f"pre_flip_df_snippet_{i-1}.parquet"
    pre_swap_ordered_list.append(pd.read_parquet(pre_swap_path))
    post_swap_path = csim_data_folder / f"post_flip_df_snippet_{i-1}.parquet"
    post_swap_ordered_list.append(pd.read_parquet(post_swap_path))
    cf_pkl_path = csim_data_folder / f"cf_data_snippet_{i}.pkl"
    with open(cf_pkl_path, 'rb') as fp:
        a = pickle.load(fp)
    cf_pkl_ordered_list.append(a)
    if i % 10 == 0:
        print(f'Processed snippet {i}')
    
#%%
test_data_trial_num = 'P610-trial_19' # 16 is demo lap
all_snippets_test_data = []
for d in trajectory_snippets_dir.iterdir():
    if test_data_trial_num not in d.stem:
        continue
    else:
        all_snippets_test_data.append(d.stem)

total_test_snippets = len(all_snippets_test_data)
idxs = range(1, total_test_snippets+1)
sorted_snippets_test_data_pkl_paths = []
for i in idxs:
    prefix = f'{test_data_trial_num}_snippet_{i}'
    sorted_snippets_test_data_pkl_paths.append(trajectory_snippets_dir / prefix / 'snippet.pkl')

test_snippet_dfs = []
for pkl in sorted_snippets_test_data_pkl_paths:
    with open(pkl, 'rb') as fp:
        a = pickle.load(fp)
    df = a['df']
    test_snippet_dfs.append(df)

#%%
#Compare test_snippet features
print(FEATURE_TO_DF_KEY_NO_ACCEL.values())
hist_lists_for_features_test_data = collections.defaultdict(list)
for df in test_snippet_dfs:
    for feature_key in FEATURE_TO_DF_KEY_NO_ACCEL.values():
        hist_lists_for_features_test_data[feature_key].extend(df[feature_key].values)
    
#%%

#%%
hist_lists_for_features_csim_data = collections.defaultdict(list)
for pre_df in pre_swap_ordered_list:
    rename_vehicle_df(pre_df)
    for feature_key in FEATURE_TO_DF_KEY_NO_ACCEL.values():
        hist_lists_for_features_csim_data[feature_key].extend(pre_df[feature_key].values)
    
#%%

#%%
for k in hist_lists_for_features_test_data:
    plot_hist_of_features(hist_lists_for_features_test_data[k], title = "test_data", feature_name=k)
#%%
for k in hist_lists_for_features_csim_data:
    plot_hist_of_features(hist_lists_for_features_csim_data[k], title = "csim_data", feature_name=k)
#%%
# reactre the data pipeline through online infernece. See if test data pushed through that results in the same normalization as CF dataset loaded through the training pipeline
#%%
# plot each feature as a line curve
one_sec = 120
time_series_feature_list_test_data = collections.defaultdict(list)
for feature_key in FEATURE_TO_DF_KEY_NO_ACCEL.values():
    for df in test_snippet_dfs:
        time_series_feature_list_test_data[feature_key].extend(df[feature_key][:one_sec].values)

time_series_feature_list_csim_data = collections.defaultdict(list)
for pre_df in pre_swap_ordered_list:
    rename_vehicle_df(pre_df)
    for feature_key in FEATURE_TO_DF_KEY_NO_ACCEL.values():
        if feature_key == 'steering':
            time_series_feature_list_csim_data[feature_key].extend(pre_df[feature_key][:one_sec].values*-1)
        else:
             time_series_feature_list_csim_data[feature_key].extend(pre_df[feature_key][:one_sec].values)
    
#%%

#%%
for k in time_series_feature_list_test_data:
    plot_feature_time_series(time_series_feature_list_test_data[k], title = "test_data", feature_name=k)
#%%
for k in time_series_feature_list_csim_data:
    plot_feature_time_series(time_series_feature_list_csim_data[k], title = "csim_data", feature_name=k)
#%%
# read lap csv and compare if the features match
lap_df = pd.read_csv(full_lap_test_csv_path, index_col=0)
rename_vehicle_df(lap_df)
lap_df.keys()
time_series_feature_list_lap_data = {}
for feature_key in FEATURE_TO_DF_KEY_NO_ACCEL.values():
    if feature_key == 'ego_vy' or feature_key == 'ego_y' or feature_key == 'ego_orientation_z' or feature_key == 'ego_orientation_x':
        time_series_feature_list_lap_data[feature_key] = lap_df[feature_key].values * -1
    else:
        time_series_feature_list_lap_data[feature_key] = lap_df[feature_key].values
#%%
for k in time_series_feature_list_lap_data:
    plot_feature_time_series(time_series_feature_list_lap_data[k], title = "lap_data_csv", feature_name=k)
#%%
features_to_be_flipped = ['ego_y', 'ego_vy', 'ego_orientation_x', 'ego_orientation_z']
for f in features_to_be_flipped:
    lap_df[f] = -lap_df[f]
    
#%%
for f in FEATURE_TO_DF_KEY_NO_ACCEL.values():
    plt.plot(lap_df[f].values); plt.title(f); plt.show()
    
#%% md
# Create data parquets from the lap csv file
#%%
buffer_data_pkl = "/home/<USER>/Data/24-D-05/csim_data_log/buffer_data.pkl"
with open(buffer_data_pkl, 'rb') as fp:
    buffer = pickle.load(fp)
s_list = buffer['time_s']
ns_list = buffer['time_ns'][0]
t_list = [s + ns*1e-9 for s, ns in zip(s_list, ns_list)]
plt.plot(t_list)

#%%
window_size = 1200
hop_size = 120
full_timesteps =1200
future_timesteps = 600
extra_bit = 120
past_timesteps = full_timesteps - future_timesteps
start, end = 0, window_size
trial_idx = 0

while end <= lap_df.shape[0]:
    Path(csim_data_parent_folder / "trial_dirs"/ f"P604-trial_{trial_idx}").mkdir(parents=True, exist_ok=True)
    Path(csim_data_parent_folder / "unflipped_trial_dirs"/ f"P604-trial_{trial_idx}").mkdir(parents=True, exist_ok=True)
    df = lap_df[start:end]
    df.loc[: , 'time'] = t_list[:1200]
    df[CARLA_TIME_KEY] = df["time"]
    df["timestamp"] = df[CARLA_TIME_KEY]
    df["out_of_bounds"] = 0
    df["out_of_bounds_distances"] = 0
    df["lateral_distances"] = 0
    df.to_parquet(csim_data_parent_folder / "unflipped_trial_dirs"/ f"P604-trial_{trial_idx}"/ f"P604-trial_{trial_idx}_with_all_annotations_and_metrics_with_instruction_category.parquet")
    with open(csim_data_parent_folder / "unflipped_trial_dirs"/ f"P604-trial_{trial_idx}"/ f"P604-trial_{trial_idx}_with_all_annotations_and_metrics_with_instruction_category.yaml", "w") as fp:
        yaml.dump(
            {"additional_metrics_dict": {}, "source_lap_type": "coaching_lap"}, fp
        )
    print(df.shape)

    # do flip and save again
    # df.loc[: , 'time'] = t_list[:1200]
    df2 = df[-full_timesteps:].copy()
    # flip recent past to first part of the snippet
    for feature_key in FEATURE_TO_DF_KEY_NO_ACCEL.values():
        # print(feature_key)
        df2[feature_key].values[:past_timesteps + extra_bit] = df2[feature_key].values[-past_timesteps - extra_bit :]
    
    df = df2
    df[CARLA_TIME_KEY] = df["time"]
    df["timestamp"] = df[CARLA_TIME_KEY]
    df["out_of_bounds"] = 0
    df["out_of_bounds_distances"] = 0
    df["lateral_distances"] = 0
    
    df.to_parquet(csim_data_parent_folder / "trial_dirs"/ f"P604-trial_{trial_idx}"/ f"P604-trial_{trial_idx}_with_all_annotations_and_metrics_with_instruction_category.parquet")
    with open(csim_data_parent_folder / "trial_dirs"/ f"P604-trial_{trial_idx}"/ f"P604-trial_{trial_idx}_with_all_annotations_and_metrics_with_instruction_category.yaml", "w") as fp:
        yaml.dump(
            {"additional_metrics_dict": {}, "source_lap_type": "coaching_lap"}, fp
        )
    
    start += hop_size
    end += hop_size
    trial_idx += 1
    print(f'Extracting trial {trial_idx}')
#%% md
## Create snippest from parquets
#%%


for i in range(num_files_in_subfolders[0]):
    post_swap_path = csim_data_folder / "post_flip" / f"P604-trial_{i}" / f"P604-trial_{i}_with_all_annotations_and_metrics_with_instruction_category.parquet"
    df = pd.read_parquet(post_swap_path)
    df.loc[: , 'time'] = t_list[:1200]
    df[CARLA_TIME_KEY] = df["time"]
    df["timestamp"] = df[CARLA_TIME_KEY]
    df["out_of_bounds"] = 0
    df["out_of_bounds_distances"] = 0
    df["lateral_distances"] = 0
    rename_vehicle_df(df)
    # df[CARLA_TIME_KEY] = df["time"]
    # df["timestamp"] = df[CARLA_TIME_KEY]
    # df["out_of_bounds"] = 0
    # df["out_of_bounds_distances"] = 0
    # df["lateral_distances"] = 0
    print(df.shape)
    df.to_parquet(post_swap_path)
    # break
#%%
from aic_bsd_model.datasets.data_scripts import (
    aic_24d05_concurrent_feedback_trajectory_snipper,
    aic_24d05_laps_to_map_segments_snipper,
)
aic_24d05_laps_to_map_segments_snipper.get_trial_type = (
    lambda *args, **kwargs: "coaching_lap"
)
aic_24d05_concurrent_feedback_trajectory_snipper.get_trial_type = (
    lambda *args, **kwargs: "coaching_lap"
)
cf_snipping_args = [
            "--trials-dir",
            # f"{csim_data_parent_folder}/trial_dirs/",
            f"{csim_data_folder}/post_flip/",
            # f"{csim_data_parent_folder}/unflipped_trial_dirs/",
            "--trajectory-snippets-dir",
            f"{csim_data_parent_folder}/fake_snippet_dir",
            
            # "--trajectory-snippet-len-s",
            # 8
        ]
aic_24d05_concurrent_feedback_trajectory_snipper.main(
                    cf_snipping_args
                )
# at this point fomr each of the parquest 1 snippet was extracted. this is because each parquet itself is only 1200 samples
#%%
# Now plot the first second of each snippet and make a nice lap

snippets_dir = Path(f"{csim_data_parent_folder}/fake_snippet_dir")
num = 0

for i in range(42):
    with open(snippets_dir/f'P604-trial_{i}_snippet_1' /'snippet.pkl', 'rb') as fp:
        a = pickle.load(fp)
    
    snippet_df = a['df']
    plt.scatter(snippet_df['ego_x'].values[1:120], snippet_df['ego_y'].values[1:120])
    print(snippet_df['ego_x'].values[0], snippet_df['ego_x'].values[120])
    # print(snippet_df.keys(), snippet_df.shape)
    

plt.show()
    
#%% md
### now create a cf dataset class with all these snippets
#%%
TEACHER_ACTION_FUTURE_SEQ_LEN_IN_SEC =1
CATEGORY_INDEX_TYPE = 'no_steering_no_turn'
args = {}
pids = ["P604"]
dataset_cls = AIC24D05CFDataset
args["trajectory_snippets_dir"] = Path(f"{csim_data_parent_folder}/fake_snippet_dir").expanduser()
args["test_trajectory_snippets_uids_to_be_considered"] = pids
args[
    "teacher_action_future_seq_len_in_sec"
] = TEACHER_ACTION_FUTURE_SEQ_LEN_IN_SEC
if CATEGORY_INDEX_TYPE == "no_steering_no_turn":
    args[
        "teacher_action_num_categories"
    ] = 8  # should not be here, because config dict should deal with this.
elif CATEGORY_INDEX_TYPE == "no_steering_no_turn_no_brake":
    args["teacher_action_num_categories"] = 6

data_config = {
    "map_file": map_file,
    "test_pids_to_be_considered": pids,
    "use_multiprocessing": False,
    "padding_value": -2000,
    "padding_side": "right",
    "return_cf_for_lap": False,  # should the cf feedback be returned as part of the lap
    "disable_tqdm": True,
    **args,
}
#%%
def dummy_func():
    return
    
dataset = dataset_cls(data_config, "test")
dataset.metric_keys = {}
dataset._prepare_dataset_stats = dummy_func
dataset.get_cf_instruction_class_series = (
    lambda snippet_df, subsampled_idxs_dict: (
        None,
        np.ones(len(subsampled_idxs_dict["subsampled_idxs_int"]), dtype=int),
    )
)
dataset.init()
len(dataset)
#%% md
# now iterate over the dataset and get stats on features after normalization etc. 
#%%
# FEATURE_TO_DF_KEY_NO_ACCEL
# for d in dataset

stub_metrics = torch.ones(1)

traj_feature_time_series_list = collections.defaultdict(list)
snippet_id_to_ds_idx_dict = {}
for ds_idx, cf_data in enumerate(dataset):
    snippet_lap_uid = cf_data['snippet_lap_uid']
    num = int(snippet_lap_uid[snippet_lap_uid.index('_')+1:])
    snippet_id_to_ds_idx_dict[num] = ds_idx

for snippet_num in range(len(dataset)):
    ds_idx = snippet_id_to_ds_idx_dict[snippet_num]
    cf_data = dataset[ds_idx]
    print(cf_data['snippet_lap_uid'])
    cf_data.pop("snippet_cf_instruction_class_series", None)
    cf_data["snippet_cf_instruction_class_series_subsampled"] = stub_metrics
    cf_collator = CFSnippetCollator()
    cf_data = cf_collator([cf_data])
    # cf_data["side_channel_lap_representation"] = lap_model_outputs["outputs"][
    #     "side_channel_lap_representation"
    # ]
    cf_data["curr_lap_masks"] = stub_metrics[None, :]
    cf_data["metrics"] = {
        "trial_time": stub_metrics[None, :],
        "trial_time2": stub_metrics[None, :],
        "trial_time-pred": stub_metrics[None, :],
        "trial_time2-pred": stub_metrics[None, :],
    }
    cf_data["cf_metrics"] = {
        "racing_line_score_abs_mean": stub_metrics[None, :],
        "smoothness_score_mean": stub_metrics[None, :],
        "racing_line_score_abs_mean-pred": stub_metrics[None, :],
        "smoothness_score_mean-pred": stub_metrics[None, :],
    }

    for f_idx, f_name in FEATURE_TO_DF_KEY_NO_ACCEL.items():
        traj_feature_time_series_list[f_name].append(cf_data['snippet_trajectory_inputs_subsampled'][0, :, f_idx].numpy().tolist())
# cf_data['snippet_trajectory_inputs_subsampled'].shape
#%% md
## Load model and run inference on each dataset item and look at prediction
### Run the test_csim_inference script in the main code. 


#%%
# traj_a = [(-2,1), (-1, 2), (0, 2), (-1,1), (0, 1), (0,0), (1,0)]
# traj_b = [(-2,2), (-1, 2), (-2, 1), (-1, 1), (-1, 0), (0, 0), (1, 0)]

# snippet_a_x = [a[0] for a in traj_a]
# snippet_a_y = [a[1] for a in traj_a]
# snippet_b_x = [a[0] for a in traj_b]
# snippet_b_y = [a[1] for a in traj_b]

# all_snippet_x_in_order = [snippet_a_x, snippet_b_x]
# all_snippet_y_in_order = [snippet_a_y, snippet_b_y]
# # print(all_snippet_x_in_order)
# # print(all_snippet_y_in_order)
# offset_x = 0 
# offset_y = 0
# for snippet_i, (x_list, y_list) in enumerate(zip(reversed(all_snippet_x_in_order), reversed(all_snippet_y_in_order))):
#     xvals = np.array(x_list[-3:-1])
#     yvals = np.array(y_list[-3:-1])
#     print(xvals, yvals)
#     plt.scatter(xvals + offset_x, yvals + offset_y)
#     print(xvals + offset_x, yvals + offset_y)
#     # plt.show()
#     offset_x = xvals[0]
#     offset_y = yvals[0]
#     print(offset_x, offset_y)
# plt.show()
#%%
all_snippet_x_in_order
#%%
all_snippet_x_list_in_order = traj_feature_time_series_list['ego_x']
all_snippet_y_list_in_order = traj_feature_time_series_list['ego_y']

offset_x = 0
offset_y = 0
for snippet_i, (x_list, y_list) in enumerate(zip(reversed(all_snippet_x_list_in_order), reversed(all_snippet_y_list_in_order))):
    # print(x_list[0])
    xvals = np.array(x_list[-10:-4])
    yvals = np.array(y_list[-10:-4])
    # print(xvals, yvals)
    plt.scatter(xvals + offset_x, yvals+offset_y)
    offset_x = xvals[0]
    offset_y = yvals[0]
plt.show()
# for snippet_i, (x_list, y_list) in enumerate(zip(all_snippet_x_list_in_order, all_snippet_y_list_in_order)):
    
#     if snippet_i > 0:
#         offset_x = x_list[:25][-5]
#         offset_y = y_list[:25][-5]
#     else:
#         offset_x = 0
#         offset_y = 0
#     xvals = np.array(x_list[5:10])
#     yvals = np.array(y_list[5:10])
#     print(xvals - offset_x)
#     plt.plot(xvals - offset_x, yvals-offset_y)
# plt.show()
#%%
x_list[:25][-5]
#%%
cf_data.keys()
cf_data['snippet_lap_uid']
#%%
plt.plot(np.vstack(traj_feature_time_series_list['ego_x'])[:, :30].T); plt.show()
#%%
df = lap_df[2000:3200]
#%%
plt.scatter(map_data['inner_edge/x'], map_data['inner_edge/y'], s=1)
plt.scatter(map_data['outer_edge/x'], map_data['outer_edge/y'], s=1)
plt.scatter(snippet_df_from_lap['ego_x'], df['ego_y'], s=1)
#%%

#%%
# now eplicate the steps in the online inerence

full_timesteps =1200
future_timesteps = 600
past_timesteps = full_timesteps - future_timesteps
df2 = df[-full_timesteps:].copy()
print(df2.keys())

plt.plot(df2['time'].values); plt.title('time');plt.show()
for feature_key in FEATURE_TO_DF_KEY_NO_ACCEL.values():
    plt.plot(df2[feature_key].values); plt.title(feature_key); plt.show()
# copy the recent 5s into the first part of the df. Don't copy the "time" column which is at position 1.

# df2.iloc[: past_timesteps + 120, 1:] = df2.iloc[-past_timesteps - 120 :, 1:]
# # To test the correctness of the data shift
# # np.all(df2.iloc[0:past_timesteps, 1:].values ==  df.iloc[-future_timesteps:, 1:].values)

# df = df2
#%%
for feature_key in FEATURE_TO_DF_KEY_NO_ACCEL.values():
    
    print(feature_key)
    df2[feature_key].values[:past_timesteps + 120] = df2[feature_key].values[-past_timesteps - 120 :]

plt.plot(df2['time'].values); plt.title('time');plt.show()
for feature_key in FEATURE_TO_DF_KEY_NO_ACCEL.values():
    plt.plot(df2[feature_key].values); plt.title(feature_key); plt.show()
#%%
df = df2
plt.plot(df['time'].values); plt.title('time');plt.show()
for feature_key in FEATURE_TO_DF_KEY_NO_ACCEL.values():
    plt.plot(df[feature_key].values); plt.title(feature_key); plt.show()
#%%
df[CARLA_TIME_KEY] = df["time"]
df["timestamp"] = df[CARLA_TIME_KEY]
df["out_of_bounds"] = 0
df["out_of_bounds_distances"] = 0
df["lateral_distances"] = 0
df.shape
#%%
df.to_parquet(csim_data_parent_folder / "P604-trial_2"/ "P604-trial_2_with_all_annotations_and_metrics_with_instruction_category.parquet")
with open(csim_data_parent_folder / "P604-trial_2"/ "P604-trial_2_with_all_annotations_and_metrics_with_instruction_category.yaml", "w") as fp:
    yaml.dump(
        {"additional_metrics_dict": {}, "source_lap_type": "coaching_lap"}, fp
    )
#%%
aic_24d05_concurrent_feedback_trajectory_snipper.calculate_sampling_freq(df)

# math.ceil(120.0 * 10)
#%%
with open("/home/<USER>/Data/24-D-05/csim_data_log/P604-trial_2/fake_snippet_dir/P604-trial_2_snippet_1/snippet.pkl", 'rb') as fp:
    snippet = pickle.load(fp)
snippet.keys()
snippet['metadata']
#%%
snippet_df = snippet['df']
for feature_key in FEATURE_TO_DF_KEY_NO_ACCEL.values():
    plt.plot(snippet_df[feature_key].values); plt.title(feature_key); plt.show()
plt.plot(snippet_df['timestamp'].values); plt.title('timestamp'); plt.show()
#%%
TEACHER_ACTION_FUTURE_SEQ_LEN_IN_SEC =1
CATEGORY_INDEX_TYPE = 'no_steering_no_turn'
args = {}
pids = ["P604"]
dataset_cls = AIC24D05CFDataset
args["trajectory_snippets_dir"] = Path("~/Data/24-D-05/csim_data_log/P604-trial_2/fake_snippet_dir/").expanduser()
args["test_trajectory_snippets_uids_to_be_considered"] = pids
args[
    "teacher_action_future_seq_len_in_sec"
] = TEACHER_ACTION_FUTURE_SEQ_LEN_IN_SEC
if CATEGORY_INDEX_TYPE == "no_steering_no_turn":
    args[
        "teacher_action_num_categories"
    ] = 8  # should not be here, because config dict should deal with this.
elif CATEGORY_INDEX_TYPE == "no_steering_no_turn_no_brake":
    args["teacher_action_num_categories"] = 6

data_config = {
    "map_file": map_file,
    "test_pids_to_be_considered": pids,
    "use_multiprocessing": False,
    "padding_value": -2000,
    "padding_side": "right",
    "return_cf_for_lap": False,  # should the cf feedback be returned as part of the lap
    "disable_tqdm": True,
    **args,
}
#%%
data_config
#%%
def dummy_func():
    return
    
dataset = dataset_cls(data_config, "test")
dataset.metric_keys = {}
dataset._prepare_dataset_stats = dummy_func
dataset.get_cf_instruction_class_series = (
    lambda snippet_df, subsampled_idxs_dict: (
        None,
        np.ones(len(subsampled_idxs_dict["subsampled_idxs_int"]), dtype=int),
    )
)
#%%
dataset.init()
#%%
len(dataset)
#%%
stub_metrics = torch.ones(1)
cf_data = dataset.__getitem__(0)
cf_data.pop("snippet_cf_instruction_class_series", None)
cf_data["snippet_cf_instruction_class_series_subsampled"] = stub_metrics
cf_collator = CFSnippetCollator()
cf_data = cf_collator([cf_data])
# cf_data["side_channel_lap_representation"] = lap_model_outputs["outputs"][
#     "side_channel_lap_representation"
# ]
cf_data["curr_lap_masks"] = stub_metrics[None, :]
cf_data["metrics"] = {
    "trial_time": stub_metrics[None, :],
    "trial_time2": stub_metrics[None, :],
    "trial_time-pred": stub_metrics[None, :],
    "trial_time2-pred": stub_metrics[None, :],
}
cf_data["cf_metrics"] = {
    "racing_line_score_abs_mean": stub_metrics[None, :],
    "smoothness_score_mean": stub_metrics[None, :],
    "racing_line_score_abs_mean-pred": stub_metrics[None, :],
    "smoothness_score_mean-pred": stub_metrics[None, :],
}
#%%
plt.plot(cf_data['snippet_trajectory_inputs_subsampled'][0, :, 1].numpy())
