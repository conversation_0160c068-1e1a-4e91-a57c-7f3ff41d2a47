#%%
from pathlib import Path
import matplotlib.pyplot as plt
import collections
import torch
import random
import numpy as np
import pandas as pd

from scipy.spatial.distance import cdist
from torch.utils.data import DataLoader
from scipy.spatial.transform import Rotation as R
import plotly.graph_objects as go
from matplotlib import cm
import matplotlib
from sklearn.model_selection import train_test_split
from aic_bsd_model.datasets.aic_24d16_dataset import AIC24D16LapDataset
from aic_bsd_model.datasets.data_collators import TrackSegmentCollator
from aic_bsd_model.datasets.aic_bsd_dataset_utils import open_pkl, TRAJ_FEATURE_LINEAR_SCALE, DEMO_TRIAL_PER_SUBJECT_24_D_05, INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16, get_pid_and_trial_num_from_uid, TrajFeatureIndex, TrajF<PERSON>ure<PERSON>ndex<PERSON><PERSON><PERSON><PERSON><PERSON>, FEATURE_TO_DF_KEY, FEATURE_TO_DF_KEY_NO_ACCEL
#%%
CONES_COORDINATES = [
    [-682, -2, 1.7],  # end of straightaway
    [-700, -144, 4.4],  # turn 1 right
    [-615, -239, 12.0],  # turn 2 left
    [-912, -117, -1.48],  # turn 3 left
    [-938, -72, -1.65],  # turn 3 right
    [-974, 80, -3.9],  # turn 4 left
    [-935, 109, -2.6],  # turn 4 right
    [-990, 2, -4.1],  # turn 4 brake zone
    [-991.5, 6, -4.2],  # turn 4 brake zone
    [-855, 171, -2.5],  # turn 5 right
    [-835, 256, -5.5],  # turn 5 left
    [-834, 330, -7.6],  # turn 5 right 2
    [-849, 518, -10.7],  # turn 6 left
    [-767, 569, -10.6],  # turn 6 right
    [-615, 587, -11.8],  # turn 7 left
    [-682, 611.5, -13.0],  # turn 7 brake zone
    [-684, 611.5, -13.0],  # turn 7 brake zone
    [-673, 535, -10.67],  # turn 8 right straight
    [-773, 420, -5.3],  # turn 8 right apex
    [-747, 289, -5.4],  # turn 9 right
    [-779, 335, -6.7],  # turn 9 brake zone
    [-780, 338, -6.7],  # turn 9 brake zone
    [-645, 365, -4.45],  # turn 10 left
]
CONES_COORDINATES_CARLA = [(i, -j, k) for (i,j,k) in CONES_COORDINATES]
#%%

# add parse args or something so that paths can be passed as args. eventually unify it with draccuss or whatever AS is using for IFM
trajectory_segments_dir = "~/Data/24-D-05/trajectory_segments_map_seg_ids/"
trials_directory = "~/Data/24-D-05/trials_final/"
trajectory_segments_dir = Path.expanduser(Path(trajectory_segments_dir))
trials_directory = Path.expanduser(Path(trials_directory))
map_file = trajectory_segments_dir / "track.csv"
with open(map_file, "r") as f:
    map_data = pd.read_csv(f)
#%%
config_dict = {}
config_dict['trajectory_segments_dir'] = trajectory_segments_dir
config_dict['trials_directory'] = trials_directory
config_dict['map_file'] = map_file
config_dict['debug_with_one_trial'] = False

#%%
collator = TrackSegmentCollator(padding_value=-2000)
all_pids_list = [pid for pid in list(DEMO_TRIAL_PER_SUBJECT_24_D_05.keys())]
# random.shuffle(all_pids_list)
train_pids_list = all_pids_list
config_dict['train_pids_to_be_considered'] = train_pids_list

# use 24D16 on 24D05 dataset so that only laps need t be parsed.
train_24d05_dataset = AIC24D16LapDataset(config_dict, dataset_type='train')
# test_24d16_dataset = AIC24D16LapDataset(trajectory_segments_dir, trials_directory, map_file, test_pids_list)
# dataloader = DataLoader(train_24d16_dataset, batch_size=1, collate_fn=collator, shuffle=False)
num_segments = 10

#%%
vis_folder = Path('/home/<USER>/Data/24-D-05/Visualizations/pytorch_seg_visualizations_scaled')

Path.mkdir(vis_folder, parents=True, exist_ok=True)
for k in DEMO_TRIAL_PER_SUBJECT_24_D_05.keys():
    Path.mkdir(Path(vis_folder) / Path(k),   parents=True, exist_ok=True)
#%%
len(train_24d05_dataset)
#%%
dataloader = DataLoader(train_24d05_dataset, batch_size=1, collate_fn=collator, shuffle=False)
assert dataloader.batch_size == 1
# all_metric_lists = collections.defaultdict(list)

counter = 0
# for batch in dataloader:
#     print(batch['curr_lap_uid'])
    
#     # counter+=1
#     for mk, subkey_list in train_24d05_dataset.metric_keys.items():
#         for subkey in subkey_list:
#             prefix_mk_key = f"curr_{mk}" if subkey is None else f"curr_{mk}_{subkey}"
#             all_metric_lists[prefix_mk_key].append(float(batch[prefix_mk_key][0]))
    
                                                   
# print(counter)
#%%
for mk, mk_list in all_metric_lists.items():
    plt.figure()
    mk_list = np.array(mk_list)
    plt.hist(mk_list, bins=100)
    plt.title(f'{mk}')
    plt.show()
    print(np.min(mk_list), np.max(mk_list), np.mean(mk_list), np.std(mk_list))
    
    

#%% md
### Plot segment level traj after coordinate transformation and scaling. Essentially visualizing the get item of the dataset
#%%

#%%
# dataloader = DataLoader(train_24d16_dataset, batch_size=1, collate_fn=collator, shuffle=False)
# assert dataloader.batch_size == 1

all_traj_segments = []
all_map_segments = []
all_seg_i_ranges= [] 
# i = 0
for batch in dataloader:
    # print(batch.keys())
    curr_lap_uid = batch['curr_lap_uid'][0]
    print(curr_lap_uid)
    pid, trial_num = get_pid_and_trial_num_from_uid(curr_lap_uid)
    #create 
    
    trajectory_segments = batch["curr_trajectory_segments"]
    trajectory_attention_masks = batch["curr_trajectory_attention_masks"]
    map_segments = batch["curr_map_segments"]
    map_attention_masks = batch["curr_map_attention_masks"]
    for i in range(num_segments):
        traj_i_segments = trajectory_segments[:, i:i+1, :, :]
        traj_i_attention_mask = trajectory_attention_masks[:, i:i+1, :]
        map_i_segments = map_segments[:, i:i+1, :, :]
        map_i_attention_mask = map_attention_masks[:, i:i+1, :]
        min_z_i = float(torch.min(traj_i_segments[traj_i_attention_mask.bool()][:, TrajFeatureIndexNoAccel.TRAJ_Z]))
        max_z_i = float(torch.max(traj_i_segments[traj_i_attention_mask.bool()][:, TrajFeatureIndexNoAccel.TRAJ_Z]))
        all_seg_i_ranges.append((curr_lap_uid, i+1, max_z_i - min_z_i))
    
    
    num_columns = 2
    fig, axes = plt.subplots(5, num_columns, figsize=(15, 15), subplot_kw={'projection': '3d'})
    for i in range(0, num_segments):
        row_idx = i // num_columns
        col_idx =  i % num_columns
        traj_i_segments = trajectory_segments[:, i:i+1, :, :]
        traj_i_attention_mask = trajectory_attention_masks[:, i:i+1, :]
        map_i_segments = map_segments[:, i:i+1, :, :]
        map_i_attention_mask = map_attention_masks[:, i:i+1, :]
        
        # if float(torch.min(traj_i_segments[traj_i_attention_mask.bool()][:, 2])) < -90:
            
        min_z_i = float(torch.min(traj_i_segments[traj_i_attention_mask.bool()][:, TrajFeatureIndexNoAccel.TRAJ_Z])) 
        max_z_i = float(torch.max(traj_i_segments[traj_i_attention_mask.bool()][:, TrajFeatureIndexNoAccel.TRAJ_Z])) 
        last_z_i  = float(torch.min(traj_i_segments[traj_i_attention_mask.bool()][-1, TrajFeatureIndexNoAccel.TRAJ_Z])) 
        
        # if i == 5:
        # print(i)
        # print(traj_i_segments[traj_i_attention_mask.bool()][:, 2].shape)
        car_x = traj_i_segments[traj_i_attention_mask.bool()][:, TrajFeatureIndexNoAccel.TRAJ_X].numpy()
        car_y = traj_i_segments[traj_i_attention_mask.bool()][:, TrajFeatureIndexNoAccel.TRAJ_Y].numpy()
        car_z = traj_i_segments[traj_i_attention_mask.bool()][:, TrajFeatureIndexNoAccel.TRAJ_Z].numpy()
        # car_z = (car_z - np.min(car_z))/(np.max(car_z) - np.min(car_z))
        
        map_inner_x = map_i_segments[map_i_attention_mask.bool()][:, 0].numpy()
        map_inner_y = map_i_segments[map_i_attention_mask.bool()][:, 1].numpy()
        map_outer_x = map_i_segments[map_i_attention_mask.bool()][:, 2].numpy()
        map_outer_y = map_i_segments[map_i_attention_mask.bool()][:, 3].numpy()
        
        # for p_i in range(len(car_x)):
        #     axes[row_idx, col_idx].scatter(car_x[p_i], car_y[p_i], color='k', alpha=car_z[p_i])
        axes[row_idx, col_idx].scatter(car_x, car_y, car_z, color='k', s=1)
        # Put special colors for start and end of the trajectory
        axes[row_idx, col_idx].scatter(car_x[0], car_y[0], car_z[0],  color='r')
        axes[row_idx, col_idx].scatter(car_x[-1], car_y[-1], car_z[-1], color='g')
        axes[row_idx, col_idx].scatter(map_inner_x, map_inner_y, color='m', s=1)
        axes[row_idx, col_idx].scatter(map_outer_x, map_outer_y, color='b', s=1)
        axes[row_idx, col_idx].set_title(f'Seg ID - {i+1}, Min Z - {min_z_i:.3f}, Max Z - {max_z_i:.3f}, Last pt = {car_x[-1]:0.2f}, {car_y[-1]:0.2f}, {last_z_i:0.2f}', fontsize=8)
        axes[row_idx, col_idx].set_xlabel('Relative X (m)')
        axes[row_idx, col_idx].set_ylabel('Relative Y (m)')
        axes[row_idx, col_idx].set_zlabel('Relative Z (m)')
        axes[row_idx, col_idx].set_aspect('equal')
    filename = vis_folder / Path(pid) / Path(f'trial_num_{trial_num}.png')
    plt.tight_layout()
    plt.savefig(filename)
    plt.close()
            # break
        
    #     break
    all_traj_segments.append(trajectory_segments[trajectory_attention_masks.bool()])
    all_map_segments.append(map_segments[map_attention_masks.bool()])

    
    # break
#%% md
### Plot full trajectories with orientation in 3d and save html using plotly for interactive inspection. Plot map and cones as well.  
#%%
vis_folder = Path('/home/<USER>/Data/24-D-05/Visualizations/pytorch_subsampled_trajectory_full_trial')
Path.mkdir(vis_folder, parents=True, exist_ok=True)
for k in DEMO_TRIAL_PER_SUBJECT_24_D_05.keys():
    Path.mkdir(Path(vis_folder) / Path(k),   parents=True, exist_ok=True)
    
track_xy = map_data[['refline/x', 'refline/y']].values #N, 2
for valid_lap_uid in train_24d05_dataset.valid_lap_list:
    print(valid_lap_uid)
    pid, trial_num = get_pid_and_trial_num_from_uid(valid_lap_uid)
    segments_dict_for_uid = train_24d05_dataset.trajectory_segments_pkl_paths_according_to_uid[valid_lap_uid]
    all_subsampled_trajectories = []

    # Use a colormap to dynamically generate colors for each chunk
    # colormap = cm.get_cmap('jet', num_segments)  # 'viridis' is the chosen color map
    colormap = matplotlib.colormaps['viridis']
    colors = [f"rgb({r*255}, {g*255}, {b*255})" for r, g, b, _ in colormap(np.linspace(0, 1, num_segments))]
    # Create a 3D scatter plot
    fig = go.Figure()
    for seg_id in range(0, num_segments):
        # print(seg_id)
        seg_path = segments_dict_for_uid[f'segment_{seg_id+1}']
        seg_snippet_dict = open_pkl(seg_path)
        _, subsampled_trajectory = train_24d05_dataset.extract_trajectory_data(seg_snippet_dict)
        
        all_subsampled_trajectories.append(subsampled_trajectory)
        ego_x_i = subsampled_trajectory[:, TrajFeatureIndex.TRAJ_X]
        ego_y_i = subsampled_trajectory[:, TrajFeatureIndex.TRAJ_Y]
        ego_z_i = subsampled_trajectory[:, TrajFeatureIndex.TRAJ_Z]
        fig.add_trace(go.Scatter3d(
            x=ego_x_i,
            y=ego_y_i,
            z=ego_z_i,
            mode='markers',
            marker=dict(
                size=4,
                color=colors[seg_id],  # Use color from the colormap
            ),
            name=f"Seg {seg_id + 1}"  # Legend entry for the chunk
        ))
        #plot rotation 
        scale = 10
        for s_len in range(0, subsampled_trajectory.shape[0], 5):
            # print(s_len)
            R_gb = np.matrix(R.from_quat(subsampled_trajectory[s_len, -4:]).as_matrix())
            p_gb = subsampled_trajectory[s_len][:3]

            # # create transformation matrix. body frame of normalization time step with respect to global
            T_gb = np.matrix(
                [
                    np.concatenate((np.array(R_gb[0, :]).flatten(), p_gb[0:1])),
                    np.concatenate((np.array(R_gb[1, :]).flatten(), p_gb[1:2])),
                    np.concatenate((np.array(R_gb[2, :]).flatten(), p_gb[2:])),
                    np.array([0, 0, 0, 1]),
                ]
            ) #np.matrix (4,4)
            # points of body coordinate axis in bodyframe
            s_b_x = np.array([2*scale, 0, 0, 1]).reshape(-1, 1)
            s_b_y = np.array([0, 2*scale, 0, 1]).reshape(-1, 1)
            s_b_z = np.array([0, 0, 0.3*scale, 1]).reshape(-1, 1)
            # print(s_p.shape, R_gb.shape)
            # points of body coordinate axis in global frae
            s_g_x = (T_gb * s_b_x)
            s_g_y = (T_gb * s_b_y)
            s_g_z = (T_gb * s_b_z)
            
            #plot x axis of body frame coordinate axis
            fig.add_trace(go.Scatter3d(
                x=[subsampled_trajectory[s_len, 0], np.array(s_g_x)[0][0]],  # X-coordinates
                y=[subsampled_trajectory[s_len, 1], np.array(s_g_x)[1][0]],  # Y-coordinates
                z=[subsampled_trajectory[s_len, 2], np.array(s_g_x)[2][0]],  # Z-coordinates
                mode='lines',  # Line mode
                line=dict(
                    color='red',  # Line color
                    width=5        # Line width
                ),
            ))
            #plot x axis of body frame coordinate axis
            fig.add_trace(go.Scatter3d(
                x=[subsampled_trajectory[s_len, 0], np.array(s_g_y)[0][0]],  # X-coordinates
                y=[subsampled_trajectory[s_len, 1], np.array(s_g_y)[1][0]],  # Y-coordinates
                z=[subsampled_trajectory[s_len, 2], np.array(s_g_y)[2][0]],  # Z-coordinates
                mode='lines',  # Line mode
                line=dict(
                    color='green',  # Line color
                    width=5        # Line width
                ),
            ))
            #plot y axis of body frame coordinate axis
            fig.add_trace(go.Scatter3d(
                x=[subsampled_trajectory[s_len, 0], np.array(s_g_z)[0][0]],  # X-coordinates
                y=[subsampled_trajectory[s_len, 1], np.array(s_g_z)[1][0]],  # Y-coordinates
                z=[subsampled_trajectory[s_len, 2], np.array(s_g_z)[2][0]],  # Z-coordinates
                mode='lines',  # Line mode
                line=dict(
                    color='blue',  # Line color
                    width=5        # Line width
                ),

            ))

    #draw map after computing nearest xy point and grabbing the z coordinate from that. 
    all_subsampled_trajectories =  np.vstack(all_subsampled_trajectories)
    pairwise_dist = cdist(all_subsampled_trajectories[:, :2], track_xy)  # (M, N)
    min_dist_traj = np.argmin(pairwise_dist, axis=0)
    map_z = all_subsampled_trajectories[min_dist_traj, TrajFeatureIndex.TRAJ_Z]
    
    i_x = map_data['inner_edge/x'].values
    i_y = map_data['inner_edge/y'].values
    
    o_x = map_data['outer_edge/x'].values
    o_y = map_data['outer_edge/y'].values
    num_skip_map_pt = 4
    fig.add_trace(go.Scatter3d(
        x=i_x[::num_skip_map_pt],
        y=i_y[::num_skip_map_pt],
        z=map_z[::num_skip_map_pt],
        mode='markers',
        marker=dict(
            size=2.3,
            color='black',  # Use color from the colormap
            opacity=0.6
        ),
    ))
    fig.add_trace(go.Scatter3d(
        x=o_x[::num_skip_map_pt],
        y=o_y[::num_skip_map_pt],
        z=map_z[::num_skip_map_pt],
        mode='markers',
        marker=dict(
            size=2.3,
            color='black',  # Use color from the colormap
            opacity=0.6
        ),
    ))
    #draw cones
    fig.add_trace(go.Scatter3d(
        x=[i[0] for i in CONES_COORDINATES_CARLA],
        y=[i[1] for i in CONES_COORDINATES_CARLA],
        z=[i[2] for i in CONES_COORDINATES_CARLA],
        mode='markers',
        marker=dict(
            size=10,
            color='blue',  # Use color from the colormap
        ),
    ))
    
    # Add title and axis labels
    fig.update_layout(
        title=f"{pid}_trial{trial_num}",
        showlegend=False, 
        scene=dict(
        xaxis=dict(
            title="X Axis (m)",
            showgrid=True,  # Enable gridlines on the X-axis
            gridcolor="lightgrey",  # Gridline color
            gridwidth=1  # Gridline width
        ),
        yaxis=dict(
            title="Y Axis (m)",
            showgrid=True,  # Enable gridlines on the Y-axis
            gridcolor="lightgrey",
            gridwidth=1
        ),
        zaxis=dict(
            title="Z Axis (m)",
            showgrid=True,  # Enable gridlines on the Z-axis
            gridcolor="lightgrey",
            gridwidth=1,
            range=[-40, 40]
        )
    ),
    )
    # Save as HTML
    filename = vis_folder / Path(pid) / Path(f'trial_num_{trial_num}.html')
    fig.write_html(filename)
    
    
    
    
    
#%%
filename
