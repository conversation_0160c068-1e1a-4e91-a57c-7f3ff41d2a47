# script to segment 24D05 trials into 10 segments

import pickle
from pathlib import Path

from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    DEMO_TRIAL_PER_SUBJECT_24_D_05,
    calculate_sampling_freq,
    extract_uid_from_path,
    get_pid_and_trial_num_from_uid,
    get_total_trials_per_subject,
    open_trial,
    parse_arguments,
)


def get_trial_type(trial_num, last_trial_idx, demo_trial_idx):
    if trial_num == 0:
        snippet_type = "sight_lap"
    elif trial_num == 1 or trial_num == 2:
        snippet_type = "baseline"
    elif trial_num == last_trial_idx or trial_num == (last_trial_idx - 1):
        snippet_type = "retention"
    elif demo_trial_idx is not None and trial_num == demo_trial_idx:
        snippet_type = "demo_lap"
    else:
        snippet_type = "coaching_lap"

    return snippet_type


def get_previous_coaching_trial_num(
    trial_num, snippet_type, last_trial_idx, demo_trial_idx
):
    """
    Each subject study started with the sight lap followed by two baseline laps.
    The coaching laps started right after the base line laps. For all subjects, except one, <PERSON>
    provided an additional demo lap in between the coaching. After the last coaching lap, the subjects
    performed the retention laps.
    There was never a situation in which 1) a demo_lap immediately followed the base line lap or 2) a demo
    lap immediately preceded the retention laps.
    """
    if snippet_type == "sight_lap" or snippet_type == "baseline":
        return -1
    elif snippet_type == "coaching_lap":
        # includes the case in which it is the first coaching lap, the previous lap considered would be the second baseline lap,
        # bsd that happens after the second baseline and before the first coaching lap is important.
        # if previous lap is a demo lap, then consider the lap before that to be the valid coaching lap
        return (
            trial_num - 2
            if get_trial_type(trial_num - 1, last_trial_idx, demo_trial_idx)
            == "demo_lap"
            else trial_num - 1
        )
    elif (
        snippet_type == "demo_lap"
    ):  # assumes that the demo lap never happened right after the baseline laps.
        return trial_num - 1
    elif snippet_type == "retention":
        # assumes that the "demo_lap" never happened right before the retention block.
        return (trial_num - 2) if trial_num == last_trial_idx else (trial_num - 1)


def get_prev_and_next_valid_lap_trial_num(
    pid, trial_num, demo_trial_idx, total_trials_per_subject
):
    first_trial_idx = 0
    valid_next_lap_trial_num = -1
    last_trial_idx = total_trials_per_subject[pid] - 1
    if (
        trial_num == first_trial_idx or trial_num == first_trial_idx + 1
    ):  # Sight lap and 1st baseline lap
        valid_prev_lap_trial_num = -1
    elif trial_num == last_trial_idx:  # last retention lap
        valid_next_lap_trial_num = -1

    # if not first trial
    if trial_num > first_trial_idx + 1:
        valid_prev_lap_trial_num = trial_num
        while valid_prev_lap_trial_num >= first_trial_idx + 1:
            valid_prev_lap_trial_num -= 1
            if demo_trial_idx is None or valid_prev_lap_trial_num != demo_trial_idx:
                break

        # after the while loop the proper valid_prev_lap_trial_num should be set
        assert valid_prev_lap_trial_num != trial_num

    # if not last trial
    if trial_num < last_trial_idx:
        valid_next_lap_trial_num = trial_num
        while valid_next_lap_trial_num <= last_trial_idx:
            valid_next_lap_trial_num += 1
            if demo_trial_idx is None or valid_next_lap_trial_num != demo_trial_idx:
                break

        # after the while loop the proper valid_next_lap_trial_num should be set
        assert valid_next_lap_trial_num != trial_num

    return valid_prev_lap_trial_num, valid_next_lap_trial_num


def make_metadata(
    uid, pid, trial_num, map_seg_id, demo_trial_idx, total_trials_per_subject
):
    first_trial_idx = 0
    last_trial_idx = total_trials_per_subject[pid] - 1
    # each uid is PXXX-trial_N

    # add info if snippet comes from  sight lap (0), baseline (1, 2), retention (N, N-1), or Jon Gomes demo
    snippet_type = get_trial_type(trial_num, last_trial_idx, demo_trial_idx)
    prev_coaching_trial_num = get_previous_coaching_trial_num(
        trial_num,
        snippet_type,
        last_trial_idx=last_trial_idx,
        demo_trial_idx=demo_trial_idx,
    )
    (
        valid_prev_lap_trial_num,
        valid_next_lap_trial_num,
    ) = get_prev_and_next_valid_lap_trial_num(
        pid, trial_num, demo_trial_idx, total_trials_per_subject
    )

    return {
        "uid": uid,
        "prev_coaching_trial_num": prev_coaching_trial_num,
        "map_seg_id": map_seg_id,
        "is_valid": True,
        "source_lap_type": snippet_type,
        "valid_prev_lap_trial_num": valid_prev_lap_trial_num,
        "valid_next_lap_trial_num": valid_next_lap_trial_num,
    }


def main(args=None):
    args = parse_arguments(args)
    trials_dir = Path.expanduser(Path(args.trials_dir))
    segment_level_trajectory_snippets_dir = Path.expanduser(
        Path(args.segment_level_trajectory_snippets_dir)
    )

    Path.mkdir(segment_level_trajectory_snippets_dir, parents=True, exist_ok=True)
    assert trials_dir is not None
    all_trial_parquets = [
        t
        for t in Path(trials_dir).glob("**/*.parquet")
        if "_with_all_annotations_and_metrics_with_instruction_category" in str(t)
    ]
    total_trials_per_subject = get_total_trials_per_subject(all_trial_parquets)
    # dictionary containing hand coded information of which was the demo lap during the session

    for trial_parquet_path in all_trial_parquets:
        uid = extract_uid_from_path(trial_parquet_path)
        print(uid)
        pid, trial_num = get_pid_and_trial_num_from_uid(uid)
        demo_trial_idx = DEMO_TRIAL_PER_SUBJECT_24_D_05[pid]
        trial_dict = open_trial(trial_parquet_path)
        delta_ts_mean, delta_ts_std, sampling_frequency = calculate_sampling_freq(
            trial_dict["dataframe"]
        )
        unique_map_segment_ids_in_trial = list(
            set(trial_dict["dataframe"]["map_segment_ids"].values)
        )
        for map_seg_id in unique_map_segment_ids_in_trial:
            # extract df slice where the map segment id matches map_seg_id
            trial_df_map_seg_id = trial_dict["dataframe"][
                trial_dict["dataframe"]["map_segment_ids"].values == map_seg_id
            ]
            print(uid, demo_trial_idx, total_trials_per_subject[pid])
            snippet_metadata = make_metadata(
                uid,
                pid,
                trial_num,
                map_seg_id,
                demo_trial_idx,
                total_trials_per_subject,
            )
            print(f"{snippet_metadata}")
            snippet_metadata["sampling_frequency"] = sampling_frequency
            seg_level_traj_snip_dir = segment_level_trajectory_snippets_dir / Path(
                f"{uid}"
            )
            Path.mkdir(seg_level_traj_snip_dir, parents=True, exist_ok=True)
            snippet_dict = {"df": trial_df_map_seg_id, "metadata": snippet_metadata}
            with open(
                seg_level_traj_snip_dir / Path(f"segment_{map_seg_id}.pkl"), "wb"
            ) as fp:
                pickle.dump(snippet_dict, fp)

            print(
                f"Saved segment {map_seg_id} from {trial_parquet_path} at {seg_level_traj_snip_dir}"
            )
        with open(seg_level_traj_snip_dir / Path(f"metrics_dict.pkl"), "wb") as fp:
            pickle.dump(trial_dict["additional_metrics_dict"], fp)
        print(f"Saved metrics dict for {trial_parquet_path.stem}")
        print()

        if args.make_up_missing_segments:
            missing_ids = set(range(1, 11)) - set(unique_map_segment_ids_in_trial)
            for seg_id in missing_ids:
                with open(
                    seg_level_traj_snip_dir / Path(f"segment_{seg_id}.pkl"), "wb"
                ) as fp:
                    pickle.dump(snippet_dict, fp)


if __name__ == "__main__":
    main()
