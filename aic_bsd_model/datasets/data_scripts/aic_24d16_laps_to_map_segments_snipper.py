# script to segment 24D16 trials into 10 segments

import pickle
from pathlib import Path

from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16,
    calculate_sampling_freq,
    extract_uid_from_path,
    get_pid_and_trial_num_from_uid,
    get_total_trials_per_subject,
    open_trial,
    parse_arguments,
)


def make_metadata(
    uid, pid, trial_num, chunk_id, total_trials_per_subject, is_map_seg_id=True
):
    first_trial_idx = 0
    last_trial_idx = total_trials_per_subject[pid] - 1

    if trial_num == first_trial_idx:
        valid_prev_lap_trial_num = -1
    elif trial_num == last_trial_idx:  # because 0 indexed
        valid_next_lap_trial_num = -1

    # if not first trial
    if trial_num != first_trial_idx:
        valid_prev_lap_trial_num = trial_num
        while valid_prev_lap_trial_num >= first_trial_idx:
            valid_prev_lap_trial_num -= 1
            if (
                valid_prev_lap_trial_num
                not in INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16[pid]
            ):
                break
        # after the while loop the proper valid_prev_lap_trial_num should be set
        assert valid_prev_lap_trial_num != trial_num

    # if not last trial
    if trial_num != last_trial_idx:
        valid_next_lap_trial_num = trial_num
        while valid_next_lap_trial_num <= last_trial_idx:
            valid_next_lap_trial_num += 1
            if (
                valid_next_lap_trial_num
                not in INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16[pid]
            ):
                break
        # after the while loop the proper valid_next_lap_trial_num should be set
        assert valid_next_lap_trial_num != trial_num

    if is_map_seg_id:
        metadata = {
            "uid": uid,
            "is_valid": not trial_num in INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16[pid],
            "map_seg_id": chunk_id,
            "valid_prev_lap_trial_num": valid_prev_lap_trial_num,
            "valid_next_lap_trial_num": valid_next_lap_trial_num,
        }
    else:
        metadata = {
            "uid": uid,
            "is_valid": not trial_num in INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16[pid],
            "chunk_id": chunk_id,
            "valid_prev_lap_trial_num": valid_prev_lap_trial_num,
            "valid_next_lap_trial_num": valid_next_lap_trial_num,
        }
    return metadata


def chunk_list(l, M):
    n = len(l)
    chunk_size, remainder = divmod(n, M)

    chunks = []
    start = 0
    for i in range(M):
        end = start + chunk_size + (1 if i < remainder else 0)
        chunks.append(l[start:end])
        start = end

    return chunks


def main(args=None):
    args = parse_arguments(args)
    trials_dir = Path.expanduser(Path(args.trials_dir))
    segment_level_trajectory_snippets_dir = Path.expanduser(
        Path(args.segment_level_trajectory_snippets_dir)
    )

    Path.mkdir(segment_level_trajectory_snippets_dir, parents=True, exist_ok=True)
    assert trials_dir is not None
    all_trial_parquets = [
        t
        for t in Path(trials_dir).glob("**/*.parquet")
        if "_with_all_annotations_and_metrics" in str(t)
    ]

    # dictionary containing the total number of trials per subjects

    total_trials_per_subject = get_total_trials_per_subject(all_trial_parquets)
    for trial_parquet_path in all_trial_parquets:
        uid = extract_uid_from_path(trial_parquet_path)
        pid, trial_num = get_pid_and_trial_num_from_uid(uid)
        trial_dict = open_trial(trial_parquet_path)
        delta_ts_mean, delta_ts_std, sampling_frequency = calculate_sampling_freq(
            trial_dict["dataframe"]
        )
        # chunk_df_indices = chunk_list(trial_dict['dataframe'].index.values, 10)

        # # snip lap level trajectory into 10 equal length chunks.
        # # Note: The metrics computed don't match up as metrics were computed based on map segment level chunks.
        # for chunk_id, chunk_df_idx in enumerate(chunk_df_indices):
        #     trial_df_chunk =  trial_dict["dataframe"].iloc[chunk_df_idx]
        #     snippet_metadata = make_metadata(
        #         uid, pid, trial_num, chunk_id+1, total_trials_per_subject, is_map_seg_id=False
        #     )
        #     snippet_metadata["sampling_frequency"] = sampling_frequency
        #     seg_level_traj_snip_dir = segment_level_trajectory_snippets_dir / Path(
        #         f"{uid}"
        #     )
        #     Path.mkdir(seg_level_traj_snip_dir, parents=True, exist_ok=True)
        #     snippet_dict = {"df": trial_df_chunk, "metadata": snippet_metadata}
        #     with open(
        #         seg_level_traj_snip_dir / Path(f"segment_{chunk_id+1}.pkl"), "wb"
        #     ) as fp:
        #         pickle.dump(snippet_dict, fp)

        #     print(
        #         f"Saved chunk {chunk_id+1} from {trial_parquet_path} at {seg_level_traj_snip_dir}"
        #     )

        # Snip lap level trajectory into map segment level trajectories.
        unique_map_segment_ids_in_trial = list(
            set(trial_dict["dataframe"]["map_segment_ids"].values)
        )
        for map_seg_id in unique_map_segment_ids_in_trial:
            # extract df slice where the map segment id matches map_seg_id
            trial_df_map_seg_id = trial_dict["dataframe"][
                trial_dict["dataframe"]["map_segment_ids"].values == map_seg_id
            ]
            snippet_metadata = make_metadata(
                uid, pid, trial_num, map_seg_id, total_trials_per_subject
            )
            snippet_metadata["sampling_frequency"] = sampling_frequency

            seg_level_traj_snip_dir = segment_level_trajectory_snippets_dir / Path(
                f"{uid}"
            )
            Path.mkdir(seg_level_traj_snip_dir, parents=True, exist_ok=True)
            snippet_dict = {"df": trial_df_map_seg_id, "metadata": snippet_metadata}
            with open(
                seg_level_traj_snip_dir / Path(f"segment_{map_seg_id}.pkl"), "wb"
            ) as fp:
                pickle.dump(snippet_dict, fp)

            print(
                f"Saved segment {map_seg_id} from {trial_parquet_path} at {seg_level_traj_snip_dir}"
            )

        with open(seg_level_traj_snip_dir / Path(f"metrics_dict.pkl"), "wb") as fp:
            pickle.dump(trial_dict["additional_metrics_dict"], fp)

        print(f"Saved metrics dict for {trial_parquet_path.stem}")
        print()


if __name__ == "__main__":
    main()
