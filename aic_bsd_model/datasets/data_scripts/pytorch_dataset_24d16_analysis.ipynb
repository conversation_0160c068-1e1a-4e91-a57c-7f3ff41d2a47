#%% md
### The purpose of this nb is to understand the stats of features of the return items of the pytorch datasets. This is what the model sees as input. Stats on the raw data does not reflect the stats of the inputs the model receives. Stats on raw data is computed in 24d16_analysis.nb in hid_common
#%%
from pathlib import Path
import matplotlib.pyplot as plt
import collections
import torch
import random
import numpy as np
import pandas as pd

from scipy.spatial.distance import cdist
from torch.utils.data import DataLoader
from scipy.spatial.transform import Rotation as R
import plotly.graph_objects as go
from matplotlib import cm
import matplotlib
from sklearn.model_selection import train_test_split
from aic_bsd_model.datasets.aic_24d16_dataset import AIC24D16LapDataset
from aic_bsd_model.datasets.data_collators import TrackSegmentCollator
from aic_bsd_model.datasets.aic_bsd_dataset_utils import open_pkl, TRAJ_FEATURE_LINEAR_SCALE, INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16, get_pid_and_trial_num_from_uid, TrajFeatureIndex, TrajFeatureIndexNoAccel, FEATURE_TO_DF_KEY, FEATURE_TO_DF_KEY_NO_ACCEL
#%%
CONES_COORDINATES = [
    [-682, -2, 1.7],  # end of straightaway
    [-700, -144, 4.4],  # turn 1 right
    [-615, -239, 12.0],  # turn 2 left
    [-912, -117, -1.48],  # turn 3 left
    [-938, -72, -1.65],  # turn 3 right
    [-974, 80, -3.9],  # turn 4 left
    [-935, 109, -2.6],  # turn 4 right
    [-990, 2, -4.1],  # turn 4 brake zone
    [-991.5, 6, -4.2],  # turn 4 brake zone
    [-855, 171, -2.5],  # turn 5 right
    [-835, 256, -5.5],  # turn 5 left
    [-834, 330, -7.6],  # turn 5 right 2
    [-849, 518, -10.7],  # turn 6 left
    [-767, 569, -10.6],  # turn 6 right
    [-615, 587, -11.8],  # turn 7 left
    [-682, 611.5, -13.0],  # turn 7 brake zone
    [-684, 611.5, -13.0],  # turn 7 brake zone
    [-673, 535, -10.67],  # turn 8 right straight
    [-773, 420, -5.3],  # turn 8 right apex
    [-747, 289, -5.4],  # turn 9 right
    [-779, 335, -6.7],  # turn 9 brake zone
    [-780, 338, -6.7],  # turn 9 brake zone
    [-645, 365, -4.45],  # turn 10 left
]
CONES_COORDINATES_CARLA = [(i, -j, k) for (i,j,k) in CONES_COORDINATES]
#%%

# add parse args or something so that paths can be passed as args. eventually unify it with draccuss or whatever AS is using for IFM
trajectory_segments_dir = "~/Data/24-D-16/trajectory_segments_map_seg_ids/"
trials_directory = "~/Data/24-D-16/trials_final/"
trajectory_segments_dir = Path.expanduser(Path(trajectory_segments_dir))
trials_directory = Path.expanduser(Path(trials_directory))
map_file = trajectory_segments_dir / "track.csv"
with open(map_file, "r") as f:
    map_data = pd.read_csv(f)
#%%
collator = TrackSegmentCollator(padding_value=-2000)
pid_to_skip = "P1611"
all_pids_list = [pid for pid in list(INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16.keys()) if pid != pid_to_skip]
# random.shuffle(all_pids_list)
train_pids_list = all_pids_list
# train_pids_list, test_pids_list = train_test_split(all_pids_list, test_size=1, random_state=42, shuffle=True)
train_24d16_dataset = AIC24D16LapDataset(trajectory_segments_dir, trials_directory, map_file, train_pids_list)
# test_24d16_dataset = AIC24D16LapDataset(trajectory_segments_dir, trials_directory, map_file, test_pids_list)
# dataloader = DataLoader(train_24d16_dataset, batch_size=1, collate_fn=collator, shuffle=False)
num_segments = 10
#%%
len(train_24d16_dataset)
#%%
vis_folder = Path('/home/<USER>/Data/24-D-16/Visualizations/pytorch_seg_visualizations_scaled')
Path.mkdir(vis_folder, exist_ok=True)
for k in INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16.keys():
    Path.mkdir(Path(vis_folder) / Path(k),   parents=True, exist_ok=True)
#%%
dataloader = DataLoader(train_24d16_dataset, batch_size=1, collate_fn=collator, shuffle=False)
assert dataloader.batch_size == 1
all_metric_lists = collections.defaultdict(list)

for batch in dataloader:
    for mk, subkey_list in train_24d16_dataset.metric_keys.items():
        for subkey in subkey_list:
            prefix_mk_key = f"curr_{mk}" if subkey is None else f"curr_{mk}_{subkey}"
            all_metric_lists[prefix_mk_key].append(float(batch[prefix_mk_key][0]))
                                                   
    
#%%
for mk, mk_list in all_metric_lists.items():
    plt.figure()
    mk_list = np.array(mk_list)
    plt.hist(mk_list, bins=100)
    plt.title(f'{mk}')
    plt.show()
    print(np.min(mk_list), np.max(mk_list), np.mean(mk_list), np.std(mk_list))
    
    

#%% md
### Plot segment level traj after coordinate transformation and scaling. Essentially visualizing the get item of the dataset
#%%
dataloader = DataLoader(train_24d16_dataset, batch_size=1, collate_fn=collator, shuffle=False)
assert dataloader.batch_size == 1

all_traj_segments = []
all_map_segments = []
all_seg_i_ranges= [] 
# i = 0
for batch in dataloader:
    # print(batch.keys())
    curr_lap_uid = batch['curr_lap_uid'][0]
    print(curr_lap_uid)
    pid, trial_num = get_pid_and_trial_num_from_uid(curr_lap_uid)
    #create 
    
    trajectory_segments = batch["curr_trajectory_segments"]
    trajectory_attention_masks = batch["tcurr_rajectory_attention_masks"]
    map_segments = batch["curr_map_segments"]
    map_attention_masks = batch["curr_map_attention_masks"]
    for i in range(num_segments):
        traj_i_segments = trajectory_segments[:, i:i+1, :, :]
        traj_i_attention_mask = trajectory_attention_masks[:, i:i+1, :]
        map_i_segments = map_segments[:, i:i+1, :, :]
        map_i_attention_mask = map_attention_masks[:, i:i+1, :]
        min_z_i = float(torch.min(traj_i_segments[traj_i_attention_mask.bool()][:, TrajFeatureIndexNoAccel.TRAJ_Z]))
        max_z_i = float(torch.max(traj_i_segments[traj_i_attention_mask.bool()][:, TrajFeatureIndexNoAccel.TRAJ_Z]))
        all_seg_i_ranges.append((curr_lap_uid, i+1, max_z_i - min_z_i))
    
    
    num_columns = 2
    fig, axes = plt.subplots(5, num_columns, figsize=(15, 15), subplot_kw={'projection': '3d'})
    for i in range(0, num_segments):
        row_idx = i // num_columns
        col_idx =  i % num_columns
        traj_i_segments = trajectory_segments[:, i:i+1, :, :]
        traj_i_attention_mask = trajectory_attention_masks[:, i:i+1, :]
        map_i_segments = map_segments[:, i:i+1, :, :]
        map_i_attention_mask = map_attention_masks[:, i:i+1, :]
        
        # if float(torch.min(traj_i_segments[traj_i_attention_mask.bool()][:, 2])) < -90:
            
        min_z_i = float(torch.min(traj_i_segments[traj_i_attention_mask.bool()][:, TrajFeatureIndexNoAccel.TRAJ_Z])) 
        max_z_i = float(torch.max(traj_i_segments[traj_i_attention_mask.bool()][:, TrajFeatureIndexNoAccel.TRAJ_Z])) 
        last_z_i  = float(torch.min(traj_i_segments[traj_i_attention_mask.bool()][-1, TrajFeatureIndexNoAccel.TRAJ_Z])) 
        
        # if i == 5:
        # print(i)
        # print(traj_i_segments[traj_i_attention_mask.bool()][:, 2].shape)
        car_x = traj_i_segments[traj_i_attention_mask.bool()][:, TrajFeatureIndexNoAccel.TRAJ_X].numpy()
        car_y = traj_i_segments[traj_i_attention_mask.bool()][:, TrajFeatureIndexNoAccel.TRAJ_Y].numpy()
        car_z = traj_i_segments[traj_i_attention_mask.bool()][:, TrajFeatureIndexNoAccel.TRAJ_Z].numpy()
        # car_z = (car_z - np.min(car_z))/(np.max(car_z) - np.min(car_z))
        
        map_inner_x = map_i_segments[map_i_attention_mask.bool()][:, 0].numpy()
        map_inner_y = map_i_segments[map_i_attention_mask.bool()][:, 1].numpy()
        map_outer_x = map_i_segments[map_i_attention_mask.bool()][:, 2].numpy()
        map_outer_y = map_i_segments[map_i_attention_mask.bool()][:, 3].numpy()
        
        # for p_i in range(len(car_x)):
        #     axes[row_idx, col_idx].scatter(car_x[p_i], car_y[p_i], color='k', alpha=car_z[p_i])
        axes[row_idx, col_idx].scatter(car_x, car_y, car_z, color='k', s=1)
        # Put special colors for start and end of the trajectory
        axes[row_idx, col_idx].scatter(car_x[0], car_y[0], car_z[0],  color='r')
        axes[row_idx, col_idx].scatter(car_x[-1], car_y[-1], car_z[-1], color='g')
        axes[row_idx, col_idx].scatter(map_inner_x, map_inner_y, color='m', s=1)
        axes[row_idx, col_idx].scatter(map_outer_x, map_outer_y, color='b', s=1)
        axes[row_idx, col_idx].set_title(f'Seg ID - {i+1}, Min Z - {min_z_i:.3f}, Max Z - {max_z_i:.3f}, Last pt = {car_x[-1]:0.2f}, {car_y[-1]:0.2f}, {last_z_i:0.2f}', fontsize=8)
        axes[row_idx, col_idx].set_xlabel('Relative X (m)')
        axes[row_idx, col_idx].set_ylabel('Relative Y (m)')
        axes[row_idx, col_idx].set_zlabel('Relative Z (m)')
        axes[row_idx, col_idx].set_aspect('equal')
    filename = vis_folder / Path(pid) / Path(f'trial_num_{trial_num}.png')
    plt.tight_layout()
    plt.savefig(filename)
    plt.close()
            # break
        
    #     break
    all_traj_segments.append(trajectory_segments[trajectory_attention_masks.bool()])
    all_map_segments.append(map_segments[map_attention_masks.bool()])

    
    # break
#%%

z_range_dict = {}
for uid, seg_id, z_range in all_seg_i_ranges:
    pid, trial_num = get_pid_and_trial_num_from_uid(uid)
    
    if z_range > 20:
        if pid not in z_range_dict:
            z_range_dict[pid]= collections.defaultdict(list)
        z_range_dict[pid][trial_num].append((seg_id, z_range))
#%%
print(z_range_dict.keys())
z_range_dict['P1614']
#%%
plt.plot(all_subsampled_trajectories[min_dist_traj, 3])
#%% md
### Plot full trajectories with orientation in 3d and save html using plotly for interactive inspection. Plot map and cones as well.  
#%%
track_xy = map_data[['refline/x', 'refline/y']].values #N, 2
for valid_lap_uid in train_24d16_dataset.valid_lap_list:
    print(valid_lap_uid)
    pid, trial_num = get_pid_and_trial_num_from_uid(valid_lap_uid)
    segments_dict_for_uid = train_24d16_dataset.trajectory_segments_pkl_paths_according_to_uid[valid_lap_uid]
    all_subsampled_trajectories = []

    # Use a colormap to dynamically generate colors for each chunk
    # colormap = cm.get_cmap('jet', num_segments)  # 'viridis' is the chosen color map
    colormap = matplotlib.colormaps['viridis']
    colors = [f"rgb({r*255}, {g*255}, {b*255})" for r, g, b, _ in colormap(np.linspace(0, 1, num_segments))]
    # Create a 3D scatter plot
    fig = go.Figure()
    for seg_id in range(0, num_segments):
        # print(seg_id)
        seg_path = segments_dict_for_uid[f'segment_{seg_id+1}']
        seg_snippet_dict = open_pkl(seg_path)
        _, subsampled_trajectory = train_24d16_dataset.extract_trajectory_data(seg_snippet_dict)
        
        all_subsampled_trajectories.append(subsampled_trajectory)
        ego_x_i = subsampled_trajectory[:, TrajFeatureIndex.TRAJ_X]
        ego_y_i = subsampled_trajectory[:, TrajFeatureIndex.TRAJ_Y]
        ego_z_i = subsampled_trajectory[:, TrajFeatureIndex.TRAJ_Z]
        fig.add_trace(go.Scatter3d(
            x=ego_x_i,
            y=ego_y_i,
            z=ego_z_i,
            mode='markers',
            marker=dict(
                size=4,
                color=colors[seg_id],  # Use color from the colormap
            ),
            name=f"Seg {seg_id + 1}"  # Legend entry for the chunk
        ))
        #plot rotation 
        scale = 10
        for s_len in range(0, subsampled_trajectory.shape[0], 5):
            # print(s_len)
            R_gb = np.matrix(R.from_quat(subsampled_trajectory[s_len, -4:]).as_matrix())
            p_gb = subsampled_trajectory[s_len][:3]

            # # create transformation matrix. body frame of normalization time step with respect to global
            T_gb = np.matrix(
                [
                    np.concatenate((np.array(R_gb[0, :]).flatten(), p_gb[0:1])),
                    np.concatenate((np.array(R_gb[1, :]).flatten(), p_gb[1:2])),
                    np.concatenate((np.array(R_gb[2, :]).flatten(), p_gb[2:])),
                    np.array([0, 0, 0, 1]),
                ]
            ) #np.matrix (4,4)
            # points of body coordinate axis in bodyframe
            s_b_x = np.array([2*scale, 0, 0, 1]).reshape(-1, 1)
            s_b_y = np.array([0, 2*scale, 0, 1]).reshape(-1, 1)
            s_b_z = np.array([0, 0, 0.3*scale, 1]).reshape(-1, 1)
            # print(s_p.shape, R_gb.shape)
            # points of body coordinate axis in global frae
            s_g_x = (T_gb * s_b_x)
            s_g_y = (T_gb * s_b_y)
            s_g_z = (T_gb * s_b_z)
            
            #plot x axis of body frame coordinate axis
            fig.add_trace(go.Scatter3d(
                x=[subsampled_trajectory[s_len, 0], np.array(s_g_x)[0][0]],  # X-coordinates
                y=[subsampled_trajectory[s_len, 1], np.array(s_g_x)[1][0]],  # Y-coordinates
                z=[subsampled_trajectory[s_len, 2], np.array(s_g_x)[2][0]],  # Z-coordinates
                mode='lines',  # Line mode
                line=dict(
                    color='red',  # Line color
                    width=5        # Line width
                ),
            ))
            #plot x axis of body frame coordinate axis
            fig.add_trace(go.Scatter3d(
                x=[subsampled_trajectory[s_len, 0], np.array(s_g_y)[0][0]],  # X-coordinates
                y=[subsampled_trajectory[s_len, 1], np.array(s_g_y)[1][0]],  # Y-coordinates
                z=[subsampled_trajectory[s_len, 2], np.array(s_g_y)[2][0]],  # Z-coordinates
                mode='lines',  # Line mode
                line=dict(
                    color='green',  # Line color
                    width=5        # Line width
                ),
            ))
            #plot y axis of body frame coordinate axis
            fig.add_trace(go.Scatter3d(
                x=[subsampled_trajectory[s_len, 0], np.array(s_g_z)[0][0]],  # X-coordinates
                y=[subsampled_trajectory[s_len, 1], np.array(s_g_z)[1][0]],  # Y-coordinates
                z=[subsampled_trajectory[s_len, 2], np.array(s_g_z)[2][0]],  # Z-coordinates
                mode='lines',  # Line mode
                line=dict(
                    color='blue',  # Line color
                    width=5        # Line width
                ),

            ))

    #draw map after computing nearest xy point and grabbing the z coordinate from that. 
    all_subsampled_trajectories =  np.vstack(all_subsampled_trajectories)
    pairwise_dist = cdist(all_subsampled_trajectories[:, :2], track_xy)  # (M, N)
    min_dist_traj = np.argmin(pairwise_dist, axis=0)
    map_z = all_subsampled_trajectories[min_dist_traj, TrajFeatureIndex.TRAJ_Z]
    
    i_x = map_data['inner_edge/x'].values
    i_y = map_data['inner_edge/y'].values
    
    o_x = map_data['outer_edge/x'].values
    o_y = map_data['outer_edge/y'].values
    num_skip_map_pt = 4
    fig.add_trace(go.Scatter3d(
        x=i_x[::num_skip_map_pt],
        y=i_y[::num_skip_map_pt],
        z=map_z[::num_skip_map_pt],
        mode='markers',
        marker=dict(
            size=2.3,
            color='black',  # Use color from the colormap
            opacity=0.6
        ),
    ))
    fig.add_trace(go.Scatter3d(
        x=o_x[::num_skip_map_pt],
        y=o_y[::num_skip_map_pt],
        z=map_z[::num_skip_map_pt],
        mode='markers',
        marker=dict(
            size=2.3,
            color='black',  # Use color from the colormap
            opacity=0.6
        ),
    ))
    #draw cones
    fig.add_trace(go.Scatter3d(
        x=[i[0] for i in CONES_COORDINATES_CARLA],
        y=[i[1] for i in CONES_COORDINATES_CARLA],
        z=[i[2] for i in CONES_COORDINATES_CARLA],
        mode='markers',
        marker=dict(
            size=10,
            color='blue',  # Use color from the colormap
        ),
    ))
    
    # Add title and axis labels
    fig.update_layout(
        title=f"{pid}_trial{trial_num}",
        showlegend=False, 
        scene=dict(
        xaxis=dict(
            title="X Axis (m)",
            showgrid=True,  # Enable gridlines on the X-axis
            gridcolor="lightgrey",  # Gridline color
            gridwidth=1  # Gridline width
        ),
        yaxis=dict(
            title="Y Axis (m)",
            showgrid=True,  # Enable gridlines on the Y-axis
            gridcolor="lightgrey",
            gridwidth=1
        ),
        zaxis=dict(
            title="Z Axis (m)",
            showgrid=True,  # Enable gridlines on the Z-axis
            gridcolor="lightgrey",
            gridwidth=1,
            range=[-40, 40]
        )
    ),
    )
    # Save as HTML
    filename = vis_folder / Path(pid) / Path(f'trial_num_{trial_num}.html')
    fig.write_html(filename)
    
    
    
    
    
#%%
%matplotlib widget
from scipy.spatial.transform import Rotation as R
#'P1607-trial_40''P1601-trial_37'
test_idx = [idx for idx, uid in enumerate(train_24d16_dataset.valid_lap_list) if uid == 'P1609-trial_16'][0]
valid_lap_uid = train_24d16_dataset.valid_lap_list[test_idx]
pid, trial_num = get_pid_and_trial_num_from_uid(valid_lap_uid)
segments_dict_for_uid = train_24d16_dataset.trajectory_segments_pkl_paths_according_to_uid[valid_lap_uid]

all_subsampled_trajectories = []
all_seg_trajs = {}
for seg_id in range(1, 11):
    # print(seg_id)
    seg_path = segments_dict_for_uid[f'segment_{seg_id}']
    seg_snippet_dict = open_pkl(seg_path)
    _, subsampled_trajectory = train_24d16_dataset.extract_trajectory_data(seg_snippet_dict)
    all_subsampled_trajectories.append(subsampled_trajectory)
    all_seg_trajs[seg_id] = subsampled_trajectory
    # if seg_id == 6:
    #     seg_6 = subsampled_trajectory
    #     last_seg_6 = subsampled_trajectory[-1, :]
    #     first_seg_6 = subsampled_trajectory[0, :]

all_subsampled_trajectories = np.vstack(all_subsampled_trajectories)
# all_subsampled_trajectories[:, -4:].shape
quaternions = all_subsampled_trajectories[:, -4:]
quaternion_rates = []
delta_t = 0.2
for i in range(all_subsampled_trajectories.shape[0]-1):
    q_t = R.from_quat(quaternions[i])       # Current quaternion
    q_next = R.from_quat(quaternions[i + 1]) # Next quaternion

    # Compute relative quaternion (q_next * q_t^-1)
    delta_q = q_next * q_t.inv()

    # Extract the relative quaternion's rotation vector (scaled angular velocity)
    delta_q_vec = delta_q.as_rotvec() / delta_t

    # Quaternion derivative is 2 * angular velocity in quaternion space
    quaternion_rate = 2 * delta_q_vec
    quaternion_rates.append(quaternion_rate)

# Convert to a NumPy array for easier handling
quaternion_rates = np.array(quaternion_rates)
# print(quaternion_rates)

fig = plt.figure()
ax = fig.add_subplot(projection='3d')
# # ax.scatter(subsampled_trajectory[:, 0], subsampled_trajectory[:, 1], subsampled_trajectory[:, 2])


ax.scatter(all_subsampled_trajectories[:, 0], all_subsampled_trajectories[:, 1], all_subsampled_trajectories[:, 2], s=6, c='k')
ax.scatter(all_seg_trajs[3][:, 0], all_seg_trajs[3][:, 1], all_seg_trajs[3][:, 2], s=12, c='r')
ax.scatter(all_seg_trajs[4][:, 0], all_seg_trajs[4][:, 1], all_seg_trajs[4][:, 2], s=12, c='g')
# ax.scatter(first_seg_6[0],first_seg_6[1],first_seg_6[2], c='r', s=40)
# ax.scatter(last_seg_6[0],last_seg_6[1],last_seg_6[2], c='g', s=40)


#%%
all_traj_segments_pytroch = torch.vstack(all_traj_segments)
all_traj_segments_pytroch.shape
#%%
for f_dim in TrajFeatureIndexNoAccel:
    traj_feature = all_traj_segments_pytroch[:, f_dim.value]
    plt.hist(traj_feature, bins=100)
    plt.title(f'{FEATURE_TO_DF_KEY_NO_ACCEL[f_dim.value]}')
    plt.show()
    print(f_dim.name, f_dim.value)
    print(torch.min(traj_feature), torch.max(traj_feature), torch.mean(traj_feature), torch.std(traj_feature))
    
    # break
#%%
all_map_segments_pytorch = torch.vstack(all_map_segments)
all_map_segments_pytorch.shape
#%%
for f_dim in range(0, 6):
    map_feature = all_map_segments_pytorch[:, f_dim]
    # if i % 2 == 0:
    #     map_feature *= TRAJ_FEATURE_LINEAR_SCALE[TrajFeatureIndexNoAccel.TRAJ_X.name]
    # elif i % 2 == 1:
    #     map_feature *= TRAJ_FEATURE_LINEAR_SCALE[TrajFeatureIndexNoAccel.TRAJ_Y.name]
    plt.hist(map_feature, bins=100)
    plt.show()
    print(torch.min(map_feature), torch.max(map_feature), torch.mean(map_feature), torch.std(map_feature))
    