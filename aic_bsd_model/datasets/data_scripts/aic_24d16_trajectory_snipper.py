import os
import pickle
from pathlib import Path

from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    calculate_sampling_freq,
    compute_braking_score,
    compute_friction_utilization_score,
    compute_oob,
    compute_racing_line_score,
    compute_smoothness_score,
    compute_steering_score,
    compute_throttle_score,
    compute_trial_time,
    extract_uid_from_path,
    get_pid_and_trial_num_from_uid,
    get_total_trials_per_subject,
    open_trial,
    parse_arguments,
)


def make_metadata(
    pid,
    trial_num,
    snippet_num,
    start,
    end,
    sampling_frequency,
    map_segment_id=None,
):
    snippet_type = "self-practice"
    if map_segment_id is None:
        return {
            "pid": pid,
            "trial_num": trial_num,
            "start_index": start,
            "end_index": end,
            "sampling_frequency": sampling_frequency,
            "snippet_type": snippet_type,
            "snippet_num": snippet_num,
        }
    else:
        return {
            "pid": pid,
            "trial_num": trial_num,
            "start_index": start,
            "end_index": end,
            "sampling_frequency": sampling_frequency,
            "snippet_type": snippet_type,
            "snippet_num": snippet_num,
            "map_segment_id": map_segment_id,
        }


def calculate_snippet_metrics(df):
    metrics_dict = {}
    metrics_dict.update(compute_trial_time(df))
    metrics_dict.update(compute_smoothness_score(df))
    metrics_dict.update(compute_steering_score(df))
    metrics_dict.update(compute_braking_score(df))
    metrics_dict.update(compute_throttle_score(df))
    metrics_dict.update(compute_oob(df))
    metrics_dict.update(compute_racing_line_score(df))
    metrics_dict.update(compute_friction_utilization_score(df))
    return metrics_dict


def main(args=None):
    args = parse_arguments(args)
    trials_dir = os.path.expanduser(args.trials_dir)
    trajectory_snippet_len_s = args.trajectory_snippet_len_s  # in seconds
    trajectory_snippets_dir = os.path.expandvars(
        os.path.expanduser(args.trajectory_snippets_dir)
    )

    os.makedirs(trajectory_snippets_dir, exist_ok=True)

    hop_size_s = args.hop_size_s

    assert trials_dir is not None
    assert trajectory_snippet_len_s > 0 and hop_size_s > 0

    # all paths to the pkl files containing the trial dataframes
    all_trial_parquets = [
        t
        for t in Path(trials_dir).glob("**/*.parquet")
        if "_with_all_annotations_and_metrics" in str(t)
    ]

    # dictionary conatining the total number of trials per subjects
    total_trials_per_subject = get_total_trials_per_subject(all_trial_parquets)

    for trial_parquet_path in all_trial_parquets:
        uid = extract_uid_from_path(trial_parquet_path)
        pid, trial_num = get_pid_and_trial_num_from_uid(uid)

        trial_dict = open_trial(trial_parquet_path)
        trial_df = trial_dict["dataframe"]
        print(f"Extracting snippets from {trial_parquet_path}")

        last_trial_idx = total_trials_per_subject[pid] - 1  # -1 because 0-indexed
        delta_ts_mean, delta_ts_std, sampling_frequency = calculate_sampling_freq(
            trial_df
        )
        window_size = round(trajectory_snippet_len_s * sampling_frequency)  # in samples
        hop_size = max(round(hop_size_s * sampling_frequency), 1)  # in samples

        start, end = 0, window_size
        num_snippets = 0  # number of snippets extracted from a trial
        while end < len(trial_df):
            num_snippets += 1
            # df slice corresponding to the snippet
            sub_df = trial_df[start:end]
            # create metadata for the snippet
            snippet_metadata = make_metadata(
                uid,
                pid,
                trial_num,
                num_snippets,
                start,
                end,
                sampling_frequency,
                last_trial_idx,
            )
            snippet_metrics = calculate_snippet_metrics(sub_df)
            # create directory folder for storing the snippet
            traj_snip_dir = os.path.join(
                trajectory_snippets_dir, f"{uid}_snippet_{num_snippets}"
            )
            os.makedirs(traj_snip_dir, exist_ok=True)
            snippet_dict = {
                "df": sub_df,
                "metadata": snippet_metadata,
                "metrics": snippet_metrics,
            }
            # Save snippet dataframe along with metadata as a dict in a pkl

            with open(os.path.join(traj_snip_dir, "snippet.pkl"), "wb") as fp:
                pickle.dump(snippet_dict, fp)
            print(
                f"Saved snippet {num_snippets} from {trial_parquet_path} at {traj_snip_dir}"
            )
            start += hop_size
            end += hop_size

        print()
        print(f"Extracted {num_snippets} snippets from {trial_parquet_path}")
        print()

        # break


if __name__ == "__main__":
    main()


# set args.trial_dir to be ~/Data/24-D-16/trials_final and arg.trajectory_snippetS_dir to be the one in 24-D-16
