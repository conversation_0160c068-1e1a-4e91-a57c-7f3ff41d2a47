import os
import shutil
from pathlib import Path

import pytest

# Import the Cache decorator from your cache_decorator module
# (make sure the module name and import path match your project)
from cache_decorator import Cache

from aic_bsd_model.datasets.aic_24d16_lap_dataset import CACHE_PATH, AIC24D16LapDataset


@pytest.fixture
def tmp_cache_dir(tmp_path):
    """
    Create a temporary cache directory, configure the CACHE_DIR environment variable,
    and then clean up afterwards.
    """
    cache_dir = tmp_path / "cache"
    cache_dir.mkdir()
    # Setting the environment variable used by <PERSON>ache if cache_dir is not provided explicitly
    os.environ["CACHE_DIR"] = str(cache_dir)
    yield cache_dir
    # Cleanup the temporary cache directory after tests
    shutil.rmtree(str(cache_dir))


class TestAIC24D16LapDataset(AIC24D16LapDataset):
    class_name = "TestAIC24D16LapDataset"

    def __init__(self, config_dict, dataset_type, cache_dir=None):
        self.map = {"race_line": [[0, 0], [1, 1]]}
        super().__init__(config_dict, dataset_type)
        # Add a test counter to verify caching is working
        self.test_counter = 0
        self.cache_dir = cache_dir
        self.list_of_pids_to_be_considered = config_dict.get(
            "list_of_pids_to_be_considered"
        )
        # make sure class variable can be accessed
        assert self.class_name == "TestAIC24D16LapDataset"

    def cache_variables_for_hash(self) -> dict:
        """
        Override to include test-specific variables that should affect the cache
        """
        ret = super().cache_variables_for_hash()
        assert ret["dataset_type"] == self.dataset_type
        ret["list_of_pids_to_be_considered"] = self.list_of_pids_to_be_considered
        return ret

    @Cache(
        cache_path=CACHE_PATH,
        enable_cache_arg_name="self.enable_dataset_cache",
    )
    def __getitem__(self, idx):
        """
        Test implementation that demonstrates caching:
        1. Returns a simple dict with test data
        2. Increments a counter to verify caching is working
        3. Uses the parent class's data but adds test-specific fields
        """
        # Get the original data from parent class
        data_dict = {}
        # Add test-specific data
        data_dict["test_counter"] = self.test_counter
        data_dict["dataset_type"] = self.dataset_type
        self.test_counter += 1  # This will only increment when cache is disabled

        return data_dict


def test_dataset_caching(tmp_cache_dir):
    # Create dataset with caching enabled
    config_dict = {"enable_dataset_cache": True, "list_of_pids_to_be_considered": [1]}
    dataset = TestAIC24D16LapDataset(config_dict, "test", cache_dir=str(tmp_cache_dir))

    # First access - counter will increment
    data1 = dataset[0]
    assert data1["test_counter"] == 0, "First access should have counter 0"

    # Second access - should get cached data, counter won't increment
    data2 = dataset[0]
    assert data2["test_counter"] == 0, "Cached access should have same counter"
    assert (
        dataset.test_counter == 1
    ), "Counter should only increment once with caching enabled"

    # Third access
    data3 = dataset[0]
    assert data3["test_counter"] == 0, "Cached access should have same counter"
    assert (
        dataset.test_counter == 1
    ), "Counter should only increment once with caching enabled"

    # Fourth access
    data4 = dataset[1]
    assert data4["test_counter"] == 1, "should increment"
    assert dataset.test_counter == 2

    # Create dataset with caching disabled
    config_dict["enable_dataset_cache"] = False
    dataset_no_cache = TestAIC24D16LapDataset(
        config_dict, "test", cache_dir=str(tmp_cache_dir)
    )

    # First access - counter will increment
    data1 = dataset_no_cache[0]
    assert (
        data1["test_counter"] == 0
    ), "First access with no cache should have counter 0"

    # Second access - counter will increment again
    data2 = dataset_no_cache[0]
    assert (
        data2["test_counter"] == 1
    ), "Second access with no cache should have incremented counter"
    assert (
        dataset_no_cache.test_counter == 2
    ), "Counter should increment on every access with caching disabled"

    # Third access
    data3 = dataset_no_cache[1]
    assert data3["test_counter"] == 2, "Cached access should have same counter"
    assert (
        dataset_no_cache.test_counter == 3
    ), "Counter should only increment once with caching disabled"

    # Verify cache files were created in tmp_cache_dir
    cache_files = list(tmp_cache_dir.glob("**/*.pkl.gz"))
    assert len(cache_files) == 2, "2 cache files should be created in tmp_cache_dir"

    # Test caching with different dataset pids
    config_dict["list_of_pids_to_be_considered"] = [2, 3]
    config_dict["enable_dataset_cache"] = True
    dataset2 = TestAIC24D16LapDataset(config_dict, "test", cache_dir=str(tmp_cache_dir))
    data1 = dataset2[0]
    assert (
        data1["test_counter"] == 0
    ), "First access with no cache should have counter 0"

    # Second access
    data2 = dataset2[0]
    assert data2["test_counter"] == 0, "Cached access should have same counter"
    assert (
        dataset2.test_counter == 1
    ), "Counter should only increment once with caching enabled"

    # Verify cache files were created in tmp_cache_dir
    cache_files = list(tmp_cache_dir.glob("**/*.pkl.gz"))
    assert len(cache_files) == 3, "3 cache files should be created in tmp_cache_dir"


if __name__ == "__main__":
    # Running the tests when the script is executed directly.
    import sys

    sys.exit(pytest.main([__file__]))
