import glob
import os
import random
from pathlib import Path

import draccus
import numpy as np
import torch

from aic_bsd_model.datasets.aic_24d16_lap_dataset import AIC24D16LapDataset
from aic_bsd_model.datasets.aic_bsd_dataset import AICBSDDataset

from .dataset_configs import DatasetConfig


def read_th_aic_bsd_dataset(cfg, tokenizer):
    trajectory_snippets_dir = Path(
        os.path.expandvars(os.path.expanduser(cfg.dataset_root_dir))
    )
    trajectory_segments_dir = Path(
        os.path.expandvars(os.path.expanduser(cfg.dataset_segments_dir))
    )
    trials_directory = Path(
        os.path.expandvars(os.path.expanduser(cfg.trials_directory))
    )
    data_sample_dirs = [d for d in trajectory_snippets_dir.iterdir() if d.is_dir()]
    map_file = trajectory_snippets_dir / "track.csv"

    train_dataset = AICBSDDataset(
        data_sample_dirs, trajectory_segments_dir, trials_directory, tokenizer, map_file
    )
    # val dataset. Use a split mechanism for BSD it might be  based on subjects.
    val_dataset = train_dataset

    return train_dataset, val_dataset


def read_th_aic_24d16_dataset(cfg):
    trajectory_segments_dir = Path(
        os.path.expandvars(os.path.expanduser(cfg.dataset_segments_dir))
    )
    trials_directory = Path(
        os.path.expandvars(os.path.expanduser(cfg.trials_directory))
    )
    map_file = trajectory_segments_dir / "track.csv"

    train_dataset = AIC24D16LapDataset(
        trajectory_segments_dir, trials_directory, map_file
    )
    # val dataset. Use a split mechanism for 24D16 it might be  based on subjects.
    val_dataset = train_dataset

    return train_dataset, val_dataset


def get_dataset(cfg: DatasetConfig, tokenizer, device="cpu", debug: bool = False):
    train_data_fn = f"{cfg.dataset_id}_train"
    val_data_fn = f"{cfg.dataset_id}_validation"

    if cfg.dataset_id == "thunderhill-aic-bsd":
        train_dataset, val_dataset = read_th_aic_bsd_dataset(cfg, tokenizer)
    elif cfg.dataset_id == "thunderhill-aic-24d16":
        train_dataset, val_dataset = read_th_aic_24d16_dataset(cfg)

    return train_dataset, val_dataset
