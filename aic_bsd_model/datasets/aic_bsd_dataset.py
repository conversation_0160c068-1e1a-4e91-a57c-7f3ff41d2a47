# load in a subjects trials in chronological order. Needs: path to folder containing trials. list of subject ids and num_trials?
# load in coach and driver srt and create a timestamped dialogue out of it? Maybe in a script form? COACH:.... SUBJECT:....
# Retreive necessary fields from the dataframe for recreating the timestamped trajectory
# Retrieve map data and add cone information to it (additional bit for every map point to indicate the presence or absence of a cone). For all map points
# within a delta threshold of a cone mark the bit to be one, indicating that there is a cone nearby. (This doesn't however capture the actual location of the cone), Maybe we need additional feature dimensions to
# encode the distance from the map point to the nearest cone.

# What should a get item return:

# (full traj N, full traj N+1, BSD(N, N+1), traj snippet from N+1, concurrent language during the traj N, teacher action corresponding to traj snippet at N+1, localized map)
# also may contain full traj N chunked up into map segments. And each map segment gets encoded into the LLM prompt


import collections
import os
import pickle
from enum import IntEnum
from typing import List

import numpy as np
import pandas as pd
from shapely import Point, geometry
from torch.utils.data import Dataset
from transformers import AutoTokenizer, logging

from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    FEATURE_TO_DF_KEY,
    TrajFeatureIndex,
    add_map,
    extract_uid_from_path,
    get_pid_and_trial_num_from_uid,
    get_uid_from_pid_and_trial_num,
    load_pkl,
    parse_arguments,
)
from aic_bsd_model.utils.math_utils import CoordinateNormalizationUtil

# TODO add map indices
STARTER_PROMPT = f"""You are a driving coach who provides structured feedback after observing a student driver's driving behavior during a coaching session on race track simulation. The lap consists of 10 map segments. For each map segment you will be provided data on time taken to complete the segment, the steering smoothness score,  the race line adherence score, brake application smoothness score and the throttle application smoothness score. Additionally, for each map segment you will be provided the distribution of different types (the categories being: brake, throttle, steering, left, right) of concurrent feedback instruction that the coach provided to the students. Following is the data for lap number """


def parse_args_and_load_datasets():
    args = parse_arguments()
    trajectory_snippets_dir = os.path.expandvars(
        os.path.expanduser(args.trajectory_snippets_dir)
    )
    tokenizer = AutoTokenizer.from_pretrained(args.llm_model_name)
    if tokenizer.pad_token_id is None:
        tokenizer.pad_token_id = tokenizer.eos_token_id

    data_sample_dirs = [
        os.path.join(trajectory_snippets_dir, d)
        for d in os.listdir(trajectory_snippets_dir)
    ]
    map_file = os.path.expandvars(os.path.expanduser(args.track_map_csv))
    train_dataset = AICBSDDataset(data_sample_dirs, tokenizer, map_file)
    return args, train_dataset


class AICBSDDataset(Dataset):
    class_name = "AICBSDDataset"

    def __init__(
        self,
        data_sample_dirs: List,
        trajectory_segments_dir,
        trials_directory,
        tokenizer,
        map_file,
        trajectory_normalization_scheme="local",
        global_trajectory_normalization_info=None,
        snippet_filename="snippet.pkl",
        target_sampling_frequency=5,
        local_map_distance_threshold=100,
    ):
        # list of directory namaes (full paths) corresponding to the dataset.
        # Each directory conatins a single data sample. This could WebDataset, in-house format.
        self.data_sample_dirs = data_sample_dirs
        # directory containing trial dirs. Each trial_dir contains 10 pkl files each pkl file corresponding to the trajectory snippet for the map segment
        self.trajectory_segments_dir = trajectory_segments_dir

        self.full_trial_dirs = [
            d for d in list(trials_directory.iterdir()) if not d.is_file()
        ]

        self.tokenizer = tokenizer  # for tokenizing any language inputs
        self.map_file = map_file  # path to THill map files
        # snippet extension used for each snippet
        self.snippet_filename = snippet_filename
        # target sampling frequency for trajectory.
        self.target_sampling_frequency = target_sampling_frequency
        # TODO (deepak.gopinath) Add args to accept an arbitrary timestep to be considered as the prediction point.
        # Currently defaults to the 0th timestep.
        self.prediction_timestep = 0
        # option 1: normalize trajectory with respect to a coordinate frame within the trajectory. This is the 'local' mode.
        # currently supports "first", "last" and "prediction_timestep" (any arbitrary timestep in the trajectroy)
        # option 2: normalize trajectory with respect to global statistics of the dataset. This is the 'global' mode. If 'global'
        # code expects global statistics information to be provided in self.global_trajectory_normalization_info
        self.trajectory_normalization_scheme = trajectory_normalization_scheme
        self.global_trajectory_normalization_info = global_trajectory_normalization_info
        assert (
            self.global_trajectory_normalization_info is not None
            if self.trajectory_normalization_scheme == "global"
            else True
        )

        self.task_prompt = """You are an AI Racing Coach. You will be given a clip of a driver on a track, 
        and your job is to help coach that driver to a faster lap time. Clip:"""

        # convert prompts into tokenized input_ids and labels
        self.task_prompt_input_ids = self.tokenizer(
            self.task_prompt, add_special_tokens=True
        ).input_ids

        # TODO (deepak.gopinath). Change -100 to a MACRO
        self.task_prompt_labels = [-100] * len(self.task_prompt_input_ids)

        # open map
        with open(map_file, "r") as f:
            map_data = pd.read_csv(f)
        self.map = add_map(map_data)

        # TODO deicde if z needs to be added
        self.raceline_xy = np.array([r[:2] for r in self.map["race_line"]])
        # add left and right linestrings if necessary
        self.raceline_linestring = geometry.LineString(self.raceline_xy)
        self.distance_along_rl = []
        for i in range(self.raceline_xy.shape[0]):
            self.distance_along_rl.append(
                self.raceline_linestring.line_locate_point(Point(self.raceline_xy[i]))
            )

        self.local_map_distance_threshold = local_map_distance_threshold
        # this might be updated if we move to WebDatasets eventually
        self.trajectory_snippets_pkl_paths = [
            d / self.snippet_filename for d in self.data_sample_dirs
        ]

        self.trajectory_segments_pkl_paths_according_to_uid = collections.defaultdict()
        self._prepare_trajectory_segment_data_for_each_lap()

        self.trajectory_metrics_pkl_paths_according_to_uid = collections.defaultdict()
        self._prepare_trajectory_metrics_for_each_lap()

        self.bsd_language_data_dict = collections.OrderedDict()
        self._prepare_bsd_language_data()

        self.metric_keys = {
            "trial_time": [None],
            "oob": ["percentage"],
            "steering_score": ["abs_mean"],
            "racing_line_score": ["abs_mean"],
            "braking_score": ["abs_mean"],
            "throttle_score": ["abs_mean"],
            "smoothness_score": ["mean"],
        }

    def _prepare_trajectory_segment_data_for_each_lap(self):
        for d in self.trajectory_segments_dir.iterdir():
            uid = d.stem
            segment_pkls = list(d.iterdir())
            if uid not in self.trajectory_segments_pkl_paths_according_to_uid:
                self.trajectory_segments_pkl_paths_according_to_uid[
                    uid
                ] = collections.OrderedDict()
                for seg_id in range(1, 11):
                    self.trajectory_segments_pkl_paths_according_to_uid[uid][
                        f"segment_{seg_id}"
                    ] = None
            for sp in segment_pkls:
                if "metrics" in sp.stem:
                    continue
                self.trajectory_segments_pkl_paths_according_to_uid[uid][sp.stem] = sp

    def _prepare_trajectory_metrics_for_each_lap(self):
        for d in self.trajectory_segments_dir.iterdir():
            uid = d.stem
            segment_pkls = list(d.iterdir())
            if uid not in self.trajectory_metrics_pkl_paths_according_to_uid:
                self.trajectory_metrics_pkl_paths_according_to_uid[
                    uid
                ] = collections.OrderedDict()
            for sp in segment_pkls:
                if "metrics" in sp.stem:
                    self.trajectory_metrics_pkl_paths_according_to_uid[uid][
                        sp.stem
                    ] = sp

    def _prepare_bsd_language_data(self):
        for trial_dir in self.full_trial_dirs:
            uid = trial_dir.stem
            pid, trial_num = get_pid_and_trial_num_from_uid(uid)
            if pid not in self.bsd_language_data_dict:
                self.bsd_language_data_dict[pid] = collections.OrderedDict()
            bsd_str = ""
            if trial_num != 0:
                bsd_path = trial_dir / str(uid + "_bsd_dict.pkl")
                with open(bsd_path, "rb") as fp:
                    bsd_dict = pickle.load(fp)
                for b in bsd_dict["subject_coach_srts"]:
                    bsd_str += b["subtitle"].content + " "
                bsd_str = bsd_str[:-1]  # skip last space
            self.bsd_language_data_dict[pid][trial_num] = bsd_str

    def __len__(self):
        return len(self.trajectory_snippets_pkl_paths)

    def compute_dataset_item_hash(self):
        pass
        """
        should have snippet name - PXXX-trialN-snippetM
        should have normalization timestep, if normalized with respect to a pose within the trajectory. If normalized 
        globally with respect to the statistics of the ENTIRE dataset, then need to have an option to indicate that. 

        (optional) map related information. such as max map points, dataset_type?, racing_line info? distance threshold. 
        
        #add map post process method
        """

    def get_df_and_metadata(self, snippet_dict):
        return snippet_dict["df"], snippet_dict["metadata"]

    def extract_full_trajectory_from_df(self, snippet_df):
        start_index, end_index = snippet_df.index[0], snippet_df.index[-1]
        subsampled_indices = list(range(start_index, end_index + 1))

        # consider "ignore_indices" as an option to zero out unnecessary features.
        # the feature len then would be len(TrajFeatureIndex) - num_ignore_features
        # Would necessitate a remapping of the indices.
        trajectory = np.zeros(
            (len(subsampled_indices), len(TrajFeatureIndex))
        )  # (T, F)
        # TODO (deepak.gopinath) if in a dataset I know what the valid_feature_idxs are just only use those.
        valid_feature_idxs = {
            k: v for k, v in FEATURE_TO_DF_KEY.items() if v is not None
        }
        trajectory[:, list(valid_feature_idxs.keys())] = snippet_df[
            list(valid_feature_idxs.values())
        ].values

        # deal with None keys. For example, acceleration

        return trajectory

    def extract_trajectory_data(self, snippet_dict):
        snippet_df, snippet_metadata = self.get_df_and_metadata(snippet_dict)
        snippet_orig_sampling_frequency = snippet_metadata["sampling_frequency"]
        num_skip_samples = int(
            round(snippet_orig_sampling_frequency) / self.target_sampling_frequency
        )
        subsampled_idxs = list(range(0, len(snippet_df), num_skip_samples))
        # (T_orig, F), where F is the feature vector
        full_trajectory = self.extract_full_trajectory_from_df(snippet_df)
        # (T_orig, F)
        (
            normalized_full_trajectory,
            normalization_transformation_matrix_full,
        ) = self.normalize_trajectory(full_trajectory)
        # (T_sub, F)
        subsampled_trajectory = full_trajectory[subsampled_idxs, :]
        # (T_sub, F)
        (
            normalized_subsampled_trajectory,
            normalization_transformation_matrix_subsampled,
        ) = self.normalize_trajectory(subsampled_trajectory)
        return (
            full_trajectory,
            subsampled_trajectory,
            normalized_full_trajectory,
            normalized_subsampled_trajectory,
            normalization_transformation_matrix_full,
            normalization_transformation_matrix_subsampled,
        )

    def tokenize_and_flatten_subtitles(
        self, subtitle_list, tokenizer, add_special_tokens: bool = True
    ):
        # each element will be a list of tokens corresponding to a subtitle
        tokenized_subtitle_list = []
        for subtitle in subtitle_list:
            if subtitle.content == "NA" and subtitle.end == 0:
                tokenized_subtitle_list.append([])
            else:
                tokenized_subtitle_list.append(
                    tokenizer(
                        subtitle.content, add_special_tokens=add_special_tokens
                    ).input_ids
                )

        flattened_tokenized_subtitles = [
            word for sentence in tokenized_subtitle_list for word in sentence
        ]

        return flattened_tokenized_subtitles

    def normalize_trajectory(self, trajectory, normalization_timestep="last"):
        if self.trajectory_normalization_scheme == "local":
            if normalization_timestep == "last":
                # get pose of the last timestep of the ENTIRE trajectory.
                # normalize the whole trajectory with respect to this pose
                (
                    normalized_trajectory,
                    normalization_transformation_matrix,
                ) = CoordinateNormalizationUtil.normalize_3d(
                    trajectory, normalization_frame_timestep=-1
                )
            elif normalization_timestep == "first":
                # get pose of the first timestep of the ENTIRE trajectory.
                # normalize the whole trajectory with respect to this pose
                (
                    normalized_trajectory,
                    normalization_transformation_matrix,
                ) = CoordinateNormalizationUtil.normalize_3d(
                    trajectory, normalization_frame_timestep=0
                )
            elif normalization_timestep == "prediction_timestep":
                # retrieve the pose at the time step corresponding to the
                # define self.prediction_timestep = original_sampling_rate  * past_traj_len
                # prediction_timestep = round(original_sampling_frequency * past_traj_len)
                (
                    normalized_trajectory,
                    normalization_transformation_matrix,
                ) = CoordinateNormalizationUtil.normalize_3d(
                    trajectory, normalization_frame_timestep=self.prediction_timestep
                )
        else:
            return trajectory, np.eye(4)
        return normalized_trajectory, normalization_transformation_matrix

    def extract_local_map(self, snippet_df, T_bg_full, T_bg_subsampled):
        num_ts_snippet = snippet_df.shape[0]

        # compute the mid point of the trajectory along the trajectory. Linestring the trajectory. Find the mid point along the trajectroy
        # decide if you want to deal with z dimension
        traj_linestring = geometry.LineString(
            snippet_df[
                [
                    FEATURE_TO_DF_KEY[TrajFeatureIndex.TRAJ_X],
                    FEATURE_TO_DF_KEY[TrajFeatureIndex.TRAJ_Y],
                ]
            ].values
        )

        traj_midpoint = traj_linestring.interpolate(traj_linestring.length / 2.0)
        # find the map point closest to the mid point of the trajectory. If the person has spunout or whatever it would
        # still be somewhere close to the trajectory. let this be map_idx_closest
        closest_map_idx_to_traj_midpoint = np.argmin(
            (
                np.linalg.norm(
                    self.raceline_xy - np.array(list(traj_midpoint.coords)), axis=1
                )
            )
        )
        # along the raceline how far is this closest point from the start of the raceline
        distance_along_rl_to_closest_map_idx = geometry.LineString(
            self.raceline_xy
        ).line_locate_point(Point(self.raceline_xy[closest_map_idx_to_traj_midpoint]))
        # subtract the longi distance of map_idx_closest from all map linestring points. [-,-,-,0,+++]
        abs_dist_of_map_pts_from_closest_map_pt = np.abs(
            np.array(self.distance_along_rl) - distance_along_rl_to_closest_map_idx
        )
        # grab idx of the map line string which are within distance threshold (absolute value) from map point at map_idx_closest.
        map_idx_within_threshold = np.nonzero(
            abs_dist_of_map_pts_from_closest_map_pt < self.local_map_distance_threshold
        )[0]
        # There would be N points that are within the distance. This N is going to be different for trajectory snippets.
        thresholded_local_map = {}
        for map_key in self.map.keys():
            thresholded_local_map[map_key] = np.array(self.map[map_key])[
                map_idx_within_threshold
            ]

        # normalize the individual points of left, right and raceline of map.

        normalized_thresholded_local_map_full = (
            CoordinateNormalizationUtil.normalize_map_wrt_frame(
                thresholded_local_map, T_bg_full
            )
        )
        normalized_thresholded_local_map_subsampled = (
            CoordinateNormalizationUtil.normalize_map_wrt_frame(
                thresholded_local_map, T_bg_subsampled
            )
        )

        return (
            normalized_thresholded_local_map_full,
            normalized_thresholded_local_map_subsampled,
        )
        # can deal with packing it into same shape in the collate function.
        # package it as [N, F], where F is [lx, ly, rx, ry, cs, cy, cz?, cv?, valid bit?]

        # add post process of maps. to create map vectors or whatever which will take the above and turn into
        # VectorNet like representation or anthing

        # add functor for post processing. cache always the post processed version.

    def get_trajectory_and_map(self, snippet_dict, key_prefix=""):
        trajectory_and_map_dict = {}
        # get full previous trial. chopped into 10 segments.
        # extract full and subsampled trajectory snippets
        (
            full_trajectory,  # at full sampling rate
            subsampled_trajectory,  # at subsampled rate
            normalized_full_trajectory,  # full trajectory normalized
            normalized_subsampled_trajectory,  # subsampled trajectory normalized. Note that this is NOT a subsampled version of normalizied full trajectory but a normalized of the subsampled trajectory
            normalization_transformation_matrix_full,
            normalization_transformation_matrix_subsampled,
        ) = self.extract_trajectory_data(snippet_dict)

        # dict with same keys as self.map, but with only the thresholded points normalized with repsect to the same frame that was used to normalize
        # the corresponding full or subsampled trajectory.
        (
            normalized_thresholded_local_map_full,
            normalized_thresholded_local_map_subsampled,
        ) = self.extract_local_map(
            snippet_dict["df"],
            normalization_transformation_matrix_full,
            normalization_transformation_matrix_subsampled,
        )
        trajectory_and_map_dict[f"{key_prefix}trajectory_inputs"] = np.array(
            normalized_full_trajectory
        )  # (T, F)
        trajectory_and_map_dict[f"{key_prefix}trajectory_inputs_subsampled"] = np.array(
            normalized_subsampled_trajectory
        )  # (T_sub, F)
        trajectory_and_map_dict[
            f"{key_prefix}normalized_thresholded_local_map_full"
        ] = normalized_thresholded_local_map_full  # dict
        trajectory_and_map_dict[
            f"{key_prefix}normalized_thresholded_local_map_subsampled"
        ] = normalized_thresholded_local_map_subsampled

        return trajectory_and_map_dict

    def get_traj_segments_from_trial(self, uid, key_prefix=""):
        segments_dict_for_uid = self.trajectory_segments_pkl_paths_according_to_uid[uid]
        seg_traj_map_data_dict = collections.OrderedDict()
        for seg_id, seg_path in segments_dict_for_uid.items():
            if "metrics" in seg_id:
                continue
            print(seg_id, key_prefix)
            seg_snippet_dict = load_pkl(seg_path)
            # each segment is normalized with repsect to itself.
            seg_tm_dict = self.get_trajectory_and_map(
                seg_snippet_dict, key_prefix=f"{key_prefix}{seg_id}_"
            )
            seg_traj_map_data_dict.update(seg_tm_dict)

        return seg_traj_map_data_dict

        # list all fles in self.trajectory_sgement_dir + trial_dir

        # for each segment id load
        #   load scenario = (traj, map) corresponding map segment.
        #   normalize each traj

        # parse each snippet. decide on whether we want to normalize each trajectory.

    def get_metrics_from_trial(self, uid, key_prefix=""):
        metrics_dict_path_for_uid = self.trajectory_metrics_pkl_paths_according_to_uid[
            uid
        ]["metrics_dict"]

        metrics_dict_for_uid = load_pkl(metrics_dict_path_for_uid)
        selected_metrics_dict = {}
        for mk, sub_key_list in self.metric_keys.items():
            segment_mk_list = metrics_dict_for_uid[f"segment_{mk}"]
            for subkey in sub_key_list:
                if subkey is None:
                    prefix_mk_key = f"{key_prefix}{mk}"
                    selected_metrics_dict[prefix_mk_key] = metrics_dict_for_uid[mk]
                else:
                    prefix_mk_key = f"{key_prefix}{mk}_{subkey}"
                    selected_metrics_dict[prefix_mk_key] = metrics_dict_for_uid[
                        f"{mk}_{subkey}"
                    ]
                for segment_i, segment_i_mk_dict in enumerate(segment_mk_list):
                    if subkey is None:
                        prefix_mk_key = f"{key_prefix}segment_{segment_i+1}_{mk}"
                        selected_metrics_dict[prefix_mk_key] = segment_i_mk_dict[
                            f"{mk}"
                        ]
                    else:
                        prefix_mk_key = (
                            f"{key_prefix}segment_{segment_i+1}_{mk}_{subkey}"
                        )
                        selected_metrics_dict[prefix_mk_key] = segment_i_mk_dict[
                            f"{mk}_{subkey}"
                        ]

        return selected_metrics_dict

    #
    def create_text_only_input_prompt(
        self, trial_num, trial_metrics, key_prefix="prev_"
    ):
        metric_key_prompt_words = {
            "trial_time": "Time Taken: ",
            "oob": "Out of Bounds Percentage: ",
            "steering_score": "Steering Smoothness Score: ",
            "braking_score": "Braking Smoothness Score: ",
            "throttle_score": "Throttle Smoothness Score: ",
            "smoothness_score": "Trajectory Smoothness Score: ",
            "racing_line_score": "Racing Line Score: ",
        }
        # metric_key_prompt_words = {"trial_time": "Time Taken: "}#, "oob": "Out of Bounds Percentage: ", "steering_score": "Steering Smoothness Score: ", "braking_score": "Braking Smoothness Score: ", "throttle_score": "Throttle Smoothness Score: ", "smoothness_score": "Trajectory Smoothness Score: ", "racing_line_score": "Racing Line Score: "}

        input_prompt = STARTER_PROMPT
        input_prompt += f"{trial_num}. The data for the maps segments is as follows. "
        for map_seg_id in range(1, 11):
            input_prompt += f"Map Segment {map_seg_id} - "
            for mk in metric_key_prompt_words.keys():
                sub_key_list = self.metric_keys[mk]
                for subkey in sub_key_list:
                    if subkey is None:
                        prefix_mk_key = f"{key_prefix}segment_{map_seg_id}_{mk}"
                    else:
                        prefix_mk_key = (
                            f"{key_prefix}segment_{map_seg_id}_{mk}_{subkey}"
                        )
                    input_prompt += f"{metric_key_prompt_words[mk]}{trial_metrics[prefix_mk_key]:.3f}, "
        import IPython

        IPython.embed(banner1="check")

        # grab each of the metrics and start packing
        # add histogram of concurrent feedback for each segment.

        # add bsd data at the end of the sequence

    def __getitem__(self, idx):
        """
        this get item should have all the necessary stuff
        for one forward pass of the ENTIRE BSD model. This should also cater to the needs of the IFM as well

        If we consider the trajectory snippet for concurrent feedback as the "indexing" sample

        We can load in a trajectory snippet.
            Full trajectory + Full controls
            Full language that occurred during the snippet
            Full instruction sequence

            Metadata related to which lap, which previous lap, which subject, bsd dict

        Before caching, normalize and rotation with respect to the pose and orientation of a 'pre-defined' timestep.  For IFM
        this could be the last timestep for IFM. Could be the prediction timestamp (somewhere in the middle of the trajectory snippet)
        for concurrent feedback.

        We can load in the full map and normalize it with respect the same frame to which the trajectory was normalized. Zero out any elements that we consider are
        beyond distance threshold. Then this processed map is specific to this trajectory.

        """
        snippet_dict = load_pkl(self.trajectory_snippets_pkl_paths[idx])
        snippet_df = snippet_dict["df"]
        # metadata information. Which gives info of which previous lap to grab, which bsd to grab,
        snippet_metadata = snippet_dict["metadata"]
        pid = snippet_metadata["pid"]
        current_trial_num = snippet_metadata["trial_num"]
        previous_trial_num = snippet_metadata["prev_coaching_trial_num"]
        previous_trial_uid = get_uid_from_pid_and_trial_num(pid, previous_trial_num)
        current_trial_uid = get_uid_from_pid_and_trial_num(pid, current_trial_num)

        # # grab trajectory segments corresponding to each map segment for previous trial
        # previous_trial_trajectory_segments_dict = self.get_traj_segments_from_trial(
        #     previous_trial_uid, key_prefix="prev_"
        # )
        # # grab trajectory segments corresponding to each map segment for current trial
        # current_trial_trajectory_segments_dict = self.get_traj_segments_from_trial(
        #     current_trial_uid, key_prefix="curr_"
        # )
        # snippet_trajectory_and_map_dict = self.get_trajectory_and_map(snippet_dict)
        previous_trial_metrics = self.get_metrics_from_trial(
            previous_trial_uid, key_prefix="prev_"
        )
        current_trial_metrics = self.get_metrics_from_trial(
            current_trial_uid, key_prefix="curr_"
        )

        input_ids = self.create_text_only_input_prompt(
            previous_trial_num, previous_trial_metrics
        )

        import IPython

        IPython.embed(banner1="check dataset")
        # get all unique subtitles (for both coach and driver) from the snippet.
        unique_coach_subtitles_in_snippet = snippet_df["subject_coach_srts"].unique()
        unique_driver_subtitles_in_snippet = snippet_df["subject_driver_srts"].unique()

        flattened_tokenized_coach_subtitles = self.tokenize_and_flatten_subtitles(
            unique_coach_subtitles_in_snippet, self.tokenizer, add_special_tokens=False
        )
        flattened_tokenized_driver_subtitles = self.tokenize_and_flatten_subtitles(
            unique_driver_subtitles_in_snippet, self.tokenizer, add_special_tokens=False
        )
        # string together the prompt and language tokens

        # TODO (deepak.gopinath, andrew silva)
        # make a distinction for language that happens during the drive vs, after the drive
        # depending on whether the LLM is trying to output concurrent language or BSD language, what
        # goes into 'input_ids and 'labels' will be different.
        # There has to be a task flag which can potentially do a remapping
        # from "concurrent_input_ids" -> input_ids or "bsd_input_ids" -> input_ids
        input_ids = (
            self.task_prompt_input_ids
            + self.driver_utterance_prompt_input_ids
            + flattened_tokenized_driver_subtitles
            + self.coach_utterance_prompt_input_ids
            + flattened_tokenized_coach_subtitles
        )

        labels = (
            self.task_prompt_labels
            + self.driver_utterance_prompt_labels
            + [-100] * len(flattened_tokenized_driver_subtitles)  # make this cleaner
            + self.coach_utterance_prompt_labels
            + flattened_tokenized_coach_subtitles  # the only thing the LLM should predict
        )

        # length of subsampled trajectory
        subsampled_trajectory_length = subsampled_trajectory.shape[0]
        #  length of all language, including task and utterance prompts
        language_input_len = len(input_ids)

        # create a placeholder for combined trajectory and language token sequence and initilaize with -200
        # TODO (deepak.gopinath) use macro for -200
        lmm_input = [-200] * (language_input_len + subsampled_trajectory_length)

        # from the design doc
        # combined_input = [p, p, p, ..., -200, -200...., s,s s,s, c,c,c,c]. p = task prompt, -200 = trajectory, s = student tokens, c = coach tokens

        # pack the task prompt at the beginning
        lmm_input[: len(self.task_prompt_input_ids)] = self.task_prompt_input_ids
        driver_and_coach_prompt_and_utterance_len = (
            len(self.driver_utterance_prompt_input_ids)
            + len(flattened_tokenized_driver_subtitles)
            + len(self.coach_utterance_prompt_input_ids)
            + len(flattened_tokenized_coach_subtitles)
        )
        # pack the driver utterance prompt and driver utterance followed by coach utterance prompt and coach utterance at the end
        lmm_input[-driver_and_coach_prompt_and_utterance_len:] = (
            self.driver_utterance_prompt_input_ids
            + flattened_tokenized_driver_subtitles
            + self.coach_utterance_prompt_input_ids
            + flattened_tokenized_coach_subtitles
        )

        attention_mask = [1] * len(lmm_input)  #

        # things to add full trajectory from previous coaching lap, full trajectory from current lap L.
        # BSD dialogue before lap L,
        import IPython

        IPython.embed(banner1="check metadata")

        return {
            "trajectory_inputs": np.array(normalized_full_trajectory),  # (T, F)
            "trajectory_inputs_subsampled": np.array(
                normalized_subsampled_trajectory
            ),  # (T_sub, F)
            "normalized_thresholded_local_map_full": normalized_thresholded_local_map_full,  # dict
            "normalized_thresholded_local_map_subsampled": normalized_thresholded_local_map_subsampled,  # dict
            "input_ids": np.array(
                input_ids
            ),  # (len(language_token), ), deal with int64 to torch.long (can this be done in the collate function)
            "attention_mask": np.array(
                attention_mask
            ),  # (len(language + trajectory), )
            "combined_input": np.array(
                lmm_input
            ),  # language with space to pack trajectory in between
            "labels": np.array(
                labels
            ),  # (len(language), () with non -100 for language tokens we are trying to predict
        }
