#!/usr/bin/env python3
"""
Generate the NeurIPS 2025 dataset submission

Run:
    # Set up dropbox cli
    wget -O dbxcli https://github.com/dropbox/dbxcli/releases/download/v3.0.0/dbxcli-linux-amd64
    chmod +x dbxcli
    # Prompt to authenticate
    ./dbxcli ls

    python create_neurips_dataset.py --data-root ~/Data/ --output-dir ~/Data/ --chunk-size 500


"""
import argparse
import glob
import os
import subprocess
import sys
import tarfile
from pathlib import Path


def chunked(lst, n):
    """Yield successive n-sized chunks from lst."""
    for i in range(0, len(lst), n):
        yield lst[i : i + n]


dataset_files_pattern = {
    "24-D-05": [
        "**/*annotations_and_metrics_with_instruction_category.trial.*",
        "**/*bsd_dict.yaml",
    ],
    "24-D-16": ["**/*_with_all_annotations_and_metrics.trial.*"],
}
dataset_name_map = {"24-D-05": "coached", "24-D-16": "self"}


def prep_dataset(data_name, data_root, output_dir, chuck_size=500):
    data_path = data_root / data_name
    # Find and sort files
    files = []
    for pattern in dataset_files_pattern[data_name]:
        print(f"Searching dataset {data_name} for files matching pattern: {pattern}")
        tmp_files = glob.glob(str(data_path / pattern), recursive=True)
        files.extend(tmp_files)
        print(f"\tFound {len(tmp_files)} matching files")
    files = sorted(files)
    if not files:
        print(f"No files found for dataset {data_name} at: {data_path}")
        return

    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    archive_paths = []
    # Process batches
    for idx, batch in enumerate(chunked(files, chuck_size), start=0):
        base_name = f"{data_name}_{idx:03d}"
        archive_path = os.path.join(output_dir, f"{base_name}.tar.gz")

        with tarfile.open(archive_path, "w:gz") as archive:
            for filepath in batch:
                rel_path = os.path.relpath(filepath, data_root)
                if "bsd_dict.yaml" in rel_path:
                    rel_path = rel_path.replace("bsd_dict.yaml", "tf_dict.yaml")
                rel_path = rel_path.replace(data_name, dataset_name_map[data_name])
                rel_path = rel_path.replace("trials_final/", "trials/")
                archive.add(filepath, arcname=rel_path)

        print(f"Created {archive_path} containing {len(batch)} files.")
        archive_paths.append(archive_path)
    return archive_paths


def upload_file(dbxcli_path, local_path, remote_dir, verbose):
    filename = os.path.basename(local_path)
    remote_path = os.path.join(remote_dir, filename).replace(os.path.sep, "/")
    cmd = [dbxcli_path, "put"]
    # if verbose:
    # cmd.append('-v')
    cmd += [local_path, remote_path]
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Failed: {local_path} -> {remote_path}", file=sys.stderr)
        return False
    else:
        print(f"Uploaded: {local_path} -> {remote_path}")
        return True


def main():
    parser = argparse.ArgumentParser(
        description="Glob files and compress them into batches of N files each."
    )
    parser.add_argument("--data-root", help="The root of datasets", default="~/Data/")
    parser.add_argument(
        "--chunk-size",
        "-n",
        type=int,
        default=500,
        help="Number of files per archive (default: 500)",
    )
    parser.add_argument(
        "--output-dir",
        "-o",
        default=".",
        help="Directory to write archives into (default: current directory)",
    )
    parser.add_argument(
        "--format",
        "-f",
        choices=["zip", "tar.gz"],
        default="tar.gz",
        help="Archive format: zip or tar.gz (default: zip)",
    )

    parser.add_argument(
        "--dbxcli",
        default="./dbxcli",
        help="Path to your dbxcli executable (default: dbxcli)",
    )
    parser.add_argument(
        "-r", "--remote-dir", default="/", help="Remote Dropbox folder (default: /)"
    )

    args = parser.parse_args()

    data_root = Path(os.path.expanduser(args.data_root))

    datasets = ["24-D-05", "24-D-16"]

    archive_paths = []
    for dataset_name in datasets:
        path = prep_dataset(dataset_name, data_root, args.output_dir, args.chunk_size)
        archive_paths.extend(path)

    archive_paths = glob.glob(args.output_dir + "/*.tar.gz")
    success = True
    for local in archive_paths:
        ok = upload_file(args.dbxcli, local, args.remote_dir, True)
        success = success and ok

    sys.exit(0 if success else 2)


if __name__ == "__main__":
    main()
