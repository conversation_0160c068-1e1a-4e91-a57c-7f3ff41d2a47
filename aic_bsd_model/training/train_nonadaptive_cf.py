"""
Training setup configuration for non-adaptive concurrent feedback model training.

This module contains the specific configuration and model setup for training
non-adaptive CF models that work with CF snippets only.
"""

import collections
import copy
import warnings
from typing import Any, Dict, <PERSON><PERSON>

import numpy as np
import torch
from torch.utils.data import DataLoader

from aic_bsd_model.datasets.aic_24d05_cf_dataset import AIC24D05CFDataset
from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    CF_METRICS_LINEAR_SCALE,
    CategoryDictIndex,
    get_pid_and_trial_num_from_uid,
)
from aic_bsd_model.losses.model_loss import (
    compute_classification_metrics,
    compute_model_loss,
    compute_snippet_metrics_prediction_loss,
    compute_teacher_action_prediction_loss,
    compute_trajectory_prediction_loss_ade,
    compute_trajectory_prediction_loss_mse,
)
from aic_bsd_model.models.aic_bsd_full_model import AICBSDFullModel
from aic_bsd_model.training.train import DummyDataset, TrainConfig, run_model
from aic_bsd_model.utils.training_utils import parse_snippet_batch
from aic_bsd_model.utils.visualization_utils import (
    visualize_batch_predicted_cf_future_trajectories,
    visualize_confusion_matrix,
    visualize_histograms,
    visualize_metrics_lineplot,
    visualize_teacher_action_prediction,
)

warnings.filterwarnings("ignore")


class CFModel(AICBSDFullModel):
    """Non-adaptive concurrent feedback model for CF snippet training."""

    def __init__(self, config_dict, train_dataset):
        super().__init__(config_dict, train_dataset)
        self.cf_loss_func_dict = config_dict["cf_loss_func_dict"]
        self.snippet_length_in_sec = config_dict["snippet_length_in_sec"]
        self.prediction_timestep_in_sec = config_dict["prediction_timestep_in_sec"]
        self.decoded_traj_len_in_sec = config_dict.get("decoded_traj_len_in_sec", 5)
        self.cf_target_sampling_frequency = config_dict["cf_target_sampling_frequency"]
        self.cf_target_metric_keys = config_dict.get("cf_target_metric_keys", None)
        self.prediction_timestep = (
            self.prediction_timestep_in_sec * self.cf_target_sampling_frequency
        )
        self.decoded_traj_len = (
            self.decoded_traj_len_in_sec * self.cf_target_sampling_frequency
        )
        self.teacher_action_future_seq_len_in_sec = config_dict.get(
            "teacher_action_future_seq_len_in_sec", 5
        )
        self.teacher_action_future_seq_len = (
            self.teacher_action_future_seq_len_in_sec
            * self.cf_target_sampling_frequency
        )

    def reset_epoch(self):
        super().reset_epoch()
        self.batch_metrics_data = []

    def get_log_dict(self):
        logs = super().get_log_dict()
        if self.should_vis_epoch():
            pass

        # Compute classification metrics

        if self.batch_metrics_data:
            classification_data = {
                k: torch.cat([x[k] for x in self.batch_metrics_data])
                for k in self.batch_metrics_data[0].keys()
                if k != "snippet_lap_uid"
            }

            # if self.epoch_type == 'eval':
            # compute per lap metrics
            all_snippet_lap_uids = []
            for d in self.batch_metrics_data:
                all_snippet_lap_uids.extend(d["snippet_lap_uid"])

            # compute classification metrics according trial id (averaging across all snippets from all subjects for a given trial id)
            trial_num_to_uid_list_dict = collections.defaultdict(list)
            per_trial_num_classification_metrics_dict_list = []
            for idx, uid in enumerate(all_snippet_lap_uids):
                pid, trial_num = get_pid_and_trial_num_from_uid(uid)
                trial_num_to_uid_list_dict[trial_num].append(idx)

            trial_num_to_uid_list_dict = dict(
                sorted(trial_num_to_uid_list_dict.items())
            )

            for trial_num, idxs_for_trial_num in trial_num_to_uid_list_dict.items():
                classification_data_per_trial_num = {
                    k: v[idxs_for_trial_num] for k, v in classification_data.items()
                }
                unweighted_per_trial_classification_metrics_dict = (
                    compute_classification_metrics(
                        classification_data_per_trial_num,
                        self.config,
                        self.epoch_type,
                        is_weighted=False,
                        skip_confusion_matrix=True,
                    )
                )
                weighted_per_trial_num_classification_metrics_dict = (
                    compute_classification_metrics(
                        classification_data_per_trial_num,
                        self.config,
                        self.epoch_type,
                        is_weighted=True,
                    )
                )
                unweighted_per_trial_classification_metrics_dict.update(
                    weighted_per_trial_num_classification_metrics_dict
                )
                per_trial_num_classification_metrics_dict_list.append(
                    unweighted_per_trial_classification_metrics_dict
                )

            counts_per_trial = [len(v) for k, v in trial_num_to_uid_list_dict.items()]
            for key in per_trial_num_classification_metrics_dict_list[0].keys():
                if key == "confusion_matrix":
                    continue
                full_list_for_key = [
                    m[key].item()
                    for m in per_trial_num_classification_metrics_dict_list
                ]
                wandb_key = self.epoch_type + "/" + f"lineplot_{key}_per_trial_num"
                lineplot_logs = visualize_metrics_lineplot(
                    full_list_for_key,
                    list(trial_num_to_uid_list_dict.keys()),
                    counts_per_trial,
                    f"{key}_per_trial_num",
                    f"Lineplot of {key}_per_trial_num",
                    wandb_key,
                )
                logs.update(lineplot_logs)

            # TODO (deepak.gopinath) deal with making lineplots of tp, tn etc per trial_num

            per_uid_classification_metrics_dict_list = []
            for uid in set(all_snippet_lap_uids):
                uid_indices = [
                    i for i, u in enumerate(all_snippet_lap_uids) if u == uid
                ]
                classification_data_per_uid = {
                    k: v[uid_indices] for k, v in classification_data.items()
                }
                uweighted_per_uid_classification_metrics_dict = (
                    compute_classification_metrics(
                        classification_data_per_uid,
                        self.config,
                        self.epoch_type,
                        is_weighted=False,
                        skip_confusion_matrix=True,
                    )
                )
                weighted_per_uid_classification_metrics_dict = (
                    compute_classification_metrics(
                        classification_data_per_uid,
                        self.config,
                        self.epoch_type,
                        is_weighted=True,
                    )
                )
                uweighted_per_uid_classification_metrics_dict.update(
                    weighted_per_uid_classification_metrics_dict
                )
                per_uid_classification_metrics_dict_list.append(
                    uweighted_per_uid_classification_metrics_dict
                )

            # log average
            for key in per_uid_classification_metrics_dict_list[
                0
            ].keys():  # f1score_weighted, f1_score_unweighted etc
                if key == "confusion_matrix":
                    continue
                full_list_for_key = [
                    m[key].item() for m in per_uid_classification_metrics_dict_list
                ]
                wandb_key = self.epoch_type + "/" + f"hist_{key}_per_lap"
                hist_logs = visualize_histograms(
                    full_list_for_key,
                    f"{key}_per_lap",
                    f"Histogram of {key}_per_lap",
                    wandb_key,
                )
                logs.update(hist_logs)
                avg_classification_metric_for_key = np.mean(full_list_for_key)
                logs[
                    self.epoch_type + "/" + key + "_avg_per_lap"
                ] = avg_classification_metric_for_key

            avg_confusion_matrix_per_lap = np.mean(
                np.array(
                    [
                        k["confusion_matrix"]
                        for k in per_uid_classification_metrics_dict_list
                    ]
                ),
                axis=0,
            )
            confusion_matrix_avg_lap_vis_logs = visualize_confusion_matrix(
                avg_confusion_matrix_per_lap,
                self.config,
                self.epoch_type + "/" + "confusion_matrix_avg_per_lap",
            )
            logs.update(confusion_matrix_avg_lap_vis_logs)

            unweighted_classification_metrics_dict = compute_classification_metrics(
                classification_data,
                self.config,
                self.epoch_type,
                is_weighted=False,
                skip_confusion_matrix=True,
            )
            weighted_classification_metrics_dict = compute_classification_metrics(
                classification_data, self.config, self.epoch_type, is_weighted=True
            )
            unweighted_classification_metrics_dict.update(
                weighted_classification_metrics_dict
            )
            for key in unweighted_classification_metrics_dict:
                if key == "confusion_matrix":
                    continue
                logs[
                    self.epoch_type + "/" + key
                ] = unweighted_classification_metrics_dict[key].item()

            confusion_matrix_vis_logs = visualize_confusion_matrix(
                np.array(unweighted_classification_metrics_dict["confusion_matrix"]),
                self.config,
                self.epoch_type + "/" + "confusion_matrix",
            )
            logs.update(confusion_matrix_vis_logs)

        return logs

    def prep_batch(self, batch):
        super().prep_batch(batch)
        batch["cf_metrics"] = {}
        for key in self.cf_target_metric_keys.keys():
            batch_metrics_key = f"snippet_{key}_series_subsampled"
            # batch["cf_metrics"][key] = batch[batch_metrics_key]
            metrics_subkey = self.cf_target_metric_keys[key]
            if metrics_subkey == "abs_mean":
                # (B)
                batch["cf_metrics"][key] = (
                    torch.mean(torch.abs(batch[batch_metrics_key]), axis=1)
                    * CF_METRICS_LINEAR_SCALE[key][metrics_subkey]
                )
            elif metrics_subkey == "mean":
                # (B)
                batch["cf_metrics"][key] = (
                    torch.mean(batch[batch_metrics_key], axis=1)
                    * CF_METRICS_LINEAR_SCALE[key][metrics_subkey]
                )

    def compute_loss(self, batch, outputs, out_metrics, additional_args):
        """
        Compute loss for non-adaptive CF model.

        Args:
            outputs: output from forward function
            batch: batch of inputs
            out_metrics: dict to append metrics to
        """
        if "append_cf_metrics" not in additional_args:
            additional_args["append_cf_metrics"] = True

        (
            snippet_trajectories,
            snippet_map_segments,
            snippet_full_trajectory_lengths,
            snippet_cf_action_series,
        ) = parse_snippet_batch(batch, return_cf_series=True)

        # Target future trajectory
        target_segments_traj = snippet_trajectories[
            :, self.prediction_timestep :, :
        ].unsqueeze(1)
        snippet_predicted_trajectory_lengths = self.decoded_traj_len * torch.ones_like(
            snippet_full_trajectory_lengths
        )
        target_teacher_action_sequence = snippet_cf_action_series[
            :,
            self.prediction_timestep : self.prediction_timestep
            + self.teacher_action_future_seq_len,
        ]
        if target_teacher_action_sequence.ndim == 2:
            target_teacher_action_sequence = target_teacher_action_sequence.unsqueeze(1)
        batch_size, num_snippets, seq_len = target_teacher_action_sequence.shape

        # Teacher action existence ground truth
        cf_teacher_action_existence_gt = torch.cat(
            [
                torch.bincount(
                    item, minlength=self.config["teacher_action_num_categories"]
                ).unsqueeze(0)
                for item in target_teacher_action_sequence.view(-1, seq_len)
            ],
            dim=0,
        )
        cf_teacher_action_existence_gt = cf_teacher_action_existence_gt.view(
            batch_size, num_snippets, -1
        )
        cf_teacher_action_existence_gt = (cf_teacher_action_existence_gt != 0).float()
        batch["cf_teacher_action_existence_gt"] = cf_teacher_action_existence_gt

        loss_computation_dict = {
            "predicted_future_trajectories": outputs["cf_predicted_future_trajectories"]
            if "cf_predicted_future_trajectories" in outputs
            else None,
            "target_segment_trajs": target_segments_traj,
            "future_pred_mask": outputs["cf_future_pred_mask"],
            "segments_length": snippet_predicted_trajectory_lengths,
            "cf_teacher_action_existence_gt": cf_teacher_action_existence_gt,
            "cf_teacher_action_pred": batch["cf_teacher_action_pred"],
            "cf_teacher_action_sig": batch["cf_teacher_action_sig"],
            "target_teacher_action_sequence": target_teacher_action_sequence,
            "loss_func_dict": self.cf_loss_func_dict,
            "lap_segments_data_valid": batch["cf_snippet_past_traj_valid"],
            "map_data_valid": batch["cf_snippet_map_data_valid"],
            "pos_weights_for_classes": self.config["train_dataset_stats"][
                "pos_weights_for_classes"
            ],
            "epoch": batch["epoch"],
            "cf_metrics": batch["cf_metrics"],
            "cf_metrics_decoding_loss_type": self.config.get(
                "cf_metrics_decoding_loss_type", "l2"
            ),
        }

        (
            total_loss,
            batch_loss_components,
            scaled_batch_loss_components,
        ) = compute_model_loss(loss_computation_dict, self.config, batch["epoch_type"])

        # Store metrics for logging
        for key in scaled_batch_loss_components:
            loss_computation_dict[key] = scaled_batch_loss_components[key].item()
        out_metrics["my_loss_metrics"].append(total_loss.cpu().detach().numpy())
        for suffix, d in [
            ("", batch_loss_components),
            ("_scaled", scaled_batch_loss_components),
        ]:
            for key in d:
                out_metrics[key + suffix].append(d[key].cpu().detach().numpy())

        if additional_args["append_cf_metrics"]:
            self.batch_metrics_data.append(
                {
                    "cf_teacher_action_existence_gt": cf_teacher_action_existence_gt.cpu().detach(),
                    "cf_teacher_action_pred": batch["cf_teacher_action_pred"]
                    .cpu()
                    .detach(),
                    "cf_teacher_action_sig": batch["cf_teacher_action_sig"]
                    .cpu()
                    .detach(),
                    "target_teacher_action_sequence": target_teacher_action_sequence.cpu().detach(),
                    "snippet_lap_uid": batch["snippet_lap_uid"],
                }
            )
        cf_loss_computation_dict = {}
        return total_loss, (cf_loss_computation_dict, loss_computation_dict)

    def visualize_batch(self, batch, loss, extra_loss, count=1):
        vis_logs = visualize_batch_predicted_cf_future_trajectories(
            batch,
            self.epoch_type,
            self.config,
            count,
            visualize_teacher_action_prediction,
        )
        return vis_logs


class TrainConfigNonadaptive(TrainConfig):
    def get_config(self, args) -> Dict[str, Any]:
        """Get default configuration for non-adaptive CF model training."""
        config_dict = {
            "seed": 42,
            "training_mode": "cf_only",
            "traj_input_dim": 13,
            "map_input_dim": 6,
            "hidden_dim": 32,
            "lap_encoder_type": "TransformerTrajEncoder",
            "lap_decoder_type": "LSTMDecoder",
            "metrics_decoder_type": "MetricsDecoderSimpleMLP",
            "lap_emb_use_cls_global_token": True,
            "num_attention_layers": 2,
            "num_attention_heads": 4,
            "transformer_dropout": 0.2,
            "num_layers": 3,
            "traj_decoder_output_dim": 2,
            "max_future_steps": 70,
            "dropout": 0.2,
            "segment_dropout_ratio": 0.0,
            "weight_decay": 3e-3,
            "lr": 0.0005,
            "batch_size": 64,
            "val_batch_size": 64,
            "use_trajectory_reconstruction": False,
            "num_workers": 10,
            "global_num_epochs": 250,
            "target_metric_keys": ["trial_time", "smoothness_score_mean"],
            "cf_target_metric_keys": {
                "racing_line_score": "abs_mean",
                "smoothness_score": "mean",
            },
            "num_validation_epochs": 5,
            "verbose_frequency": 5,
            "verbose": True,
            "vis_interval": 80,
            "vis_num_instances": 6,
            "save_interval": 10,
            "index_sampling_noise": 0.0,
            "constant_shift_noise": 0.0,
            "constant_scale_noise": 0.0,
            "epoch_data_train_size": 2048,
            "use_random_sampler": False,
            "predict_delta": True,
            "test_pid": None,
        }

        cf_loss_config = {
            "segment_traj_prediction_ade_loss_coeff": 0.0,
            "segment_traj_prediction_mse_loss_coeff": 2.0,
            "snippet_teacher_action_prediction_loss_coeff": 2.0,
            "snippet_metrics_prediction_loss_coeff": 2.0,
            "use_reconstruction_norm_loss": False,
            "traj_prediction_loss_type": "mse",
        }

        dataset_config = {
            # TODO consider separate sampling freqencies for laps and cf snippets
            "cf_target_sampling_frequency": 5,
            "trajectory_normalization_scheme": "local",
            "global_trajectory_normalization_info": None,
            "local_map_distance_threshold": 100,
            "prediction_timestep_in_sec": 5,  # used with CFDataset
            "normalization_timestep": "first",
            "with_cf_snippets": True,  # should be false for 24D16Lap, 24D05Lap, True for 24D05Full, True for 24D05CF
            "num_cf_snippets_per_lap": 1,  # unused with 24D16Lap. Keep this at 1 for 24D05CF.
            "snippet_filename": "snippet.pkl",
            "snippet_length_in_sec": 10,
            "decoded_traj_len_in_sec": 5,
            "teacher_action_future_seq_len_in_sec": 5,
            "annotation_majority_threshold": 0.6,
            "padding_side": "right",
            "padding_value": -2000,
            "use_full_sequence": False,
            "num_prev_laps": 5,
            "return_cf_for_lap": False,
            "enable_dataset_cache": True,
            "index_sampling_noise": 0.0,
            "constant_shift_noise": 0.0,
            "constant_scale_noise": 0.0,
            "vis_video_interval": 2,  # Make video every 2 vis epochs (vis_interval*2)
            "vis_video_num_trial": 4,
            "return_next_lap_metrics": True,
            "valid_annotation_categories_index_dict_type": args.category_index_type,
            "include_all_null": False,
        }

        cf_model_config = {
            "cf_use_trajectory_prediction": True,
            "is_adaptive": False,
            "teacher_action_loss_type": "existence",
            "cf_encoder_type": "TransformerTrajEncoder",
            "cf_traj_decoder_type": "LSTMDecoder",
            "cf_use_teacher_action_encoding": False,
            "teacher_action_encoding_type": "existence",
            "cf_use_metrics_prediction": False,
            "side_channel_dropout": 0.0,
            "snippet_past_encoding_dropout": 0.0,
            "cf_map_channel_dropout": 0.0,
            "use_gaussian_dropout": False,
            "gaussian_dropout_ratio": 0.2,
            "cf_metrics_decoder_type": "MetricsDecoderSimpleMLP",
        }

        config_dict.update(cf_loss_config)
        config_dict.update(dataset_config)
        config_dict.update(cf_model_config)

        if args.category_index_type == "default":
            teacher_action_num_categories = (
                max(CategoryDictIndex.DEFAULT.value.values()) + 1
            )
        elif args.category_index_type == "no_steering":
            teacher_action_num_categories = (
                max(CategoryDictIndex.NO_STEERING.value.values()) + 1
            )
        elif args.category_index_type == "no_steering_no_turn":
            teacher_action_num_categories = (
                max(CategoryDictIndex.NO_STEERING_NO_TURN.value.values()) + 1
            )
        elif args.category_index_type == "no_steering_no_turn_no_brake":
            teacher_action_num_categories = (
                max(CategoryDictIndex.NO_STEERING_NO_TURN_NO_BRAKE.value.values()) + 1
            )
        elif args.category_index_type == "basic":
            teacher_action_num_categories = (
                max(CategoryDictIndex.BASIC.value.values()) + 1
            )

        config_dict["teacher_action_num_categories"] = teacher_action_num_categories

        return config_dict

    def setup_datasets(self, config_dict: Dict[str, Any]) -> Tuple[Any, Any, Any]:
        """Set up datasets for non-adaptive CF model training."""
        if config_dict["online_inference"]:
            return DummyDataset(), DummyDataset(), DummyDataset()
        train_dataset = AIC24D05CFDataset(config_dict, dataset_type="train")
        train_dataset.cache_dataset()
        config_dict["train_dataset_stats"] = train_dataset.get_dataset_stats()

        vis_dataset = copy.deepcopy(train_dataset)

        eval_dataset = AIC24D05CFDataset(config_dict, dataset_type="test")
        eval_dataset.cache_dataset()

        print(f"Size of train dataset: {len(train_dataset)} snippets")
        print(f"Size of test dataset: {len(eval_dataset)} snippets")

        return train_dataset, eval_dataset, vis_dataset

    def setup_loss_functions(
        self,
    ) -> Dict[str, Any]:
        """Set up loss functions for non-adaptive CF model training."""
        if self.config["cf_use_metrics_prediction"]:
            self.config.update(
                {
                    "loss_component_names": [
                        "segment_traj_prediction_ade_loss",
                        "segment_traj_prediction_mse_loss",
                        "snippet_teacher_action_prediction_loss",
                        "snippet_metrics_prediction_loss",
                    ]
                }
            )
        else:
            self.config.update(
                {
                    "loss_component_names": [
                        "segment_traj_prediction_ade_loss",
                        "segment_traj_prediction_mse_loss",
                        "snippet_teacher_action_prediction_loss",
                    ]
                }
            )
        if self.config["cf_use_metrics_prediction"]:
            return {
                "segment_traj_prediction_mse_loss": compute_trajectory_prediction_loss_mse,
                "segment_traj_prediction_ade_loss": compute_trajectory_prediction_loss_ade,
                "snippet_teacher_action_prediction_loss": compute_teacher_action_prediction_loss,
                "snippet_metrics_prediction_loss": compute_snippet_metrics_prediction_loss,
            }, {}
        else:
            return {
                "segment_traj_prediction_mse_loss": compute_trajectory_prediction_loss_mse,
                "segment_traj_prediction_ade_loss": compute_trajectory_prediction_loss_ade,
                "snippet_teacher_action_prediction_loss": compute_teacher_action_prediction_loss,
            }, {}

    def create_model(
        self, config_dict: Dict[str, Any], train_dataset: DataLoader, eval_dataset
    ) -> CFModel:
        """Create and return a non-adaptive CF model instance."""
        return CFModel(config_dict, train_dataset)


def main(args=None):
    config_cls = TrainConfigNonadaptive()
    return run_model(config_cls, args)


if __name__ == "__main__":
    main()
