"""
Training script for adaptive concurrent feedback models.

This script uses the common training utilities and adaptive CF-specific setup
to train models that can use side channel information from lap encodings.
"""

import collections
import copy
from typing import Any, Dict, <PERSON><PERSON>

import numpy as np
import torch
from torch.utils.data import DataLoader

from aic_bsd_model.datasets.aic_24d05_combined_dataset import (
    AIC24D05CombinedPrevKDataset,
)
from aic_bsd_model.datasets.aic_24d05_lap_dataset import AIC24D05LapDataset
from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    CF_METRICS_LINEAR_SCALE,
    CategoryDictIndex,
    get_pid_and_trial_num_from_uid,
)
from aic_bsd_model.losses.model_loss import (  # compute_infonce_loss,
    compute_classification_metrics,
    compute_kld_loss,
    compute_lap_representation_smoothness_loss,
    compute_metrics_decoding_loss,
    compute_model_loss,
    compute_snippet_metrics_prediction_loss,
    compute_sparsity_loss,
    compute_teacher_action_prediction_loss,
    compute_trajectory_prediction_loss_ade,
    compute_trajectory_prediction_loss_mse,
    compute_trajectory_reconstruction_loss,
    compute_trajectory_reconstruction_norm_loss,
)
from aic_bsd_model.models.aic_bsd_full_model import AICBSDFullModel
from aic_bsd_model.models.hf_model import EVAL_EPOCH
from aic_bsd_model.training.train import DummyDataset, TrainConfig, run_model
from aic_bsd_model.utils.training_utils import (
    parse_arguments,
    parse_batch,
    parse_config,
    parse_snippet_batch,
)
from aic_bsd_model.utils.visualization_utils import (
    VideoVisCFDataset,
    image_to_video,
    plot_metric,
    visualize_batch_predicted_cf_future_trajectories,
    visualize_batch_reconstructed_lap_segments,
    visualize_confusion_matrix,
    visualize_histograms,
    visualize_metrics_lineplot,
    visualize_teacher_action_prediction,
)


class AdaptiveCFModel(AICBSDFullModel):
    """Adaptive concurrent feedback model with side channel information."""

    def __init__(self, config_dict, train_dataset, eval_dataset):
        super().__init__(config_dict, train_dataset)
        self.eval_dataset = eval_dataset
        self.cf_loss_func_dict = config_dict["cf_loss_func_dict"]
        self.lap_loss_func_dict = config_dict["lap_loss_func_dict"]
        self.snippet_length_in_sec = config_dict["snippet_length_in_sec"]
        self.prediction_timestep_in_sec = config_dict["prediction_timestep_in_sec"]
        self.decoded_traj_len_in_sec = config_dict.get("decoded_traj_len_in_sec", 5)
        self.cf_target_sampling_frequency = config_dict["cf_target_sampling_frequency"]
        self.cf_target_metric_keys = config_dict.get("cf_target_metric_keys", None)
        self.prediction_timestep = (
            self.prediction_timestep_in_sec * self.cf_target_sampling_frequency
        )
        self.decoded_traj_len = (
            self.decoded_traj_len_in_sec * self.cf_target_sampling_frequency
        )
        self.teacher_action_future_seq_len_in_sec = config_dict.get(
            "teacher_action_future_seq_len_in_sec", 5
        )
        self.teacher_action_future_seq_len = (
            self.teacher_action_future_seq_len_in_sec
            * self.cf_target_sampling_frequency
        )
        self.predict_next_lap_metrics = config_dict.get(
            "predict_next_lap_metrics", False
        )

    def reset_epoch(self):
        super().reset_epoch()
        self.cf_batch_metrics_data = []
        self.estimated_metric_dict = {
            key: [] for key in self.config["target_metric_keys"]
        }
        self.gt_metric_dict = {key: [] for key in self.config["target_metric_keys"]}

    def concatenate_and_plot_metrics(self):
        # Concatenate metrics at the end of the epoch
        est, gt = {}, {}
        for key in self.estimated_metric_dict:
            est[key] = torch.cat(self.estimated_metric_dict[key], dim=0)
        for key in self.gt_metric_dict:
            gt[key] = torch.cat(self.gt_metric_dict[key], dim=0)

        plots = plot_metric(est, gt)
        return plots

    def get_log_dict(self):
        logs = super().get_log_dict()
        if self.should_vis_epoch():
            metrics_scatters = self.concatenate_and_plot_metrics()
            metrics_scatters = {
                f"{self.epoch_type}/{k}": v for k, v in metrics_scatters.items()
            }
            logs.update(metrics_scatters)

        # Compute classification metrics
        if self.cf_batch_metrics_data:
            classification_data = {
                k: torch.cat([x[k] for x in self.cf_batch_metrics_data])
                for k in self.cf_batch_metrics_data[0].keys()
                if k != "snippet_lap_uid"
            }
            # if self.epoch_type == 'eval':
            # compute per lap metrics
            all_snippet_lap_uids = []
            for d in self.cf_batch_metrics_data:
                all_snippet_lap_uids.extend(d["snippet_lap_uid"])

            # compute classification metrics according trial id (averaging across all snippets from all subjects for a given trial id)
            trial_num_to_uid_list_dict = collections.defaultdict(list)
            per_trial_num_classification_metrics_dict_list = []
            for idx, uid in enumerate(all_snippet_lap_uids):
                pid, trial_num = get_pid_and_trial_num_from_uid(uid)
                trial_num_to_uid_list_dict[trial_num].append(idx)

            trial_num_to_uid_list_dict = dict(
                sorted(trial_num_to_uid_list_dict.items())
            )

            for trial_num, idxs_for_trial_num in trial_num_to_uid_list_dict.items():
                classification_data_per_trial_num = {
                    k: v[idxs_for_trial_num] for k, v in classification_data.items()
                }
                unweighted_per_trial_classification_metrics_dict = (
                    compute_classification_metrics(
                        classification_data_per_trial_num,
                        self.config,
                        self.epoch_type,
                        is_weighted=False,
                        skip_confusion_matrix=True,
                    )
                )
                weighted_per_trial_num_classification_metrics_dict = (
                    compute_classification_metrics(
                        classification_data_per_trial_num,
                        self.config,
                        self.epoch_type,
                        is_weighted=True,
                    )
                )
                unweighted_per_trial_classification_metrics_dict.update(
                    weighted_per_trial_num_classification_metrics_dict
                )
                per_trial_num_classification_metrics_dict_list.append(
                    unweighted_per_trial_classification_metrics_dict
                )

            counts_per_trial = [len(v) for k, v in trial_num_to_uid_list_dict.items()]
            for key in per_trial_num_classification_metrics_dict_list[0].keys():
                if key == "confusion_matrix":
                    continue
                full_list_for_key = [
                    m[key].item()
                    for m in per_trial_num_classification_metrics_dict_list
                ]
                wandb_key = self.epoch_type + "/" + f"lineplot_{key}_per_trial_num"
                lineplot_logs = visualize_metrics_lineplot(
                    full_list_for_key,
                    list(trial_num_to_uid_list_dict.keys()),
                    counts_per_trial,
                    f"{key}_per_trial_num",
                    f"Lineplot of {key}_per_trial_num",
                    wandb_key,
                )
                logs.update(lineplot_logs)

            # TODO (deepak.gopinath) deal with making lineplots of tp, tn etc per trial_num

            # classification metrics computed per lap.
            per_uid_classification_metrics_dict_list = []
            for uid in set(all_snippet_lap_uids):
                uid_indices = [
                    i for i, u in enumerate(all_snippet_lap_uids) if u == uid
                ]
                classification_data_per_uid = {
                    k: v[uid_indices] for k, v in classification_data.items()
                }
                uweighted_per_uid_classification_metrics_dict = (
                    compute_classification_metrics(
                        classification_data_per_uid,
                        self.config,
                        self.epoch_type,
                        is_weighted=False,
                        skip_confusion_matrix=True,
                    )
                )
                weighted_per_uid_classification_metrics_dict = (
                    compute_classification_metrics(
                        classification_data_per_uid,
                        self.config,
                        self.epoch_type,
                        is_weighted=True,
                    )
                )
                uweighted_per_uid_classification_metrics_dict.update(
                    weighted_per_uid_classification_metrics_dict
                )
                per_uid_classification_metrics_dict_list.append(
                    uweighted_per_uid_classification_metrics_dict
                )

            # log average
            for key in per_uid_classification_metrics_dict_list[
                0
            ].keys():  # f1score_weighted, f1_score_unweighted etc
                if key == "confusion_matrix":
                    continue
                full_list_for_key = [
                    m[key].item() for m in per_uid_classification_metrics_dict_list
                ]
                wandb_key = self.epoch_type + "/" + f"hist_{key}_per_lap"
                hist_logs = visualize_histograms(
                    full_list_for_key,
                    f"{key}_per_lap",
                    f"Histogram of {key}_per_lap",
                    wandb_key,
                )
                logs.update(hist_logs)
                avg_classification_metric_for_key = np.mean(full_list_for_key)
                logs[
                    self.epoch_type + "/" + key + "_avg_per_lap"
                ] = avg_classification_metric_for_key

            avg_confusion_matrix_per_lap = np.mean(
                np.array(
                    [
                        k["confusion_matrix"]
                        for k in per_uid_classification_metrics_dict_list
                    ]
                ),
                axis=0,
            )
            confusion_matrix_avg_lap_vis_logs = visualize_confusion_matrix(
                avg_confusion_matrix_per_lap,
                self.config,
                self.epoch_type + "/" + "confusion_matrix_avg_per_lap",
            )
            logs.update(confusion_matrix_avg_lap_vis_logs)

            # classification data computed on the whole dataset
            unweighted_classification_metrics_dict = compute_classification_metrics(
                classification_data,
                self.config,
                self.epoch_type,
                is_weighted=False,
                skip_confusion_matrix=True,
            )
            weighted_classification_metrics_dict = compute_classification_metrics(
                classification_data, self.config, self.epoch_type, is_weighted=True
            )
            unweighted_classification_metrics_dict.update(
                weighted_classification_metrics_dict
            )
            for key in unweighted_classification_metrics_dict:
                if key == "confusion_matrix":
                    continue
                logs[
                    self.epoch_type + "/" + key
                ] = unweighted_classification_metrics_dict[key].item()

            confusion_matrix_vis_logs = visualize_confusion_matrix(
                np.array(unweighted_classification_metrics_dict["confusion_matrix"]),
                self.config,
                self.epoch_type + "/" + "confusion_matrix",
            )
            logs.update(confusion_matrix_vis_logs)

        return logs

    def prep_batch(self, batch):
        super().prep_batch(batch)
        batch["metrics"] = {}
        prefix = "next_" if self.predict_next_lap_metrics else "curr_"
        for key in self.metric_keys:
            batch["metrics"][key] = batch[prefix + key]

        batch["cf_metrics"] = {}
        for key in self.cf_target_metric_keys.keys():
            batch_metrics_key = f"snippet_{key}_series_subsampled"
            # batch["cf_metrics"][key] = batch[batch_metrics_key]
            metrics_subkey = self.cf_target_metric_keys[key]
            if metrics_subkey == "abs_mean":
                # (B)
                batch["cf_metrics"][key] = (
                    torch.mean(torch.abs(batch[batch_metrics_key]), axis=1)
                    * CF_METRICS_LINEAR_SCALE[key][metrics_subkey]
                )
            elif metrics_subkey == "mean":
                # (B)
                batch["cf_metrics"][key] = (
                    torch.mean(batch[batch_metrics_key], axis=1)
                    * CF_METRICS_LINEAR_SCALE[key][metrics_subkey]
                )

    def compute_cf_loss(self, batch, outputs, out_metrics, additional_args):
        """Compute loss for concurrent feedback component."""
        (
            snippet_trajectories,
            snippet_map_segments,
            snippet_full_trajectory_lengths,
            snippet_cf_action_series,
        ) = parse_snippet_batch(batch, return_cf_series=True)

        # Target future trajectory
        target_segments_traj = snippet_trajectories[
            :, self.prediction_timestep :, :
        ].unsqueeze(1)
        snippet_predicted_trajectory_lengths = self.decoded_traj_len * torch.ones_like(
            snippet_full_trajectory_lengths
        )
        target_teacher_action_sequence = snippet_cf_action_series[
            :,
            self.prediction_timestep : self.prediction_timestep
            + self.teacher_action_future_seq_len,
        ]
        if target_teacher_action_sequence.ndim == 2:
            target_teacher_action_sequence = target_teacher_action_sequence.unsqueeze(1)

        batch_size, num_snippets, seq_len = target_teacher_action_sequence.shape

        # Teacher action existence ground truth
        cf_teacher_action_existence_gt = torch.cat(
            [
                torch.bincount(
                    item, minlength=self.config["teacher_action_num_categories"]
                ).unsqueeze(0)
                for item in target_teacher_action_sequence.view(-1, seq_len)
            ],
            dim=0,
        )
        cf_teacher_action_existence_gt = cf_teacher_action_existence_gt.view(
            batch_size, num_snippets, -1
        )
        cf_teacher_action_existence_gt = (cf_teacher_action_existence_gt != 0).float()
        batch["cf_teacher_action_existence_gt"] = cf_teacher_action_existence_gt

        cf_loss_computation_dict = {
            "predicted_future_trajectories": outputs["cf_predicted_future_trajectories"]
            if "cf_predicted_future_trajectories" in outputs
            else None,
            "target_segment_trajs": target_segments_traj,
            "future_pred_mask": outputs["cf_future_pred_mask"],
            "segments_length": snippet_predicted_trajectory_lengths,
            "cf_teacher_action_existence_gt": cf_teacher_action_existence_gt,
            "cf_teacher_action_pred": batch["cf_teacher_action_pred"],
            "cf_teacher_action_sig": batch["cf_teacher_action_sig"],
            "target_teacher_action_sequence": target_teacher_action_sequence,
            "loss_func_dict": self.cf_loss_func_dict,
            "lap_segments_data_valid": batch["cf_snippet_past_traj_valid"],
            "map_data_valid": batch["cf_snippet_map_data_valid"],
            "pos_weights_for_classes": self.config["train_dataset_stats"][
                "pos_weights_for_classes"
            ],
            "epoch": batch["epoch"],
            "cf_metrics": batch["cf_metrics"],
            "cf_metrics_decoding_loss_type": self.config.get(
                "cf_metrics_decoding_loss_type", "l2"
            ),
        }

        (
            cf_total_loss,
            cf_batch_loss_components,
            cf_scaled_batch_loss_components,
        ) = compute_model_loss(
            cf_loss_computation_dict,
            self.config,
            batch["epoch_type"],
            loss_component_name_key_prefix="cf_",
        )

        # Store metrics for logging
        for key in cf_scaled_batch_loss_components:
            cf_loss_computation_dict[key] = cf_scaled_batch_loss_components[key].item()
        out_metrics["my_cf_loss_metrics"].append(cf_total_loss.cpu().detach().numpy())
        for suffix, d in [
            ("", cf_batch_loss_components),
            ("_scaled", cf_scaled_batch_loss_components),
        ]:
            for key in d:
                out_metrics[key + suffix].append(d[key].cpu().detach().numpy())

        if additional_args["append_cf_metrics"]:
            self.cf_batch_metrics_data.append(
                {
                    "cf_teacher_action_existence_gt": cf_teacher_action_existence_gt.cpu().detach(),
                    "cf_teacher_action_pred": batch["cf_teacher_action_pred"]
                    .cpu()
                    .detach(),
                    "cf_teacher_action_sig": batch["cf_teacher_action_sig"]
                    .cpu()
                    .detach(),
                    "target_teacher_action_sequence": target_teacher_action_sequence.cpu().detach(),
                    "snippet_lap_uid": batch["snippet_lap_uid"],
                }
            )

        return cf_total_loss, cf_loss_computation_dict

    def compute_lap_loss(self, batch, outputs, out_metrics, additional_args):
        """Compute loss for lap component."""
        if self.config["lap_model_temporal_structure"] is None:
            (
                curr_trajectory_segments_full,
                curr_map_segments_full,
                curr_traj_segments_lengths_full,
            ) = parse_batch(batch)
            # create dummy lap masks_full when k=1
            lap_masks_full = torch.ones(
                curr_trajectory_segments_full.shape[:2],
                device=curr_trajectory_segments_full.device,
                dtype=curr_trajectory_segments_full.dtype,
            )
        else:
            (
                curr_trajectory_segments_full,
                curr_map_segments_full,
                curr_traj_segments_lengths_full,
                lap_masks_full,
            ) = parse_batch(batch, return_lap_masks=True)

        # Flatten (B, K) --> (B*K)
        curr_trajectory_segments = curr_trajectory_segments_full.view(
            -1, *curr_trajectory_segments_full.shape[2:]
        )
        curr_traj_segments_lengths = curr_traj_segments_lengths_full.view(
            -1, *curr_traj_segments_lengths_full.shape[2:]
        )
        reconstructed_segment_trajectories = outputs[
            "reconstructed_segment_trajectories"
        ].view(-1, *outputs["reconstructed_segment_trajectories"].shape[2:])
        reconstructed_pred_mask = outputs["reconstructed_pred_mask"].view(
            -1, *outputs["reconstructed_pred_mask"].shape[2:]
        )
        lap_segments_data_valid = batch["lap_segments_data_valid"].view(
            -1, *batch["lap_segments_data_valid"].shape[2:]
        )
        map_data_valid = batch["map_data_valid"].view(
            -1, *batch["map_data_valid"].shape[2:]
        )
        lap_decoded_metrics = outputs["lap_decoded_metrics"]
        lap_masks_flattened = lap_masks_full.view(-1)

        for mk in batch["metrics"]:
            batch["metrics"][mk] = batch["metrics"][mk].view(-1)

        for mk in lap_decoded_metrics:
            lap_decoded_metrics[mk] = lap_decoded_metrics[mk].view(-1)

        lap_loss_computation_dict = {
            "reconstructed_segment_trajectories": reconstructed_segment_trajectories,
            "target_segment_trajs": curr_trajectory_segments,
            "reconstructed_pred_mask": reconstructed_pred_mask,
            "segments_length": curr_traj_segments_lengths,
            "decoded_metrics": lap_decoded_metrics,
            "loss_func_dict": self.lap_loss_func_dict,
            "lap_segments_data_valid": lap_segments_data_valid,
            "map_data_valid": map_data_valid,
            "metrics": batch["metrics"],
            "batch_item_masks": lap_masks_flattened,
            "batch_item_masks_non_flattened": lap_masks_full,
            "sparsity_loss_key": "side_channel_lap_representation",
            "side_channel_lap_representation": batch["side_channel_lap_representation"],
            "sparsity_dim": 1,
            "metrics_decoding_loss_type": self.config.get(
                "metrics_decoding_loss_type", "l2"
            ),
            "latent_z_sequence": batch["latent_z_sequence"],
            "updated_latent_z_sequence": batch["updated_latent_z_sequence"],
            "smoothness_loss_type": self.config.get("smoothness_loss_type", "l2"),
            "epoch": batch["epoch"],
        }
        if self.config["lap_model_temporal_structure"] == "LapModelTemporalVRNN":
            lap_loss_computation_dict.update(
                {
                    "enc_mean_t": outputs["enc_mean_t"],
                    "enc_std_t": outputs["enc_std_t"],
                    "prior_mean_t": outputs["prior_mean_t"],
                    "prior_std_t": outputs["prior_std_t"],
                }
            )

        (
            lap_total_loss,
            lap_batch_loss_components,
            lap_scaled_batch_loss_components,
        ) = compute_model_loss(
            lap_loss_computation_dict,
            self.config,
            batch["epoch_type"],
            loss_component_name_key_prefix="lap_",
        )

        if torch.isnan(lap_total_loss):
            import IPython

            IPython.embed(banner1="check loss nan")

        # Store metrics for logging
        for key in lap_scaled_batch_loss_components:
            lap_loss_computation_dict[key] = lap_scaled_batch_loss_components[
                key
            ].item()
        out_metrics["my_lap_loss_metrics"].append(lap_total_loss.cpu().detach().numpy())
        for suffix, d in [
            ("", lap_batch_loss_components),
            ("_scaled", lap_scaled_batch_loss_components),
        ]:
            for key in d:
                out_metrics[key + suffix].append(d[key].cpu().detach().numpy())
        return lap_total_loss, lap_loss_computation_dict

    def compute_metrics_vrnn_loss(self, batch, outputs, out_metrics, additional_args):
        (
            _,
            _,
            _,
            lap_masks_full,
        ) = parse_batch(batch, return_lap_masks=True)
        lap_decoded_metrics = outputs["lap_decoded_metrics"]

        # (B, K) --> (BK)
        lap_masks_flattened = lap_masks_full.view(-1)

        for mk in batch["metrics"]:
            batch["metrics"][mk] = batch["metrics"][mk].view(-1)

        for mk in lap_decoded_metrics:
            lap_decoded_metrics[mk] = lap_decoded_metrics[mk].view(-1)

        lap_loss_computation_dict = {
            "decoded_metrics": lap_decoded_metrics,
            "enc_mean_t": outputs["enc_mean_t"],
            "enc_std_t": outputs["enc_std_t"],
            "prior_mean_t": outputs["prior_mean_t"],
            "prior_std_t": outputs["prior_std_t"],
            "loss_func_dict": self.lap_loss_func_dict,
            "metrics": batch["metrics"],
            "metrics_decoding_loss_type": self.config.get(
                "metrics_decoding_loss_type", "l2"
            ),
            "batch_item_masks": lap_masks_flattened,
            "batch_item_masks_non_flattened": lap_masks_full,  # for kld computation
            "epoch": batch["epoch"],
        }
        (
            lap_total_loss,
            lap_batch_loss_components,
            lap_scaled_batch_loss_components,
        ) = compute_model_loss(
            lap_loss_computation_dict,
            self.config,
            batch["epoch_type"],
            loss_component_name_key_prefix="lap_",
        )
        # To have these additional loss components available in the Trainer logs, we add them to the output.
        for key in lap_scaled_batch_loss_components:
            lap_loss_computation_dict[key] = lap_scaled_batch_loss_components[
                key
            ].item()
        out_metrics["my_lap_loss_metrics"].append(lap_total_loss.cpu().detach().numpy())
        for suffix, d in [
            ("", lap_batch_loss_components),
            ("_scaled", lap_scaled_batch_loss_components),
        ]:
            for key in d:
                out_metrics[key + suffix].append(d[key].cpu().detach().numpy())
        return lap_total_loss, lap_loss_computation_dict

    def compute_lap_metrics_loss(self, batch, outputs, out_metrics, additional_args):
        (
            _,
            _,
            _,
            lap_masks_full,
        ) = parse_batch(batch, return_lap_masks=True)
        lap_decoded_metrics = outputs["lap_decoded_metrics"]

        # (B, K) --> (BK)
        lap_masks = lap_masks_full.view(-1)

        for mk in batch["metrics"]:
            batch["metrics"][mk] = batch["metrics"][mk].view(-1)

        for mk in lap_decoded_metrics:
            lap_decoded_metrics[mk] = lap_decoded_metrics[mk].view(-1)

        lap_loss_computation_dict = {
            "decoded_metrics": lap_decoded_metrics,
            "loss_func_dict": self.lap_loss_func_dict,
            "metrics": batch["metrics"],
            "batch_item_masks": lap_masks,
            "epoch": batch["epoch"],
        }
        (
            lap_total_loss,
            lap_batch_loss_components,
            lap_scaled_batch_loss_components,
        ) = compute_model_loss(
            lap_loss_computation_dict,
            self.config,
            batch["epoch_type"],
            loss_component_name_key_prefix="lap_",
        )
        # To have these additional loss components available in the Trainer logs, we add them to the output.
        for key in lap_scaled_batch_loss_components:
            lap_loss_computation_dict[key] = lap_scaled_batch_loss_components[
                key
            ].item()
        out_metrics["my_lap_loss_metrics"].append(lap_total_loss.cpu().detach().numpy())
        for suffix, d in [
            ("", lap_batch_loss_components),
            ("_scaled", lap_scaled_batch_loss_components),
        ]:
            for key in d:
                out_metrics[key + suffix].append(d[key].cpu().detach().numpy())
        return lap_total_loss, lap_loss_computation_dict

    def compute_loss(self, batch, outputs, out_metrics, additional_args):
        if "append_cf_metrics" not in additional_args:
            additional_args["append_cf_metrics"] = True
        if self.config["training_mode"] == "lap_only":
            lap_model_outputs = outputs
        else:
            (lap_model_outputs, cf_model_outputs) = outputs
        cf_total_loss, cf_loss_computation_dict = self.compute_cf_loss(
            batch, cf_model_outputs, out_metrics, additional_args
        )
        if self.config["lap_model_temporal_structure"] == "MetricsEncoderDecoderVRNN":
            lap_total_loss, lap_loss_computation_dict = self.compute_metrics_vrnn_loss(
                batch, lap_model_outputs, out_metrics, additional_args
            )
        elif self.config["lap_model_temporal_structure"] == "MetricsEncoderDecoderLSTM":
            lap_total_loss, lap_loss_computation_dict = self.compute_lap_metrics_loss(
                batch, lap_model_outputs, out_metrics, additional_args
            )
        else:
            lap_total_loss, lap_loss_computation_dict = self.compute_lap_loss(
                batch, lap_model_outputs, out_metrics, additional_args
            )

        total_loss = cf_total_loss + lap_total_loss
        if "infonce_loss" in batch:
            assert "infonce_loss_scaled" in batch
            total_loss += batch["infonce_loss_scaled"]
            out_metrics["infonce_loss"].append(
                batch["infonce_loss"].cpu().detach().numpy()
            )
            out_metrics["infonce_loss_scaled"].append(
                batch["infonce_loss_scaled"].cpu().detach().numpy()
            )

        if "weight_penalty" in batch:
            for key in batch["weight_penalty"]:
                if "scaled" in key:
                    total_loss += batch["weight_penalty"][key]
                out_metrics[f"weight_penalty_{key}"].append(
                    batch["weight_penalty"][key].cpu().detach().numpy()
                )

        # total_loss = lap_total_loss
        out_metrics["my_loss_metrics"].append(total_loss.cpu().detach().numpy())
        # cf_loss_computation_dict = None
        return total_loss, (cf_loss_computation_dict, lap_loss_computation_dict)

    def my_forward(self, batch):
        outputs = super().my_forward(batch)
        for key in self.config["target_metric_keys"]:
            if self.config["lap_model_temporal_structure"] is not None:
                # when k >  1. need to deal with the lap masks
                lap_masks = batch["curr_lap_masks"].bool()
                valid_gt_metrics = batch["metrics"][f"{key}"][lap_masks].detach().cpu()
                valid_pred_metrics = (
                    batch["metrics"][f"{key}-pred"][lap_masks].detach().cpu()
                )
                self.estimated_metric_dict[key].append(valid_pred_metrics)
                self.gt_metric_dict[key].append(valid_gt_metrics)
            else:
                # when k == 1 all metrics are valid
                self.estimated_metric_dict[key].append(
                    batch["metrics"][f"{key}-pred"].detach().cpu()
                )
                self.gt_metric_dict[key].append(
                    batch["metrics"][f"{key}"].detach().cpu()
                )
        return outputs

    def visualize_batch(self, batch, loss, extra_loss, count=1):
        """Visualize batch with CF and lap components."""
        cf_vis_logs = visualize_batch_predicted_cf_future_trajectories(
            batch,
            self.epoch_type,
            self.config,
            count,
            visualize_teacher_action_prediction,
        )
        reconstructed_vis_logs = visualize_batch_reconstructed_lap_segments(
            batch, self.epoch_type, self.config, count
        )

        vis_logs = cf_vis_logs | reconstructed_vis_logs
        return vis_logs


class TrainConfigAdaptive(TrainConfig):
    def get_config(self, args) -> Dict[str, Any]:
        """Get default configuration for adaptive CF model training."""

        if args.category_index_type == "default":
            teacher_action_num_categories = (
                max(CategoryDictIndex.DEFAULT.value.values()) + 1
            )
        elif args.category_index_type == "no_steering":
            teacher_action_num_categories = (
                max(CategoryDictIndex.NO_STEERING.value.values()) + 1
            )
        elif args.category_index_type == "no_steering_no_turn":
            teacher_action_num_categories = (
                max(CategoryDictIndex.NO_STEERING_NO_TURN.value.values()) + 1
            )
        elif args.category_index_type == "no_steering_no_turn_no_brake":
            teacher_action_num_categories = (
                max(CategoryDictIndex.NO_STEERING_NO_TURN_NO_BRAKE.value.values()) + 1
            )
        elif args.category_index_type == "basic":
            teacher_action_num_categories = (
                max(CategoryDictIndex.BASIC.value.values()) + 1
            )

        config_dict = {
            "seed": 42,
            "training_mode": "full_model",
            "traj_input_dim": 13,
            "map_input_dim": 6,
            "hidden_dim": 32,
            "num_attention_layers": 2,
            "num_attention_heads": 4,
            "transformer_dropout": 0.2,
            "num_layers": 3,
            "traj_decoder_output_dim": 2,
            "max_future_steps": 70,
            "dropout": 0.2,
            "segment_dropout_ratio": 0.0,
            "weight_decay": 5e-3,
            "lr": 0.0005,
            "batch_size": 64,
            "val_batch_size": 64,
            "use_trajectory_reconstruction": True,
            "num_workers": 10,
            "global_num_epochs": 250,
            "target_metric_keys": ["trial_time", "racing_line_score_abs_mean"],
            "cf_target_metric_keys": {
                "racing_line_score": "abs_mean",
                "smoothness_score": "mean",
            },
            "num_validation_epochs": 5,
            "verbose_frequency": 5,
            "verbose": True,
            "vis_interval": 20,
            "vis_num_instances": 3,
            "save_interval": 10,
            "predict_delta": True,
            "test_pid": None,
            "use_infonce": True,
            "use_weight_penalty": True,
        }

        dataset_config = {
            "cf_target_sampling_frequency": 5,
            "trajectory_normalization_scheme": "local",
            "global_trajectory_normalization_info": None,
            "local_map_distance_threshold": 100,
            "prediction_timestep_in_sec": 5,
            "normalization_timestep": "first",
            "with_cf_snippets": True,
            "num_cf_snippets_per_lap": 1,
            "snippet_filename": "snippet.pkl",
            "snippet_length_in_sec": 10,
            "decoded_traj_len_in_sec": 5,
            "teacher_action_future_seq_len_in_sec": 5,
            "annotation_majority_threshold": 0.6,
            "padding_side": "right",
            "padding_value": -2000,
            "use_full_sequence": False,
            "num_prev_laps": 3,
            "return_cf_for_lap": False,
            "enable_dataset_cache": True,
            "index_sampling_noise": 0.0,
            "constant_shift_noise": 0.0,
            "constant_scale_noise": 0.0,
            "vis_video_interval": 2,
            "vis_video_num_trial": 4,
            "return_next_lap_metrics": True,
            "valid_annotation_categories_index_dict_type": args.category_index_type,
            "include_all_null": False,
        }

        cf_model_config = {
            "cf_use_trajectory_prediction": True,
            "is_adaptive": True,
            "teacher_action_loss_type": "existence",
            "cf_encoder_type": "TransformerTrajEncoder",
            "cf_traj_decoder_type": "LSTMDecoder",
            "teacher_action_num_categories": teacher_action_num_categories,
            "cf_use_teacher_action_encoding": False,
            "teacher_action_encoding_type": "existence",
            "cf_use_metrics_prediction": False,
            "side_channel_dropout": 0.0,
            "cf_map_channel_dropout": 0.0,
            "snippet_past_encoding_dropout": 0.0,
            "use_gaussian_dropout": True,
            "gaussian_dropout_ratio": 0.2,
            "use_random_side_channel": False,
            "use_multi_side_channel": False,
            "cf_metrics_decoder_type": "MetricsDecoderSimpleMLP",
        }

        lap_model_config = {
            "lap_encoder_type": "TransformerTrajEncoder",
            "lap_decoder_type": "LSTMDecoder",
            "lap_metrics_encoder_type": None,
            # "lap_metrics_encoder_type": "MetricsEncoderMLP",
            "metrics_decoder_type": "MetricsDecoderSimpleMLP",
            "lap_emb_use_cls_global_token": True,
            "lap_model_temporal_structure": "LSTM",
            # "lap_model_temporal_structure": "Transformer",
            # "lap_model_temporal_structure": "LapModelTemporalVRNN",
            # "lap_model_temporal_structure": "MetricsEncoderDecoderVRNN",
            "transformer_hidden_dim_multiplier": 2,
            "predict_next_lap_metrics": True,
            "run_lstm_step_wise": False,
            "num_lstm_prediction_samples": 1,
        }

        cf_loss_config = {
            "segment_traj_prediction_ade_loss_coeff": 0.0,
            "segment_traj_prediction_mse_loss_coeff": 2.0,
            "snippet_teacher_action_prediction_loss_coeff": 2.0,
            "snippet_metrics_prediction_loss_coeff": 2.0,
            "use_reconstruction_norm_loss": False,
            "traj_prediction_loss_type": "mse",
        }
        lap_loss_config = {
            "metrics_decoding_loss_coeff": 2.0,
            "segment_traj_reconstruction_loss_coeff": 1.0,
            "segment_traj_reconstruction_norm_loss_coeff": 0.0,
            "segment_traj_reconstruction_smoothness_loss_coeff": 5.0,
            "sparsity_loss_coeff": 1.0,
            "lap_representation_smoothness_loss_coeff": 1.0,
            "infonce_loss_coeff": 2.0,
            "weight_penalty_coeff": 1.0,
            "kld_loss_coeff": 100.0,
            "use_reconstruction_norm_loss": False,
        }

        config_dict.update(cf_loss_config)
        config_dict.update(lap_loss_config)
        config_dict.update(dataset_config)
        config_dict.update(cf_model_config)
        config_dict.update(lap_model_config)

        if config_dict["lap_model_temporal_structure"] == "MetricsEncoderDecoderVRNN":
            config_dict.update(
                {
                    "lap_loss_component_names": [
                        "metrics_decoding_loss",
                        "kld_loss",
                        # "segment_traj_reconstruction_smoothness_loss",
                    ]
                }
            )
        elif config_dict["lap_model_temporal_structure"] == "LapModelTemporalVRNN":
            config_dict.update(
                {
                    "lap_loss_component_names": [
                        "metrics_decoding_loss",
                        "kld_loss",
                        "segment_traj_reconstruction_loss",
                        "segment_traj_reconstruction_norm_loss",
                        "sparsity_loss",
                        "lap_representation_smoothness_loss",
                    ]
                }
            )
        elif config_dict["lap_model_temporal_structure"] == "MetricsEncoderDecoderLSTM":
            config_dict.update(
                {
                    "lap_loss_component_names": [
                        "metrics_decoding_loss",
                    ]
                }
            )
        else:
            config_dict.update(
                {
                    "lap_loss_component_names": [
                        "metrics_decoding_loss",
                        "segment_traj_reconstruction_loss",
                        "segment_traj_reconstruction_norm_loss",
                        "sparsity_loss",
                        "lap_representation_smoothness_loss",
                    ]
                }
            )

        return config_dict

    def setup_datasets(self, config_dict: Dict[str, Any]) -> Tuple[Any, Any, Any]:
        """Set up datasets for adaptive CF model training."""
        if config_dict["online_inference"]:
            return DummyDataset(), DummyDataset(), DummyDataset()
        use_lap_dataset = config_dict["use_lap_dataset"]
        if not use_lap_dataset:
            print("Use 24d05 prevk dataset")
            train_dataset = AIC24D05CombinedPrevKDataset(
                config_dict, dataset_type="train"
            )
            eval_dataset = AIC24D05CombinedPrevKDataset(
                config_dict, dataset_type="test"
            )
            print(f"Size of train dataset: {len(train_dataset)} snippets")
            print(f"Size of test dataset: {len(eval_dataset)} snippets")
        else:
            print("Use 24d05 lap dataset")
            train_dataset = AIC24D05LapDataset(config_dict, dataset_type="train")
            eval_dataset = AIC24D05LapDataset(config_dict, dataset_type="test")
            print(f"Size of train dataset: {len(train_dataset)} laps")
            print(f"Size of test dataset: {len(eval_dataset)} laps")

        # Get stats of the CF dataset class
        if not use_lap_dataset:
            config_dict[
                "train_dataset_stats"
            ] = train_dataset.cf_dataset.get_dataset_stats()

        vis_dataset = copy.deepcopy(train_dataset)
        return train_dataset, eval_dataset, vis_dataset

    def setup_loss_functions(self) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Set up loss functions for adaptive CF model training."""
        if self.config["cf_use_metrics_prediction"]:
            self.config.update(
                {
                    "cf_loss_component_names": [
                        "segment_traj_prediction_ade_loss",
                        "segment_traj_prediction_mse_loss",
                        "snippet_teacher_action_prediction_loss",
                        "snippet_metrics_prediction_loss",
                    ]
                }
            )
        else:
            self.config.update(
                {
                    "cf_loss_component_names": [
                        "segment_traj_prediction_ade_loss",
                        "segment_traj_prediction_mse_loss",
                        "snippet_teacher_action_prediction_loss",
                    ]
                }
            )

        if self.config["lap_model_temporal_structure"] == "MetricsEncoderDecoderVRNN":
            lap_loss_func_dict = {
                "metrics_decoding_loss": compute_metrics_decoding_loss,
                "kld_loss": compute_kld_loss
                # "segment_traj_reconstruction_smoothness_loss": compute_trajectory_reconstruction_smoothness_loss,
            }
        elif self.config["lap_model_temporal_structure"] == "LapModelTemporalVRNN":
            lap_loss_func_dict = {
                "metrics_decoding_loss": compute_metrics_decoding_loss,
                "kld_loss": compute_kld_loss,
                "segment_traj_reconstruction_loss": compute_trajectory_reconstruction_loss,
                "segment_traj_reconstruction_norm_loss": compute_trajectory_reconstruction_norm_loss,
                "sparsity_loss": compute_sparsity_loss,
                "lap_representation_smoothness_loss": compute_lap_representation_smoothness_loss,
            }
        elif self.config["lap_model_temporal_structure"] == "MetricsEncoderDecoderLSTM":
            lap_loss_func_dict = {
                "metrics_decoding_loss": compute_metrics_decoding_loss,
                # "segment_traj_reconstruction_smoothness_loss": compute_trajectory_reconstruction_smoothness_loss,
            }
        else:
            lap_loss_func_dict = {
                "metrics_decoding_loss": compute_metrics_decoding_loss,
                "segment_traj_reconstruction_loss": compute_trajectory_reconstruction_loss,
                "segment_traj_reconstruction_norm_loss": compute_trajectory_reconstruction_norm_loss,
                "sparsity_loss": compute_sparsity_loss,
                "lap_representation_smoothness_loss": compute_lap_representation_smoothness_loss,
                # "segment_traj_reconstruction_smoothness_loss": compute_trajectory_reconstruction_smoothness_loss,
            }

        if self.config["cf_use_metrics_prediction"]:
            cf_loss_func_dict = {
                "segment_traj_prediction_mse_loss": compute_trajectory_prediction_loss_mse,
                "segment_traj_prediction_ade_loss": compute_trajectory_prediction_loss_ade,
                "snippet_teacher_action_prediction_loss": compute_teacher_action_prediction_loss,
                "snippet_metrics_prediction_loss": compute_snippet_metrics_prediction_loss,
            }
        else:
            cf_loss_func_dict = {
                "segment_traj_prediction_mse_loss": compute_trajectory_prediction_loss_mse,
                "segment_traj_prediction_ade_loss": compute_trajectory_prediction_loss_ade,
                "snippet_teacher_action_prediction_loss": compute_teacher_action_prediction_loss,
            }

        return cf_loss_func_dict, lap_loss_func_dict

    def create_model(
        self, config_dict: Dict[str, Any], train_dataset: DataLoader, eval_dataset
    ) -> AdaptiveCFModel:
        """Create and return an adaptive CF model instance."""
        model = AdaptiveCFModel(config_dict, train_dataset, eval_dataset)
        if config_dict.get("freeze_cf_model", False):
            print("Freezing CF model")
            for param in model.concurrent_feedback_module.parameters():
                param.requires_grad = False
        return model


def main(args=None):
    config_cls = TrainConfigAdaptive()
    return run_model(config_cls, args)


if __name__ == "__main__":
    main()
