"""
Training setup configuration for skill/lap model training.

This module contains the specific configuration and model setup for training
skill models that predict lap metrics and reconstruct trajectories.
"""

import copy
from typing import Any, Dict, <PERSON><PERSON>

import torch
from torch.utils.data import DataLoader

from aic_bsd_model.datasets.aic_24d05_lap_dataset import (
    AIC24D05FullSequenceDataset,
    AIC24D05LapDataset,
)
from aic_bsd_model.datasets.aic_24d16_lap_dataset import (
    AIC24D16FullSequenceDataset,
    AIC24D16LapDataset,
)
from aic_bsd_model.datasets.aic_bsd_dataset_utils import split_pids_laps
from aic_bsd_model.losses.model_loss import (
    compute_classification_metrics,
    compute_kld_loss,
    compute_lap_representation_smoothness_loss,
    compute_metrics_decoding_loss,
    compute_model_loss,
    compute_sparsity_loss,
    compute_trajectory_reconstruction_loss,
    compute_trajectory_reconstruction_norm_loss,
)
from aic_bsd_model.models.aic_bsd_full_model import AICBSDFullModel
from aic_bsd_model.training.train import TrainConfig, run_model
from aic_bsd_model.utils.training_utils import parse_batch
from aic_bsd_model.utils.visualization_utils import (
    plot_metric,
    visualize_batch_reconstructed_lap_segments,
)


class AICBSDModel(AICBSDFullModel):
    """Skill model for lap-based training with metric prediction and trajectory reconstruction."""

    def __init__(self, config_dict, train_dataset):
        super(AICBSDModel, self).__init__(config_dict, train_dataset)
        self.loss_func_dict = config_dict["lap_loss_func_dict"]
        self.predict_next_lap_metrics = config_dict.get(
            "predict_next_lap_metrics", False
        )

    def reset_epoch(self):
        super().reset_epoch()
        self.estimated_metric_dict = {
            key: [] for key in self.config["target_metric_keys"]
        }
        self.gt_metric_dict = {key: [] for key in self.config["target_metric_keys"]}

    def get_log_dict(self):
        logs = super().get_log_dict()
        if self.should_vis_epoch():
            metrics_scatters = self.concatenate_and_plot_metrics()
            metrics_scatters = {
                f"{self.epoch_type}/{k}": v for k, v in metrics_scatters.items()
            }
            logs.update(metrics_scatters)
        return logs

    def prep_batch(self, batch):
        super().prep_batch(batch)
        batch["metrics"] = {}
        prefix = "next_" if self.predict_next_lap_metrics else "curr_"
        for key in self.metric_keys:
            batch["metrics"][key] = batch[prefix + key]

    def concatenate_and_plot_metrics(self):
        # Concatenate metrics at the end of the epoch
        est, gt = {}, {}
        for key in self.estimated_metric_dict:
            est[key] = torch.cat(self.estimated_metric_dict[key], dim=0)
        for key in self.gt_metric_dict:
            gt[key] = torch.cat(self.gt_metric_dict[key], dim=0)

        plots = plot_metric(est, gt)
        return plots

    def my_forward(self, batch):
        outputs = super().my_forward(batch)
        for key in self.config["target_metric_keys"]:
            self.estimated_metric_dict[key].append(
                batch["metrics"][f"{key}-pred"].detach().cpu()
            )
            self.gt_metric_dict[key].append(batch["metrics"][f"{key}"].detach().cpu())
        return outputs

    def compute_loss(self, batch, outputs, out_metrics, additional_args=None):
        """
        Compute loss for skill model training.

        Args:
            outputs: output from forward function
            batch: batch of inputs
            out_metrics: dict to append metrics to
        """
        (
            curr_trajectory_segments,
            curr_map_segments,
            curr_traj_segments_lengths,
        ) = parse_batch(batch)
        device = curr_trajectory_segments.device

        loss_computation_dict = {
            "reconstructed_segment_trajectories": outputs[
                "reconstructed_segment_trajectories"
            ],
            "target_segment_trajs": curr_trajectory_segments,
            "reconstructed_pred_mask": outputs["reconstructed_pred_mask"],
            "segments_length": curr_traj_segments_lengths,
            "decoded_metrics": outputs["lap_decoded_metrics"],
            "loss_func_dict": self.loss_func_dict,
            "lap_segments_data_valid": batch["lap_segments_data_valid"],
            "map_data_valid": batch["map_data_valid"],
            "metrics": batch["metrics"],
            "batch_item_masks": torch.ones(
                curr_trajectory_segments.shape[0], device=device
            ),
            "metrics_decoding_loss_type": self.config.get(
                "metrics_decoding_loss_type", "l2"
            ),
            "epoch": batch["epoch"],
        }

        (
            total_loss,
            batch_loss_components,
            scaled_batch_loss_components,
        ) = compute_model_loss(loss_computation_dict, self.config, batch["epoch_type"])

        # Add loss components to output for logging
        for key in scaled_batch_loss_components:
            loss_computation_dict[key] = scaled_batch_loss_components[key].item()
        out_metrics["my_loss_metrics"].append(total_loss.cpu().detach().numpy())
        for suffix, d in [
            ("", batch_loss_components),
            ("_scaled", scaled_batch_loss_components),
        ]:
            for key in d:
                out_metrics[key + suffix].append(d[key].cpu().detach().numpy())
        return total_loss, loss_computation_dict

    def visualize_batch(self, batch, loss, extra_loss, count=1):
        vis_logs = visualize_batch_reconstructed_lap_segments(
            batch, self.epoch_type, self.config, count
        )
        return vis_logs

    def visualize_epoch(self):
        return {}


class TrainSkillConfig(TrainConfig):
    def get_config(self, args) -> Dict[str, Any]:
        """Get default configuration for skill model training."""
        config_dict = {
            "seed": 42,
            "training_mode": "lap_only",
            "traj_input_dim": 13,
            "map_input_dim": 6,
            "hidden_dim": 32,
            "num_attention_layers": 2,
            "num_attention_heads": 4,
            "transformer_dropout": 0.2,
            "num_layers": 3,
            "traj_decoder_output_dim": 2,
            "max_future_steps": 70,
            "dropout": 0.2,
            "segment_dropout_ratio": 0.0,
            "weight_decay": 5e-3,
            "lr": 0.0005,
            "batch_size": 64,
            "val_batch_size": 64,
            "use_trajectory_reconstruction": True,
            "num_workers": 10,
            "global_num_epochs": 250,
            "target_metric_keys": ["trial_time", "racing_line_score_abs_mean"],
            "num_validation_epochs": 5,
            "verbose_frequency": 5,
            "verbose": True,
            "vis_interval": 20,
            "vis_num_instances": 3,
            "save_interval": 10,
            "predict_delta": True,
            "test_pid": None,
            "use_infonce": True,
            "use_weight_penalty": True,
        }

        # Dataset configuration
        dataset_config = {
            "cf_target_sampling_frequency": 5,
            "trajectory_normalization_scheme": "local",
            "global_trajectory_normalization_info": None,
            "local_map_distance_threshold": 100,
            "prediction_timestep_in_sec": 5,
            "normalization_timestep": "first",
            "with_cf_snippets": True,
            "num_cf_snippets_per_lap": 1,
            "snippet_filename": "snippet.pkl",
            "snippet_length_in_sec": 10,
            "annotation_majority_threshold": 0.6,
            "padding_side": "right",
            "padding_value": -2000,
            "use_full_sequence": False,
            "num_prev_laps": 3,
            "return_cf_for_lap": False,
            "enable_dataset_cache": True,
            "index_sampling_noise": 0.0,
            "constant_shift_noise": 0.0,
            "constant_scale_noise": 0.0,
            "vis_video_interval": 2,
            "vis_video_num_trial": 4,
            "return_next_lap_metrics": True,
            "valid_annotation_categories_index_dict_type": args.category_index_type,
            "include_all_null": False,
        }

        lap_model_config = {
            "lap_encoder_type": "TransformerTrajEncoder",
            "lap_decoder_type": "LSTMDecoder",
            "lap_metrics_encoder_type": "MetricsEncoderMLP",
            "metrics_decoder_type": "MetricsDecoderSimpleMLP",
            "lap_emb_use_cls_global_token": True,
            # "lap_model_temporal_structure": "LSTM",
            # "lap_model_temporal_structure": "LapModelTemporalVRNN",
            "lap_model_temporal_structure": "MetricsEncoderDecoderVRNN",
            "transformer_hidden_dim_multiplier": 2,
            "predict_next_lap_metrics": True,
        }

        lap_loss_config = {
            "metrics_decoding_loss_coeff": 2.0,
            "segment_traj_reconstruction_loss_coeff": 1.0,
            "segment_traj_reconstruction_norm_loss_coeff": 0.0,
            "segment_traj_reconstruction_smoothness_loss_coeff": 5.0,
            "sparsity_loss_coeff": 1.0,
            "lap_representation_smoothness_loss_coeff": 1.0,
            "infonce_loss_coeff": 2.0,
            "weight_penalty_coeff": 1.0,
            "kld_loss_coeff": 100.0,
            "use_reconstruction_norm_loss": False,
        }

        config_dict.update(lap_loss_config)
        config_dict.update(dataset_config)
        config_dict.update(lap_model_config)

        if config_dict["lap_model_temporal_structure"] == "MetricsEncoderDecoderVRNN":
            config_dict.update(
                {
                    "lap_loss_component_names": [
                        "metrics_decoding_loss",
                        "kld_loss",
                        # "segment_traj_reconstruction_smoothness_loss",
                    ]
                }
            )
        elif config_dict["lap_model_temporal_structure"] == "MetricsEncoderDecoderLSTM":
            config_dict.update(
                {
                    "lap_loss_component_names": [
                        "metrics_decoding_loss",
                    ]
                }
            )
        else:
            config_dict.update(
                {
                    "lap_loss_component_names": [
                        "metrics_decoding_loss",
                        "segment_traj_reconstruction_loss",
                        "segment_traj_reconstruction_norm_loss",
                        "sparsity_loss",
                        "lap_representation_smoothness_loss",
                    ]
                }
            )

        return config_dict

    def setup_datasets(self, config_dict: Dict[str, Any]) -> Tuple[Any, Any, Any]:
        """Set up datasets for skill model training."""
        if config_dict["use_full_sequence"]:
            train_dataset = AIC24D16FullSequenceDataset(
                config_dict, dataset_type="train"
            )
            vis_dataset = copy.deepcopy(train_dataset)
            eval_dataset = AIC24D16FullSequenceDataset(config_dict, dataset_type="test")
        else:
            train_dataset = AIC24D05LapDataset(config_dict, dataset_type="train")
            vis_dataset = copy.deepcopy(train_dataset)
            eval_dataset = AIC24D05LapDataset(config_dict, dataset_type="test")

        print(f"Size of train dataset: {len(train_dataset)} laps")
        print(f"Size of test dataset: {len(eval_dataset)} laps")

        # Store training dataset info
        pids, trial_nums, lap_uids = split_pids_laps(train_dataset)
        config_dict["training_ds_pids-trial_nums-lap_uids"] = pids, trial_nums, lap_uids

        return train_dataset, eval_dataset, vis_dataset

    def setup_loss_functions(
        self,
    ) -> Dict[str, Any]:
        """Set up loss functions for non-adaptive CF model training."""
        """Set up loss functions for adaptive CF model training."""
        if self.config["lap_model_temporal_structure"] == "MetricsEncoderDecoderVRNN":
            lap_loss_func_dict = {
                "metrics_decoding_loss": compute_metrics_decoding_loss,
                "kld_loss": compute_kld_loss
                # "segment_traj_reconstruction_smoothness_loss": compute_trajectory_reconstruction_smoothness_loss,
            }
        elif self.config["lap_model_temporal_structure"] == "MetricsEncoderDecoderLSTM":
            lap_loss_func_dict = {
                "metrics_decoding_loss": compute_metrics_decoding_loss,
                # "segment_traj_reconstruction_smoothness_loss": compute_trajectory_reconstruction_smoothness_loss,
            }
        else:
            lap_loss_func_dict = {
                "metrics_decoding_loss": compute_metrics_decoding_loss,
                "segment_traj_reconstruction_loss": compute_trajectory_reconstruction_loss,
                "segment_traj_reconstruction_norm_loss": compute_trajectory_reconstruction_norm_loss,
                "sparsity_loss": compute_sparsity_loss,
                "lap_representation_smoothness_loss": compute_lap_representation_smoothness_loss,
                # "segment_traj_reconstruction_smoothness_loss": compute_trajectory_reconstruction_smoothness_loss,
            }

        cf_loss_func_dict = {}
        return cf_loss_func_dict, lap_loss_func_dict

    def create_model(
        self, config_dict: Dict[str, Any], train_dataset: DataLoader, eval_dataset
    ) -> AICBSDModel:
        """Create and return a skill model instance."""
        return AICBSDModel(config_dict, train_dataset)


def main():
    config_cls = TrainSkillConfig()
    run_model(config_cls)


if __name__ == "__main__":
    main()
