"""
Common training utilities and shared trainer setup code.

This module contains reusable training components that can be shared across
different training configurations (skill, adaptive CF, non-adaptive CF).
"""

import copy
import os
from math import inf
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import torch
from hail_launch.load_env import get_training_meta, init_wandb
from sklearn.model_selection import train_test_split
from torch.utils.data import DataLoader, Dataset
from transformers import TrainingArguments

import wandb
from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    INVALID_TRIAL_IDX_PER_SUBJECT_24_D_05,
    split_pids_laps,
)
from aic_bsd_model.models.hf_model import CustomLoggingCallback, HFTrainer
from aic_bsd_model.utils.cache_utils import clear_cache
from aic_bsd_model.utils.training_utils import (
    generate_session_id,
    parse_arguments,
    parse_config,
    set_seed,
)

# Default output directory
OUTPUT_DIR = Path.expanduser(Path("~/model_outputs/aic_bsd"))


class DummyDataset(Dataset):
    def __len__(self):
        return 100

    def __getitem__(self, idx):
        return {"data": torch.full([10, 20], idx)}

    def get_collator(self, config):
        pass


def setup_data_paths(args) -> Dict[str, Path]:
    """Set up data paths from command line arguments."""
    trajectory_segments_dir = (
        Path.expanduser(Path(args.data_dir)) / "trajectory_segments_map_seg_ids"
    )
    trajectory_snippets_dir = (
        Path.expanduser(Path(args.data_dir)) / "trajectory_snippets"
    )
    trials_directory = Path.expanduser(Path(args.data_dir)) / "trials_final"
    map_file = trajectory_segments_dir / "track.csv"

    return {
        "trajectory_segments_dir": trajectory_segments_dir,
        "trajectory_snippets_dir": trajectory_snippets_dir,
        "trials_directory": trials_directory,
        "map_file": map_file,
    }


def setup_train_test_split(args, config_dict: Dict[str, Any]) -> Tuple[list, list]:
    """Set up train/test split for subjects."""
    all_pids_list = [
        pid for pid in list(INVALID_TRIAL_IDX_PER_SUBJECT_24_D_05.keys()) if pid != ""
    ]

    if args.use_one_subject_to_train:
        train_pids_list, test_pids_list = train_test_split(
            all_pids_list,
            test_size=len(all_pids_list) - 1,
            random_state=config_dict["seed"],
            shuffle=True,
        )
    else:
        if "test_pid" in config_dict and config_dict["test_pid"] is not None:
            print("Splitting train and test set according to provided test subject id")
            if type(config_dict["test_pid"]) is list:
                test_pids_list = config_dict["test_pid"]
            elif type(config_dict["test_pid"]) is str:
                test_pids_list = [config_dict["test_pid"]]
            train_pids_list = [p for p in all_pids_list if p not in test_pids_list]
        else:
            print("Splitting train and test sets using the random seed")
            train_pids_list, test_pids_list = train_test_split(
                all_pids_list,
                test_size=args.test_size,
                random_state=config_dict["seed"],
                shuffle=True,
            )

    if args.use_train_as_test:
        test_pids_list = train_pids_list

    train_pids_list = sorted(train_pids_list)
    test_pids_list = sorted(test_pids_list)
    return train_pids_list, test_pids_list


def print_model_size(model: torch.nn.Module) -> None:
    """Print model size in MB."""
    param_size = 0
    for param in model.parameters():
        param_size += param.nelement() * param.element_size()

    buffer_size = 0
    for buffer in model.buffers():
        buffer_size += buffer.nelement() * buffer.element_size()

    total_size_mb = (param_size + buffer_size) / (1024**2)
    print(f"Model Size MB: {total_size_mb}")


def initialize_wandb(
    project_name: str,
    run_name: str,
    config_dict: Dict[str, Any],
    output_dir: Path = OUTPUT_DIR,
    mode: str = "online",
) -> None:
    """Initialize Weights & Biases logging."""
    # settings = wandb.Settings(sagemaker_disable=True)
    # wandb.init(
    #     project=project_name,
    #     dir=output_dir,
    #     name=run_name,
    #     config=config_dict,
    #     mode=mode,
    #     settings=settings,
    # )

    # Disable wandb for online_inference
    # TODO add disable param in hail_launch
    # config_dict["is_main_proc"] = False

    logger = init_wandb(run_name, project_name, config_dict, output_dir)


def run_training(
    trainer: HFTrainer,
    checkpoint: Optional[str] = None,
    final_model_path: str = "./final_model",
) -> None:
    """Execute training with optional checkpoint loading."""
    # Load checkpoint if provided
    if checkpoint:
        print(f"\n\nLoading checkpoint {checkpoint}")
        trainer._load_from_checkpoint(checkpoint)
        print("\n")

    # Ensure single GPU usage
    assert (
        torch.cuda.device_count() <= 1
    ), "Do not use more than 1 GPU for now, set CUDA_VISIBLE_DEVICES=0"

    # Run training
    trainer.train(resume_from_checkpoint=checkpoint)

    # Save final model
    trainer.save_model(final_model_path)
    print(f"Final model saved to: {final_model_path}")


def run_online_inference(
    trainer: HFTrainer,
    checkpoint: Optional[str] = None,
) -> None:
    """
    Run inference in online mode.


        dummy_dataset = DummyDataset()
        # Run training
        trainer.predict(dummy_dataset)
    """
    # Load checkpoint if provided
    if checkpoint:
        print(f"\n\nLoading checkpoint {checkpoint}")
        trainer._load_from_checkpoint(checkpoint)
        print("\n")

    # Ensure single GPU usage
    assert (
        torch.cuda.device_count() <= 1
    ), "Do not use more than 1 GPU for now, set CUDA_VISIBLE_DEVICES=0"


def run_model(config_class, args=None):
    """Main training function for adaptive CF models."""
    # Parse command line arguments
    args = parse_arguments(args=args)
    run_meta = get_training_meta()
    global OUTPUT_DIR
    OUTPUT_DIR = run_meta["output_dir"]

    # Get base configuration
    config_dict = config_class.get_config(args)

    # Apply command line config overrides
    args_config = parse_config(args.config_str)
    print("Overriding args_config", args_config)
    config_dict.update(args_config)
    config_dict.update(vars(args))

    # Common training setup
    run_name, config_dict = config_class.setup_common_training(
        args, config_dict, project_name="aic_base_uncoached_model"
    )
    if args.online_inference:
        config_class.setup_inference(config_dict)
    if run_meta["on_sagemaker"]:
        config_dict["disable_tqdm"] = True

    # Set up datasets
    train_dataset, eval_dataset, vis_dataset = config_class.setup_datasets(config_dict)
    collator = train_dataset.get_collator(config_dict)

    # Set up loss functions
    cf_loss_func_dict, lap_loss_func_dict = config_class.setup_loss_functions()
    config_dict["cf_loss_func_dict"] = cf_loss_func_dict
    config_dict["lap_loss_func_dict"] = lap_loss_func_dict
    print("Using CF loss function dict:", config_dict["cf_loss_func_dict"])
    print("Using lap loss function dict:", config_dict["lap_loss_func_dict"])

    # Create visualization dataloader and model
    model = config_class.create_model(config_dict, vis_dataset, eval_dataset)

    # Print model size
    print_model_size(model)

    # Set up training arguments and trainer
    training_args = config_class.setup_training_arguments(config_dict, run_name)
    callback = CustomLoggingCallback(model, config_dict)
    trainer = config_class.setup_trainer(
        model=model,
        training_args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        collator=collator,
        config_dict=config_dict,
        callbacks=[callback],
        vis_epoch=10,
    )
    config_dict.update(run_meta)
    config_dict["is_main_proc"] = True

    # Initialize Weights & Biases
    initialize_wandb(
        project_name="aic_base_uncoached_model",
        run_name=run_name,
        config_dict=config_dict,
    )

    if args.online_inference or args.inference:
        # Run inference
        run_online_inference(
            trainer=trainer,
            checkpoint=args.checkpoint,
        )
        return trainer
    else:
        # Run training
        run_training(
            trainer=trainer,
            checkpoint=args.checkpoint,
            final_model_path="./final_model",
        )

    print(f"Training completed. Output directory: {training_args.output_dir}")


class TrainConfig:
    def get_config(self, args) -> Dict[str, Any]:
        raise NotImplemented()

    def setup_common_training(
        self,
        args,
        config_dict: Dict[str, Any],
        project_name: str = "aic_base_uncoached_model",
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Common setup steps for all training scripts.

        Returns:
            run_name: Generated session ID for this training run
            updated_config_dict: Configuration dictionary with paths and splits added
        """
        self.config = config_dict
        self.inference = args.inference
        # Set seed for reproducibility
        set_seed(config_dict["seed"])

        # Clear cache if requested
        if config_dict.get("clear_cache", False):
            clear_cache()

        # Set up data paths
        data_paths = setup_data_paths(args)
        config_dict.update(data_paths)

        # Set up train/test split
        train_pids_list, test_pids_list = setup_train_test_split(args, config_dict)
        config_dict["train_pids_to_be_considered"] = train_pids_list
        config_dict["test_pids_to_be_considered"] = test_pids_list

        # Generate run name
        run_name = generate_session_id(args.run_description)

        print(f"Train Subject List: {train_pids_list}")
        print(f"Test Subject List: {test_pids_list}")
        print(f"Output dir: {OUTPUT_DIR / run_name}")
        print()

        return run_name, config_dict

    def setup_training_arguments(
        self,
        config_dict: Dict[str, Any],
        run_name: str,
        metric_for_best_model: str = "my_loss_metrics",
    ) -> TrainingArguments:
        num_workers = config_dict.get("num_workers", 0)
        if self.inference:
            num_workers = 0
        ret = TrainingArguments(
            output_dir=OUTPUT_DIR / run_name,
            num_train_epochs=config_dict["global_num_epochs"],
            per_device_train_batch_size=config_dict["batch_size"],
            per_device_eval_batch_size=config_dict["val_batch_size"],
            eval_strategy="epoch",
            logging_strategy="epoch",
            logging_steps=1,
            save_strategy="best",
            do_train=not self.inference,
            do_eval=self.inference,
            metric_for_best_model=metric_for_best_model,
            greater_is_better=False,
            save_total_limit=3,
            learning_rate=config_dict["lr"],
            weight_decay=config_dict["weight_decay"],
            report_to=["wandb"],
            run_name=run_name,
            dataloader_num_workers=num_workers,
            remove_unused_columns=False,
            ddp_backend=None,
            disable_tqdm=config_dict.get("disable_tqdm", False),
        )
        return ret

    def setup_trainer(
        self,
        model: torch.nn.Module,
        training_args: TrainingArguments,
        train_dataset,
        eval_dataset,
        collator,
        config_dict: Dict[str, Any],
        callbacks: List,
        vis_epoch: int = 10,
    ) -> HFTrainer:
        """Set up HuggingFace trainer with custom callback."""
        trainer = HFTrainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            data_collator=collator,
            callbacks=callbacks,
            vis_epoch=vis_epoch,
        )
        callbacks[0].trainer = trainer
        return trainer

    def setup_online_inference(self, training_args, trainer, config_dict):
        pass

    def setup_inference(self, config_dict):
        config_dict["target_metric_keys"] = ["trial_time", "trial_time2"]
        config_dict["disable_tqdm"] = True
        config_dict["disable_wandb"] = True

    def setup_datasets(self, config_dict: Dict[str, Any]) -> Tuple[Any, Any, Any]:
        raise NotImplemented()
        return train_dataset, eval_dataset, vis_dataset

    def setup_functions(self) -> Dict[str, Any]:
        return {}

    def create_model(
        self, config_dict: Dict[str, Any], vis_dataloader: DataLoader
    ) -> "AICBSDModel":
        raise NotImplemented()
