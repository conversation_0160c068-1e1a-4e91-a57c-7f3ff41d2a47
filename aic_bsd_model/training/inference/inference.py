"""
Common inference utilities and model loading code.

This module contains reusable inference components that can be shared across
different model types (skill, adaptive CF, non-adaptive CF).
"""

import copy
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import torch
from torch.utils.data import DataLoader
from transformers import TrainingArguments

from aic_bsd_model.utils.training_utils import set_seed


def run_dataset_inference(
    model: torch.nn.Module,
    dataloader: DataLoader,
    max_batches: Optional[int] = None,
    return_losses: bool = False,
    device: str = "cuda" if torch.cuda.is_available() else "cpu",
) -> List[Dict[str, Any]]:
    """
    Run inference on an entire dataset.

    Args:
        model: The model to run inference with
        dataloader: DataLoader for the dataset
        max_batches: Maximum number of batches to process (None for all)
        return_losses: Whether to compute and return losses
        device: Device to run inference on

    Returns:
        List of inference results for each batch
    """
    results = []
    model.eval()

    for batch_idx, batch in enumerate(dataloader):
        if max_batches is not None and batch_idx >= max_batches:
            break

        # Prepare batch
        batch = prepare_batch_for_inference(batch, device)

        # Run inference
        result = run_model_inference(model, batch, return_loss=return_losses)
        result["batch_idx"] = batch_idx

        results.append(result)

        if batch_idx % 10 == 0:
            print(f"Processed batch {batch_idx}")

    print(f"Inference completed on {len(results)} batches")
    return results


def extract_predictions(
    inference_results: List[Dict[str, Any]], output_key: str = "outputs"
) -> List[Any]:
    """
    Extract predictions from inference results.

    Args:
        inference_results: List of inference results from run_dataset_inference
        output_key: Key to extract from each result

    Returns:
        List of extracted predictions
    """
    predictions = []
    for result in inference_results:
        if output_key in result:
            predictions.append(result[output_key])
    return predictions


def save_inference_results(
    results: List[Dict[str, Any]],
    save_path: Union[str, Path],
    save_format: str = "torch",
) -> None:
    """
    Save inference results to disk.

    Args:
        results: Inference results to save
        save_path: Path to save the results
        save_format: Format to save in ("torch", "numpy")
    """
    save_path = Path(save_path)
    save_path.parent.mkdir(parents=True, exist_ok=True)

    if save_format == "torch":
        torch.save(results, save_path)
    elif save_format == "numpy":
        # Convert tensors to numpy arrays
        numpy_results = []
        for result in results:
            numpy_result = {}
            for key, value in result.items():
                if isinstance(value, torch.Tensor):
                    numpy_result[key] = value.cpu().numpy()
                else:
                    numpy_result[key] = value
            numpy_results.append(numpy_result)
        np.save(save_path, numpy_results)
    else:
        raise ValueError(f"Unsupported save format: {save_format}")

    print(f"Results saved to {save_path}")


def setup_inference_config(
    training_config_dict: Dict[str, Any],
    inference_overrides: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    Set up configuration for inference based on training config.

    Args:
        training_config_dict: Configuration used during training
        inference_overrides: Optional overrides for inference

    Returns:
        Configuration dictionary for inference
    """
    # Start with training config
    inference_config = copy.deepcopy(training_config_dict)

    # Apply inference-specific settings
    inference_config["batch_size"] = inference_config.get("val_batch_size", 1)
    inference_config["num_workers"] = 0  # Usually better for inference

    # Apply any user overrides
    if inference_overrides:
        inference_config.update(inference_overrides)

    return inference_config


def create_inference_dataloader(
    dataset,
    config_dict: Dict[str, Any],
    batch_size: Optional[int] = None,
    shuffle: bool = False,
) -> DataLoader:
    """
    Create a DataLoader for inference.

    Args:
        dataset: Dataset to create loader for
        config_dict: Configuration dictionary
        batch_size: Batch size (uses config if None)
        shuffle: Whether to shuffle the data

    Returns:
        DataLoader for inference
    """
    if batch_size is None:
        batch_size = config_dict.get("val_batch_size", 1)

    collator = dataset.get_collator(config_dict)

    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        collate_fn=collator,
        num_workers=config_dict.get("num_workers", 0),
    )
