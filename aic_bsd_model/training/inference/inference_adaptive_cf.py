"""
Inference script for adaptive concurrent feedback models.

This script loads trained adaptive CF models and runs inference on datasets
to predict future trajectories and teacher actions with side channel information.
"""

import argparse
from pathlib import Path
from typing import Any, Dict

import torch

from aic_bsd_model.training.inference.inference import (
    create_inference_dataloader,
    extract_predictions,
    run_dataset_inference,
    save_inference_results,
    setup_inference_config,
)
from aic_bsd_model.utils.training_utils import parse_config, set_seed


def parse_inference_arguments():
    """Parse command line arguments for inference."""
    parser = argparse.ArgumentParser(
        description="Inference script for adaptive concurrent feedback models."
    )
    parser.add_argument(
        "--checkpoint", type=str, required=True, help="Path to the model checkpoint"
    )
    parser.add_argument(
        "--data-dir",
        type=str,
        default="~/Data/24-D-05",
        help="Folder in which the trajectory segments are stored",
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        default="./inference_results",
        help="Directory to save inference results",
    )
    parser.add_argument(
        "--config-str",
        type=str,
        default="",
        help="Configuration string to override default settings",
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=None,
        help="Batch size for inference (uses config default if not specified)",
    )
    parser.add_argument(
        "--max-batches",
        type=int,
        default=None,
        help="Maximum number of batches to process (None for all)",
    )
    parser.add_argument(
        "--dataset-type",
        type=str,
        choices=["train", "test"],
        default="test",
        help="Which dataset split to run inference on",
    )
    parser.add_argument(
        "--use-lap-dataset",
        action="store_true",
        help="Use lap only dataset instead of combined dataset",
    )
    parser.add_argument(
        "--device",
        type=str,
        default="cuda" if torch.cuda.is_available() else "cpu",
        help="Device to run inference on",
    )
    parser.add_argument(
        "--save-format",
        type=str,
        choices=["torch", "numpy"],
        default="torch",
        help="Format to save results in",
    )
    parser.add_argument(
        "--compute-losses",
        action="store_true",
        help="Whether to compute losses during inference",
    )
    return parser.parse_args()


def setup_inference_datasets(config_dict: Dict[str, Any], args, dataset_type: str):
    """Set up datasets for inference."""

    # Create a mock args object for dataset setup
    class MockArgs:
        def __init__(self, use_lap_dataset):
            self.use_lap_dataset = use_lap_dataset

    mock_args = MockArgs(args.use_lap_dataset)

    # Temporarily modify config to get the right dataset
    original_pids = config_dict.get("test_pids_to_be_considered", [])
    if dataset_type == "train":
        config_dict["test_pids_to_be_considered"] = config_dict.get(
            "train_pids_to_be_considered", []
        )

    try:
        # _, eval_dataset, _ = setup_adaptive_cf_datasets(config_dict, mock_args)
        return eval_dataset
    finally:
        # Restore original config
        config_dict["test_pids_to_be_considered"] = original_pids


def main():
    """Main inference function for adaptive CF models."""
    # Parse arguments
    args = parse_inference_arguments()

    # Get base configuration
    config_dict = get_adaptive_cf_config()

    # Apply config overrides
    args_config = parse_config(args.config_str)
    if args_config:
        print("Applying config overrides:", args_config)
        config_dict.update(args_config)

    # Set up inference configuration
    inference_overrides = {}
    if args.batch_size is not None:
        inference_overrides["batch_size"] = args.batch_size

    config_dict = setup_inference_config(config_dict, inference_overrides)

    # Set seed for reproducibility
    set_seed(config_dict["seed"])

    # Set up data paths
    trajectory_segments_dir = (
        Path.expanduser(Path(args.data_dir)) / "trajectory_segments_map_seg_ids"
    )
    trajectory_snippets_dir = (
        Path.expanduser(Path(args.data_dir)) / "trajectory_snippets"
    )
    trials_directory = Path.expanduser(Path(args.data_dir)) / "trials_final"
    map_file = trajectory_segments_dir / "track.csv"

    config_dict.update(
        {
            "trajectory_segments_dir": trajectory_segments_dir,
            "trajectory_snippets_dir": trajectory_snippets_dir,
            "trials_directory": trials_directory,
            "map_file": map_file,
        }
    )

    # Set up basic train/test split
    from sklearn.model_selection import train_test_split

    from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
        INVALID_TRIAL_IDX_PER_SUBJECT_24_D_05,
    )

    all_pids_list = [
        pid for pid in list(INVALID_TRIAL_IDX_PER_SUBJECT_24_D_05.keys()) if pid != ""
    ]
    train_pids_list, test_pids_list = train_test_split(
        all_pids_list, test_size=0.2, random_state=config_dict["seed"], shuffle=True
    )
    config_dict["train_pids_to_be_considered"] = train_pids_list
    config_dict["test_pids_to_be_considered"] = test_pids_list

    # Set up dataset
    dataset = setup_inference_datasets(config_dict, args, args.dataset_type)
    print(
        f"Running inference on {args.dataset_type} dataset with {len(dataset)} samples"
    )

    # Create dataloader
    dataloader = create_inference_dataloader(
        dataset, config_dict, batch_size=args.batch_size, shuffle=False
    )

    # Set up loss functions
    cf_loss_func_dict, lap_loss_func_dict = setup_adaptive_cf_loss_functions()
    config_dict["cf_loss_func_dict"] = cf_loss_func_dict
    config_dict["lap_loss_func_dict"] = lap_loss_func_dict

    # Load model
    print(f"Loading model from {args.checkpoint}")
    model = load_model_from_checkpoint(
        model_class=create_adaptive_cf_model,
        config_dict=config_dict,
        checkpoint_path=args.checkpoint,
        vis_dataloader=None,
        device=args.device,
        train_dataset=dataset,
        eval_dataset=dataset,
    )

    print(f"Running inference on {args.device}")

    # Run inference
    results = run_dataset_inference(
        model=model,
        dataloader=dataloader,
        max_batches=args.max_batches,
        return_losses=args.compute_losses,
        device=args.device,
    )

    # Save results
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    checkpoint_name = Path(args.checkpoint).stem
    dataset_suffix = "lap" if args.use_lap_dataset else "combined"
    results_file = (
        output_dir
        / f"adaptive_cf_inference_{checkpoint_name}_{args.dataset_type}_{dataset_suffix}.{args.save_format}"
    )

    save_inference_results(results, results_file, args.save_format)

    # Extract and save predictions separately for convenience
    predictions = extract_predictions(results, "outputs")
    predictions_file = (
        output_dir
        / f"adaptive_cf_predictions_{checkpoint_name}_{args.dataset_type}_{dataset_suffix}.{args.save_format}"
    )
    save_inference_results(predictions, predictions_file, args.save_format)

    print(f"Inference completed. Results saved to {output_dir}")
    print(f"Processed {len(results)} batches")


if __name__ == "__main__":
    main()
