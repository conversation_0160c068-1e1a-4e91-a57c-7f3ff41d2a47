## Example usage:
# python train_skill_encoder.py
# --run_description "cls_dropout0_seg_reconstruction_one_subject_t_as_test"
# --use_one_subject_to_train
# --use_train_as_test
import argparse
import collections
import random
from datetime import datetime
from pathlib import Path

import matplotlib.pyplot as plt
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.model_selection import train_test_split
from torch.utils.data import DataLoader, Dataset, RandomSampler

import wandb
from aic_bsd_model.datasets.aic_24d16_lap_dataset import (
    AIC24D16LapDataset,
    TrajFeatureIndexNoAccel,
)
from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16,
    get_uid_from_pid_and_trial_num,
    split_pids_laps,
)
from aic_bsd_model.datasets.data_collators import TrackSegmentCollator
from aic_bsd_model.datasets.dataloader_utils import TestSubjectSampler
from aic_bsd_model.losses.model_loss import (
    compute_metrics_decoding_loss,
    compute_model_loss,
    compute_trajectory_reconstruction_loss,
    compute_trajectory_reconstruction_smoothness_loss,
)
from aic_bsd_model.models.aic_base_model import LapModelNonTemporal
from aic_bsd_model.utils.training_utils import parse_batch
from aic_bsd_model.utils.visualization_utils import plot_metric, vis_traj

LOSS_FUNC_DICT = {
    "metrics_decoding_loss": compute_metrics_decoding_loss,
    "segment_traj_reconstruction_loss": compute_trajectory_reconstruction_loss,
    "segment_traj_reconstruction_smoothness_loss": compute_trajectory_reconstruction_smoothness_loss,
}


def compute_param_change(optimized_parameters, prev_params):
    norm_changes = []
    for p, prev_p in zip(optimized_parameters, prev_params):
        if p.requires_grad:
            norm_changes.append(torch.norm(p.data - prev_p))
    return torch.norm(torch.stack(norm_changes))


def parse_arguments():
    parser = argparse.ArgumentParser(
        description="Training script for driving metrics prediction."
    )
    parser.add_argument(
        "--run_description", type=str, required=True, help="Description of the run."
    )
    parser.add_argument(
        "--data-dir",
        type=str,
        help="Folder in which the snipped trajectory are to be stored",
        default="~/Data/24-D-16",
    )
    # Boolean argument for using training data as test data
    parser.add_argument(
        "--use_train_as_test",
        action="store_true",
        help="Use the training dataset as the test dataset.",
    )
    # Boolean argument for using one subject as training dat
    parser.add_argument(
        "--use_one_subject_to_train",
        action="store_true",
        help="Use only one subject to train",
    )
    parser.add_argument(
        "--debug_with_one_trial", action="store_true", help="Use only one trial. "
    )
    parser.add_argument(
        "--test_size", type=int, default=1, help="Size of the test dataset."
    )
    parser.add_argument(
        "--device",
        type=str,
        help="Device to use for training. Default is 'cuda'.",
        default="cuda:0",
    )

    args = parser.parse_args()
    return args


# Parse command-line arguments
args = parse_arguments()

# Get current timestamp
timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

# set random seeds
seed = 42
random.seed(seed)
np.random.seed(seed)
torch.manual_seed(seed)
torch.cuda.manual_seed(seed)
torch.autograd.set_detect_anomaly(True)

config_dict = {
    "traj_input_dim": 13,
    "map_input_dim": 6,
    "hidden_dim": 128,
    "is_cross_attention": True,  # for traj and map fusion
    # map_hidden_dim: 64,
    "num_attention_heads": 4,
    "num_layers": 3,
    # map_encoder_num_layers:3,
    "metrics_output_dim": 2,
    "skill_embedding_dim": 2,
    "traj_decoder_output_dim": 3,
    #'global_encoder_hidden_dim': 64,
    #'global_encoder_num_layers': 3,
    "lap_emb_use_cls_global_token": True,
    "dropout": 0.1,
    "segment_dropout_ratio": 0.0,
    "weight_decay": 1e-5,
    "lr": 0.0001,
    "batch_size": 48,
    "num_workers": 0,
    "global_num_epochs": 5000,
    "target_metric_keys": ["trial_time", "smoothness_score_mean"],
    "padding_value": -2000,
    "num_validation_epochs": 4,
    "metrics_decoding_loss_coeff": 2.0,
    "segment_traj_reconstruction_loss_coeff": 1.0,
    "segment_traj_reconstruction_smoothness_loss_coeff": 5.0,
    "skill_emb_loss_coeff": 1.0,
    "verbose_frequency": 5,
    "verbose": True,
    "index_sampling_noise": 1.0,
    "constant_shift_noise": 0.0,
    "constant_scale_noise": 0.0,
    "epoch_data_train_size": 2048,
    "use_random_sampler": False,
    "use_skill_embedding": True,
    "use_residual_connection_skill": False,
    "predict_delta": True,
    "loss_component_names": [
        "metrics_decoding_loss",
        "segment_traj_reconstruction_loss",
        "segment_traj_reconstruction_smoothness_loss",
        "skill_emb_loss",
    ],
}

# args key values to the config dict
config_dict.update(vars(args))

# add parse args or something so that paths can be passed as args. eventually unify it with draccuss or whatever AS is using for IFM
trajectory_segments_dir = (
    Path.expanduser(Path(args.data_dir)) / "trajectory_segments_map_seg_ids"
)
trials_directory = Path.expanduser(Path(args.data_dir)) / "trials_final"
map_file = trajectory_segments_dir / "track.csv"

print()
print(f"Trajectory Segments Dir: {trajectory_segments_dir}")
print(f"Trials Dir: {trials_directory}")
print(f"Map File Path: {map_file}")

config_dict["trajectory_segments_dir"] = trajectory_segments_dir
config_dict["trials_directory"] = trials_directory
# config_dict["trajectory_snippets_dir"] = trajectory_snippets_dir
config_dict["map_file"] = map_file

collator = TrackSegmentCollator(padding_value=config_dict["padding_value"])
pid_to_skip = "P1611"
all_pids_list = [
    pid
    for pid in list(INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16.keys())
    if pid != pid_to_skip
]
if args.use_one_subject_to_train:
    train_pids_list, test_pids_list = train_test_split(
        all_pids_list, test_size=len(all_pids_list) - 1, random_state=42, shuffle=True
    )
else:
    assert args.test_size > 0 and args.test_size < len(all_pids_list)
    train_pids_list, test_pids_list = train_test_split(
        all_pids_list, test_size=args.test_size, random_state=42, shuffle=True
    )

if args.use_train_as_test:
    test_pids_list = train_pids_list

print(f"Using configuration dict: {config_dict}")

print(f"Train Subject List: {train_pids_list}")
print(f"Test Subject List: {test_pids_list}")
config_dict["train_pids_to_be_considered"] = train_pids_list
config_dict["test_pids_to_be_considered"] = test_pids_list

train_24d16_dataset = AIC24D16LapDataset(config_dict, dataset_type="train")
test_24d16_dataset = AIC24D16LapDataset(config_dict, dataset_type="test")

print(f"Size of train dataset: {len(train_24d16_dataset)} laps")
print(f"Size of test dataset: {len(test_24d16_dataset)} laps")

if args.debug_with_one_trial:
    print(f" train dataset: {train_24d16_dataset.valid_lap_list} ")
    print(f" test dataset: {test_24d16_dataset.valid_lap_list} ")

# create dataloaders
sampler = None
if config_dict["use_random_sampler"]:
    assert config_dict["epoch_data_train_size"] > 0
    print(
        f"Using RandomSampler with replacement and epoch_size = {config_dict['epoch_data_train_size']} for training"
    )
    sampler = RandomSampler(
        train_24d16_dataset,
        replacement=True,
        num_samples=config_dict["epoch_data_train_size"],
    )
else:
    print(
        f"Using default dataloader with batch size {config_dict['batch_size']} for training"
    )

train_dataloader = DataLoader(
    train_24d16_dataset,
    batch_size=config_dict["batch_size"],
    collate_fn=collator,
    num_workers=config_dict["num_workers"],
    # drop_last=True,
    sampler=sampler,
)
if not config_dict["use_skill_embedding"]:
    # test dataloader does not have shuffling.
    test_dataloader = DataLoader(
        test_24d16_dataset,
        batch_size=config_dict["batch_size"],
        collate_fn=collator,
        shuffle=False,
        num_workers=config_dict["num_workers"],
    )
else:
    print(
        f"Using custom sequential lap dataloader for test set since skill embedding is being used"
    )
    # if skill embedding is used during test time, we want to be able to properly propagate the skill embedding form lap to lap.
    # in order to do this we need a custom sampler for the dataloader
    # so that each batch item corresponds to the lap in sequential orders
    test_subject_laps_idxs = []
    prior_reset_idx = []
    # iterate trough each test subject
    for test_pid in test_pids_list:
        # for the test subject with test_pid, get all uids that are in the valid_lap list in the test_dataset that correspond to this pid
        test_pid_uid_list = [
            uid for uid in test_24d16_dataset.valid_lap_list if test_pid in uid
        ]
        # get all valid trial nums for this pid.
        test_pid_trial_num_list = []
        for uid in test_pid_uid_list:
            trial_num_index = uid.index("_")
            trial_num = int(uid[trial_num_index + 1 :])
            test_pid_trial_num_list.append(trial_num)

        # sort them so that the trial nums are sequential and allows for proper propagation of skill emebdding.
        test_pid_trial_num_list = sorted(test_pid_trial_num_list)
        # get ordered uid list for test_pid
        ordered_test_pid_uid_list = [
            get_uid_from_pid_and_trial_num(test_pid, trial_num)
            for trial_num in test_pid_trial_num_list
        ]
        # get the dataset idxs corresponding to the ordered pids.
        ordered_test_pid_dataset_idxs = [
            test_24d16_dataset.get_data_idx_from_trial_uid(ordered_uid)
            for ordered_uid in ordered_test_pid_uid_list
        ]

        test_subject_laps_idxs.extend(ordered_test_pid_dataset_idxs)
        prior_reset_idx.append(len(ordered_test_pid_dataset_idxs))

    sequential_lap_sampler = TestSubjectSampler(test_subject_laps_idxs)
    prior_reset_idx = np.cumsum(prior_reset_idx)
    prior_reset_idx = [0] + list(prior_reset_idx)
    test_dataloader = DataLoader(
        test_24d16_dataset,
        collate_fn=collator,
        batch_size=1,
        num_workers=config_dict["num_workers"],
        sampler=sequential_lap_sampler,
    )


dataloaders_dict = {
    # "test": test_dataloader,
    "train": train_dataloader,
    "test": test_dataloader,
}

# create model
model = LapModelNonTemporal(config_dict)

device = args.device
model.to(device)


# Train loop

# Initialize W&B
wandb.init(
    project="aic_base_uncoached_model",
    name=f"{args.run_description}-{timestamp}",
    config=config_dict,
)


global_batch_num = {"train": 0, "test": 0}
epoch_counters = {"train": 0, "test": 0}  # Separate epoch counters for each phase

pids, trial_nums, lap_uids = split_pids_laps(train_24d16_dataset)
config_dict["training_ds_pids-trial_nums-lap_uids"] = pids, trial_nums, lap_uids


skill_embedding = None

# TODO (deepak.gopinath) have a better way of configuring parameters. Potentially include skill_embedding within the model class.
learned_parameters = (
    list(model.parameters()) + list(skill_embedding.parameters())
    if config_dict["use_skill_embedding"]
    else list(model.parameters())
)

optimizer = optim.AdamW(
    learned_parameters, lr=config_dict["lr"], weight_decay=config_dict["weight_decay"]
)

prev_params = [p.clone().detach() for p in learned_parameters]
print(f"Model Size: {sum(p.numel() for p in learned_parameters if p.requires_grad)}")


# start training loop
for epoch in range(config_dict["global_num_epochs"]):
    model.train()
    epoch_losses = {}

    losses_dict = collections.OrderedDict()
    for loss_component_name in config_dict["loss_component_names"]:
        losses_dict[loss_component_name] = {}

    # dictionaries to keep track of estimated and gt metric for each dataloader type for each epoch
    estimated_metric_dict = {}
    gt_metric_dict = {}
    for dataloader_name in dataloaders_dict.keys():
        estimated_metric_dict[dataloader_name] = {}
        gt_metric_dict[dataloader_name] = {}

    # iterate over train and test dataloaders
    for dataloader_name, dataloader in dataloaders_dict.items():
        if dataloader_name == "train":
            model.train()
        else:
            # Skip test phase unless it's the num validation epochs
            if epoch % config_dict["num_validation_epochs"] != 0:
                continue

            if config_dict["use_skill_embedding"]:
                reset_idx = 0

            model.eval()

        epoch_losses[dataloader_name] = 0.0
        for loss_component_name in config_dict["loss_component_names"]:
            losses_dict[loss_component_name][dataloader_name] = 0.0

        print()
        print(f"In {dataloader_name} mode. Global Epoch: {epoch+1}")
        with torch.set_grad_enabled(dataloader_name == "train"):
            for batch_idx, batch in enumerate(dataloader):
                # each batch itm is a lap
                global_batch_num[dataloader_name] += 1
                # do parse batch item in a function. use a dict for the parsed item.
                # Move batch data to device
                (
                    curr_trajectory_segments,
                    curr_map_segments,
                    curr_trajectory_attention_masks,
                    curr_map_attention_masks,
                    curr_traj_segments_lengths,
                ) = parse_batch(batch, device)

                # set the current skill embedding prior and posterior target
                # TODO (deepak.gopinath) make this into a separate function
                if dataloader_name == "train":
                    if config_dict["use_skill_embedding"]:
                        curr_lap_uids = batch["curr_lap_uid"]
                        # (B, embedding_dim)
                        curr_lap_skill_embed_prior = skill_embedding(curr_lap_uids)
                        curr_lap_skill_embed_posterior_target = skill_embedding(
                            curr_lap_uids, plus_one=True
                        )
                        print(
                            skill_embedding.zeroth_lap_embedding.weight,
                            skill_embedding.remaining_laps_skill_embedding.weight,
                        )
                    else:
                        curr_lap_skill_embed_prior = torch.zeros(
                            (
                                len(batch["curr_lap_uid"]),
                                config_dict["skill_embedding_dim"],
                            ),
                            device=device,
                        )
                        curr_lap_skill_embed_posterior_target = torch.zeros(
                            (
                                len(batch["curr_lap_uid"]),
                                config_dict["skill_embedding_dim"],
                            ),
                            device=device,
                        )
                elif dataloader_name == "test":
                    if not config_dict["use_skill_embedding"]:
                        curr_lap_skill_embed_prior = torch.zeros(
                            (
                                len(batch["curr_lap_uid"]),
                                config_dict["skill_embedding_dim"],
                            ),
                            device=device,
                        )
                        curr_lap_skill_embed_posterior_target = torch.zeros(
                            (
                                len(batch["curr_lap_uid"]),
                                config_dict["skill_embedding_dim"],
                            ),
                            device=device,
                        )
                    else:
                        if batch_idx == prior_reset_idx[reset_idx]:
                            reset_idx += 1
                            # (B=1, skill_dim)
                            curr_lap_skill_embed_prior = torch.zeros(
                                (
                                    1,
                                    config_dict["skill_embedding_dim"],
                                ),
                                device=device,
                            )
                        elif batch_idx != 0:
                            # propagate the skill embedding from lap to next lap
                            curr_lap_skill_embed_prior = (
                                curr_lap_skill_embed_posterior_estimated
                            )

                # MODEL FORWARD
                try:
                    # model forward using full batch
                    (
                        curr_lap_representation,
                        curr_lap_decoded_metrics,
                        curr_lap_decoded_segment_trajectories,
                        pred_mask,
                        curr_lap_skill_embed_posterior_estimated,
                    ) = model(
                        curr_trajectory_segments,
                        curr_map_segments,
                        curr_trajectory_attention_masks,
                        curr_map_attention_masks,
                        curr_traj_segments_lengths,
                        lap_skill_prior=curr_lap_skill_embed_prior
                        if config_dict["use_skill_embedding"]
                        else None,
                    )
                except Exception as e:
                    import IPython

                    IPython.embed(header="check model forward")
                    raise e

                    # TODO(guy.rosman): did not handle gpu access on my compute from the get-go, need to see if reproducible
                    # continue

                # visualize trajectories. (deepak.gopinath) Move to another functoin
                # check indexing.

                if dataloader_name == "test" and batch_idx == 0:
                    # TODO (deepak.gopinath). Don't visualize if max num visualization images have reached.
                    # Or else if there are multiple batches for the test set the logs will be overwritten
                    for batch_i in range(min(1, curr_trajectory_segments.shape[0])):
                        for segment_i in range(curr_trajectory_segments.shape[1]):
                            xy = (
                                curr_trajectory_segments[
                                    batch_i,
                                    segment_i,
                                    :,
                                    TrajFeatureIndexNoAccel.TRAJ_X : TrajFeatureIndexNoAccel.TRAJ_X
                                    + 2,
                                ]
                                .detach()
                                .cpu()
                            )

                            valid = ~(xy[:, 0] == config_dict["padding_value"])
                            xryr = (
                                curr_lap_decoded_segment_trajectories[
                                    batch_i,
                                    segment_i,
                                    :,
                                    TrajFeatureIndexNoAccel.TRAJ_X : TrajFeatureIndexNoAccel.TRAJ_X
                                    + 2,
                                ]
                                .detach()
                                .cpu()
                            )
                            curr_lap_uid = batch["curr_lap_uid"][batch_i]

                            vis_traj(
                                batch,
                                batch_i,
                                xy[:, 0],
                                xy[:, 1],
                                valid,
                                xryr[:, 0],
                                xryr[:, 1],
                                curr_lap_uid,
                                dataloader_name,
                                segment_i,
                            )

                target_metrics = []
                individual_metric_loss = {}

                for target_metric_key in config_dict["target_metric_keys"]:
                    target_metrics.append(batch[f"curr_{target_metric_key}"])

                # keep track of each of the estimated and ground truth metric every batch for a dataloader for each epoch
                for target_metric_i, target_metric_key in enumerate(
                    config_dict["target_metric_keys"]
                ):
                    if target_metric_key not in estimated_metric_dict[dataloader_name]:
                        estimated_metric_dict[dataloader_name][target_metric_key] = []
                        gt_metric_dict[dataloader_name][target_metric_key] = []

                    # consider removing this try: except
                    try:
                        estimated_metric_dict[dataloader_name][
                            target_metric_key
                        ].append(
                            curr_lap_decoded_metrics[:, target_metric_i].detach().cpu()
                        )
                    except:
                        import IPython

                        IPython.embed(header="check")

                    gt_metric_dict[dataloader_name][target_metric_key].append(
                        target_metrics[target_metric_i].detach().cpu()
                    )

                    individual_metric_loss[target_metric_key] = (
                        nn.MSELoss()(
                            curr_lap_decoded_metrics[:, target_metric_i],
                            target_metrics[target_metric_i].to(device),
                        )
                        .detach()
                        .cpu()
                    )
                    wandb.log(
                        {
                            f"Batch {dataloader_name} {target_metric_key} Loss": individual_metric_loss[
                                target_metric_key
                            ].item()
                        }
                    )

                # COMPUTE LOSS COMPONENTS FOR EACH BATCH

                target_metrics = torch.vstack(target_metrics).T.to(device)

                loss_computation_dict = {}
                loss_computation_dict[
                    "decoded_segment_trajs"
                ] = curr_lap_decoded_segment_trajectories
                loss_computation_dict["target_segment_trajs"] = curr_trajectory_segments
                loss_computation_dict["pred_mask"] = pred_mask
                loss_computation_dict["segments_length"] = curr_traj_segments_lengths
                loss_computation_dict["lap_decoded_metrics"] = curr_lap_decoded_metrics
                loss_computation_dict["target_lap_metrics"] = target_metrics
                loss_computation_dict[
                    "lap_skill_embed_posterior_estimated"
                ] = curr_lap_skill_embed_posterior_estimated
                loss_computation_dict["lap_skill_embed_posterior_target"] = (
                    curr_lap_skill_embed_posterior_target
                    if dataloader_name == "train"
                    else None
                )
                loss_computation_dict["loss_func_dict"] = LOSS_FUNC_DICT

                (
                    total_batch_loss,
                    batch_loss_components,
                    scaled_batch_loss_components,
                ) = compute_model_loss(
                    loss_computation_dict, config_dict, dataloader_name
                )

                skip_batch = False
                if torch.isnan(total_batch_loss):
                    # if there is nan in the loss.
                    import IPython

                    IPython.embed(header="total_batch_loss is nan")
                    print("total_batch_loss is nan")
                    continue

                if dataloader_name == "train":
                    # Backward pass and optimization for the train dataloader
                    optimizer.zero_grad()
                    try:
                        total_batch_loss.backward()
                    except Exception as e:
                        # if loss.Backward fails that could be because, there are detached tensors,
                        # missing gradients for certain parameters, not zeroing out gradients before accumulating them.
                        skip_batch = True
                        import IPython

                        IPython.embed(header="check total_batch_loss backward")

                    for name, param in model.named_parameters():
                        if param.grad is not None and torch.isnan(param.grad).any():
                            print(f"{name} grad is nan. skipping batch")
                            skip_batch = True
                            continue

                    if skip_batch:
                        print("zeroing out gradients due to skipped batch")
                        optimizer.zero_grad()
                        continue

                    # plot gradnet norm
                    # add gradient visualization. Is gradient clipping necessary
                    torch.nn.utils.clip_grad_norm_(learned_parameters, 1e-3)
                    optimizer.step()

                    param_change = compute_param_change(learned_parameters, prev_params)

                    # Log parameter change norm
                    wandb.log(
                        {
                            f"Batch {dataloader_name} model param norm change": param_change.item()
                        }
                    )
                    if (
                        config_dict["verbose"]
                        and batch_idx % config_dict["verbose_frequency"] == 0
                    ):
                        print(
                            f"Batch {batch_idx+1} out of {len(dataloader)}, Parameter Change Norm: {param_change.item()}"
                        )
                    prev_params = [p.clone().detach() for p in learned_parameters]

                # Accumulate losses for logging
                epoch_losses[dataloader_name] += total_batch_loss.item()
                for loss_component_name in config_dict["loss_component_names"]:
                    losses_dict[loss_component_name][
                        dataloader_name
                    ] += scaled_batch_loss_components[loss_component_name].item()

                # Log batch losses to W&B
                wandb.log(
                    {f"Batch {dataloader_name} Total Loss": total_batch_loss.item()}
                )
                for loss_component_name in config_dict["loss_component_names"]:
                    wandb.log(
                        {
                            f"Batch {dataloader_name} {loss_component_name}": scaled_batch_loss_components[
                                loss_component_name
                            ].item()
                        }
                    )

                if (
                    config_dict["verbose"]
                    and batch_idx % config_dict["verbose_frequency"] == 0
                ):
                    print(
                        f"Batch {batch_idx+1} out of {len(dataloader)}. Total batch loss: {total_batch_loss.item()}"
                    )

        # TODO: add a test phase for continuous skill evaluation, has to read continuous samples from the dataset

        # dataloader specific visualization
        reports = {}
        # if "test" in gt_metric_dict and len(estimated_metric_dict["test"]) > 0:
        if dataloader_name == "test":
            for target_metric_key in config_dict["target_metric_keys"]:
                plot_metric(target_metric_key, estimated_metric_dict, gt_metric_dict)

        # plot skill embedding every train epoch
        # TODO (deepak.gopinath) find a better way to do this.
        if config_dict["use_skill_embedding"]:
            if (
                dataloader_name == "train"
                and skill_embedding.embedding_dim > 1
                and epoch_counters[dataloader_name] % 5 == 0
            ):
                for train_pid in train_pids_list:
                    # num_laps_for_pid, skill_embed_dim
                    skill_embedding_for_pid = (
                        skill_embedding.get_embeddings_for_pid(train_pid).detach().cpu()
                    )
                    print(
                        f"Visualizing skill embedding for dataloader {dataloader_name} for pid {train_pid} "
                    )
                    fig, ax = plt.subplots(figsize=(10, 30))
                    hmap = ax.imshow(skill_embedding_for_pid, aspect="auto")
                    plt.xlabel("Skill Embedding")
                    plt.ylabel("Lap Num")
                    plt.title(
                        f"Skill embedding for PID {train_pid} - Epoch {epoch_counters[dataloader_name]}"
                    )
                    row_numbers = list(range(0, skill_embedding_for_pid.shape[0]))
                    ax.set_yticks(np.arange(skill_embedding_for_pid.shape[0]))
                    ax.set_yticklabels(row_numbers, fontsize=8)
                    plt.colorbar(hmap)
                    # Get the image data from the buffer

                    # Log the image to W&B
                    wandb.log(
                        {f"Scatterplot_embedding: PID {train_pid}": wandb.Image(fig)}
                    )
                    plt.close()

        # Log epoch losses for each dataloader to W&B

        # len(dataloader) is the number of batches
        wandb.log(
            {
                f"Epoch {dataloader_name} Total Loss": epoch_losses[dataloader_name]
                / len(dataloader)
            }
        )
        for loss_component_name in config_dict["loss_component_names"]:
            wandb.log(
                {
                    f"Epoch {dataloader_name} {loss_component_name}": losses_dict[
                        loss_component_name
                    ][dataloader_name]
                    / len(dataloader)
                }
            )

        epoch_counters[dataloader_name] += 1
        print(
            f"Epoch {epoch_counters[dataloader_name]}/{config_dict['global_num_epochs']}, Average Batch Total Loss: {epoch_losses[dataloader_name] / len(dataloader):.4f}, "
        )
        for loss_component_name in config_dict["loss_component_names"]:
            print(
                f"Average Batch {loss_component_name}: {losses_dict[loss_component_name][dataloader_name] / len(dataloader):.4f}",
            )


# TODO: save model and optimizer state_dict

# add one more epoch over test set.
import IPython

IPython.embed(banner1="check model")
# define loss function
