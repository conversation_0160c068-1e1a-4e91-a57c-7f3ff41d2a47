// To regenerate the message
// THIS_PATH=intent/multiagents/data_specific/thunderhill/online_inference/
// python -m grpc_tools.protoc -I${THIS_PATH} --python_out=${THIS_PATH} --pyi_out=${THIS_PATH} --grpc_python_out=${THIS_PATH} ${THIS_PATH}/grpc_thunderhill_msg.proto

syntax = "proto3";

// The greeting service definition.
service GetDataServeice {
  // Sends a greeting
  rpc GetData (DataRequest) returns (DataReply) {}
}

// The request message containing the user's name.
message DataRequest {
  repeated int64 time_s = 1;
  repeated int64 time_ns = 2;
  repeated float x = 3;
  repeated float y = 4;
  repeated float z = 5;
  repeated float vel_x = 6;
  repeated float vel_y = 7;
  repeated float vel_z = 8;
  repeated float heading = 9;
  repeated float throttle = 10;
  repeated float brake = 11;
  repeated float steering = 12;
  bool do_cf_prediction = 13;
  bool do_lap_prediction = 14;
  bool do_data_update = 15;
  bool reset_data = 16;
  map<string, string> other_info = 17;
  repeated float ego_quaternion_x = 18;
  repeated float ego_quaternion_y = 19;
  repeated float ego_quaternion_z = 20;
  repeated float ego_quaternion_w = 21;

}

// The response message containing the greetings
message DataReply {
  bool valid = 1;
  repeated float time = 2;
  repeated float x = 3;
  repeated float y = 4;
  repeated float yaw = 5;
  repeated string subtitle = 6;
  repeated float skill = 7;

}