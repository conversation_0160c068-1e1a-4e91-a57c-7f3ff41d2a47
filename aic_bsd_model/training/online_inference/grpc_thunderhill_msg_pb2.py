# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: grpc_thunderhill_msg.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x1agrpc_thunderhill_msg.proto"\xec\x03\n\x0b\x44\x61taRequest\x12\x0e\n\x06time_s\x18\x01 \x03(\x03\x12\x0f\n\x07time_ns\x18\x02 \x03(\x03\x12\t\n\x01x\x18\x03 \x03(\x02\x12\t\n\x01y\x18\x04 \x03(\x02\x12\t\n\x01z\x18\x05 \x03(\x02\x12\r\n\x05vel_x\x18\x06 \x03(\x02\x12\r\n\x05vel_y\x18\x07 \x03(\x02\x12\r\n\x05vel_z\x18\x08 \x03(\x02\x12\x0f\n\x07heading\x18\t \x03(\x02\x12\x10\n\x08throttle\x18\n \x03(\x02\x12\r\n\x05\x62rake\x18\x0b \x03(\x02\x12\x10\n\x08steering\x18\x0c \x03(\x02\x12\x18\n\x10\x64o_cf_prediction\x18\r \x01(\x08\x12\x19\n\x11\x64o_lap_prediction\x18\x0e \x01(\x08\x12\x16\n\x0e\x64o_data_update\x18\x0f \x01(\x08\x12\x12\n\nreset_data\x18\x10 \x01(\x08\x12/\n\nother_info\x18\x11 \x03(\x0b\x32\x1b.DataRequest.OtherInfoEntry\x12\x18\n\x10\x65go_quaternion_x\x18\x12 \x03(\x02\x12\x18\n\x10\x65go_quaternion_y\x18\x13 \x03(\x02\x12\x18\n\x10\x65go_quaternion_z\x18\x14 \x03(\x02\x12\x18\n\x10\x65go_quaternion_w\x18\x15 \x03(\x02\x1a\x30\n\x0eOtherInfoEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01"l\n\tDataReply\x12\r\n\x05valid\x18\x01 \x01(\x08\x12\x0c\n\x04time\x18\x02 \x03(\x02\x12\t\n\x01x\x18\x03 \x03(\x02\x12\t\n\x01y\x18\x04 \x03(\x02\x12\x0b\n\x03yaw\x18\x05 \x03(\x02\x12\x10\n\x08subtitle\x18\x06 \x03(\t\x12\r\n\x05skill\x18\x07 \x03(\x02\x32\x38\n\x0fGetDataServeice\x12%\n\x07GetData\x12\x0c.DataRequest\x1a\n.DataReply"\x00\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "grpc_thunderhill_msg_pb2", _globals
)
if not _descriptor._USE_C_DESCRIPTORS:
    DESCRIPTOR._loaded_options = None
    _globals["_DATAREQUEST_OTHERINFOENTRY"]._loaded_options = None
    _globals["_DATAREQUEST_OTHERINFOENTRY"]._serialized_options = b"8\001"
    _globals["_DATAREQUEST"]._serialized_start = 31
    _globals["_DATAREQUEST"]._serialized_end = 523
    _globals["_DATAREQUEST_OTHERINFOENTRY"]._serialized_start = 475
    _globals["_DATAREQUEST_OTHERINFOENTRY"]._serialized_end = 523
    _globals["_DATAREPLY"]._serialized_start = 525
    _globals["_DATAREPLY"]._serialized_end = 633
    _globals["_GETDATASERVEICE"]._serialized_start = 635
    _globals["_GETDATASERVEICE"]._serialized_end = 691
# @@protoc_insertion_point(module_scope)
