class Buffer:
    def __init__(self, input_hz, output_hz, queue_time):
        self.input_hz = input_hz
        self.output_hz = output_hz
        self.queue_time = queue_time
        self.max_len = int(queue_time * 1.1) * input_hz
        self.queue = []
        assert input_hz % output_hz == 0

        step = self.input_hz // self.output_hz
        item_idxs = list(range(self.max_len))
        latest_first_item_idxs = item_idxs[::-1]
        self.picked_idxs = set(latest_first_item_idxs[::step])

    def append(self, data):
        self.queue.append(data)
        if len(self.queue) > self.max_len:
            self.queue.pop(0)

    def get(self):
        if len(self.queue) < self.max_len:
            return None
        arr = [d for i, d in enumerate(self.queue) if i in self.picked_idxs]
        return arr
