# Copyright 2021 The gRPC Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""The graceful shutdown example for the asyncio Greeter server."""

import asyncio
import logging
import tempfile
from pathlib import Path
from typing import List

import grpc
import hid_common.util
import hid_common.util.filters
import numpy as np
import pandas as pd
import yaml

try:
    import grpc_thunderhill_msg_pb2
    import grpc_thunderhill_msg_pb2_grpc
except ModuleNotFoundError:
    from aic_bsd_model.training.online_inference import grpc_thunderhill_msg_pb2
    from aic_bsd_model.training.online_inference import grpc_thunderhill_msg_pb2_grpc

import hid_common
import torch
from hid_common.util.filters.trajectory_filters import mark_map_segment_ids

from aic_bsd_model.datasets.aic_24d05_cf_dataset import AIC24D05CFDataset
from aic_bsd_model.datasets.aic_24d05_combined_dataset import (
    AIC24D05CombinedPrevKDataset,
    CFSnippetCollator,
)
from aic_bsd_model.datasets.aic_24d05_lap_dataset import AIC24D05LapDataset
from aic_bsd_model.datasets.data_scripts import (
    aic_24d05_concurrent_feedback_trajectory_snipper,
    aic_24d05_laps_to_map_segments_snipper,
)
from aic_bsd_model.training.train_adaptive_cf import main

# Coroutines to be invoked when the event loop is shutting down.
_cleanup_coroutines = []
map_dir = Path("~/Data/24-D-16/trajectory_segments_map_seg_ids/track.csv").expanduser()
workdir = Path("~/ai_coaching_logs/").expanduser()
lap_df_dir = workdir
lap_trial_dir = workdir / "lap_trial_dir"
cf_trial_dir = workdir / "cf_trial_dir"
lap_segments_dir = workdir / "lap_segments"
cf_segments_dir = workdir / "cf_segments"
CARLA_TIME_KEY = "carla_objects log time"

lap_trial_base_name = "P604-trial"
cf_trial_base_name = "P604-trial"

with open(map_dir, "r") as f:
    map_df = pd.read_csv(f)

aic_24d05_laps_to_map_segments_snipper.get_trial_type = (
    lambda *args, **kwargs: "coaching_lap"
)
aic_24d05_concurrent_feedback_trajectory_snipper.get_trial_type = (
    lambda *args, **kwargs: "coaching_lap"
)


def list_csv_sorted_by_mtime(root: Path, *, newest_first: bool = True) -> List[Path]:
    """
    Recursively find all .csv files under `root` and return them sorted by modification time.

    :param root: the base directory to search (pathlib.Path)
    :param newest_first: if True, sort descending (most recent first)
    :return: list of Paths to .csv files
    """
    # Find all .csv files (case-sensitive); use .rglob for recursive search
    csv_files = list(root.rglob("*.csv"))
    # Sort by modification timestamp
    sorted_files = sorted(
        csv_files, key=lambda p: p.stat().st_mtime, reverse=newest_first
    )
    return sorted_files


class Buffer:
    def __init__(self, input_hz, output_hz, queue_time):
        self.input_hz = input_hz
        self.output_hz = output_hz
        self.queue_time = queue_time
        self.max_len = int(queue_time * input_hz)
        self.queue = []
        assert input_hz % output_hz == 0

        step = self.input_hz // self.output_hz
        item_idxs = list(range(self.max_len))
        latest_first_item_idxs = item_idxs[::-1]
        self.picked_idxs = set(latest_first_item_idxs[::step])

    def append(self, data):
        self.queue.append(data)
        if len(self.queue) > self.max_len:
            self.queue.pop(0)

    def get(self):
        if len(self.queue) < self.max_len:
            return None
        arr = [d for i, d in enumerate(self.queue) if i in self.picked_idxs]
        return arr


def rename_vehicle_df(df):
    df.rename(
        columns={
            "ego_position_x": "ego_x",
            "ego_position_y": "ego_y",
            "ego_position_z": "ego_z",
            #    "ego_velocity_x": "ego_vx", "ego_velocity_y": "ego_vy", "ego_velocity_z": "ego_vz",
            # "ego_orientation_x": "ego_orientation_x", "ego_orientation_y": "ego_orientation_y", "ego_orientation_z": "ego_orientation_z", "ego_orientation_w": "ego_orientation_w",
            "steer": "steering",
            "throttle_pedal": "throttle",
            "brake_pedal": "brake",
        },
        inplace=True,
    )
    df[CARLA_TIME_KEY] = df["time"]


def process_df_data(df):
    # Rename to fit mark_map_segment_ids
    rename_vehicle_df(df)
    # Stub
    df["ego_orientation_x"] = 0
    df["ego_orientation_y"] = 0
    df["ego_orientation_z"] = 0
    df["ego_orientation_w"] = 1
    df["ego_vx"] = 1
    df["ego_vy"] = 1
    df["ego_vz"] = 0
    # Write trial to workdir, dataset will load trial data from workdir
    map_id_df = hid_common.util.filters.trajectory_filters.mark_map_segment_ids(
        df, map_df
    )["annotation_df"]
    df = df.merge(map_id_df, on=CARLA_TIME_KEY, how="left")
    df["timestamp"] = df[CARLA_TIME_KEY]
    return df


def dump_df(df, is_lap_data, trial_id=0):
    if is_lap_data:
        trial_base_name = lap_trial_base_name
        trial_dir = lap_trial_dir
    else:
        trial_base_name = cf_trial_base_name
        trial_dir = cf_trial_dir

    df_suffix = "_with_all_annotations_and_metrics_with_instruction_category"
    trial_name = f"{trial_base_name}_{trial_id}"
    trial_dir2 = trial_dir / trial_name
    Path.mkdir(trial_dir2, parents=True, exist_ok=True)
    filename = f"{trial_name}{df_suffix}.trial"
    df.to_parquet(trial_dir2 / (filename + ".parquet"))
    with open(trial_dir2 / (filename + ".yaml"), "w") as fp:
        yaml.dump(
            {"additional_metrics_dict": {}, "source_lap_type": "coaching_lap"}, fp
        )


def dummy_func():
    return


def make_lap_data(trial_id=0):
    csvs = list_csv_sorted_by_mtime(lap_df_dir, newest_first=True)
    lap_df_path = csvs[0]
    df = pd.read_csv(lap_df_path)

    df = process_df_data(df)

    dump_df(df, True, trial_id)

    # Turn to Dataframe

    # dataset = make_lap_dataset()
    # dataset.__getitem__(0)
    a = 0


def make_cf_data(data: List, trial_id=0):
    # df = pd.DataFrame(data)
    # Stub data
    csvs = list_csv_sorted_by_mtime(lap_df_dir, newest_first=True)
    lap_df_path = csvs[0]
    df = pd.read_csv(lap_df_path)

    df = process_df_data(df)
    df = df[:1200]
    # Add stub future data
    # smaller index should be the history

    df["out_of_bounds"] = 0
    df["out_of_bounds_distances"] = 0
    df["lateral_distances"] = 0

    dump_df(df, False, trial_id)

    # Write trial to workdir

    # Turn to Dataframe


def make_dataset(is_lap=True):
    args = {}
    if is_lap:
        pids = ["P604"]
        dataset_cls = AIC24D05LapDataset
        args["trajectory_segments_dir"] = lap_segments_dir

    else:
        pids = ["P604"]
        dataset_cls = AIC24D05CFDataset
        args["trajectory_snippets_dir"] = cf_segments_dir
        args["test_trajectory_snippets_uids_to_be_considered"] = pids
        args["teacher_action_num_categories"] = 3

    data_config = {
        "map_file": map_dir,
        "test_pids_to_be_considered": pids,
        "use_multiprocessing": False,
        "padding_value": -2000,
        "padding_side": "right",
        "return_cf_for_lap": False,  # should the cf feedback be returned as part of the lap
        **args,
    }
    dataset = dataset_cls(data_config, "test")
    dataset.metric_keys = {}
    if not is_lap:
        dataset._prepare_dataset_stats = dummy_func
        dataset.get_cf_instruction_class_series = (
            lambda snippet_df, subsampled_idxs_dict: (
                None,
                np.ones(len(subsampled_idxs_dict["subsampled_idxs_int"]), dtype=int),
            )
        )
    dataset.init()

    return dataset, data_config


def make_batch_data_lap(dataset_idx=(0, 0, 0)):
    lap_dataset, lap_data_config = make_dataset(is_lap=True)
    laps = []
    for i in range(3):
        laps.append(lap_dataset.__getitem__(dataset_idx[i]))
    lap_collator = lap_dataset.get_collator(lap_data_config)
    lap_data_config["with_cf_snippets"] = False
    lap_data = lap_collator(laps)
    curr_traj_segments = lap_data["curr_trajectory_segments"]
    stub_metrics = torch.ones(curr_traj_segments.shape[:1])
    lap_data["curr_trial_time"] = stub_metrics
    lap_data["curr_trial_time2"] = stub_metrics

    lap_collator = AIC24D05CombinedPrevKDataset.get_collator(lap_data_config)
    k_lap_data = lap_collator([lap_data])
    k_lap_data["metrics"] = {
        "trial_time": stub_metrics[None, :],
        "trial_time2": stub_metrics[None, :],
    }
    return k_lap_data


def make_batch_data_cf(lap_model_outputs):
    stub_metrics = torch.ones(1)

    cf_dataset, cf_data_config = make_dataset(is_lap=False)
    cf_data = cf_dataset.__getitem__(0)
    cf_data.pop("snippet_cf_instruction_class_series", None)
    cf_data["snippet_cf_instruction_class_series_subsampled"] = stub_metrics
    cf_collator = CFSnippetCollator()
    cf_data = cf_collator([cf_data])
    cf_data["side_channel_lap_representation"] = lap_model_outputs["outputs"][
        "side_channel_lap_representation"
    ]
    cf_data["metrics"] = {
        "trial_time": stub_metrics[None, :],
        "trial_time2": stub_metrics[None, :],
        "trial_time-pred": stub_metrics[None, :],
        "trial_time2-pred": stub_metrics[None, :],
    }
    return cf_data


class Greeter(grpc_thunderhill_msg_pb2_grpc.GetDataServeiceServicer):
    def __init__(self, model):
        self.model = model
        self.lap_buffer = Buffer(120, 5, 8.4)
        pids = ["P604"]
        # pids = []
        self.data_config = {
            # "trajectory_segments_dir": lap_segments_dir,
            "map_file": map_dir,
            "test_pids_to_be_considered": pids,
        }
        # self.lap_dataset = AIC24D16LapDataset(self.data_config, "test")
        # self.lap_dataset = None
        # Ignore metrics
        # self.cf_dataset = AIC24D05CFDataset(self.data_config, "test")

    async def GetData(
        self,
        request: grpc_thunderhill_msg_pb2.DataRequest,
        context: grpc.aio.ServicerContext,
    ) -> grpc_thunderhill_msg_pb2.DataReply:
        # await asyncio.sleep(4)
        # logging.info("Sleep completed, responding")
        reply = grpc_thunderhill_msg_pb2.DataReply(valid=False)
        if request.do_data_update:
            self.lap_buffer.append(request)

        if request.do_lap_prediction and self.model is not None:
            try:
                dataset = make_dataset(is_lap=True)
                data = dataset.__getitem__(0)

            except Exception as e:
                logging.warning(f"Do prediction get data failed :{e}.")
                return reply

        if request.do_cf_prediction and self.model is not None:
            try:
                data = self.lap_buffer.get_data()
                cf_dataset = make_dataset(is_lap=False)
                data = cf_dataset.__getitem__(0)
                a = 0

            except Exception as e:
                logging.warning(f"Do prediction get data failed :{e}.")
                return reply

            try:
                batch = Dataset(data)
                batch["inter"] = self.intermediate
                pred_traj, pred_subtitle = self.model.forward(batch)
                self.intermediate = batch[""]
                if pred_traj is not None and pred_subtitle is not None:
                    x = pred_traj[..., 0].reshape(-1).tolist()
                    y = pred_traj[..., 1].reshape(-1).tolist()
                    subtitle = str(pred_subtitle)
                    reply = grpc_thunderhill_msg_pb2.DataReply(
                        x=x, y=y, subtitle=[subtitle], valid=True
                    )
                else:
                    reply = grpc_thunderhill_msg_pb2.DataReply(
                        x=[0.0], y=[0.0], subtitle=[""], valid=False
                    )
            except ImportError as e:
                logging.warning(f"Model forward error :{e}.")

            # print("pred_traj", pred_traj)
            # print("pred_subtitle", pred_subtitle)

        else:
            logging.warning("Request received, but no model is present.")
        return reply


async def serve(model) -> None:
    greeter = Greeter(model)
    server = grpc.aio.server()
    grpc_thunderhill_msg_pb2_grpc.add_GetDataServeiceServicer_to_server(greeter, server)
    listen_addr = "[::]:50057"
    server.add_insecure_port(listen_addr)
    logging.warning("Starting server on %s", listen_addr)
    await server.start()

    async def server_graceful_shutdown():
        logging.warning("Starting graceful shutdown...")
        # Shuts down the server with 5 seconds of grace period. During the
        # grace period, the server won't accept new connections and allow
        # existing RPCs to continue within the grace period.
        await server.stop(3)

    _cleanup_coroutines.append(server_graceful_shutdown())
    await server.wait_for_termination()


def serve_sync(model):
    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(serve(model))
    finally:
        loop.run_until_complete(*_cleanup_coroutines)
        loop.close()


async def testing():
    greeter = Greeter(1)
    req = grpc_thunderhill_msg_pb2.DataRequest(do_lap_prediction=True)
    reply = await greeter.GetData(req, 1)


import matplotlib.pyplot as plt


def plot_ego_position(
    df,
    x_col: str = "ego_position_x",
    y_col: str = "ego_position_y",
    kind: str = "line",
    figsize: tuple = (8, 6),
    save_path: str = None,
    **plot_kwargs,
):
    """
    Plot ego position from a DataFrame, and optionally save the figure.

    Parameters
    ----------
    df : pandas.DataFrame
        DataFrame containing at least `x_col` and `y_col`.
    x_col : str, default 'ego_position_x'
        Column name for x coordinates.
    y_col : str, default 'ego_position_y'
        Column name for y coordinates.
    kind : {'line', 'scatter'}, default 'line'
        Type of plot.
    figsize : tuple, default (8, 6)
        Figure size.
    save_path : str, optional
        If provided, saves the plot to this filepath (e.g. 'plot.png', 'fig.pdf').
    **plot_kwargs
        Additional keyword args passed to `plt.plot` or `plt.scatter`.

    Returns
    -------
    matplotlib.axes.Axes
        The axes object with the plot.
    """
    fig, ax = plt.subplots(figsize=figsize)

    if kind == "scatter":
        ax.scatter(df[x_col], df[y_col], **plot_kwargs)
    else:
        ax.plot(df[x_col], df[y_col], **plot_kwargs)

    ax.set_xlabel(x_col)
    ax.set_ylabel(y_col)
    ax.set_title(f"Ego Position ({kind})")
    ax.grid(True)

    if save_path:
        fig.savefig(save_path, bbox_inches="tight")
        # Optionally, you can print or log:
        print(f"Saved plot to {save_path}")

    return ax


if __name__ == "__main__":
    csvs = list_csv_sorted_by_mtime(lap_df_dir, newest_first=True)

    lap_df_path = csvs[1]
    df = pd.read_csv(lap_df_path)
    ax = plot_ego_position(df, kind="scatter", s=20, save_path="ego_scatter.png")

    plt.show()
    a = 0
