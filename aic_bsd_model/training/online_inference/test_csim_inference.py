import collections
import math
import pickle
import shutil
from pathlib import Path

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import torch
import yaml
from matplotlib.gridspec import GridSpec

from aic_bsd_model.datasets.aic_24d05_cf_dataset import AIC24D05CFDataset
from aic_bsd_model.datasets.aic_24d05_combined_dataset import (
    AIC24D05CombinedPrevKDataset,
    CFSnippetCollator,
)
from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN,
    COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN_NO_BRAKE,
    DEMO_TRIAL_PER_SUBJECT_24_D_05,
    FEATURE_TO_DF_KEY,
    FEATURE_TO_DF_KEY_NO_ACCEL,
    INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16,
    TRAJ_FEATURE_LINEAR_SCALE,
    VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN,
    VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN_NO_BRAKE,
    TrajFeatureIndex,
    TrajFeatureIndexNoAccel,
    get_pid_and_trial_num_from_uid,
    open_pkl,
)
from aic_bsd_model.training.train_adaptive_cf import main
from aic_bsd_model.training.train_nonadaptive_cf import TrainConfigNonadaptive
from aic_bsd_model.training.train_nonadaptive_cf import main as non_adaptive_main
from aic_bsd_model.utils.training_utils import parse_batch, parse_snippet_batch
from aic_bsd_model.utils.visualization_utils import (
    vis_traj,
    visualize_batch_predicted_cf_future_trajectories,
    visualize_teacher_action_prediction,
)

# add parse args or something so that paths can be passed as args. eventually unify it with draccuss or whatever AS is using for IFM
trajectory_segments_dir = Path.expanduser(
    Path("~/Data/24-D-05/trajectory_segments_map_seg_ids/")
)
trajectory_snippets_dir = Path.expanduser(Path("~/Data/24-D-05/trajectory_snippets/"))
temp_trajectory_snippets_dir = Path.expanduser(
    Path("~/Data/24-D-05/temp_trajectory_snippets/")
)
trials_directory = Path("~/Data/24-D-05/trials_final/")

trajectory_segments_dir = Path.expanduser(Path(trajectory_segments_dir))
# trials_directory = Path.expanduser(Path(trials_directory))
# trajectory_snippets_dir = Path.expanduser(Path(trajectory_snippets_dir))
# vis_output_folder = Path.expanduser(Path(vis_output_folder))
map_file = trajectory_segments_dir / "track.csv"
with open(map_file, "r") as f:
    map_data = pd.read_csv(f)

csim_data_parent_folder = "~/Data/24-D-05/csim_data_log/"
csim_data_parent_folder = Path.expanduser(Path(csim_data_parent_folder))
# csim_data_folder =  "~/Data/24-D-05/csim_data_log/temp_snippet_6s_swap/"
# csim_data_folder = Path.expanduser(Path(csim_data_folder))
vis_output_test_folder = Path.expanduser(
    Path("~/Data/24-D-05/csim_data_log/test_output_folder")
)
vis_output_test_thru_trainingpipeline_folder = Path.expanduser(
    Path("~/Data/24-D-05/csim_data_log/test_thru_training_pipeline_output_folder")
)

vis_output_csim_folder = Path.expanduser(
    Path("~/Data/24-D-05/csim_data_log/csim_output_folder")
)
# full_lap_test_csv_path = "~/Data/24-D-05/csim_data_log/west_lap_ccw_model_test_6s.csv"
full_lap_test_csv_path = (
    "~/Data/24-D-05/csim_data_log/west_lap_ccw_model_test_unspin.csv"
)

CARLA_TIME_KEY = "carla_objects log time"
TEACHER_ACTION_FUTURE_SEQ_LEN_IN_SEC = 1
CATEGORY_INDEX_TYPE = "no_steering_no_turn"
if CATEGORY_INDEX_TYPE == "no_steering_no_turn":
    IDX_CATEGORY_TYPE_DICT = {
        v: k
        for k, v in COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN.items()
    }
    LATERAL_CATEGORIES = {1: "move_to_the_right", 2: "move_to_the_left"}
    LONGI_CATEGORIES = {
        3: "add_gas",
        4: "off_the_gas",
        5: "stay_on_the_gas",
        6: "brake",
        7: "off_the_brakes",
    }
    LATERAL_CATEGORIES_COMPRESSED = {
        3: "speed_up",
        4: "slow_down",
        5: "speed_up",
        6: "slow_down",
        7: "speed_up",
    }
elif CATEGORY_INDEX_TYPE == "no_steering_no_turn_no_brake":
    IDX_CATEGORY_TYPE_DICT = {
        v: k
        for k, v in COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN_NO_BRAKE.items()
    }
    LATERAL_CATEGORIES = {1: "move_to_the_right", 2: "move_to_the_left"}
    LONGI_CATEGORIES = {
        3: "add_gas",
        4: "off_the_gas",
        5: "stay_on_the_gas",
    }
    LATERAL_CATEGORIES_COMPRESSED = {
        3: "speed_up",
        4: "slow_down",
        5: "speed_up",
    }

DEFAULT_PER_LABEL_DECISION_THRESHOLD = {
    1: 0.5,  # move right
    2: 0.5,  # move left
    3: 0.5,  # add gas
    4: 0.5,  # off the gas
    5: 0.5,  # stay on the gas
    6: 0.5,  # brake
    7: 0.5,  # off the brakes
}
PER_LABEL_DECISION_THRESHOLD = {
    1: 0.4,  # move right
    2: 0.6,  # move left
    3: 0.5,  # add gas
    4: 0.69,  # off the gas
    5: 0.4,  # stay on the gas
    6: 0.5,  # brake
    7: 0.5,  # off the brakes
}
DECISION_THRESHOLD = 0.5


def clear_directory(path):
    """
    Remove all contents of the directory at `path`,
    but leave the directory itself in place.
    """
    root = Path(path)
    for child in root.iterdir():
        try:
            if child.is_file() or child.is_symlink():
                child.unlink()
            elif child.is_dir():
                shutil.rmtree(child)
        except Exception as e:
            print(f"Failed to delete {child}: {e}")


model_path = "~/Data/24-D-05/csim_data_log/models/09-03T22_09_52-e22f3-test_nonadaptive_mapdropout_withgauss_withcfmetrics/latest/checkpoint-83000"
# model_path = "~/Data/24-D-05/csim_data_log/models/08-26T19_16_03-7c213-csim_modeltest_nonadaptive/latest/checkpoint-83000"
# model_path = "~/Data/24-D-05/csim_data_log/models/09-04T22_41_02-9cf2b-test_nonadaptive_shorter_future1s/latest/checkpoint-42750"
# no_steering_no_turn_no brake, with gauss, no skill,
# model_path= "~/Data/24-D-05/csim_data_log/models/09-04T22_01_19-d661d-csim_nonadaptive_no_steer_no_turn_no_brake_withgauss_no_cfskill/latest/checkpoint-31700"
model_config = []

# clear_directory(vis_output_csim_folder)
# clear_directory(vis_output_test_folder)

Path.mkdir(
    vis_output_csim_folder / Path(model_path).parent.parent.stem,
    parents=True,
    exist_ok=True,
)
Path.mkdir(
    vis_output_test_folder / Path(model_path).parent.parent.stem,
    parents=True,
    exist_ok=True,
)
Path.mkdir(
    vis_output_test_thru_trainingpipeline_folder / Path(model_path).parent.parent.stem,
    parents=True,
    exist_ok=True,
)
Path.mkdir(temp_trajectory_snippets_dir, parents=True, exist_ok=True)

model_config += ["is_adaptive=False"]
checkpoint_path = Path(model_path).expanduser()
model_config += [
    "use_gaussian_dropout=True,cf_use_metrics_prediction=True,cf_map_channel_dropout=0.2"
]
# model_config += ["use_gaussian_dropout=True,cf_use_metrics_prediction=False"]
# model_config += [
#     "use_gaussian_dropout=True,cf_use_metrics_prediction=False,teacher_action_future_seq_len_in_sec=1"
# ]
# model_config += ["use_gaussian_dropout=True,cf_use_metrics_prediction=False,teacher_action_future_seq_len_in_sec=1,valid_annotation_index_dict_type=no_steering_no_turn_no_brake,teacher_action_num_categories=6"]
model_config = ",".join(model_config)
print(f"model_config: {model_config}")


def get_model_ready():
    trainer = main(
        [
            "--online-inference",
            "True",
            "--checkpoint",
            str(checkpoint_path),
            "--category-index-type",
            f"{CATEGORY_INDEX_TYPE}",
            "--config-str",
            model_config,
        ]
    )

    model = trainer.model
    model.eval()
    model.training_mode = "cf_only"
    model.compute_loss = lambda *args, **kwargs: (0, (0, 0))
    return model


def run_sequential_inference(
    dataset, snippet_id_to_ds_idx_dict, model, vis_output_folder
):
    stub_metrics = torch.ones(1)

    config_dict = {
        "valid_annotation_categories_index_dict_type": CATEGORY_INDEX_TYPE,
        "padding_value": -2000,
    }
    for snippet_num in range(len(dataset)):
        ds_idx = snippet_id_to_ds_idx_dict[snippet_num]
        cf_data = dataset[ds_idx]
        cf_data.pop("snippet_cf_instruction_class_series", None)
        cf_data["snippet_cf_instruction_class_series_subsampled"] = stub_metrics
        cf_collator = CFSnippetCollator()
        cf_data = cf_collator([cf_data])
        # cf_data["side_channel_lap_representation"] = lap_model_outputs["outputs"][
        #     "side_channel_lap_representation"
        # ]
        device = cf_data["snippet_trajectory_inputs_subsampled"].device
        stub_metrics = stub_metrics.to(device)
        cf_data["curr_lap_masks"] = stub_metrics[None, :]
        cf_data["metrics"] = {
            "trial_time": stub_metrics[None, :],
            "trial_time2": stub_metrics[None, :],
            "trial_time-pred": stub_metrics[None, :],
            "trial_time2-pred": stub_metrics[None, :],
        }
        cf_data["cf_metrics"] = {
            "racing_line_score_abs_mean": stub_metrics[None, :],
            "smoothness_score_mean": stub_metrics[None, :],
            "racing_line_score_abs_mean-pred": stub_metrics[None, :],
            "smoothness_score_mean-pred": stub_metrics[None, :],
        }
        cf_model_outputs = model(cf_data)
        cf_data.update(cf_model_outputs)
        cf_data["cf_teacher_action_existence_gt"] = torch.zeros_like(
            cf_data["cf_teacher_action_sig"]
        )
        teacher_action_pred = cf_model_outputs["outputs"]["cf_teacher_action_sig"]
        # print("teacher_action_pred", teacher_action_pred)
        l = teacher_action_pred.cpu().detach().numpy()[0, 0].tolist()
        subtitle_list, _ = subtitle_pred_to_str(l)
        vis_ret = vis_traj_one_off(
            cf_data,
            f"test",
            config_dict,
            1,
            visualize_teacher_action_prediction,
            subtitle=subtitle_list[0],
        )
        vis_ret["fig"].savefig(
            vis_output_folder
            / Path(model_path).parent.parent.stem
            / f"snippet_num_{snippet_num}.png"
        )

        print(snippet_num, subtitle_list)
        print()


def dummy_func():
    return


def subtitle_pred_to_str(subtitle_prediction, list_of_cf_predictions=None):
    # NO_STEERING_NO_TURN
    ret = []
    longi_predictions = [
        i
        for i in range(1, len(subtitle_prediction))
        if i in list(LONGI_CATEGORIES.keys())
    ]
    valid_longi_predictions = []
    valid_lateral_predictions = []
    for i in range(1, len(subtitle_prediction)):
        # if subtitle_prediction[i] > DECISION_THRESHOLD:
        # if subtitle_prediction[i] > PER_LABEL_DECISION_THRESHOLD[i]:
        if subtitle_prediction[i] > DEFAULT_PER_LABEL_DECISION_THRESHOLD[i]:
            if i in list(LONGI_CATEGORIES.keys()):
                valid_longi_predictions.append(
                    (LONGI_CATEGORIES[i], subtitle_prediction[i])
                )  # name to be displayed along with the prediction value
            if i in list(LATERAL_CATEGORIES.keys()):
                valid_lateral_predictions.append(
                    (LATERAL_CATEGORIES[i], subtitle_prediction[i])
                )
    return_prediction_dict = {}
    if len(valid_longi_predictions) != 0:
        max_longi_pred_index = np.argmax([i[1] for i in valid_longi_predictions])
        longi_text_to_show = valid_longi_predictions[max_longi_pred_index][0]
        print(
            f"max_longi prediction probability for {longi_text_to_show}: {valid_longi_predictions[max_longi_pred_index][1]}"
        )
        return_prediction_dict[longi_text_to_show] = valid_longi_predictions[
            max_longi_pred_index
        ][1]
    else:
        longi_text_to_show = ""

    if len(valid_lateral_predictions) != 0:
        max_lateral_pred_index = np.argmax([i[1] for i in valid_lateral_predictions])
        lateral_text_to_show = valid_lateral_predictions[max_lateral_pred_index][0]
        print(
            f"max_lat prediction probability for {lateral_text_to_show}: {valid_lateral_predictions[max_lateral_pred_index][1]}"
        )
        return_prediction_dict[lateral_text_to_show] = valid_lateral_predictions[
            max_lateral_pred_index
        ][1]
    else:
        lateral_text_to_show = ""

    if longi_text_to_show == "" or lateral_text_to_show == "":
        connecting_text = ""
    else:
        connecting_text = "_and_"
    subtitle_str = lateral_text_to_show + connecting_text + longi_text_to_show

    ret.append(subtitle_str)

    return ret, return_prediction_dict


def vis_traj_one_off(
    batch, dataloader_name, config_dict, count, plot_callback=None, subtitle=""
):
    if "cf_predicted_future_trajectories" not in batch:
        return {}
    snippet_i = 0

    cf_predicted_future_trajectories = batch["cf_predicted_future_trajectories"]
    valid_annotation_categories_index_dict_type = config_dict[
        "valid_annotation_categories_index_dict_type"
    ]

    (
        snippet_trajectories,
        snippet_map_segments,
        snippet_full_trajectory_lengths,
        snippet_cf_action_series,
    ) = parse_snippet_batch(batch, return_cf_series=True)
    ret = {}
    for batch_i in range(min(count, snippet_trajectories.shape[0])):
        xy = (
            snippet_trajectories[
                batch_i,
                :,
                TrajFeatureIndexNoAccel.TRAJ_X : TrajFeatureIndexNoAccel.TRAJ_X + 2,
            ]
            .detach()
            .cpu()
        )

        xy_valid = ~(xy[:, 0] == config_dict["padding_value"])
        pred_valid = (
            batch["cf_future_pred_mask"][batch_i, snippet_i, :, 0].detach().cpu()
        )
        xy_pred = (
            cf_predicted_future_trajectories[
                batch_i,
                snippet_i,
                :,
                TrajFeatureIndexNoAccel.TRAJ_X : TrajFeatureIndexNoAccel.TRAJ_X + 2,
            ]
            .detach()
            .cpu()
        )
        curr_lap_uid = batch["snippet_lap_uid"][batch_i]

        map_xy = batch["snippet_normalized_map_subsampled"][batch_i, :].cpu()
        cones_xy = batch["snippet_cones"][batch_i, :].cpu()

        fig = plt.figure(figsize=(6, 8))
        gs = GridSpec(2, 1, figure=fig, height_ratios=[5, 1], hspace=0.1)

        ax = fig.add_subplot(gs[0, 0])  # big plot
        table_ax = fig.add_subplot(gs[1, 0])  # table underneath
        handle = {}
        handle["plot"] = (fig, ax)
        handle["table"] = (fig, table_ax)

        vis_traj(
            batch,
            batch_i,
            xy,
            xy_pred,
            map_xy,
            cones_xy,
            xy_valid,
            pred_valid,
            curr_lap_uid,
            dataloader_name,
            snippet_i,
            pred_label="Predicted",
            fix_ax=(fig, ax),
            additional_title_tag=subtitle,
        )
        if plot_callback is not None:
            plot_callback(
                handle,
                batch,
                batch_i,
                xy,
                xy_pred,
                xy_valid,
                curr_lap_uid,
                dataloader_name,
                snippet_i,
                valid_annotation_categories_index_dict_type,
            )

        ret[f"fig"] = fig
    return ret


def prep_dataset_id_to_dsidx_dict(dataset_cls, data_config, dataset_type="csim"):
    dataset = dataset_cls(data_config, "test")
    dataset.metric_keys = {}
    dataset._prepare_dataset_stats = dummy_func
    dataset.get_cf_instruction_class_series = lambda snippet_df, subsampled_idxs_dict: (
        None,
        np.ones(len(subsampled_idxs_dict["subsampled_idxs_int"]), dtype=int),
    )
    dataset.init()
    print(len(dataset))
    snippet_id_to_ds_idx_dict = {}
    # import IPython; IPython.embed(banner1='check snippet idx')
    if dataset_type == "csim":
        for ds_idx, cf_data in enumerate(dataset):
            snippet_lap_uid = cf_data["snippet_lap_uid"]
            num = int(snippet_lap_uid[snippet_lap_uid.index("_") + 1 :])
            snippet_id_to_ds_idx_dict[num] = ds_idx
    elif dataset_type == "test":
        for ds_idx, cf_data in enumerate(dataset):
            snippet_num = cf_data["snippet_num"]
            # snippets are 1-indexed
            snippet_id_to_ds_idx_dict[snippet_num - 1] = ds_idx
    return dataset, snippet_id_to_ds_idx_dict


def get_dataset_args(pid, trajectory_snippet_dir):
    args = {}
    pids = [pid]
    dataset_cls = AIC24D05CFDataset
    args["trajectory_snippets_dir"] = trajectory_snippet_dir
    args["test_trajectory_snippets_uids_to_be_considered"] = pids
    args["teacher_action_future_seq_len_in_sec"] = TEACHER_ACTION_FUTURE_SEQ_LEN_IN_SEC
    if CATEGORY_INDEX_TYPE == "no_steering_no_turn":
        args["teacher_action_num_categories"] = 8  # should not be here, because configD
    elif CATEGORY_INDEX_TYPE == "no_steering_no_turn_no_brake":
        args["teacher_action_num_categories"] = 6  # should not be here, because configD
    data_config = {
        "map_file": map_file,
        "test_pids_to_be_considered": pids,
        "use_end_of_past_trajectory": True,
        "use_multiprocessing": False,
        "padding_value": -2000,
        "padding_side": "right",
        "return_cf_for_lap": False,  # should the cf feedback be returned as part of the lap
        "disable_tqdm": True,
        **args,
    }
    return dataset_cls, data_config


def run_csim_data_inference():
    # create dataset using the csim data collected.
    dataset_cls, data_config = get_dataset_args(
        "P604", Path(f"{csim_data_parent_folder}/fake_snippet_dir").expanduser()
    )
    dataset, snippet_id_to_ds_idx_dict = prep_dataset_id_to_dsidx_dict(
        dataset_cls=dataset_cls, data_config=data_config, dataset_type="csim"
    )
    model = get_model_ready()
    run_sequential_inference(
        dataset=dataset,
        snippet_id_to_ds_idx_dict=snippet_id_to_ds_idx_dict,
        model=model,
        vis_output_folder=vis_output_csim_folder,
    )


def run_test_data_inference():
    test_data_trial_num = "P610-trial_19"  # 16 is demo lap
    all_snippets_test_data = []
    for d in trajectory_snippets_dir.iterdir():
        if test_data_trial_num not in d.stem:
            continue
        else:
            all_snippets_test_data.append(d)

    clear_directory(temp_trajectory_snippets_dir)
    # copy selected snippets into a temp folder
    for f in all_snippets_test_data:
        target_f = temp_trajectory_snippets_dir / f.name
        shutil.copytree(f, target_f)

    dataset_cls, data_config = get_dataset_args("P610", temp_trajectory_snippets_dir)
    dataset, snippet_id_to_ds_idx_dict = prep_dataset_id_to_dsidx_dict(
        dataset_cls=dataset_cls, data_config=data_config, dataset_type="test"
    )

    # import IPython; IPython.embed(banner1='dataset')
    model = get_model_ready()
    run_sequential_inference(
        dataset=dataset,
        snippet_id_to_ds_idx_dict=snippet_id_to_ds_idx_dict,
        model=model,
        vis_output_folder=vis_output_test_folder,
    )


def run_test_through_training_pipeline_folder():
    config_cls = TrainConfigNonadaptive()
    trainer = non_adaptive_main(
        [
            "--inference",
            "True",
            "--checkpoint",
            str(checkpoint_path),
            "--category-index-type",
            f"{CATEGORY_INDEX_TYPE}",
            "--config-str",
            model_config,
        ]
    )
    import IPython

    IPython.embed(banner1="check")


def inference_main():
    # print('Running test inference')
    # run_test_data_inference()
    print("Running csim inference")
    run_csim_data_inference()
    # print('Running test through training pipeline inference')
    # run_test_through_training_pipeline_folder()


if __name__ == "__main__":
    inference_main()
