# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import warnings

import grpc

try:
    import grpc_thunderhill_msg_pb2 as grpc__thunderhill__msg__pb2
except ImportError:
    from aic_bsd_model.training.online_inference import (
        grpc_thunderhill_msg_pb2 as grpc__thunderhill__msg__pb2,
    )

GRPC_GENERATED_VERSION = "1.63.0"
GRPC_VERSION = grpc.__version__
EXPECTED_ERROR_RELEASE = "1.65.0"
SCHEDULED_RELEASE_DATE = "June 25, 2024"
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower

    _version_not_supported = first_version_is_lower(
        GRPC_VERSION, GRPC_GENERATED_VERSION
    )
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    warnings.warn(
        f"The grpc package installed is at version {GRPC_VERSION},"
        + f" but the generated code in grpc_thunderhill_msg_pb2_grpc.py depends on"
        + f" grpcio>={GRPC_GENERATED_VERSION}."
        + f" Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}"
        + f" or downgrade your generated code using grpcio-tools<={GRPC_VERSION}."
        + f" This warning will become an error in {EXPECTED_ERROR_RELEASE},"
        + f" scheduled for release on {SCHEDULED_RELEASE_DATE}.",
        RuntimeWarning,
    )


class GetDataServeiceStub(object):
    """The greeting service definition."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetData = channel.unary_unary(
            "/GetDataServeice/GetData",
            request_serializer=grpc__thunderhill__msg__pb2.DataRequest.SerializeToString,
            response_deserializer=grpc__thunderhill__msg__pb2.DataReply.FromString,
            _registered_method=True,
        )


class GetDataServeiceServicer(object):
    """The greeting service definition."""

    def GetData(self, request, context):
        """Sends a greeting"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")


def add_GetDataServeiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
        "GetData": grpc.unary_unary_rpc_method_handler(
            servicer.GetData,
            request_deserializer=grpc__thunderhill__msg__pb2.DataRequest.FromString,
            response_serializer=grpc__thunderhill__msg__pb2.DataReply.SerializeToString,
        ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
        "GetDataServeice", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class GetDataServeice(object):
    """The greeting service definition."""

    @staticmethod
    def GetData(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/GetDataServeice/GetData",
            grpc__thunderhill__msg__pb2.DataRequest.SerializeToString,
            grpc__thunderhill__msg__pb2.DataReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True,
        )
