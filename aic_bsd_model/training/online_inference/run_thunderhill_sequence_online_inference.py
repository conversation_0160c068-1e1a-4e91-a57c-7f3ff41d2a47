import random
import time
from typing import Any

import numpy as np
import pandas
from data_sources.teaching.convert_thunderhill_data import (
    ThunderhillVehicleData,
    convert_data_online,
)

# from radutils.buffer import Buffer
from intent.multiagents.data_specific.thunderhill.run_thunderhill_online_inference import (
    ProtobufPredictionDatasetOnline,
    ThunderhillOnlineTrainer,
)
from intent.multiagents.data_specific.thunderhill.thunderhill_callbacks import (
    ThunderhillTrainerCallback,
)
from intent.multiagents.data_specific.thunderhill_sequence.train_thunderhill_sequence import (
    ThunderhillSequenceTrainingConfig,
)
from intent.multiagents.new_train_pedestrian_trajectory_prediction import (
    TrainingLauncher,
    launch,
)
from intent.multiagents.train_thunderhill_teaching import ThunderhillTrainingConfig
from intent.multiagents.training_utils import TrainingContext
from triceps.protobuf.prediction_dataset import dataset_collate_fn
from tristan.predictors.causal.data.prediction_dataset import (
    DatasetsFilter,
    NewPredictionDataset,
    full_preprocess,
)

DECISION_THRESHOLD = 0.5


class ThunderhillOnlineDataset:
    def __init__(self):
        self.data = None

    def __getitem__(self, item):
        return self.data


class ProtobufPredictionDatasetOnlineSequence(ProtobufPredictionDatasetOnline):
    def __init__(self, params):
        super().__init__(params=params)

    # def __getitem__(self, item):
    # pass


def copy_batch(batch):
    new_batch = batch.copy()
    for k, v in new_batch.items():
        if isinstance(v, dict):
            new_batch[k] = batch[k].copy()
    return new_batch


class ThunderhillSequenceOnlineTrainer(ThunderhillOnlineTrainer):
    def __init__(self, **kwargs):
        self.params = kwargs["trainer_param"]
        self.dataset_orig = kwargs["datasets"]
        # Don't log anything
        self.params["disable_cache"] = True
        self.params["logger_type"] = "none"
        self.params["inference"] = True
        self.params["disable_optimization"] = True
        self.params["disable_visualizations"] = True
        self.params["evaluation_dataset"] = "inference"
        self.params["main_param_hash"] = "thunderhill_online"
        self.sequence_length = self.params["scenario_sequence_length"]
        self.online_dataset = ThunderhillOnlineDataset()

        super().__init__(**kwargs)

        self.input_dir = self.params["input_dir"][0]
        self.output_dir = self.input_dir + "/compactsim_output"

        # self.dataset=NewPredictionDataset(ProtobufPredictionDatasetOnline(self.params), self.params)
        self.dataset = ProtobufPredictionDatasetOnlineSequence(self.params)
        self.dataset.item_post_processors = (
            self.dataset_orig["train"].datasets[0].item_post_processors
        )
        self.dataset.data_transforms = (
            self.dataset_orig["train"].datasets[0].data_transforms
        )  # map handler and thunderhill state handler

        self.past_batch = []
        self.past_batch_time = []
        self.update_interval = 1
        self.buffer_time = 70 + self.update_interval
        self.buffer_length = 100
        self.on_epoch_start(self.training_ctx)

    def append_past_batch(self, batch):
        time_now = time.monotonic()
        last_time = self.past_batch_time[-1] if len(self.past_batch_time) else 0
        if time_now - last_time > self.update_interval:
            self.past_batch.append(batch)
            self.past_batch_time.append(time_now)
            # while time_now - self.past_batch_time[0] > self.buffer_time:
            while len(self.past_batch) > self.buffer_length:
                self.past_batch.pop(0)
                self.past_batch_time.pop(0)

    def sample_batch_sequence(self):
        if len(self.past_batch) - 1 < self.sequence_length:
            # raise ImportError("not enough past batch to form sequence")
            return None

        ret = []

        samples = random.sample(
            range(len(self.past_batch) - 1), self.sequence_length - 1
        )
        samples.sort()
        for i in samples:
            ret.append(copy_batch(self.past_batch[i]))
        return ret

    def load_pb(self, pb):
        # self.dataset.read_data=lambda : pb
        self.dataset.proto_prediction_instance = pb
        batch_item = self.dataset.__getitem__(0)
        batch_item = full_preprocess(batch_item, self.params)
        self.append_past_batch(copy_batch(batch_item))

        sequence = self.sample_batch_sequence()
        if sequence is None:
            return None
        sequence.append(batch_item)

        ret_seq_batch = dataset_collate_fn(sequence)

        return ret_seq_batch

    def forward(self, request_data):
        pb = self.make_pb(request_data)
        batch_item = self.load_pb(pb)
        if batch_item is None:
            return None, None

        batch_item = dataset_collate_fn([batch_item])
        loss, result = self.train_batch(self.training_ctx, batch_item, {})

        pred_traj = result["result"]["predicted_trajectories"]
        pred_subtitle = batch_item["subtitle_pred_sig"]
        subtitle_str = self.convert_subtitle_prediction_to_str(pred_subtitle)
        return pred_traj, subtitle_str


class ThunderhillOnlineSequenceInferenceConfig(ThunderhillSequenceTrainingConfig):
    trainer_cls: Any = ThunderhillSequenceOnlineTrainer
    dataset_cls = NewPredictionDataset
    datasets_filter_cls = DatasetsFilter

    @classmethod
    def from_params(cls, params: dict):
        config = super().from_params(params)
        config.additional_trainer_callbacks = config.additional_trainer_callbacks + (
            ThunderhillTrainerCallback(params),
        )
        config.additional_model_callbacks = ()
        return config


if __name__ == "__main__":
    launch(TrainingLauncher(config_cls=ThunderhillOnlineSequenceInferenceConfig))
