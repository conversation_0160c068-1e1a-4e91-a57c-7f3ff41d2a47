# Env setup
* `pip install -r aic_bsd_model/training/online_inference/requirements.txt`
* run `protobuf_gen.sh` at repo root

# Architecture
* ROS listener(gRPC client) subscribe to ros vehicle state topic and make gRPC call to model(gRPC server), which can 1. send data, 2. ask for lap prediction then cache result on the model side, 3. ask for CF prediction with cached lap prediction.

# Data
Lap data:
1. Read recorded lap data, 2. save the lap data as Dataframe. 3. Use hid_common to process dataframe into segment trials (it's only map segment) 4. turn trials into 10 segment (datasets/data_scripts/) 4. Use 24D16Dataset to load trials

```
cd ~/motion_sim_hmi_scenarios
source ~/motion_sim_hmi_scenarios/install/setup.bash 

./scripts/run_carla.sh XC_dataaug3
./scripts/run_hmi.sh west_ccw_table
```

# Client(ROS listener)
```
source ~/motion_sim_hmi_scenarios/install/setup.bash 

`python aic_bsd_model/training/online_inference/thunderhill_ros_client.py`
```

# Server(Model) 
`python aic_bsd_model/training/online_inference/thunderhill_data_server.py`

