# Copyright 2021 The gRPC Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""The graceful shutdown example for the asyncio Greeter server."""
import argparse
import asyncio
import collections
import copy
import logging
import pickle
import shutil
import tempfile
import time
import traceback
from distutils.dir_util import remove_tree
from pathlib import Path
from typing import List

import grpc
import hid_common.util
import hid_common.util.filters
import numpy as np
import pandas as pd
import yaml
from git import rmtree

from aic_bsd_model.training.generate_training_cmds import category_index_type
from aic_bsd_model.utils.training_utils import str2bool

try:
    import grpc_thunderhill_msg_pb2
    import grpc_thunderhill_msg_pb2_grpc
except ModuleNotFoundError:
    from aic_bsd_model.training.online_inference import grpc_thunderhill_msg_pb2
    from aic_bsd_model.training.online_inference import grpc_thunderhill_msg_pb2_grpc

import hid_common
import torch
from hid_common.util.filters.trajectory_filters import mark_map_segment_ids

from aic_bsd_model.datasets.aic_24d05_cf_dataset import AIC24D05CFDataset
from aic_bsd_model.datasets.aic_24d05_combined_dataset import (
    AIC24D05CombinedPrevKDataset,
    CFSnippetCollator,
)
from aic_bsd_model.datasets.aic_24d05_lap_dataset import AIC24D05LapDataset
from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN,
    COMBINED_VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN_NO_BRAKE,
    VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN,
    VALID_ANNOTATION_CATEGORIES_INDEX_DICT_NO_STEERING_NO_TURN_NO_BRAKE,
)
from aic_bsd_model.datasets.data_scripts import (
    aic_24d05_concurrent_feedback_trajectory_snipper,
    aic_24d05_laps_to_map_segments_snipper,
)
from aic_bsd_model.training.train_adaptive_cf import main

# Coroutines to be invoked when the event loop is shutting down.
_cleanup_coroutines = []
map_dir = Path("~/Data/24-D-05/trajectory_segments_map_seg_ids/track.csv").expanduser()
lap_csv_dir = Path("~/ai_coaching_logs/").expanduser()
workdir = Path(__file__).parent.parent.parent.parent / ".inference"
workdir.mkdir(parents=True, exist_ok=True)
lap_df_dir = workdir
lap_csv_prev_k_dir = workdir / "prev_k_lap"

lap_trial_dir = workdir / "lap_trial_dir"
cf_trial_dir = workdir / "cf_trial_dir"
lap_segments_dir = workdir / "lap_segments"
cf_segments_dir = workdir / "cf_segments"
temp_snippet_dir = workdir / "temp_snippet"
[
    t.mkdir(parents=True, exist_ok=True)
    for t in [
        lap_csv_dir,
        lap_csv_prev_k_dir,
        lap_trial_dir,
        cf_trial_dir,
        lap_segments_dir,
        cf_segments_dir,
        temp_snippet_dir,
    ]
]

CARLA_TIME_KEY = "carla_objects log time"

lap_trial_base_name = "P604-trial"
cf_trial_base_name = "P604-trial"

TEACHER_ACTION_FUTURE_SEQ_LEN_IN_SEC = 5
CATEGORY_INDEX_TYPE = "no_steering_no_turn"
ADDITIONAL_PADDING = 28  # a little over 0.2 s at 120Hz

LONGI_LATERAL_CATEGORIES_DICT = {
    "no_steering_no_turn": {
        "lateral_categories": {1: "move_to_the_right", 2: "move_to_the_left"},
        "longi_categories": {
            3: "add_gas",
            4: "off_the_gas",
            5: "stay_on_the_gas",
            6: "brake",
            7: "off_the_brakes",
        },
    },
    "no_steering_no_turn_no_brake": {
        "lateral_categories": {1: "move_to_the_right", 2: "move_to_the_left"},
        "longi_categories": {
            3: "add_gas",
            4: "off_the_gas",
            5: "stay_on_the_gas",
        },
    },
}

NONADAPTIVE_MODEL_ID_DICT = {
    "modelA": {  # gauss=True, cf_use_metrics_prediction=False, no_steering_no_turn
        "checkpoint_path": "~/aic_models/08-26T19_16_03-7c213-csim_modeltest_nonadaptive/latest/checkpoint-83000",
        "model_config": "use_gaussian_dropout=True,cf_use_skill_prediction=False",
        "category_index_type": "no_steering_no_turn",
        "teacher_action_future_seq_len_in_sec": 5,
    },
    "modelB": {  # gauss = True, cf_use_metrics_prediction=True, no_steer_no_turn
        "checkpoint_path": "~/aic_models/09-03T03_31_38-80dcf-s42_nonadaptive_with_gaussian_with_skillpred/latest/checkpoint-19920",
        "model_config": "use_gaussian_dropout=True,cf_use_skill_prediction=True",
        "category_index_type": "no_steering_no_turn",
        "teacher_action_future_seq_len_in_sec": 5,
    },
    "modelC": {
        "checkpoint_path": "~/aic_models/09-04T22_01_19-d661d-csim_nonadaptive_no_steer_no_turn_no_brake_withgauss_no_cfskill/latest/checkpoint-31700",
        "model_config": "use_gaussian_dropout=True,cf_use_metrics_prediction=False,valid_annotation_categories_index_dict_type=no_steering_no_turn_no_brake,teacher_action_num_categories=6",
        "category_index_type": "no_steering_no_turn_no_brake",
        "teacher_action_future_seq_len_in_sec": 5,
    },
    "modelD": {
        "checkpoint_path": "~/aic_models/09-04T22_41_02-9cf2b-test_nonadaptive_shorter_future1s/latest/checkpoint-42750",
        "model_config": "use_gaussian_dropout=True,cf_use_metrics_prediction=False,teacher_action_future_seq_len_in_sec=1",
        "category_index_type": "no_steering_no_turn",
        "teacher_action_future_seq_len_in_sec": 1,
    },
}

ADAPTIVE_MODEL_ID_DICT = {}
MODEL_ID_DICT = {
    "nonadaptive": NONADAPTIVE_MODEL_ID_DICT,
    "adaptive": ADAPTIVE_MODEL_ID_DICT,
}
DEFAULT_PER_LABEL_DECISION_THRESHOLD = {
    1: 0.5,  # move right
    2: 0.5,  # move left
    3: 0.5,  # add gas
    4: 0.5,  # off the gas
    5: 0.5,  # stay on the gas
    6: 0.5,  # brake
    7: 0.5,  # off the brakes
}
PER_LABEL_DECISION_THRESHOLD = {
    1: 0.4,  # move right
    2: 0.6,  # move left
    3: 0.5,  # add gas
    4: 0.69,  # off the gas
    5: 0.4,  # stay on the gas
    6: 0.5,  # brake
    7: 0.5,  # off the brakes
}
DECISION_THRESHOLD = 0.5


with open(map_dir, "r") as f:
    map_df = pd.read_csv(f)


aic_24d05_laps_to_map_segments_snipper.get_trial_type = (
    lambda *args, **kwargs: "coaching_lap"
)
aic_24d05_concurrent_feedback_trajectory_snipper.get_trial_type = (
    lambda *args, **kwargs: "coaching_lap"
)


import os
import shutil


def clear_directory(path):
    """
    Remove all contents of the directory at `path`,
    but leave the directory itself in place.
    """
    root = Path(path)
    for child in root.iterdir():
        try:
            if child.is_file() or child.is_symlink():
                child.unlink()
            elif child.is_dir():
                shutil.rmtree(child)
        except Exception as e:
            print(f"Failed to delete {child}: {e}")


def remove_dir(path):
    """
    Remove a directory and all its contents, silently ignoring missing-path errors.
    """
    if not os.path.isdir(path):
        return
    try:
        shutil.rmtree(path)
    except Exception:
        pass


clear_directory(lap_csv_prev_k_dir)
Path(lap_csv_prev_k_dir).mkdir(parents=True, exist_ok=True)


def list_csv_sorted_by_mtime(root: Path, *, newest_first: bool = True) -> List[Path]:
    """
    Recursively find all .csv files under `root` and return them sorted by modification time.

    :param root: the base directory to search (pathlib.Path)
    :param newest_first: if True, sort descending (most recent first)
    :return: list of Paths to .csv files
    """
    # Find all .csv files (case-sensitive); use .rglob for recursive search
    csv_files = list(root.rglob("*.csv"))
    # Sort by modification timestamp
    sorted_files = sorted(
        csv_files, key=lambda p: p.stat().st_mtime, reverse=newest_first
    )
    return sorted_files


def rename_vehicle_df(df):
    df.rename(
        columns={
            "ego_position_x": "ego_x",
            "ego_position_y": "ego_y",
            "ego_position_z": "ego_z",
            "ego_velocity_x": "ego_vx",
            "ego_velocity_y": "ego_vy",
            "ego_velocity_z": "ego_vz",
            "ego_orientation_x": "ego_orientation_x",
            "ego_orientation_y": "ego_orientation_y",
            "ego_orientation_z": "ego_orientation_z",
            "ego_orientation_w": "ego_orientation_w",
            "steer": "steering",
            "throttle_pedal": "throttle",
            "brake_pedal": "brake",
        },
        inplace=True,
    )
    df[CARLA_TIME_KEY] = df["time"]


def process_df_data(df):
    # Rename to fit mark_map_segment_ids
    rename_vehicle_df(df)

    # Write trial to workdir, dataset will load trial data from workdir
    map_id_df = hid_common.util.filters.trajectory_filters.mark_map_segment_ids(
        df, map_df
    )["annotation_df"]
    df = df.merge(map_id_df, on=CARLA_TIME_KEY, how="left")
    df["map_segment_ids"] = df["map_segment_ids"].cummax()
    df["timestamp"] = df[CARLA_TIME_KEY]
    return df


def dump_df(df, is_lap_data, trial_id=0):
    if is_lap_data:
        trial_base_name = lap_trial_base_name
        trial_dir = lap_trial_dir
    else:
        trial_base_name = cf_trial_base_name
        trial_dir = cf_trial_dir

    df_suffix = "_with_all_annotations_and_metrics_with_instruction_category"
    trial_name = f"{trial_base_name}_{trial_id}"
    trial_dir2 = trial_dir / trial_name
    Path.mkdir(trial_dir2, parents=True, exist_ok=True)
    filename = f"{trial_name}{df_suffix}.trial"
    df.to_parquet(trial_dir2 / (filename + ".parquet"))
    with open(trial_dir2 / (filename + ".yaml"), "w") as fp:
        yaml.dump(
            {"additional_metrics_dict": {}, "source_lap_type": "coaching_lap"}, fp
        )


def dummy_func():
    return


def lap_csv_to_lap_data(trial_id=0, csv_path=None):
    if csv_path is None:
        csvs = list_csv_sorted_by_mtime(lap_csv_dir, newest_first=True)
        csv_path = csvs[0]
    df = pd.read_csv(csv_path)

    df = process_df_data(df)

    dump_df(df, True, trial_id)


def debug_lap_csv_to_cf_data(data: List, trial_id=0):
    """This is used for offline debugging"""
    # Stub CF data
    csvs = list_csv_sorted_by_mtime(lap_csv_dir, newest_first=True)
    lap_df_path = csvs[1]
    df = pd.read_csv(lap_df_path)

    df = process_df_data(df)
    # Add stub future data
    # smaller index should be the history

    df["out_of_bounds"] = 0
    df["out_of_bounds_distances"] = 0
    df["lateral_distances"] = 0

    dump_df(df, False, trial_id)


def request_to_cf_data_parquet(
    request, trial_id=0, full_timesteps=50, future_timesteps=25, file_counter=1
):
    """This is used for online inference"""
    # Those fields are the same as RecordLap csv
    df = pd.DataFrame()
    time = np.asarray(request.time_s) + np.asarray(request.time_ns) * 1e-9
    df["time"] = time
    df["ego_position_x"] = list(request.x)
    df["ego_position_y"] = list(request.y)
    df["ego_position_z"] = list(request.z)
    df["ego_orientation_x"] = list(request.ego_quaternion_x)
    df["ego_orientation_y"] = list(request.ego_quaternion_y)
    df["ego_orientation_z"] = list(request.ego_quaternion_z)
    df["ego_orientation_w"] = list(request.ego_quaternion_w)
    df["ego_velocity_x"] = list(request.vel_x)
    df["ego_velocity_y"] = list(request.vel_y)
    df["ego_velocity_z"] = list(request.vel_z)
    vel = np.asarray([request.vel_x, request.vel_y, request.vel_z])
    df["speed"] = np.linalg.norm(vel, axis=0)
    df["acceleration"] = 0
    df["brake_pedal"] = list(request.brake)
    df["throttle_pedal"] = list(request.throttle)
    df["steer"] = list(request.steering)
    df["verbal_feedback"] = 0

    # print(f' Save Pre flip df')
    # Path.mkdir(temp_snippet_dir / "pre_flip" / f"P604-trial_{file_counter}", parents=True, exist_ok=True)
    # full_pre_path = temp_snippet_dir / "pre_flip" / f"P604-trial_{file_counter}" / f"P604-trial_{file_counter}_with_all_annotations_and_metrics_with_instruction_category.parquet"
    # df.to_parquet(full_pre_path)
    # with open(temp_snippet_dir / "pre_flip" / f"P604-trial_{file_counter}" / f"P604-trial_{file_counter}_with_all_annotations_and_metrics_with_instruction_category.yaml", "w") as fp:
    #     yaml.dump(
    #         {"additional_metrics_dict": {}, "source_lap_type": "coaching_lap"}, fp
    #     )
    # #

    past_timesteps = full_timesteps - future_timesteps
    df2 = df[-full_timesteps:].copy()

    # copy the recent 5s into the first part of the df. Don't copy the "time" column which is at position 1.

    # Copy the recent past 600 + ADDITIONAL_PADDING samples. a little over .2 s. This is for the normalization to happen properly in the dataset class
    df2.iloc[: past_timesteps + ADDITIONAL_PADDING, 1:] = df2.iloc[
        -past_timesteps - ADDITIONAL_PADDING :, 1:
    ]
    # To test the correctness of the data shift
    # np.all(df2.iloc[0:past_timesteps, 1:].values ==  df.iloc[-future_timesteps:, 1:].values)

    df = df2

    # print(f' Save post flip df')
    # Path.mkdir(temp_snippet_dir / "post_flip" / f"P604-trial_{file_counter}", parents=True, exist_ok=True)
    # full_pre_path = temp_snippet_dir / "post_flip" / f"P604-trial_{file_counter}" / f"P604-trial_{file_counter}_with_all_annotations_and_metrics_with_instruction_category.parquet"
    # df.to_parquet(full_pre_path)
    # with open(
    #         temp_snippet_dir / "post_flip" / f"P604-trial_{file_counter}" / f"P604-trial_{file_counter}_with_all_annotations_and_metrics_with_instruction_category.yaml",
    #         "w") as fp:
    #     yaml.dump(
    #         {"additional_metrics_dict": {}, "source_lap_type": "coaching_lap"}, fp
    #     )
    # Remove existing cf data
    clear_directory(cf_trial_dir)
    clear_directory(cf_segments_dir)

    df = process_df_data(df)
    # Add stub future data
    # smaller index should be the history

    df["out_of_bounds"] = 0
    df["out_of_bounds_distances"] = 0
    df["lateral_distances"] = 0

    dump_df(df, False, trial_id)


def make_dataset(
    is_lap=True,
    category_index_type=CATEGORY_INDEX_TYPE,
    teacher_action_future_seq_len_in_sec=TEACHER_ACTION_FUTURE_SEQ_LEN_IN_SEC,
):
    args = {}
    if is_lap:
        pids = ["P604"]
        dataset_cls = AIC24D05LapDataset
        args["trajectory_segments_dir"] = lap_segments_dir

    else:
        # TODO make the teacher_action_num_categories configurable
        pids = ["P604"]
        dataset_cls = AIC24D05CFDataset
        args["trajectory_snippets_dir"] = cf_segments_dir
        args["test_trajectory_snippets_uids_to_be_considered"] = pids
        args[
            "teacher_action_future_seq_len_in_sec"
        ] = teacher_action_future_seq_len_in_sec
        if category_index_type == "no_steering_no_turn":
            args[
                "teacher_action_num_categories"
            ] = 8  # should not be here, because config dict should deal with this.
        elif category_index_type == "no_steering_no_turn_no_brake":
            args["teacher_action_num_categories"] = 6

    data_config = {
        "map_file": map_dir,
        "test_pids_to_be_considered": pids,
        "use_end_of_past_trajectory": True,
        "use_multiprocessing": False,
        "padding_value": -2000,
        "padding_side": "right",
        "return_cf_for_lap": False,  # should the cf feedback be returned as part of the lap
        "disable_tqdm": True,
        **args,
    }
    dataset = dataset_cls(data_config, "test")
    dataset.metric_keys = {}
    if not is_lap:
        dataset._prepare_dataset_stats = dummy_func
        dataset.get_cf_instruction_class_series = (
            lambda snippet_df, subsampled_idxs_dict: (
                None,
                np.ones(len(subsampled_idxs_dict["subsampled_idxs_int"]), dtype=int),
            )
        )
    dataset.init()

    return dataset, data_config


def make_batch_data_lap(
    dataset_idx=(0, 0, 0),
    category_index_type=CATEGORY_INDEX_TYPE,
    teacher_action_future_seq_len_in_sec=TEACHER_ACTION_FUTURE_SEQ_LEN_IN_SEC,
):
    lap_dataset, lap_data_config = make_dataset(
        is_lap=True,
        category_index_type=category_index_type,
        teacher_action_future_seq_len_in_sec=teacher_action_future_seq_len_in_sec,
    )
    laps = []
    for i in range(len(dataset_idx)):
        laps.append(lap_dataset.__getitem__(dataset_idx[i]))
    lap_collator = lap_dataset.get_collator(lap_data_config)
    lap_data_config["with_cf_snippets"] = False
    lap_data = lap_collator(laps)
    curr_traj_segments = lap_data["curr_trajectory_segments"]
    stub_metrics = torch.ones(curr_traj_segments.shape[:1])
    # TODO replace with real metrics value when using metrics input in Lap data
    lap_data["curr_trial_time"] = stub_metrics
    lap_data["curr_trial_time2"] = stub_metrics

    lap_collator = AIC24D05CombinedPrevKDataset.get_collator(lap_data_config)
    k_lap_data = lap_collator([lap_data])
    k_lap_data["metrics"] = {
        "trial_time": stub_metrics[None, :],
        "trial_time2": stub_metrics[None, :],
    }
    return k_lap_data


def make_batch_data_cf_from_snippet_pkl(
    lap_model_outputs, category_index_type, teacher_action_future_seq_len_in_sec
):
    stub_metrics = torch.ones(1)

    cf_dataset, cf_data_config = make_dataset(
        is_lap=False,
        category_index_type=category_index_type,
        teacher_action_future_seq_len_in_sec=teacher_action_future_seq_len_in_sec,
    )
    cf_data = cf_dataset.__getitem__(0)
    cf_data.pop("snippet_cf_instruction_class_series", None)
    cf_data["snippet_cf_instruction_class_series_subsampled"] = stub_metrics
    cf_collator = CFSnippetCollator()
    cf_data = cf_collator([cf_data])
    cf_data["side_channel_lap_representation"] = lap_model_outputs["outputs"][
        "side_channel_lap_representation"
    ]
    cf_data["curr_lap_masks"] = stub_metrics[None, :]
    cf_data["metrics"] = {
        "trial_time": stub_metrics[None, :],
        "trial_time2": stub_metrics[None, :],
        "trial_time-pred": stub_metrics[None, :],
        "trial_time2-pred": stub_metrics[None, :],
    }
    cf_data["cf_metrics"] = {
        "racing_line_score_abs_mean": stub_metrics[None, :],
        "smoothness_score_mean": stub_metrics[None, :],
        "racing_line_score_abs_mean-pred": stub_metrics[None, :],
        "smoothness_score_mean-pred": stub_metrics[None, :],
    }
    return cf_data


def subtitle_pred_to_str(subtitle_prediction, category_index_type):
    ret = []
    longi_categories = LONGI_LATERAL_CATEGORIES_DICT[category_index_type][
        "longi_categories"
    ]
    lateral_categories = LONGI_LATERAL_CATEGORIES_DICT[category_index_type][
        "lateral_categories"
    ]

    valid_longi_predictions = []
    valid_lateral_predictions = []
    for i in range(1, len(subtitle_prediction)):
        # if subtitle_prediction[i] > DECISION_THRESHOLD:
        # if subtitle_prediction[i] > PER_LABEL_DECISION_THRESHOLD[i]:
        if subtitle_prediction[i] > DEFAULT_PER_LABEL_DECISION_THRESHOLD[i]:
            if i in list(longi_categories.keys()):
                valid_longi_predictions.append(
                    (longi_categories[i], subtitle_prediction[i])
                )  # name to be displayed along with the prediction value
            if i in list(lateral_categories.keys()):
                valid_lateral_predictions.append(
                    (lateral_categories[i], subtitle_prediction[i])
                )
    return_prediction_dict = {}
    if len(valid_longi_predictions) != 0:
        max_longi_pred_index = np.argmax([i[1] for i in valid_longi_predictions])
        longi_text_to_show = valid_longi_predictions[max_longi_pred_index][0]
        print(
            f"max_longi prediction probability for {longi_text_to_show}: {valid_longi_predictions[max_longi_pred_index][1]}"
        )
        return_prediction_dict[longi_text_to_show] = valid_longi_predictions[
            max_longi_pred_index
        ][1]
    else:
        longi_text_to_show = ""

    if len(valid_lateral_predictions) != 0:
        max_lateral_pred_index = np.argmax([i[1] for i in valid_lateral_predictions])
        lateral_text_to_show = valid_lateral_predictions[max_lateral_pred_index][0]
        print(
            f"max_lat prediction probability for {lateral_text_to_show}: {valid_lateral_predictions[max_lateral_pred_index][1]}"
        )
        return_prediction_dict[lateral_text_to_show] = valid_lateral_predictions[
            max_lateral_pred_index
        ][1]
    else:
        lateral_text_to_show = ""

    if longi_text_to_show == "" or lateral_text_to_show == "":
        connecting_text = ""
    else:
        connecting_text = "_and_"
    subtitle_str = lateral_text_to_show + connecting_text + longi_text_to_show

    ret.append(subtitle_str)
    return ret, return_prediction_dict


class Greeter(grpc_thunderhill_msg_pb2_grpc.GetDataServeiceServicer):
    def __init__(
        self,
        model,
        is_adaptive,
        category_index_type,
        teacher_action_future_seq_len_in_sec,
    ):
        self.model = model
        prediction_timestep_in_sec = self.model.config["prediction_timestep_in_sec"]
        snippet_length_in_sec = self.model.config["snippet_length_in_sec"]
        cf_target_sampling_frequency = self.model.config["cf_target_sampling_frequency"]
        input_frequency = 120
        self.category_index_type = category_index_type
        self.teacher_action_future_seq_len_in_sec = teacher_action_future_seq_len_in_sec

        self.full_timesteps = int(snippet_length_in_sec * input_frequency)
        self.past_timesteps = int(prediction_timestep_in_sec * input_frequency)
        self.future_timesteps = self.full_timesteps - self.past_timesteps
        print(
            f"full_timesteps: {self.full_timesteps}, past_timesteps: {self.past_timesteps}"
        )
        if self.model:
            self.model.eval()

        pids = ["P604"]
        # pids = []
        self.data_config = {
            # "trajectory_segments_dir": lap_segments_dir,
            "map_file": map_dir,
            "test_pids_to_be_considered": pids,
        }
        self.lap_model_outputs = None
        self.last_lap_update = time.monotonic()
        self.last_lap_cooldown = 2
        self.lap_csvs = []
        self.lap_k = 3
        self.is_adaptive = is_adaptive
        # self.lap_dataset = AIC24D16LapDataset(self.data_config, "test")
        # self.lap_dataset = None
        # Ignore metrics
        # self.cf_dataset = AIC24D05CFDataset(self.data_config, "test")
        self.lap_snipping_args = [
            "--segment-level-trajectory-snippets-dir",
            str(lap_segments_dir),
            "--trials-dir",
            str(lap_trial_dir),
            "--make-up-missing-segments",
            "True",
        ]
        self.cf_snipping_args = copy.deepcopy(self.lap_snipping_args) + [
            "--trials-dir",
            str(cf_trial_dir),
            "--trajectory-snippets-dir",
            str(cf_segments_dir),
            # "--trajectory-snippet-len-s",
            # 8
        ]

        self.list_of_cf_predictions = []
        self.max_len_of_cf_predictions_in_sec = 2
        self.max_len_list_of_cf_predictions = (
            self.max_len_of_cf_predictions_in_sec * cf_target_sampling_frequency
        )
        self.max_prob_for_each_category_dict = collections.defaultdict(list)
        self.file_counter = 0
        self.init_lap_model()

    def init_lap_model(self):
        # Only use the 2 base laps at the start of the study.
        print(f"in init lap model")
        init_laps_to_use = 2
        clear_directory(lap_trial_dir)
        clear_directory(lap_segments_dir)
        csvs = list_csv_sorted_by_mtime(lap_csv_dir, newest_first=True)[
            :init_laps_to_use
        ][::-1]
        for csv in csvs:
            csv_path = lap_csv_prev_k_dir / (csv.parent.stem + "-" + csv.name)
            shutil.copy(csv, csv_path)
            self.lap_csvs.append(csv_path)

        if self.lap_csvs:
            self.run_lap_model(self.lap_csvs)

    def run_lap_model(self, lap_csvs):
        self.list_of_cf_predictions = []
        print(self.max_prob_for_each_category_dict)
        self.max_prob_for_each_category_dict = collections.defaultdict(list)
        clear_directory(lap_trial_dir)
        clear_directory(lap_segments_dir)
        lap_data_idx = []
        for i in range(len(lap_csvs)):
            lap_csv_to_lap_data(i, csv_path=lap_csvs[i])
            lap_data_idx.append(i)
        # Write trial to workdir

        aic_24d05_laps_to_map_segments_snipper.main(self.lap_snipping_args)

        k_lap_data = make_batch_data_lap(
            lap_data_idx,
            self.category_index_type,
            self.teacher_action_future_seq_len_in_sec,
        )

        self.model.training_mode = "lap_only"
        self.model.compute_loss = lambda *args, **kwargs: (0, (0, 0))
        lap_model_outputs = self.model(k_lap_data)
        self.lap_model_outputs = lap_model_outputs

    async def GetData(
        self,
        request: grpc_thunderhill_msg_pb2.DataRequest,
        context: grpc.aio.ServicerContext,
    ) -> grpc_thunderhill_msg_pb2.DataReply:
        # await asyncio.sleep(4)
        # logging.info("Sleep completed, responding")
        reply = grpc_thunderhill_msg_pb2.DataReply(valid=False)

        if request.do_lap_prediction and self.model is not None:
            try:
                # dataset = make_dataset(is_lap=True)
                # data = dataset.__getitem__(0)
                now = time.monotonic()
                if now - self.last_lap_update < self.last_lap_cooldown:
                    # Ignore lap update too fast
                    return reply
                self.last_lap_update = now

                print("lap update", now)

                clear_directory(lap_trial_dir)
                clear_directory(lap_segments_dir)
                l = list_csv_sorted_by_mtime(lap_csv_dir, newest_first=True)
                csvs = l[: self.lap_k]
                csv_path = lap_csv_prev_k_dir / (
                    csvs[0].parent.stem + "-" + csvs[0].name
                )
                print("taking lap ", csvs[0])
                shutil.copy(csvs[0], csv_path)
                self.lap_csvs.append(csv_path)

                if len(self.lap_csvs) > self.lap_k:
                    shutil.rmtree(self.lap_csvs[0], True)
                    self.lap_csvs.pop(0)

                print("lap data:", self.lap_csvs)
                self.run_lap_model(self.lap_csvs)

            except Exception as e:
                raise e
                logging.warning(f"Do prediction get data failed :{e}.")
                return reply

        if request.do_cf_prediction and self.model is not None:
            if self.lap_model_outputs is None and self.is_adaptive:
                return reply
            if len(request.x) < 25:
                print("the buffer requested is too short")
                return reply
            try:
                request_to_cf_data_parquet(
                    request,
                    full_timesteps=self.full_timesteps,
                    future_timesteps=self.future_timesteps,
                    file_counter=self.file_counter,
                )
                self.file_counter += 1
                # convert parquet into pkl
                aic_24d05_concurrent_feedback_trajectory_snipper.main(
                    self.cf_snipping_args
                )
                # Save cf data tensor
                cf_data = make_batch_data_cf_from_snippet_pkl(
                    self.lap_model_outputs,
                    self.category_index_type,
                    self.teacher_action_future_seq_len_in_sec,
                )

                # Path.mkdir(temp_snippet_dir / "cf_pkl", parents=True, exist_ok=True)
                # with open(temp_snippet_dir/ "cf_pkl" / f"cf_data_snippet_{self.file_counter}.pkl", 'wb') as f:
                #     pickle.dump(cf_data, f)

                self.model.training_mode = "cf_only"
                cf_model_outputs = self.model(cf_data)
                teacher_action_pred = cf_model_outputs["outputs"][
                    "cf_teacher_action_sig"
                ]

                reply.valid = True
                l = teacher_action_pred.cpu().detach().numpy()[0, 0].tolist()
                self.list_of_cf_predictions.append(l)
                if (
                    len(self.list_of_cf_predictions)
                    > self.max_len_list_of_cf_predictions
                ):
                    self.list_of_cf_predictions.pop(0)

                if "only_internal_update" in request.other_info:
                    if request.other_info["only_internal_update"] == "True":
                        reply.subtitle.extend([""])
                    else:
                        print("ACTUAL PREDICTION")
                        # print(np.vstack(self.list_of_cf_predictions)[:, 1:])
                        ret, prediction_dict = subtitle_pred_to_str(
                            l, self.category_index_type
                        )
                        for k in prediction_dict:
                            self.max_prob_for_each_category_dict[k].append(
                                prediction_dict[k]
                            )

                        # Path.mkdir(temp_snippet_dir / "cf_pkl_prediction", parents=True, exist_ok=True)
                        # with open(temp_snippet_dir / "cf_pkl_prediction" / f"cf_data_prediction_{self.file_counter}.pkl", 'wb') as f:
                        #     pickle.dump((ret, prediction_dict), f)
                        reply.subtitle.extend(ret)
                else:
                    print("ACTUAL PREDICTION")
                    ret, prediction_dict = subtitle_pred_to_str(
                        l, self.category_index_type
                    )
                    for k in prediction_dict:
                        self.max_prob_for_each_category_dict[k].append(
                            prediction_dict[k]
                        )
                    reply.subtitle.extend(ret)

                # print('lIST OF PREDICTIONS', self.list_of_cf_predictions)

            except Exception as e:
                traceback.print_exc()
                logging.warning(f"Do prediction get data failed :{e}.")
                return reply

        return reply


async def serve(
    trainer, args, category_index_type, teacher_action_future_seq_len_in_sec
) -> None:
    model = trainer.model
    greeter = Greeter(
        model,
        args.adaptive_model,
        category_index_type,
        teacher_action_future_seq_len_in_sec,
    )
    server = grpc.aio.server()
    grpc_thunderhill_msg_pb2_grpc.add_GetDataServeiceServicer_to_server(greeter, server)
    listen_addr = "[::]:50057"
    server.add_insecure_port(listen_addr)
    logging.warning("Starting server on %s", listen_addr)
    await server.start()

    async def server_graceful_shutdown():
        logging.warning("Starting graceful shutdown...")
        # Shuts down the server with 5 seconds of grace period. During the
        # grace period, the server won't accept new connections and allow
        # existing RPCs to continue within the grace period.
        await server.stop(3)

    _cleanup_coroutines.append(server_graceful_shutdown())
    await server.wait_for_termination()


def serve_sync(
    trainer, args, category_index_type, teacher_action_future_seq_len_in_sec
):
    loop = asyncio.get_event_loop()
    # try:
    loop.run_until_complete(
        serve(trainer, args, category_index_type, teacher_action_future_seq_len_in_sec)
    )
    # finally:
    #     loop.run_until_complete(*_cleanup_coroutines)
    #     loop.close()


async def testing():
    greeter = Greeter(1)
    req = grpc_thunderhill_msg_pb2.DataRequest(do_lap_prediction=True)
    reply = await greeter.GetData(req, 1)


def parse_args():
    parser = argparse.ArgumentParser()
    # parser.add_argument(
    #     "--adaptive_checkpoint_path",
    #     type=str,
    #     help="Path to the model dir, like .../checkpoint-1000",
    #     # default="~/aic_models/06-22T17_30_08-d09f9-prevk3_predict_nextlap_nosteering_noturn/checkpoint-83000",
    #     default="~/aic_models/08-26T19_15_43-601d3-csim_modeltest_adaptivek3_nometricsin_laplstm_nextlap_prediction/latest/checkpoint-73040",
    # )
    # with gauss, with cf prediction, with mapdropout=0.2, no metrics in, next lap metrics prediction, k3
    # parser.add_argument(
    #     "--adaptive_checkpoint_path",
    #     type=str,
    #     help="Path to the model dir, like .../checkpoint-1000",
    #     default="~/aic_models/09-04T23_05_54-d6b8b-csim_modeltest_adaptivek3_nometricsin_laplstm_nextlap_prediction_withgaussian_withcfmetrics_prediction_with_mapdropout/latest/checkpoint-42750",
    # )

    parser.add_argument(
        "--model_type",
        type=str,
        help="Type of model running. in ['nonadaptive', 'adaptive']",
        default="nonadaptive",
    )
    parser.add_argument(
        "--model_name",
        type=str,
        help="shortcut model name for models. in model[A, B, C, D]",
        default="modelA",
    )

    parser.add_argument("--adaptive_model", type=str2bool, default=False)
    parser.add_argument("--local_dev", type=str2bool, default=False)
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()
    local_dev = args.local_dev

    model_type = args.model_type
    model_id_dict = MODEL_ID_DICT[model_type]
    model_name = args.model_name
    model_config = []
    if args.adaptive_model:
        model_config += ["is_adaptive=True"]
        checkpoint_path = Path(
            model_id_dict[model_name]["checkpoint_path"]
        ).expanduser()
    else:
        model_config += ["is_adaptive=False"]
        checkpoint_path = Path(
            model_id_dict[model_name]["checkpoint_path"]
        ).expanduser()

    print(f"Launching model {checkpoint_path}")
    model_config += [model_id_dict[model_name]["model_config"]]
    print(f"With model config {model_config}")

    model_config = ",".join(model_config)
    category_index_type = model_id_dict[model_name]["category_index_type"]
    teacher_action_future_seq_len_in_sec = model_id_dict[model_name][
        "teacher_action_future_seq_len_in_sec"
    ]
    print(f"Category index type = {category_index_type}")
    print(f"Teacher action future seq_len = {teacher_action_future_seq_len_in_sec}")

    trainer = main(
        [
            "--online-inference",
            "True",
            "--checkpoint",
            str(checkpoint_path),
            "--category-index-type",
            f"{category_index_type}",
            "--config-str",
            model_config,
        ]
    )

    if local_dev:
        workdir = Path("~/aic_inference/").expanduser()
        lap_segments_dir = workdir / "lap_segments"
        lap_csv_to_lap_data(0)
        lap_csv_to_lap_data(1)

        # Write trial to workdir
        args = [
            "--segment-level-trajectory-snippets-dir",
            str(lap_segments_dir),
            "--trials-dir",
            str(lap_trial_dir),
        ]
        aic_24d05_laps_to_map_segments_snipper.main(args)

        k_lap_data = make_batch_data_lap((0, 0, 0))

        model = trainer.model
        model.eval()
        model.training_mode = "lap_only"
        model.compute_loss = lambda *args, **kwargs: (0, (0, 0))
        lap_model_outputs = model(k_lap_data)
        # side channel representation is already in the batch variable.

        debug_lap_csv_to_cf_data(0)
        args.extend(
            [
                "--trials-dir",
                str(cf_trial_dir),
                "--trajectory-snippets-dir",
                str(cf_segments_dir),
            ]
        )

        aic_24d05_concurrent_feedback_trajectory_snipper.main(args)
        cf_data = make_batch_data_cf_from_snippet_pkl(lap_model_outputs)
        model.training_mode = "cf_only"
        cf_model_outputs = model(cf_data)
        teacher_action_pred = cf_model_outputs["outputs"]["cf_teacher_action_sig"]
        print("teacher_action_pred", teacher_action_pred)
        l = teacher_action_pred.cpu().detach().numpy()[0, 0].tolist()
        subtitle_pred_to_str(l)
        a = 0

        loop = asyncio.get_event_loop()
        try:
            loop.run_until_complete(testing())
        finally:
            # loop.run_until_complete(_cleanup_coroutines)
            loop.close()
    else:
        with tempfile.TemporaryDirectory() as temp_dir:
            # global workdir, lap_segments_dir
            # workdir = Path(temp_dir)
            lap_segments_dir = workdir / "lap_segments"
            logging.basicConfig(level=logging.INFO)

            serve_sync(
                trainer, args, category_index_type, teacher_action_future_seq_len_in_sec
            )
