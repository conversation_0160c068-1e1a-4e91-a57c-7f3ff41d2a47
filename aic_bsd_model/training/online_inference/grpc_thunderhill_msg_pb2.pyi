from typing import ClassVar as _ClassVar
from typing import Iterable as _Iterable
from typing import Mapping as _Mapping
from typing import Optional as _Optional

from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf.internal import containers as _containers

DESCRIPTOR: _descriptor.FileDescriptor

class DataRequest(_message.Message):
    __slots__ = (
        "time_s",
        "time_ns",
        "x",
        "y",
        "z",
        "vel_x",
        "vel_y",
        "vel_z",
        "heading",
        "throttle",
        "brake",
        "steering",
        "do_cf_prediction",
        "do_lap_prediction",
        "do_data_update",
        "reset_data",
        "other_info",
        "ego_quaternion_x",
        "ego_quaternion_y",
        "ego_quaternion_z",
        "ego_quaternion_w",
    )

    class OtherInfoEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(
            self, key: _Optional[str] = ..., value: _Optional[str] = ...
        ) -> None: ...
    TIME_S_FIELD_NUMBER: _ClassVar[int]
    TIME_NS_FIELD_NUMBER: _ClassVar[int]
    X_FIELD_NUMBER: _ClassVar[int]
    Y_FIELD_NUMBER: _ClassVar[int]
    Z_FIELD_NUMBER: _ClassVar[int]
    VEL_X_FIELD_NUMBER: _ClassVar[int]
    VEL_Y_FIELD_NUMBER: _ClassVar[int]
    VEL_Z_FIELD_NUMBER: _ClassVar[int]
    HEADING_FIELD_NUMBER: _ClassVar[int]
    THROTTLE_FIELD_NUMBER: _ClassVar[int]
    BRAKE_FIELD_NUMBER: _ClassVar[int]
    STEERING_FIELD_NUMBER: _ClassVar[int]
    DO_CF_PREDICTION_FIELD_NUMBER: _ClassVar[int]
    DO_LAP_PREDICTION_FIELD_NUMBER: _ClassVar[int]
    DO_DATA_UPDATE_FIELD_NUMBER: _ClassVar[int]
    RESET_DATA_FIELD_NUMBER: _ClassVar[int]
    OTHER_INFO_FIELD_NUMBER: _ClassVar[int]
    EGO_QUATERNION_X_FIELD_NUMBER: _ClassVar[int]
    EGO_QUATERNION_Y_FIELD_NUMBER: _ClassVar[int]
    EGO_QUATERNION_Z_FIELD_NUMBER: _ClassVar[int]
    EGO_QUATERNION_W_FIELD_NUMBER: _ClassVar[int]
    time_s: _containers.RepeatedScalarFieldContainer[int]
    time_ns: _containers.RepeatedScalarFieldContainer[int]
    x: _containers.RepeatedScalarFieldContainer[float]
    y: _containers.RepeatedScalarFieldContainer[float]
    z: _containers.RepeatedScalarFieldContainer[float]
    vel_x: _containers.RepeatedScalarFieldContainer[float]
    vel_y: _containers.RepeatedScalarFieldContainer[float]
    vel_z: _containers.RepeatedScalarFieldContainer[float]
    heading: _containers.RepeatedScalarFieldContainer[float]
    throttle: _containers.RepeatedScalarFieldContainer[float]
    brake: _containers.RepeatedScalarFieldContainer[float]
    steering: _containers.RepeatedScalarFieldContainer[float]
    do_cf_prediction: bool
    do_lap_prediction: bool
    do_data_update: bool
    reset_data: bool
    other_info: _containers.ScalarMap[str, str]
    ego_quaternion_x: _containers.RepeatedScalarFieldContainer[float]
    ego_quaternion_y: _containers.RepeatedScalarFieldContainer[float]
    ego_quaternion_z: _containers.RepeatedScalarFieldContainer[float]
    ego_quaternion_w: _containers.RepeatedScalarFieldContainer[float]
    def __init__(
        self,
        time_s: _Optional[_Iterable[int]] = ...,
        time_ns: _Optional[_Iterable[int]] = ...,
        x: _Optional[_Iterable[float]] = ...,
        y: _Optional[_Iterable[float]] = ...,
        z: _Optional[_Iterable[float]] = ...,
        vel_x: _Optional[_Iterable[float]] = ...,
        vel_y: _Optional[_Iterable[float]] = ...,
        vel_z: _Optional[_Iterable[float]] = ...,
        heading: _Optional[_Iterable[float]] = ...,
        throttle: _Optional[_Iterable[float]] = ...,
        brake: _Optional[_Iterable[float]] = ...,
        steering: _Optional[_Iterable[float]] = ...,
        do_cf_prediction: bool = ...,
        do_lap_prediction: bool = ...,
        do_data_update: bool = ...,
        reset_data: bool = ...,
        other_info: _Optional[_Mapping[str, str]] = ...,
        ego_quaternion_x: _Optional[_Iterable[float]] = ...,
        ego_quaternion_y: _Optional[_Iterable[float]] = ...,
        ego_quaternion_z: _Optional[_Iterable[float]] = ...,
        ego_quaternion_w: _Optional[_Iterable[float]] = ...,
    ) -> None: ...

class DataReply(_message.Message):
    __slots__ = ("valid", "time", "x", "y", "yaw", "subtitle", "skill")
    VALID_FIELD_NUMBER: _ClassVar[int]
    TIME_FIELD_NUMBER: _ClassVar[int]
    X_FIELD_NUMBER: _ClassVar[int]
    Y_FIELD_NUMBER: _ClassVar[int]
    YAW_FIELD_NUMBER: _ClassVar[int]
    SUBTITLE_FIELD_NUMBER: _ClassVar[int]
    SKILL_FIELD_NUMBER: _ClassVar[int]
    valid: bool
    time: _containers.RepeatedScalarFieldContainer[float]
    x: _containers.RepeatedScalarFieldContainer[float]
    y: _containers.RepeatedScalarFieldContainer[float]
    yaw: _containers.RepeatedScalarFieldContainer[float]
    subtitle: _containers.RepeatedScalarFieldContainer[str]
    skill: _containers.RepeatedScalarFieldContainer[float]
    def __init__(
        self,
        valid: bool = ...,
        time: _Optional[_Iterable[float]] = ...,
        x: _Optional[_Iterable[float]] = ...,
        y: _Optional[_Iterable[float]] = ...,
        yaw: _Optional[_Iterable[float]] = ...,
        subtitle: _Optional[_Iterable[str]] = ...,
        skill: _Optional[_Iterable[float]] = ...,
    ) -> None: ...
