from typing import Any

import numpy as np
import pandas
from data_sources.teaching.convert_thunderhill_data import (
    ThunderhillVehicleData,
    convert_data_online,
)
from intent.multiagents.data_specific.thunderhill.online_inference.thunderhill_data_server import (
    serve_sync,
)
from intent.multiagents.data_specific.thunderhill.thunderhill_callbacks import (
    LABEL_TO_TEACHER_ACTION_DICT,
    ThunderhillTrainerCallback,
)
from intent.multiagents.new_prediction_trainer import NewPredictionProtobufTrainer
from intent.multiagents.new_train_pedestrian_trajectory_prediction import (
    TrainingLauncher,
    launch,
)
from intent.multiagents.train_thunderhill_teaching import ThunderhillTrainingConfig
from intent.multiagents.training_utils import TrainingContext

DISPLAY_TEXT = {
    "no_sentence": "",
    "no action/other": "",
    "brake": "slow_down",
    "accelerate": "speed_up",
    "lateral-position": "",
    "left": "be_on_the_left",
    "right": "be_on_the_right",
    "straight": "stay_straight",
    "turn": "turn",
    "look": "",
}

LONGI_CATEGORIES = {2: "brake", 3: "accelerate"}
LATERAL_CATEGORIES = {5: "left", 6: "right", 8: "turn"}

DECISION_THRESHOLD = 0.5


def null_func(*argc, **kwargs):
    pass


class ThunderhillOnlineDataset:
    def __init__(self):
        self.data = None

    def __getitem__(self, item):
        return self.data


from triceps.protobuf.prediction_dataset import ProtobufPredictionDataset


class ProtobufPredictionDatasetOnline(ProtobufPredictionDataset):
    def __init__(self, params):
        super().__init__("thunderhill_online_pb", params=params)
        self.num_total_instances = 1
        self.valid_instances = [0]
        self.params = params
        self.rel_filename = "thunderhill_online_pb"


class ThunderhillOnlineTrainer(NewPredictionProtobufTrainer):
    def __init__(self, **kwargs):
        self.params = kwargs["trainer_param"]
        self.dataset_orig = kwargs["datasets"]
        # Don't log anything
        self.params["disable_cache"] = True
        self.params["logger_type"] = "none"
        self.params["inference"] = True
        self.params["disable_optimization"] = True
        self.params["disable_visualizations"] = True
        self.params["evaluation_dataset"] = "inference"
        self.params["main_param_hash"] = "thunderhill_online"
        self.online_dataset = ThunderhillOnlineDataset()

        kwargs["datasets"] = {"inference": [self.online_dataset]}
        super().__init__(**kwargs)

        self.input_dir = self.params["input_dir"][0]
        self.output_dir = self.input_dir + "/compactsim_output"

        self.training_ctx = TrainingContext(
            self.params,
            "validation",
            time_intervals={
                k: 0 for k in ("trajectory_gen", "cost_computation", "backprop")
            },
        )

        with open(self.input_dir + "/track.csv", "r") as f:
            self.map_data = pandas.read_csv(f)

        # self.dataset=NewPredictionDataset(ProtobufPredictionDatasetOnline(self.params), self.params)
        self.dataset = ProtobufPredictionDatasetOnline(self.params)
        self.dataset.item_post_processors = (
            self.dataset_orig["train"].datasets[0].item_post_processors
        )
        self.dataset.data_transforms = (
            self.dataset_orig["train"].datasets[0].data_transforms
        )  # map handler and thunderhill state handler

        self.on_epoch_start(self.training_ctx)
        self.training_strategy.log_end_batch_loop = null_func
        self.training_strategy.append_statistics = null_func

    def parse_request(self, request_data):
        # d = np.arange(8, step=0.5)
        # return ThunderhillVehicleData(
        #     timestamp_s=d,
        #     timestamp_ns=d,
        #
        #     x=d,
        #                        y=d,
        #                        yaw=d,
        #                        vel=d,
        #                        # throttle=list(request_data.throttle),
        #                        # brake=list(request_data.brake),
        #                        )

        return ThunderhillVehicleData(
            timestamp_s=list(request_data.time_s),
            timestamp_ns=list(request_data.time_ns),
            x=list(request_data.x),
            y=list(request_data.y),
            yaw=list(request_data.yaw),
            vel=list(request_data.velocity),
            # throttle=list(request_data.throttle),
            # brake=list(request_data.brake),
        )

    def make_pb(self, request_data):
        data = self.parse_request(request_data)
        pb = convert_data_online(
            data, self.input_dir, self.output_dir, "inference", {}, self.map_data
        )

        return pb

    def load_pb(self, pb):
        # self.dataset.read_data=lambda : pb
        self.dataset.proto_prediction_instance = pb
        batch_item = self.dataset.__getitem__(0)
        return batch_item

    def forward(self, request_data):
        pb = self.make_pb(request_data)
        batch_item = self.load_pb(pb)
        from triceps.protobuf.prediction_dataset import dataset_collate_fn
        from tristan.predictors.causal.data.prediction_dataset import full_preprocess

        batch_item = full_preprocess(batch_item, self.params)
        batch_item = dataset_collate_fn([batch_item])
        loss, result = self.train_batch(self.training_ctx, batch_item, {})

        pred_traj = result["result"]["predicted_trajectories"]
        pred_subtitle = batch_item["subtitle_pred_sig"]
        subtitle_str = self.convert_subtitle_prediction_to_str(pred_subtitle)
        return pred_traj, subtitle_str

    def convert_subtitle_prediction_to_str(self, pred_subtitle):
        # subtitle_mean = pred_subtitle[0, 0].mean(0)
        # TODO (deepak.gopinath make use of the arg use-subtitle-mon-equals-one here to decide whether to compute mean over samples or not.)
        subtitle_mean = pred_subtitle[
            0, 0
        ]  # this works when use-subtitle-mon-equals-one=True
        print("SUBTITLE PREDICTION", subtitle_mean)
        # TODO (deepak.gopinath). Consider separating lateral and longitudinal commands.
        longi_predictions = [
            LABEL_TO_TEACHER_ACTION_DICT[str(i)]
            for i in range(2, 10)
            if i in list(LONGI_CATEGORIES.keys())
        ]
        valid_longi_predictions = []
        valid_lateral_predictions = []
        for i in range(2, 10):
            if subtitle_mean[i] > DECISION_THRESHOLD:
                if i in list(LONGI_CATEGORIES.keys()):
                    valid_longi_predictions.append(
                        (LABEL_TO_TEACHER_ACTION_DICT[str(i)], subtitle_mean[i])
                    )
                if i in list(LATERAL_CATEGORIES.keys()):
                    valid_lateral_predictions.append(
                        (LABEL_TO_TEACHER_ACTION_DICT[str(i)], subtitle_mean[i])
                    )

        if len(valid_longi_predictions) != 0:
            max_longi_pred_index = np.argmax(
                [i[1].detach().cpu().numpy().item() for i in valid_longi_predictions]
            )
            longi_text_to_show = DISPLAY_TEXT[
                valid_longi_predictions[max_longi_pred_index][0]
            ]
        else:
            # there are no longi categories whose prob > 0.5
            longi_text_to_show = ""

        if len(valid_lateral_predictions) != 0:
            max_lateral_pred_index = np.argmax(
                [i[1].detach().cpu().numpy().item() for i in valid_lateral_predictions]
            )
            lateral_text_to_show = DISPLAY_TEXT[
                valid_lateral_predictions[max_lateral_pred_index][0]
            ]
        else:
            # there are no lateral categories whose prob > 0.5
            lateral_text_to_show = ""

        if longi_text_to_show == "" or lateral_text_to_show == "":
            connecting_text = ""  # only either longi or lateral exists
        else:
            connecting_text = "_and_"

        subtitle_str = longi_text_to_show + connecting_text + lateral_text_to_show
        if subtitle_str == "speed_up_and_turn":
            subtitle_str = "turn_and_speed_up"
        # lateral_text_to_show = DISPLAY_TEXT[valid_longi_predictions[max_lateral_pred_index][0]]
        # # Consider picking the highest probability category each for longitudinal and lateral!
        # subtitle_str = [LABEL_TO_TEACHER_ACTION_DICT[str(i)] for i in range(2, 10) if subtitle_mean[i] > 0.5]
        return subtitle_str

    def train(self, additional_logging_handlers=None):
        serve_sync(self)


class ThunderhillOnlineInferenceConfig(ThunderhillTrainingConfig):
    trainer_cls: Any = ThunderhillOnlineTrainer

    @classmethod
    def from_params(cls, params: dict):
        config = super().from_params(params)
        config.additional_trainer_callbacks = config.additional_trainer_callbacks + (
            ThunderhillTrainerCallback(params),
        )
        config.additional_model_callbacks = ()
        return config


if __name__ == "__main__":
    launch(TrainingLauncher(config_cls=ThunderhillOnlineInferenceConfig))
