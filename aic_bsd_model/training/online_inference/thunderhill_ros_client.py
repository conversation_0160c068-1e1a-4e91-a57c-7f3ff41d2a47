import copy
import logging
import math
import time
from dataclasses import dataclass

import grpc

try:
    import grpc_thunderhill_msg_pb2
    import grpc_thunderhill_msg_pb2_grpc
except ModuleNotFoundError:
    from aic_bsd_model.training.online_inference import grpc_thunderhill_msg_pb2
    from aic_bsd_model.training.online_inference import grpc_thunderhill_msg_pb2_grpc

import pickle

import numpy as np

try:
    import rclpy

    # try:
    from derived_object_msgs.msg import ObjectArray
    from motion_sim_hmi_scenario_msgs.msg import ScenarioTimingInfo
    from platform_interface.msg import UnifiedVehicleState
    from rclpy.node import Node
    from scipy.spatial.transform import Rotation as R
    from std_msgs.msg import String
except:
    Node = dict


class Buffer:
    def __init__(self, input_hz, output_hz, queue_time, logger):
        self.input_hz = input_hz
        self.output_hz = output_hz
        self.queue_time = queue_time
        # at queue_time=10 with input_hz=120, we have self.max_len at 1200 exactly. If 1.1 times then it would be 1320
        self.max_len = int(queue_time * 1.1) * input_hz
        print(f"max_len: {self.max_len}")
        self.queue = []
        self.logger = logger
        assert input_hz % output_hz == 0

    def append(self, data, logger=None):
        if not self.queue:
            # only run on first received data, prefill buffer
            dt = 1 / self.input_hz
            logger.warning(
                f"msg time {data.timestamp_s}, {data.timestamp_ns} queue_time {self.queue_time}, dt {dt}"
            )
            start_time = (data.timestamp_s + data.timestamp_ns * 1e-9) - self.queue_time
            self.logger.warning(f"start time {start_time}")

            for i in range(int(self.input_hz * self.queue_time)):
                d = copy.deepcopy(data)
                t = start_time + i * dt

                d.timestamp_s = int(t)
                d.timestamp_ns = int((t - d.timestamp_s) * 1e9)
                self.queue.append(d)

            # self.print_time_stamp(logger)

        self.queue.append(data)
        if len(self.queue) > self.max_len:
            self.queue.pop(0)

    def print_time_stamp(self, logger=None):
        ts_list = np.asarray(
            [float(d.timestamp_s + d.timestamp_ns * 1e-9) for d in self.queue]
        )
        ts_diff_list = np.diff(ts_list)

        self.logger.warning(
            f"ts_stats {np.mean(ts_diff_list)}, {np.std(ts_diff_list)}, {np.max(ts_diff_list)}, {np.min(ts_diff_list)}"
        )

    def get(self):
        if len(self.queue) < self.max_len:
            print(f"Buffer size less than {self.max_len}. Returning None")
            return None
        # self.print_time_stamp()
        return self.queue


@dataclass
class ThunderhillVehicleData:
    timestamp_s: int
    timestamp_ns: int
    x: float
    y: float
    z: float
    ego_quaternion_x: float
    ego_quaternion_y: float
    ego_quaternion_z: float
    ego_quaternion_w: float
    vel_x: float
    vel_y: float
    vel_z: float
    heading: float
    handwheel_angle: float
    throttle_pedal_percent: float
    brake_pedal_percent: float
    # do_prediction: bool
    # do_data_update: bool
    # reset_data: bool
    # other_info: dict = None


def seconds_to_s_ns(time_s):
    s = int(time_s)
    ns = int((time_s - s) * 1e9)
    return s, ns


def make_grpc_request(
    buffer_data,
    do_lap_prediction,
    do_cf_prediction,
    do_data_update,
    reset_data,
    other_info=None,
):
    # NOTE(gRPC Python Team): .close() is possible on a channel and should be
    # used in circumstances in which the with statement does not fit the needs
    # of the code.
    def make_seqence(data, lamb):
        return [lamb(d) for d in data]

    if not do_data_update or not buffer_data:

        def make_seqence(data, lamb):
            return None

    response = None
    req_data = grpc_thunderhill_msg_pb2.DataRequest(
        time_s=make_seqence(buffer_data, lamb=lambda x: x.timestamp_s),
        time_ns=make_seqence(buffer_data, lamb=lambda x: x.timestamp_ns),
        x=make_seqence(buffer_data, lamb=lambda x: x.x),
        y=make_seqence(buffer_data, lamb=lambda x: x.y),
        z=make_seqence(buffer_data, lamb=lambda x: x.z),
        ego_quaternion_x=make_seqence(buffer_data, lamb=lambda x: x.ego_quaternion_x),
        ego_quaternion_y=make_seqence(buffer_data, lamb=lambda x: x.ego_quaternion_y),
        ego_quaternion_z=make_seqence(buffer_data, lamb=lambda x: x.ego_quaternion_z),
        ego_quaternion_w=make_seqence(buffer_data, lamb=lambda x: x.ego_quaternion_w),
        vel_x=make_seqence(buffer_data, lamb=lambda x: x.vel_x),
        vel_y=make_seqence(buffer_data, lamb=lambda x: x.vel_y),
        vel_z=make_seqence(buffer_data, lamb=lambda x: x.vel_z),
        heading=make_seqence(buffer_data, lamb=lambda x: x.heading),
        steering=make_seqence(buffer_data, lamb=lambda x: x.handwheel_angle),
        throttle=make_seqence(buffer_data, lamb=lambda x: x.throttle_pedal_percent),
        brake=make_seqence(buffer_data, lamb=lambda x: x.brake_pedal_percent),
        do_cf_prediction=do_cf_prediction,
        do_lap_prediction=do_lap_prediction,
        do_data_update=do_data_update,
        reset_data=reset_data,
        other_info=other_info,
    )
    try:
        with grpc.insecure_channel("localhost:50057") as channel:
            stub = grpc_thunderhill_msg_pb2_grpc.GetDataServeiceStub(channel)

            response = stub.GetData(req_data)
    except grpc.RpcError as e:
        logging.error("grpc call failed, but ignored", type(e))
        if e.code() == grpc.StatusCode.UNAVAILABLE:
            return None

    if response and response.valid:
        print(time.monotonic(), "response: ", response.subtitle)
    return response


topic_ = "/hero/unified_vehicle_state"


class MinimalPublisher(Node):
    def __init__(self):
        super().__init__("thunderhill_online_inference_pub")
        self.publisher_ = self.create_publisher(String, topic_, 10)
        timer_period = 0.5  # seconds
        self.timer = self.create_timer(timer_period, self.timer_callback)
        self.i = 0

    def timer_callback(self):
        msg = String()
        msg.data = "Hello World: %d" % self.i
        self.publisher_.publish(msg)
        self.get_logger().info('Publishing: "%s"' % msg.data)
        self.i += 1


# def read_ros_data(msg):
#     time = msg.header.stamp
#     timestamp_s = time.sec
#     timestamp_ns = time.nanosec
#     pos = msg.pose_local_m.position
#     q = msg.pose_local_m.orientation
#     # rotation = R.from_quat((q.x, q.y, q.z, q.w))
#     # euler = rotation.as_euler("XYZ")
#     twist = msg.twist_vehicle
#
#     data = ThunderhillVehicleData(
#         timestamp_s,
#         timestamp_ns,
#         pos.x,
#         pos.y,
#         pos.z,
#         ego_quaternion_x=q.x,
#         ego_quaternion_y=q.y,
#         ego_quaternion_z=q.z,
#         ego_quaternion_w=q.w,
#         vel_x=twist.linear.x,
#         vel_y=twist.linear.y,
#         vel_z=twist.linear.z,
#         heading=msg.heading_rad,
#         handwheel_angle=msg.handwheel_angle_rad,
#         throttle_pedal_percent=msg.throttle_pedal_percent,
#         brake_pedal_percent=msg.brake_pedal_percent,
#     )
#
#     return data


class ThunderhillROSClient(Node):
    def __init__(self, node, logger=None):
        super().__init__("thunderhill_ros_client_listener")
        self.logger = logger
        # self.subscription = node.create_subscription(
        #     UnifiedVehicleState, topic_, self.listener_callback, 10
        # )
        self.gate_subscription = node.create_subscription(
            ScenarioTimingInfo,
            "/experiment/shared_autonomy/scenario_timing_info",
            self.listener_callback_gate,
            1,
        )
        # self.subscription  # prevent unused variable warning
        self.input_hz = 120
        self.output_hz = 5
        self.hz_ratio = int(self.input_hz // self.output_hz)
        self.buffer = Buffer(self.input_hz, self.output_hz, 10, logger=self.logger)
        self.listener_count = 0
        self.inference_interval = 1
        self.inference_interval_count = self.inference_interval * self.input_hz
        self.raw_buffer = []

    def listener_callback_gate(self, msg):
        print("gate msg", msg)
        if "gate passed" in msg.condition:
            print("gate passed")
            if "start_gate" in msg.reference:
                pass
            elif "finish_gate" in msg.reference:
                wait_time = 5
                print(f"calling finished gate, waiting for csvs for {wait_time} ")
                time.sleep(wait_time)
                self.make_grpc_request(True, False, False, False)

        elif "scenario ended" in msg.condition:
            pass

    # def listener_callback(self, msg):
    #     self.listener_count += 1
    #     # return
    #
    #     # if self.buffer_count_limit % self.hz_ratio:
    #     data = read_ros_data(msg)
    #     self.buffer.append(data, self.logger)

    def get_buffer_data(self):
        buffer_data = self.buffer.get()

        def make_seqence(data, lamb):
            return [lamb(d) for d in data]

        time_s = make_seqence(buffer_data, lamb=lambda x: x.timestamp_s)
        time_ns = (make_seqence(buffer_data, lamb=lambda x: x.timestamp_ns),)
        buffer_ts = {"time_s": time_s, "time_ns": time_ns}
        with open("buffer_data.pkl", "wb") as f:
            pickle.dump(buffer_ts, f)

    def convert_raw_carla(self, carla_data):
        timestamp = carla_data["time"]
        timestamp_s, timestamp_ns = seconds_to_s_ns(timestamp)

        tv_data = ThunderhillVehicleData(
            timestamp_s,
            timestamp_ns,
            x=carla_data["ego_position_x"],
            y=carla_data["ego_position_y"],
            z=carla_data["ego_position_z"],
            ego_quaternion_x=carla_data["ego_orientation_x"],
            ego_quaternion_y=carla_data["ego_orientation_y"],
            ego_quaternion_z=carla_data["ego_orientation_z"],
            ego_quaternion_w=carla_data["ego_orientation_w"],
            vel_x=carla_data["ego_vx"],
            vel_y=carla_data["ego_vy"],
            vel_z=carla_data["ego_vz"],
            heading=0.0,
            handwheel_angle=carla_data["steer"],
            throttle_pedal_percent=carla_data["throttle_pedal"],
            brake_pedal_percent=carla_data["brake_pedal"],
        )
        return tv_data

    def append_data_buffer(self, carla_data):
        tv_data = self.convert_raw_carla(carla_data)
        self.buffer.append(tv_data, self.logger)

    def make_grpc_request(
        self,
        do_lap_prediction,
        do_cf_prediction,
        do_data_update,
        reset_data,
        other_info=None,
    ):
        buffer_data = self.buffer.get()

        reply = make_grpc_request(
            buffer_data,
            do_lap_prediction,
            do_cf_prediction,
            do_data_update,
            reset_data,
            other_info,
        )

        return reply


def main(args=None):
    rclpy.init(args=args)

    # minimal_publisher = MinimalPublisher()

    # rclpy.spin(minimal_publisher)

    # Destroy the node explicitly
    # (optional - otherwise it will be done automatically
    # when the garbage collector destroys the node object)
    # minimal_publisher.destroy_node()

    client = ThunderhillROSClient()

    rclpy.spin(client)

    # Destroy the node explicitly
    # (optional - otherwise it will be done automatically
    # when the garbage collector destroys the node object)
    client.destroy_node()
    rclpy.shutdown()
    rclpy.shutdown()


if __name__ == "__main__":
    main()
