#!/usr/bin/zsh
# Run this script with
#   zsh -i intent/multiagents/data_specific/thunderhill/online_inference/run_thunderhill_subtitle_inference_server.zsh
#set -ex
OTHER_ARGS=""
# Load model
if [ -z "$1" ]
	then
		echo "No argument supplied"
	else
		_MODEL_NAME="$1"
		echo "Model argument supplied, loading ${_MODEL_NAME}"
		OTHER_ARGS="${OTHER_ARGS} --resume-session-name ${_MODEL_NAME}"
fi


micromamba activate pt200
cd ~/risk_aware_driving/

python ~/risk_aware_driving/intent/multiagents/data_specific/thunderhill/run_thunderhill_online_inference.py --input-dir ~/data/prediction/thunderhill/v11 --learning-rate 1e-4 --scene-image-mode=none --agent-image-mode=none --num-epochs 6000 --vis-interval 100 --val-interval 4 --past-timesteps 20 --future-timesteps 20 --past-timestep-size 0.2 --future-timestep-size 0.2 --future-timestep-starting-offset 0 --interp-type none --max-agents 1 --num-visualization-images=7 --num-visualization-worst-cases=7 --MoN-number-samples=6 --interp-type none --use-discriminator false --datasets-name-lists=none --epoch-size 24 --batch-size 4 --val-epoch-size 20 --val-batch-size 4 --vis-batch-size 4 --vis-epoch-size 4 --val-num-workers 12 --vis-num-workers 8 --report-agent-type-metrics=true --use-marginal-error=true --err-horizons-timepoints 5 --use-thunderhill-dataset true --use-semantics false --use-discriminator false --use-mlp-decoder true --predictor-hidden-state-dim=16 --learning-rate-reduce-on-plateau=false --learning-rate-reduce-on-plateau-patience 3 --learning-rate-reduce-on-plateau-factor 0.95 --map-use-vectors true --use-transformer-baseline-encoder true --map-attention-local-layer 2 --use-internal-map-encoder true --encoder-decoder-type=gnn --use-batch-graph-encoder=true --cache-dir ~/intent/cache --plot-debug-normalization false --use-simple-metrics true --encoder-use-map true --thunderhill-num-label-classes 10 --ignore-ego --max-agents 1 --use-anchor-trajectories linear_layer --encoder-num-mlp-layers 3 --decoder-num-mlp-layers 3 --map-points-max=10000 --encoder-normalized-other-agents-position=false --predictor-normalization-scale 0.01 --use-map-velocity-information=true --learning-rate-constant 4e-5 --subtitle-loss-type existence --subtitle-loss-type existence --dropout-ratio 0.1 --training-set-ratio 0.8 --mon-term-coeff 1 --use-two-tone-sampler true --full-dataset-epochs false --visualize-full-dataset false --thunderhill-subtitle-usage-percentage 10 --max-files 500 --num-workers 10 --use-controls-as-input False --use-subtitle-mon-equals-one True --use-compactsim-dataset True --is-skill-pred True --skill-vector-dim 3 --skill-score-scaler-path /home/<USER>/data/prediction/thunderhill/v11/score_scaler_dict.pkl ${=OTHER_ARGS}

