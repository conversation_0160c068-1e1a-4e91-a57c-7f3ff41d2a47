#!/usr/bin/env python3
"""
thunderhill_stub_publisher.py
Stream synthetic Thunderhill-like vehicle data to the gRPC server for consistency testing.

Usage:
  python thunderhill_stub_publisher.py \
    --host localhost --port 50057 \
    --input-hz 120 --output-hz 5 \
    --queue-time 10.15 --duration 60 \
    --inference-period 1.0 \
    --speed-mps 15.0 --radius-m 50.0
"""

import argparse
import math
import time
from dataclasses import dataclass

import grpc

try:
    import grpc_thunderhill_msg_pb2
    import grpc_thunderhill_msg_pb2_grpc
except ModuleNotFoundError:
    # Fallback to your package layout if running inside your repo
    from aic_bsd_model.training.online_inference import grpc_thunderhill_msg_pb2
    from aic_bsd_model.training.online_inference import grpc_thunderhill_msg_pb2_grpc


class Buffer:
    """
    Matches your original buffering/downsampling behavior:

    - Keep last `queue_time` seconds at `input_hz`
    - Pick indices so that the latest sample is included and overall stride matches `output_hz`
    """
    def __init__(self, input_hz: int, output_hz: int, queue_time: float):
        self.input_hz = int(input_hz)
        self.output_hz = int(output_hz)
        self.queue_time = float(queue_time)
        # keep just over queue_time to ensure coverage
        self.max_len = int(self.queue_time * 1.1 * self.input_hz)
        self.queue = []
        assert self.input_hz % self.output_hz == 0, "input_hz must be a multiple of output_hz"
        step = self.input_hz // self.output_hz
        item_idxs = list(range(self.max_len))
        # choose latest-first, then stride backwards
        latest_first_item_idxs = item_idxs[::-1]
        self.picked_idxs = set(latest_first_item_idxs[::step])

    def append(self, data):
        self.queue.append(data)
        if len(self.queue) > self.max_len:
            self.queue.pop(0)

    def get(self):
        """Return downsampled list over the current window, or None until buffer is full."""
        if len(self.queue) < self.max_len:
            return None
        return [d for i, d in enumerate(self.queue) if i in self.picked_idxs]


@dataclass
class ThunderhillVehicleData:
    timestamp_s: int
    timestamp_ns: int
    x: float
    y: float
    z: float
    vel_x: float
    vel_y: float
    vel_z: float
    heading: float                 # radians
    handwheel_angle: float         # radians
    throttle_pedal_percent: float  # [0,100]
    brake_pedal_percent: float     # [0,100]


def make_grpc_request(
    buffer_data,
    do_lap_prediction: bool,
    do_cf_prediction: bool,
    do_data_update: bool,
    reset_data: bool,
    other_info: str = None,
    host: str = "localhost",
    port: int = 50057,
):
    """Build and send grpc_thunderhill_msg_pb2.DataRequest, return server response."""
    def make_seq(data, f):
        return [f(d) for d in data]

    if not do_data_update or not buffer_data:
        # Server expects None for sequences when not updating data
        def make_seq(data, f):
            return None

    req_data = grpc_thunderhill_msg_pb2.DataRequest(
        time_s=make_seq(buffer_data, lambda x: x.timestamp_s),
        time_ns=make_seq(buffer_data, lambda x: x.timestamp_ns),
        x=make_seq(buffer_data, lambda x: x.x),
        y=make_seq(buffer_data, lambda x: x.y),
        z=make_seq(buffer_data, lambda x: x.z),
        vel_x=make_seq(buffer_data, lambda x: x.vel_x),
        vel_y=make_seq(buffer_data, lambda x: x.vel_y),
        vel_z=make_seq(buffer_data, lambda x: x.vel_z),
        heading=make_seq(buffer_data, lambda x: x.heading),
        steering=make_seq(buffer_data, lambda x: x.handwheel_angle),
        throttle=make_seq(buffer_data, lambda x: x.throttle_pedal_percent),
        brake=make_seq(buffer_data, lambda x: x.brake_pedal_percent),
        do_cf_prediction=do_cf_prediction,
        do_lap_prediction=do_lap_prediction,
        do_data_update=do_data_update,
        reset_data=reset_data,
        other_info=other_info,
    )

    target = f"{host}:{port}"
    with grpc.insecure_channel(target) as channel:
        stub = grpc_thunderhill_msg_pb2_grpc.GetDataServeiceStub(channel)
        response = stub.GetData(req_data)

    if response.valid:
        print(f"{time.monotonic():.3f} response valid: {response.subtitle}")
    else:
        print(f"{time.monotonic():.3f} response invalid")
    return response


def synth_sample(t_ns: int, x: float, y: float, z: float,
                 v: float, heading: float, handwheel: float,
                 throttle_pct: float, brake_pct: float) -> ThunderhillVehicleData:
    s = t_ns // 1_000_000_000
    ns = t_ns % 1_000_000_000
    # Resolve body-frame velocity from speed and heading (world frame)
    vel_x = v * math.cos(heading)
    vel_y = v * math.sin(heading)
    vel_z = 0.0
    return ThunderhillVehicleData(
        timestamp_s=s,
        timestamp_ns=ns,
        x=x, y=y, z=z,
        vel_x=vel_x, vel_y=vel_y, vel_z=vel_z,
        heading=heading,
        handwheel_angle=handwheel,
        throttle_pedal_percent=throttle_pct,
        brake_pedal_percent=brake_pct,
    )


def run_stub(
    host: str,
    port: int,
    input_hz: int,
    output_hz: int,
    queue_time: float,
    duration: float,
    inference_period: float,
    speed_mps: float,
    radius_m: float,
    steering_gain_rad_per_m: float = 0.02,
):
    """
    Generate a smooth oval trajectory:
      - constant speed `speed_mps`
      - heading follows tangent
      - handwheel ~ curvature with a small sinusoidal modulation
      - throttle/brake signals that alternate slowly to test transitions
    """
    buf = Buffer(input_hz, output_hz, queue_time)
    ts_ns = time.time_ns()  # start timestamp
    dt = 1.0 / input_hz

    # Circle param: theta evolves at omega = v / r
    omega = speed_mps / max(radius_m, 1e-3)
    theta = 0.0

    next_infer_time = time.monotonic()
    end_time = time.monotonic() + duration

    sent = 0
    appended = 0
    print(
        f"Starting stub stream -> {host}:{port} | "
        f"in:{input_hz}Hz out:{output_hz}Hz win:{queue_time}s infer:{inference_period}s "
        f"speed:{speed_mps}m/s radius:{radius_m}m"
    )

    try:
        while time.monotonic() < end_time:
            loop_start = time.monotonic()

            # Oval-ish by modulating radius a little over time
            r = radius_m * (1.0 + 0.1 * math.sin(0.1 * theta))
            x = r * math.cos(theta)
            y = r * math.sin(theta)
            z = 0.0

            heading = theta + math.pi / 2.0  # tangent (CCW)
            # Very rough handwheel proxy from curvature + small oscillation
            curvature = 1.0 / max(r, 1e-3)
            handwheel = steering_gain_rad_per_m * curvature + 0.03 * math.sin(0.5 * theta)

            # Simple throttle/brake play to test transitions (5s cycles)
            cycle = math.sin(2.0 * math.pi * (loop_start % 5.0) / 5.0)
            throttle_pct = max(0.0, 60.0 + 40.0 * cycle)
            brake_pct = max(0.0, 20.0 - 20.0 * cycle)

            sample = synth_sample(
                ts_ns, x, y, z, speed_mps, heading, handwheel, throttle_pct, brake_pct
            )
            buf.append(sample)
            appended += 1

            # time marches on
            ts_ns += int(dt * 1e9)
            theta += omega * dt

            # Fire a request every `inference_period`
            if loop_start >= next_infer_time:
                payload = buf.get()  # None until buffer warms up
                _ = make_grpc_request(
                    buffer_data=payload,
                    do_lap_prediction=False,
                    do_cf_prediction=True,
                    do_data_update=(payload is not None),
                    reset_data=False,
                    other_info="stub_publisher",
                    host=host,
                    port=port,
                )
                sent += 1
                next_infer_time += inference_period

            # Sleep to maintain input_hz
            elapsed = time.monotonic() - loop_start
            sleep_s = max(0.0, dt - elapsed)
            if sleep_s > 0:
                time.sleep(sleep_s)

    except KeyboardInterrupt:
        print("Interrupted by user.")
    finally:
        print(f"Done. Appended {appended} samples, sent {sent} requests.")


def parse_args():
    p = argparse.ArgumentParser()
    p.add_argument("--host", default="localhost")
    p.add_argument("--port", type=int, default=50057)
    p.add_argument("--input-hz", type=int, default=120)
    p.add_argument("--output-hz", type=int, default=5)
    p.add_argument("--queue-time", type=float, default=10.15)
    p.add_argument("--duration", type=float, default=30.0, help="Seconds to run")
    p.add_argument("--inference-period", type=float, default=1.0, help="Seconds between requests")
    p.add_argument("--speed-mps", type=float, default=15.0)
    p.add_argument("--radius-m", type=float, default=50.0)
    p.add_argument("--uniform-value", type=float, default=None)
    return p.parse_args()


if __name__ == "__main__":
    args = parse_args()

    run_stub(
        host=args.host,
        port=args.port,
        input_hz=args.input_hz,
        output_hz=args.output_hz,
        queue_time=args.queue_time,
        duration=args.duration,
        inference_period=args.inference_period,
        speed_mps=args.speed_mps,
        radius_m=args.radius_m,
    )
