#!/bin/bash
set -ex
GITHUB_TOKEN=$(aws secretsmanager get-secret-value --secret-id sagemaker-github.com-token2 --query SecretString --output text --region $AWS_REGION)
if [ -z "$GITHUB_TOKEN" ]; then
    echo "Error: Failed to retrieve GitHub token from AWS Secrets Manager"
    exit 1
fi


# Clone repository with token
git clone --depth 1 https://x-access-token:${GITHUB_TOKEN}@github.com/ToyotaResearchInstitute/cache_decorator.git 
pip install  wandb srt opencv-python-headless shapely torchmetrics 'transformers==4.*' peft draccus jsonargparse pyarrow wandb pandas matplotlib lz4 umap-learn scikit-learn scipy simplejson more-itertools Pillow 'imageio[ffmpeg]'
pip install ./cache_decorator

echo RUNNING_ON_SAGEMAKER $RUNNING_ON_SAGEMAKER

if [ "$RUNNING_ON_SAGEMAKER" = "True" ]; then
    AWS_PROFILE=""
else
    AWS_PROFILE="--profile rad"
fi
echo
echo
echo

# aws s3 cp --only-show-errors --recursive s3://tri-hid-data-shared-autonomy/24-D-16/model_training_data/trajectory_segments_map_seg_ids  ~/Data/24-D-16/trajectory_segments_map_seg_ids $AWS_PROFILE

# aws s3 cp --only-show-errors --recursive s3://tri-hid-data-shared-autonomy/24-D-16/trials_final/  ~/Data/24-D-16/trials_final $AWS_PROFILE

# 24D05
aws s3 cp --only-show-errors --recursive s3://tri-hid-data-shared-autonomy/24-D-05/trials_final/  ~/Data/24-D-05/trials_final $AWS_PROFILE
aws s3 cp --only-show-errors --recursive s3://tri-hid-data-shared-autonomy/24-D-05/trajectory_segments_map_seg_ids/  ~/Data/24-D-05/trajectory_segments_map_seg_ids $AWS_PROFILE
aws s3 cp --only-show-errors --recursive s3://tri-hid-data-shared-autonomy/24-D-05/trajectory_snippets/  ~/Data/24-D-05/trajectory_snippets $AWS_PROFILE
