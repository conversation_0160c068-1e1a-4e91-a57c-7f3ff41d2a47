import argparse
from pathlib import Path

from sklearn.model_selection import train_test_split
from torch.utils.data import DataLoader

from aic_bsd_model.datasets.aic_24d05_cf_dataset import AIC24D05CFDataset
from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    INVALID_TRIAL_IDX_PER_SUBJECT_24_D_05,
)
from aic_bsd_model.datasets.data_collators import CFSnippetCollator


def parse_arguments():
    parser = argparse.ArgumentParser(
        description="Training script for driving metrics prediction."
    )
    parser.add_argument(
        "--data-dir",
        type=str,
        help="Folder in which the snipped trajectory are to be stored",
        default="~/Data/24-D-05",
    )
    # Boolean argument for using training data as test data
    parser.add_argument(
        "--use_train_as_test",
        action="store_true",
        help="Use the training dataset as the test dataset.",
    )
    # Boolean argument for using one subject as training dat
    parser.add_argument(
        "--use_one_subject_to_train",
        action="store_true",
        help="Use only one subject to train",
    )
    parser.add_argument(
        "--debug_with_one_trial", action="store_true", help="Use only one trial. "
    )
    parser.add_argument(
        "--test_size", type=int, default=1, help="Size of the test dataset."
    )
    parser.add_argument(
        "--device",
        type=str,
        help="Device to use for training. Default is 'cuda'.",
        default="cuda:0",
    )

    args = parser.parse_args()
    return args


# Parse command-line arguments
args = parse_arguments()

config_dict = {
    "traj_input_dim": 13,
    "map_input_dim": 6,
    "hidden_dim": 128,
    "is_cross_attention": True,  # for traj and map fusion
    # map_hidden_dim: 64,
    "num_attention_heads": 4,
    "num_layers": 3,
    # map_encoder_num_layers:3,
    "metrics_output_dim": 2,
    "skill_embedding_dim": 2,
    "traj_decoder_output_dim": 3,
    #'global_encoder_hidden_dim': 64,
    #'global_encoder_num_layers': 3,
    "lap_emb_use_cls_global_token": True,
    "dropout": 0.1,
    "segment_dropout_ratio": 0.0,
    "weight_decay": 1e-5,
    "lr": 0.0001,
    "batch_size": 4,
    "num_workers": 0,
    "global_num_epochs": 5000,
    "target_metric_keys": ["trial_time", "smoothness_score_mean"],
    "padding_value": -2000,
    "padding_side": "right",
    "num_validation_epochs": 4,
    "metrics_decoding_loss_coeff": 2.0,
    "segment_traj_reconstruction_loss_coeff": 1.0,
    "segment_traj_reconstruction_smoothness_loss_coeff": 5.0,
    "skill_emb_loss_coeff": 1.0,
    "verbose_frequency": 5,
    "verbose": True,
    "epoch_data_train_size": 2048,
    "use_random_sampler": False,
    "use_full_sequence": True,
    "predict_delta": True,
    "normalization_timestep": "last",
    "loss_component_names": [
        "metrics_decoding_loss",
        "segment_traj_reconstruction_loss",
        "segment_traj_reconstruction_smoothness_loss",
    ],
    "teacher_action_num_categories": 12,
    "enable_dataset_cache": False,
}

# args key values to the config dict
config_dict.update(vars(args))

# add parse args or something so that paths can be passed as args. eventually unify it with draccuss or whatever AS is using for IFM
trajectory_segments_dir = (
    Path.expanduser(Path(args.data_dir)) / "trajectory_segments_map_seg_ids"
)
trajectory_snippets_dir = Path.expanduser(Path(args.data_dir)) / "trajectory_snippets"
# trials_directory = Path.expanduser(Path(args.data_dir)) / "trials_final"
map_file = trajectory_segments_dir / "track.csv"

print()
print(f"Trajectory Segments Dir: {None}")
print(f"Trials Dir: {None}")
print(f"Map File Path: {map_file}")

config_dict["trajectory_segments_dir"] = None
config_dict["trials_directory"] = None
config_dict["trajectory_snippets_dir"] = trajectory_snippets_dir
config_dict["map_file"] = map_file

all_pids_list = [pid for pid in list(INVALID_TRIAL_IDX_PER_SUBJECT_24_D_05.keys())]
if args.use_one_subject_to_train:
    train_pids_list, test_pids_list = train_test_split(
        all_pids_list, test_size=len(all_pids_list) - 1, random_state=42, shuffle=True
    )
else:
    assert args.test_size > 0 and args.test_size < len(all_pids_list)
    train_pids_list, test_pids_list = train_test_split(
        all_pids_list, test_size=args.test_size, random_state=42, shuffle=True
    )

if args.use_train_as_test:
    test_pids_list = train_pids_list

print(f"Using configuration dict: {config_dict}")

print(f"Train Subject List: {train_pids_list}")
print(f"Test Subject List: {test_pids_list}")
config_dict["train_pids_to_be_considered"] = train_pids_list
config_dict["test_pids_to_be_considered"] = test_pids_list


train_24d05_cf_dataset = AIC24D05CFDataset(config_dict, dataset_type="train")
train_24d05_cf_dataset.cache_dataset()

test_24d05_cf_dataset = AIC24D05CFDataset(config_dict, dataset_type="test")
test_24d05_cf_dataset.cache_dataset()
sampler = None
collator = CFSnippetCollator(
    padding_value=config_dict["padding_value"],
    padding_side=config_dict["padding_side"],
)
import IPython

IPython.embed(banner1="check dataset sample")
train_dataloader = DataLoader(
    train_24d05_cf_dataset,
    batch_size=config_dict["batch_size"],
    collate_fn=collator,
    num_workers=config_dict["num_workers"],
    # drop_last=True,
    sampler=sampler,
)
import IPython

IPython.embed(banner1="check")
