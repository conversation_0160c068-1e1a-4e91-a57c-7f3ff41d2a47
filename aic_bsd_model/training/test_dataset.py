import argparse
from pathlib import Path

from sklearn.model_selection import train_test_split
from torch.utils.data import DataLoader

import wandb
from aic_bsd_model.datasets.aic_24d05_combined_dataset import (
    AIC24D05CombinedDataset,
    AIC24D05CombinedFullSequenceDataset,
    AIC24D05CombinedPrevKDataset,
)
from aic_bsd_model.datasets.aic_24d05_lap_dataset import (
    AIC24D05FullSequenceDataset,
    AIC24D05LapDataset,
)
from aic_bsd_model.datasets.aic_24d16_lap_dataset import (
    AIC24D16FullSequenceDataset,
    AIC24D16LapDataset,
)
from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    INVALID_TRIAL_IDX_PER_SUBJECT_24_D_05,
    INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16,
    get_uid_from_pid_and_trial_num,
    split_pids_laps,
)
from aic_bsd_model.datasets.data_collators import (
    TrackSegmentCollator,
    TrackSegmentCollatorFullSequence,
)
from aic_bsd_model.utils.cache_utils import clear_cache

EXP_ID = "24-D-16"

IS_FULL_DATASET = True


def parse_arguments():
    parser = argparse.ArgumentParser(
        description="Training script for driving metrics prediction."
    )
    parser.add_argument(
        "--data-dir",
        type=str,
        help="Folder in which the snipped trajectory are to be stored",
        default=f"~/Data/{EXP_ID}",
    )
    # Boolean argument for using training data as test data
    parser.add_argument(
        "--use_train_as_test",
        action="store_true",
        help="Use the training dataset as the test dataset.",
    )
    # Boolean argument for using one subject as training dat
    parser.add_argument(
        "--use_one_subject_to_train",
        action="store_true",
        help="Use only one subject to train",
    )
    parser.add_argument(
        "--debug_with_one_trial", action="store_true", help="Use only one trial. "
    )
    parser.add_argument(
        "--test_size", type=int, default=1, help="Size of the test dataset."
    )
    parser.add_argument(
        "--device",
        type=str,
        help="Device to use for training. Default is 'cuda'.",
        default="cuda:0",
    )

    args = parser.parse_args()
    return args


# Parse command-line arguments
args = parse_arguments()

config_dict = {
    "traj_input_dim": 13,
    "map_input_dim": 6,
    "hidden_dim": 128,
    "is_cross_attention": True,  # for traj and map fusion
    # map_hidden_dim: 64,
    "num_attention_heads": 4,
    "num_layers": 3,
    # map_encoder_num_layers:3,
    "metrics_output_dim": 2,
    "skill_embedding_dim": 2,
    "traj_decoder_output_dim": 3,
    #'global_encoder_hidden_dim': 64,
    #'global_encoder_num_layers': 3,
    "lap_emb_use_cls_global_token": True,
    "dropout": 0.1,
    "segment_dropout_ratio": 0.0,
    "weight_decay": 1e-5,
    "lr": 0.0001,
    "batch_size": 4,
    "num_workers": 0,
    "global_num_epochs": 5000,
    "target_metric_keys": ["trial_time", "smoothness_score_mean"],
    "padding_value": -2000,
    "padding_side": "right",
    "num_validation_epochs": 4,
    "metrics_decoding_loss_coeff": 2.0,
    "segment_traj_reconstruction_loss_coeff": 1.0,
    "segment_traj_reconstruction_smoothness_loss_coeff": 5.0,
    "skill_emb_loss_coeff": 1.0,
    "verbose_frequency": 5,
    "verbose": True,
    "index_sampling_noise": 0.0,
    "constant_shift_noise": 0.0,
    "constant_scale_noise": 0.0,
    "epoch_data_train_size": 2048,
    "use_random_sampler": False,
    "target_sampling_frequency": 5,
    "predict_delta": True,
    "loss_component_names": [
        "metrics_decoding_loss",
        "segment_traj_reconstruction_loss",
        "segment_traj_reconstruction_smoothness_loss",
    ],
}
dataset_config = {
    # TODO consider separate sampling freqencies for laps and cf snippets
    "cf_target_sampling_frequency": 5,
    "trajectory_normalization_scheme": "local",
    "global_trajectory_normalization_info": None,
    "local_map_distance_threshold": 100,
    "prediction_timestep_in_sec": 5,  # used with CFDataset
    "normalization_timestep": "first",
    "with_cf_snippets": True,  # should be false for 24D16Lap, 24D05Lap, True for 24D05Full, True for 24D05CF
    "num_cf_snippets_per_lap": 1,  # unused with 24D16Lap. Keep this at 1 for 24D05CF.
    "snippet_filename": "snippet.pkl",
    "snippet_length_in_sec": 10,
    "annotation_majority_threshold": 0.6,
    "padding_side": "right",
    "padding_value": -2000,
    "enable_dataset_cache": False,
    "teacher_action_num_categories": 12,
    "return_next_lap_metrics": True,
}
# update config dict with dataset config
config_dict.update(dataset_config)


def setup_dataset(dataset_id, config_dict):
    config_dict["batch_size"] = 7
    # args key values to the config dict
    config_dict.update(vars(args))
    print(f"USING EXP ID - {dataset_id}")
    use_full_sequence = config_dict["use_full_sequence"]
    data_dir = config_dict["data_dir"]

    # add parse args or something so that paths can be passed as args. eventually unify it with draccuss or whatever AS is using for IFM
    trajectory_segments_dir = (
        Path.expanduser(Path(data_dir)) / "trajectory_segments_map_seg_ids"
    )
    trajectory_snippets_dir = Path.expanduser(Path(data_dir)) / "trajectory_snippets"
    trials_directory = Path.expanduser(Path(data_dir)) / "trials_final"
    map_file = trajectory_segments_dir / "track.csv"

    print()
    print(f"Trajectory Segments Dir: {trajectory_segments_dir}")
    print(f"Trials Dir: {trials_directory}")
    print(f"Map File Path: {map_file}")

    config_dict["trajectory_segments_dir"] = trajectory_segments_dir
    config_dict["trials_directory"] = trials_directory
    config_dict["trajectory_snippets_dir"] = trajectory_snippets_dir
    config_dict["map_file"] = map_file

    if dataset_id == "24-D-05":
        all_pids_list = [
            pid for pid in list(INVALID_TRIAL_IDX_PER_SUBJECT_24_D_05.keys())
        ]
    elif dataset_id == "24-D-16":
        pid_to_skip = "P1611"
        all_pids_list = [
            pid
            for pid in list(INVALID_TRIAL_IDX_PER_SUBJECT_24_D_16.keys())
            if pid != pid_to_skip
        ]
    if args.use_one_subject_to_train:
        train_pids_list, test_pids_list = train_test_split(
            all_pids_list,
            test_size=len(all_pids_list) - 1,
            random_state=42,
            shuffle=True,
        )
    else:
        assert args.test_size > 0 and args.test_size < len(all_pids_list)
        train_pids_list, test_pids_list = train_test_split(
            all_pids_list, test_size=args.test_size, random_state=42, shuffle=True
        )

    if args.use_train_as_test:
        test_pids_list = train_pids_list

    print(f"Using configuration dict: {config_dict}")

    print(f"Train Subject List: {train_pids_list}")
    print(f"Test Subject List: {test_pids_list}")
    config_dict["train_pids_to_be_considered"] = train_pids_list
    config_dict["test_pids_to_be_considered"] = test_pids_list
    print(
        f"Dataset Id - {dataset_id},  Combined - {config_dict['use_combined_dataset']}, Full sequence - {use_full_sequence}"
    )
    if dataset_id == "24-D-05":
        if config_dict["use_combined_dataset"]:
            if use_full_sequence:
                train_dataset = AIC24D05CombinedFullSequenceDataset(
                    config_dict, dataset_type="train"
                )
            else:
                train_dataset = AIC24D05CombinedDataset(
                    config_dict, dataset_type="train"
                )
        else:
            if config_dict["use_prevk_dataset"]:
                train_dataset = AIC24D05CombinedPrevKDataset(
                    config_dict, dataset_type="train"
                )
            else:
                if use_full_sequence:
                    train_dataset = AIC24D05FullSequenceDataset(
                        config_dict, dataset_type="train"
                    )
                else:
                    train_dataset = AIC24D05LapDataset(
                        config_dict, dataset_type="train"
                    )

                train_dataset.cache_dataset()

    elif dataset_id == "24-D-16":
        if use_full_sequence:
            train_dataset = AIC24D16FullSequenceDataset(
                config_dict, dataset_type="train"
            )
        else:
            train_dataset = AIC24D16LapDataset(config_dict, dataset_type="train")
        train_dataset.cache_dataset()

    collator = train_dataset.get_collator(config_dict)
    return train_dataset, collator


# create dataloaders
def test_dataset(dataset_id, config):
    train_dataset, collator = setup_dataset(dataset_id, config)
    print(f"Using {dataset_id} with batch size {config['batch_size']} for training")
    sampler = None
    import IPython

    IPython.embed(banner1="check dataset")
    train_dataloader = DataLoader(
        train_dataset,
        batch_size=config_dict["batch_size"],
        collate_fn=collator,
        num_workers=config_dict["num_workers"],
        sampler=sampler,
    )
    d = next(iter(train_dataloader))
    print(d.keys())


EXP_ID = "24-D-05"
args.data_dir = Path.expanduser(Path("~/Data/" + EXP_ID))

# config_dict["use_full_sequence"] = True
# test_dataset(EXP_ID, config_dict)
config_dict["use_full_sequence"] = False
config_dict["use_combined_dataset"] = False
config_dict["use_prevk_dataset"] = False
# use 1 if you want only1  previous lap. Use a big number if you want all history upuntil the lap from which the snippet came from
config_dict["num_prev_laps"] = 1
config_dict["return_cf_for_lap"] = False  # only applicable for 24D05
if True:
    clear_cache()

test_dataset(EXP_ID, config_dict)
