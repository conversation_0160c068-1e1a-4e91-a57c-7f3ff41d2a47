#!/usr/bin/env python3
"""
This script generates sweep training scripts for the BSD model.
It will create combinations of the listed configs and run them in parallel in n scripts.

Usage:
python generate_training_cmds.py
    it will generate the script and print out the command to run the script.
    It will genereate a machine_script and training_script  
        machine_script will start training_script in parallel. 
        training_script will use a fixed CUDA_VISIBLE_DEVICES=X to run training in sequence
            Different training_script will run on different GPU
    Action:
        - Change the cuda_ids to the desired GPU ids (according to your number of machiens and GPU ids)
        - Run this script
        - Copy the command from the output, note copy all commands for a single machine
        - Run the commands in the terminal on the machine, and repeat for other machines

"""

import itertools
import math
import os
import random
import shutil
from datetime import datetime
from pathlib import Path

from hail_launch.util.sweep_util import generate_combined_configs, generate_runs

from aic_bsd_model.datasets.aic_bsd_dataset_utils import (
    CategoryDictIndex,
    get_pid_and_trial_num_from_uid,
)

# Find the path to parent directory containing .gitignore
current_dir = Path(".").resolve()
current_file = Path(__file__).resolve()
parent_with_gitignore = current_file
while parent_with_gitignore.parent != parent_with_gitignore:  # while not at root
    if (parent_with_gitignore / ".gitignore").exists():
        break
    parent_with_gitignore = parent_with_gitignore.parent
repo_root = parent_with_gitignore
script_dir_name = ".training_scripts/"
script_dir = repo_root / script_dir_name
shutil.rmtree(script_dir, ignore_errors=True)
script_dir.mkdir(parents=True, exist_ok=True)

TRAINING_SCRIPT = "aic_bsd_model/training/train_adaptive_cf.py"
TRAINING_SCRIPT = "aic_bsd_model/training/train_nonadaptive_cf.py"

# These are the params you need to change for your run
wandb_run_group = "adaptive_cf_hf_nogauss_withcf_metrics-sweep"
wandb_run_group = "nonadaptive-metrics_sweep-bz"
# len(num_gpus_in_machines) == num_machines
num_gpus_per_machines = [3, 3, 3]
num_parallel_runs_per_gpu = 5

# Sagemaker
use_sagemaker = True
# use_sagemaker = False
sm_num_total_run_per_gpu = 4
num_parallel_runs_per_gpu = 2


def compute_cuda_allocation(in_num_gpus_in_machines, in_num_parallel_runs_per_gpu):
    cuda_ids_per_machine = []
    for machine_idx, num_gpus in enumerate(in_num_gpus_in_machines):
        cuda_ids_per_machine.append(
            [
                (machine_idx, i)
                for i in range(num_gpus)
                for _ in range(in_num_parallel_runs_per_gpu)
            ]
        )

    cuda_ids = []
    for i in range(len(in_num_gpus_in_machines)):
        cuda_ids.extend(cuda_ids_per_machine[i])

    num_scripts = len(cuda_ids)
    return cuda_ids, num_scripts


date_str = datetime.now().strftime("%Y%m%d-%H")

# additional constant config str for all runs. See train_skill_hf.py --config-str for usage.
# additional_config_str = "num_workers=6,segment_traj_reconstruction_loss_coeff=1.0,segment_traj_reconstruction_smoothness_loss_coeff=0.0,index_sampling_noise=0.0,\
# normalization_timestep=first,padding_side=right,target_sampling_frequency=5,metrics_decoder_type=MetricsDecoderSimpleMLP,\
# lap_encoder_type=TransformerTrajEncoder,lap_decoder_type=LSTMDecoder,global_num_epochs=321"
config_epochs = {
    # "global_num_epochs": [("e1k", 1000)],
}
# Define the configurations as a dictionary with abbreviations as tuples (abbr, full_name)
# configs_list = []
# additional_config_str = "segment_traj_reconstruction_smoothness_loss_coeff=0.0,index_sampling_noise=0.0,\
# normalization_timestep=first,padding_side=right,global_num_epochs=101"
category_index_type = "no_steering_no_turn"
if category_index_type == "default":
    teacher_action_num_categories = max(CategoryDictIndex.DEFAULT.value.values()) + 1
elif category_index_type == "no_steering":
    teacher_action_num_categories = (
        max(CategoryDictIndex.NO_STEERING.value.values()) + 1
    )
elif category_index_type == "no_steering_no_turn":
    teacher_action_num_categories = (
        max(CategoryDictIndex.NO_STEERING_NO_TURN.value.values()) + 1
    )
elif category_index_type == "no_steering_no_turn_no_brake":
    teacher_action_num_categories = (
        max(CategoryDictIndex.NO_STEERING_NO_TURN_NO_BRAKE.value.values()) + 1
    )
elif category_index_type == "basic":
    teacher_action_num_categories = max(CategoryDictIndex.BASIC.value.values()) + 1

additional_config_str = f"valid_annotation_categories_index_dict_type={category_index_type},teacher_action_num_categories={teacher_action_num_categories},global_num_epochs=150,use_gaussian_dropout=False"
config_epochs = {
    # "global_num_epochs": [("e1k", 1000)],
}
# Define the configurations as a dictionary with abbreviations as tuples (abbr, full_name)
configs_list = []


config_best_model = {
    # "lap_encoder_type": [
    #     # ("Cross", "LapEncoderWithCrossAttentionFusion"),
    #     ("Transformer", "TransformerTrajEncoder"),
    # ],
    # "lap_decoder_type": [
    #     # ("segD", "SegmentTrajectoryDecoder"),s
    #     # ("transD", "TransformerDecoder"),
    #     ("lstmD", "LSTMDecoder"),
    # ],
    # "segment_traj_prediction_mse_loss_coeff": [
    #     ("traj1", 1.0),
    #     ("traj10", 10.0),
    # ],
    # "snippet_teacher_action_prediction_loss_coeff": [
    #     ("act1", 1.0),
    #     ("act10", 10.0),
    # ],
    # "lap_decoder_type": [("transD", "TransformerDecoder"), ("lstmD", "LSTMDecoder")],
    # "lap_metrics_encoder_type":[('met_none', 'MetricsDecoder'), ('metTrans', 'MetricsTransformerDecoder')],
    # "metrics_decoder_type": [("met", "MetricsDecoderSimpleMLP")],
    # "normalization_timestep": [("nor_f", "first")],
    # "padding_side": [("pd_r", "right")],
    **config_epochs,
    # "hidden_dim": [
    #     ("h32", 32),
    #     # ("h64", 64),
    #     # ("h128", 128),
    #     # ("h256", 256),
    # ],
    # "side_channel_dropout": [("scd01", 0.1), ("scd05", 0.5), ("scd1", 0.9)],
    # "transformer_dropout": [("tra_d0", 0.0), ("tra_d15", 0.15)],
    # "gaussian_dropout_ratio": [("gau_d0", 0.0), ("gau_d15", 0.15)],
    # "num_attention_layers": [("at_lay1", 1), ("at_lay2", 2)],
    # "transformer_hidden_dim_multiplier": [
    #     # ("tra_hid_dim1", 1),
    #     ("tra_hid_dim2", 2),
    #     ("tra_hid_dim3", 3),
    # ],
    # "num_attention_heads": [("at_head4", 4), ("at_head8", 8)],
    # "num_layers": [("lay2", 2), ("lay3", 3)],
    # "cf_use_metrics_prediction": [("S", True), ("nS", False)],
    "cf_use_metrics_prediction": [("S", True),],
    # "segment_traj_prediction_mse_loss_coeff": [("T", 2.0), ("nT", 0.0)],
    "segment_traj_prediction_mse_loss_coeff": [("T", 2.0),],
    # "teacher_action_future_seq_len_in_sec": [("teachFut5", 5), ("teachFut3", 3),("teachFut1", 1)],
    "teacher_action_future_seq_len_in_sec": [ ("teachFut3", 3)],
    # "cf_map_channel_dropout": [("mdrop2", 0.2),("mdrop4", 0.4)],
    "cf_map_channel_dropout": [("mdrop4", 0.4)],
    "batch_size": [("b64", 64),("b128", 128),("b256", 256),],
    "seed": [
        ("s42", 42),
        ("s1", 1),
        ("s2", 2),
        ("s3", 3),
        ("s5", 5),
        ("s7", 7),
        ("s8", 8),
        ("s9", 9),
    ],
    # "predict_next_lap_metrics": [("curr_lap", False), ("next_lap", True)],
    # "weight_decay": [
    #     ("wd1e-3", 1e-3),
    #     ("wd3e-3", 3e-3),
    # ],
    # "residual_connection": [("res", True), ("no_res", False)],
    # "lap_emb_use_cls_global_token": [("lap_cls", True), ("lap_max", False)],
}
# configs_list.append(config_right_padding)
# configs_list.append(config_left_padding)
configs_list.append(config_best_model)


script_output_dir = (
    f"~/model_outputs/aic_bsd/training_script/{wandb_run_group}-{date_str}/"
)
# script_output_dir = os.path.expanduser(script_output_dir)
Path(script_output_dir).expanduser().mkdir(parents=True, exist_ok=True)
if use_sagemaker:
    script_output_dir = f"/opt/ml/model/logs/{wandb_run_group}-{date_str}/"
print("run this command to create the output directory\n")
mkdir_instruction = f"mkdir -p {script_output_dir}"
print(mkdir_instruction)
print("")


def split_list(l, n):
    k, m = divmod(len(l), n)
    return [l[i * k + min(i, m) : (i + 1) * k + min(i + 1, m)] for i in range(n)]


def generate_training_scripts(configs_list):
    global num_gpus_per_machines, num_run_per_gpu
    # Calculate commands per script
    runs = generate_runs(configs_list)

    # Filterout runs with both traj and snippet loss coeff set to 0
    runs = [
        r
        for r in runs
        if not (
            "segment_traj_prediction_mse_loss_coeff=0.0" in r.arguments
            and "snippet_teacher_action_prediction_loss_coeff=0.0" in r.arguments
        )
    ]
    random.shuffle(runs)
    total_runs = len(runs)

    cuda_ids, num_scripts = compute_cuda_allocation(
        num_gpus_per_machines, num_parallel_runs_per_gpu
    )
    if use_sagemaker:
        # When use sagemaker, we want to run all runs in parallel.
        num_machines = math.ceil(total_runs / sm_num_total_run_per_gpu)
        num_gpus_per_machines = [1] * num_machines
        cuda_ids, num_scripts = compute_cuda_allocation(
            num_gpus_per_machines, num_parallel_runs_per_gpu
        )

    runs_split_idx = split_list(list(range(total_runs)), num_scripts)
    # remove all scripts in script_dir
    for script_name in script_dir.glob("training-g*.sh"):
        script_name.unlink()
    previous_machine_idx = -1
    scripts_instructions = []
    instructions = []
    first_script_per_machine = True

    # Generate multiple training scripts
    for script_idx in range(num_scripts):
        machine_idx = cuda_ids[script_idx][0]
        cuda_id = cuda_ids[script_idx][1]
        if machine_idx != previous_machine_idx:
            print(f"\n# Run these commands on machine {machine_idx}")
            previous_machine_idx = machine_idx
            if instructions:
                instructions.append("wait")
                scripts_instructions.append(instructions)
            instructions = [mkdir_instruction]
            first_script_per_machine = True

        script_name = f"training-g{script_idx+1}_cuda{cuda_id}_machine{machine_idx}.sh"
        with open(script_dir / script_name, "w") as f:
            f.write("#!/bin/bash\n")
            f.write(f"set -xe\n")
            f.write(f"export CUDA_VISIBLE_DEVICES={cuda_id}\n")
            f.write(f"export WANDB_RUN_GROUP={wandb_run_group}\n\n")

            # Write commands for this script
            for run_idx in runs_split_idx[script_idx]:
                run = runs[run_idx]
                config_str = ",".join(run.arguments)
                if additional_config_str:
                    config_str += f",{additional_config_str}"

                # Create the command with both the config string and the run description
                cmd = f"python -u {TRAINING_SCRIPT} --run-description {run.name} --config-str '{config_str}'\n"
                f.write(cmd)

            # Make the script executable
            os.chmod(script_dir / script_name, 0o755)
        # print the actual cmd shell
        ins = f"bash ./{script_dir_name}/{script_name}  &> {script_output_dir}/{script_name}.log &"
        if first_script_per_machine:
            ins = f"bash ./{script_dir_name}/{script_name} &"
            first_script_per_machine = False
        instructions.append(ins)
        print(ins)
    scripts_instructions.append(instructions)

    print(
        f"#Generated {num_scripts} training scripts with {total_runs} total training runs\n  Running {num_parallel_runs_per_gpu} jobs per GPU."
    )
    print("{} training runs per script.".format(total_runs / num_scripts))
    print("To stop background jobs, run:\n    kill $(jobs -p)\n     pkill -ef aic_bsd")
    return scripts_instructions


def write_sagemaker_training_scripts(scripts_instructions):
    script_names = []
    for machine_idx, instructions in enumerate(scripts_instructions):
        script_name = f"{script_dir_name}/training-sagemaker-machine{machine_idx}.sh"
        script_names.append(script_name)
        with open(script_name, "w") as f:
            f.write("#!/bin/bash\n")
            f.write(f"set -xe\n")
            f.write(f"export WANDB_RUN_GROUP={wandb_run_group}\n\n")
            f.write("\n".join(instructions))
    return script_names


def main():
    # Generate all combinations of configurations
    scripts_instructions = generate_training_scripts(configs_list)

    if use_sagemaker:
        sm_scripts = write_sagemaker_training_scripts(scripts_instructions)
        # sm_scripts = sm_scripts[:1]
        val = input(
            f"Will start {len(sm_scripts)} sagemaker jobs.\nDo you want to start sagemaker sweep? (y/n): "
        )
        if val.lower() not in ["y", "yes"]:
            return
        print("Starting sagemaker sweep")

        from hail_launch.launch_info import Run, RunConfigSagemaker, Sweep
        from hail_launch.launch_sweep import launch_sagemaker_sweep

        runs = generate_runs(configs_list)
        runs = [Run(f"run-{i}", [s]) for i, s in enumerate(sm_scripts)]
        sagemaker_run_config = RunConfigSagemaker(
            instance_type="ml.g6.4xlarge",
            max_training_time=100,
        )
        sweep = Sweep(
            f"aic_bsd-sm-{wandb_run_group}",
            runs,
            training_cmd="bash ",
            training_script="",
            sagemaker_run_config=sagemaker_run_config,
            setup_script="aic_bsd_model/training/aic_sagemaker_setup.sh",
            post_training_script="aic_bsd_model/training/aic_sagemaker_post_training.sh",
        )
        launch_sagemaker_sweep(sweep)


if __name__ == "__main__":
    main()
