  

Install your conda environment by running

## Install conda/micromamba

**conda and micromamba, they are used interchangeably**, micromamba is the preferred default

```

# micromamba is recommendated for new installs

# This will instsall micromamba to the default localtion

"${SHELL}" <(curl -L micro.mamba.pm/install.sh) < /dev/null

  

bash # Or zsh if you are using it, to refesh your shell

```

## Install conda environment

```

micromamba env create -f environment.bsd.yml

# Install pre-commit-hook

micromamba run -n aic_bsd pre-commit install --allow-missing-config

```

Activate the conda environment by running

```

micromamba activate aic_bsd

```
## Dataset folders and organization
1. Create folders named  `~/Data/24-D-16` and `~/Data/24-D-05` to store the uncoached and coached datasets respectively
2. Download  `s3://tri-hid-data-shared-autonomy/24-D-16/model_training_data/trajectory_segments_map_seg_ids/ ` into the `~/Data/24-D-16 folder`. So the path should look  `~/Data/24-D-16/trajectory_segments_map_seg_ids`. This folder contains multiple folders each of which corresponds to a trial. Each trial folder contains the segmented trajectory as well as lap level metrics.
    ``` aws s3 cp --recursive s3://tri-hid-data-shared-autonomy/24-D-16/model_training_data/trajectory_segments_map_seg_ids  ~/Data/24-D-16/trajectory_segments_map_seg_ids --profile rad```
2.  Download  `s3://tri-hid-data-shared-autonomy/24-D-16/trials_final/`  into  `~/Data/24-D-16`  . The full path should like  `~/Data/24-D-16/trials_final`  . Currently the data in this folder is not actually being used, but for completeness we will have it now
    ``` aws s3 cp --recursive s3://tri-hid-data-shared-autonomy/24-D-16/trials_final/  ~/Data/24-D-16/trials_final --profile rad```
3. Download 24-D-05
```aws s3 cp --recursive --only-show-errors s3://tri-hid-data-shared-autonomy/24-D-05/trials_final/  ~/Data/24-D-05/trials_final --profile rad
 aws s3 cp --recursive --only-show-errors s3://tri-hid-data-shared-autonomy/24-D-05/trajectory_segments_map_seg_ids/  ~/Data/24-D-05/trajectory_segments_map_seg_ids --profile rad
 aws s3 cp --recursive --only-show-errors s3://tri-hid-data-shared-autonomy/24-D-05/trajectory_snippets/  ~/Data/24-D-05/trajectory_snippets --profile rad
```
3.  The code is in  [https://github.shared-services.aws.tri.global/tri-projects/aic_bsd_model](https://github.shared-services.aws.tri.global/tri-projects/aic_bsd_model). The folder structure should be self explanatory.
4.  Create conda environment using the yml. Note that, by default numpy version installed will be > 2.0. Pytorch was giving some errors due to that, therefore uninstall and install numpy 1.26.4. Yml fix needs to happen
5.  The train script is named  `train_skill_encoder.py`  and is located in the  `training` folder. There is config dict (currently hand coded in the script) where the training params are set. There are flags for using cls tokens, cost coeffs etc. A few different args (mainly for overfitting tests) can be passed via terminal. Example usage would like  `python train_skill_encoder.py --run_description "RUN_DESCRIPTION"`


# Slurm training

`python aic_bsd_model/slurm/start_slurm_training.py --dry-run false`

```
# List all runs
slist 
# see run output
vim slurm_output/6122
```