
# hid_common

For storing data processing and other general scripts for HID and friends. The scripts in this repository can be used to process Thunderhill track days as well as sessions in the compact simulator.

## How to Contribute
* To contribute code to this repository, please open a PR and get approval from 1 assigned reviewer before merging in.
* Each PR should have a description specifying what it adds.
  * Commit titles should be concise so that they fit on a single line. If more information is needed, add it on subsequent lines in the commit message.
* PRs should typically be less than 500 lines, and limited in time. Discuss with potential reviewers on longer PRs. If a feature branch is needed -- know in advance what is the merging policy.
* When there are conflicts with the base branch, PRs should be rebased, not merged. This maintains git history and prevents conflicts with other pending PRs.
* PRs should include new or updated documentation on how to run any newly added functionality.
* If the PR has a new functionality, there should be a way to run it, mention it in the PR comment, including a run cmd if its new, unless to really minor, or the new run script/test is documented in the documentation that is added in the PR (e.g. README).
* PRs that temporarily break the tests should be for a very short time, with a good reason. Send a public service announcement to the appropriate channel (e.g. crest_collaboration/interaction-foundation-models/shared_autonomy channel or similar, depends on repo/change)"


## Install conda/micromamba
**conda and micromamba, they are used interchangeably**, micromamba is the preferred default

**micromamba is recommended for new installs**

This will install micromamba to the default location

```
"${SHELL}" <(curl -L micro.mamba.pm/install.sh)  < /dev/null

# Once the above line has finished, run the next line to refresh your shell
bash  # Or zsh if you are using it
```
## Install conda environment

```
micromamba env create -f environment.hid_common.yml
# Install pre-commit-hook
micromamba run -n hid_common pre-commit install --allow-missing-config
```
Activate the conda environment by running
```
micromamba activate hid_common
```
## Pip install repo into micromamba environment
Finally, with the micromamba env active, run the following:
```
pip install -e . --config-settings editable_mode=compat
```

## How to run setfit_classification.py

### Dependencies

```setfit_classification.py``` relies on a modified version Hugging Face's implementation of SetFit implementation. Download the TRI version of setfit and pip install it within the hid_common conda environment

```
    git clone https://github.com/ToyotaResearchInstitute/setfit
    cd setfit/
    pip install -e . 
```
### Running the script

    python setfit_classification.py --coded-segments-pathname PATH_TO_DATA_FOLDER/coded_segments.csv
    --other-sentences-pathname PATH_TO_DATA_FOLDER/misc_sentences.txt
    --sentence-level-transcript-input-dir
    PATH_TO_DATA_FOLDER/GoPro_Transcripts/ --visualize-embedding True --use-full-train-set True

## How to run process_thunderhill_data.py

### Data organization

## Thunderhill folders structure

In order to the run this script successfully, you will need three different data folders

1. GoPro_MediaInfo - Output of the unix utility mediainfo run on all goPro videos organized according to date, vehicle and camera type. Used by the script to extract the timestamp of modification and duration. [Box download link for GoPro_MediaInfo](https://app.box.com/s/********************************)
2. GoPro_Transcripts - Output of running OpenAI's Whisper API (at the sentence-level) on all GoPro videos organized according to date, vehicle and camera type. For each video X.MP4 there is a corresponding X.srt which contains the transcription for that video. [Box download link for GoPro_Transcripts](https://app.box.com/s/********************************)
3. GoPro_Transcripts_Word-Level - Output of running OpenAI's Whisper API (at the word-level - could be one or a few words) on all GoPro videos organized according to date, vehicle and camera type. For each video X.MP4 there is a corresponding X.srt which contains the transcription for that video. [Box download link for GoPro_Transcripts_Word-Level](https://app.box.com/s/********************************)
4. ROS - Folder containing the data extracted from ROSbag stored as pkl files. Currently, only has a data from Leia. In order to download the ROS data, please go to [top-level url](https://app.box.com/folder/198012110496) and download the ROS folder and place it in the data path.
5. Data Association excel sheet - This is the excel sheet which contains the detailed session by session information organized according to vehicle and date. [Google Drive link to the Data Association](https://docs.google.com/spreadsheets/d/1aYOo-h_wd0aqMKzRM5RBmJnPD7ecP2fgWA2irjv6yuM/edit?usp=share_link).
6. Track map csv - This is a csv with the track map in ENU coordinates. [Google Drive link to the map file](https://drive.google.com/file/d/1No6LJadkjbcQ8vMdhtTS0clm2Jig9c4Y/view?usp=share_link). Columns are: ['s', 'kappa', 'dt', 'refline/x', 'refline/y', 'refline/z', 'refline/v', 'inner_edge/x', 'inner_edge/y', 'outer_edge/x', 'outer_edge/y', 'e_min', 'e_max', 'image', 'image_metadata', 'seq_num', 'course_id', 'name', 't']


```markdown
├── Data
│   ├── Thunderhill
│   │   ├── DataAssociation.xlsx
│   │   ├── track.csv
│   │   ├── ROS
│   │   │   ├── 2022-11-07
│   │   │   │   ├── ROS
│   │   │   │   │   ├── Leia
│   │   │   │   │   ├── run_00x
│   │   │   │   │   │   │   ├── bag
│   │   │   │   │   │   │   │   ├── bag_0.db3
│   │   │   │   │   │   │   │   ├── metadata.yaml
│   │   │   │   │   │   │   ├── params
│   │   │   │   │   │   │   │   ├── params.yaml
│   │   │   │   │   │   │   │   ├── participants.yaml
│   │   │   │   │   │   │   ├── save
│   │   │   │   │   │   │   │   ├── all_processed_data.pkl
│   │   │   │   │   │   │   ├── metadata.yaml
│   │   │   │   │   │   │   ├── th_nov_ros_signals.yaml
│   │   ├── GoPro_MediaInfo
│   │   │   ├── 2022-11-07
│   │   │   │   ├── GoPro
│   │   │   │   │   ├── Leia
│   │   │   │   │   │   ├── cabin
│   │   │   │   │   │   │   ├── G*.txt
│   │   │   │   │   │   ├── front
│   │   │   │   │   │   │   ├── G*.txt
│   │   ├── GoPro_Transcripts
│   │   │   ├── 2022-11-07
│   │   │   │   ├── GoPro
│   │   │   │   │   ├── Leia
│   │   │   │   │   │   ├── cabin
│   │   │   │   │   │   │   ├── G*.srt
│   │   │   │   │   │   ├── front
│   │   │   │   │   │   │   ├── G*.srt
│   │   ├── GoPro_Transcripts_Word-Level
│   │   │   ├── 2022-11-07
│   │   │   │   ├── GoPro
│   │   │   │   │   ├── Leia
│   │   │   │   │   │   ├── cabin
│   │   │   │   │   │   │   ├── G*.srt
│   │   │   │   │   │   ├── front
│   │   │   │   │   │   │   ├── G*.srt

```
Note that, for GoPro_MediaInfo and GoPro_Transcripts, the folder structure at the date level will be repeated for 11/8, 11/9 and 11/10 as well and the folder structure at the vehicle name level will be repeated for FR-S and Supra. 

## Data analysis Python environment data structures

* After mcap loading and caching we have two main data dictionaries that are used to store data and annotations:
  * `data_dict` - contains a map from an episode UID to a pandas frame with the raw data samples from the mcap.
  * `annotation_data_dict` - contains a map from an episode UID to dictionary that maps annotator sources IDs to annotation data. Each annotation data is a dictionary with either `results` field, or similar fields, that capture annotations. The results field can be a numpy array that matches a detection or lack thereof at specific timestamps, but a `timestamps` key:value pair has to be included to match the timestamps. Alternatively, annotations can include a list of dictionaries with {time_interval, label} for individual detections.

### Running the script

The script can be run using the following command. When run to completion, currently, it stops before exiting using an embed() command. At this point, all the data dictionaries will contain the necessary processed data to proceed with. Note that, to generate transcript_labels.pkl please run setfit_classification.py first

    python ./process_thunderhill_data.py --sentence-level-transcript-input-dir PATH_TO_DATA_FOLDER/GoPro_Transcripts/ --rosbag-input-dir PATH_TO_DATA_FOLDER/ROS/ --session-xlsx PATH_TO_DATA_FOLDER/DataAssociation.xlsx --mediainfo-input-dir PATH_TO_DATA_FOLDER/GoPro_MediaInfo  --word-level-transcript-input-dir PATH_TO_DATA_FOLDER/GoPro_Transcripts_Word-Level/ --track-map-csv PATH_TO_DATA_FOLDER/track.csv --transcript-label-pkl PATH_TO_DATA_FOLDER/new_transcript_labels.pkl

## Converting db3 rosbags to mcap rosbags

Download the appropriate mcap-cli tools from 

    https://mcap.dev/guides/cli

By default, running the wget install command will install the mcap tool in the home directory. Ensure that the latest version is installed. As of the writing of this README the latest version is 0.0.38. 

    cd ~/data_sources/compact_sim
    ./mcap_convert_upload.sh $LOG_DIR $PATH_TO_MCAP_CLI_TOOL_FILE $FLAG_FOR_UPLOAD $S3_BUCKET_NAME

## Setting default data locations for your system
In order to set custom default values for your system, modify the paths in `workspace_paths.yaml` to be the correct ones for your system. If you don't want a default set, leave the string empty

_FYI_
* If you set an argument in the command line, it will override these defaults!

## Opening data environment

* Jupyter notebook example:
    * Config AWS access
      * In hid_common mamba environment run `aws configure sso`
      * When prompt put in
        * ```
          SSO session name (Recommended): tri-sso
          sso_start_url:                 https://d-9067152ece.awsapps.com/start#
          sso_region:                    us-east-1
          sso_registration_scopes:       sso:account:access
          ```
        * Click "Allow" on the web page when prompt
        * Select Account `hid-data-13504`
        * Press "Enter" when prompt, setting all to default configuration
        * Then try `aws s3 ls --profile HID-Data-Shared-Autonomy-************`, it should return a list of bucket names, `tri-hid-data-shared-autonomy` should be in there
    * SSO login
      * Do `aws sso login  --profile HID-Data-Shared-Autonomy-************`
        * Do the above login command if you see this error `fatal error: Error when retrieving token from sso: Token has expired and refresh failed`
    * Download data with 
      * Example data, change s3 path as needed 
        * `aws s3 sync s3://tri-hid-data-shared-autonomy/23-D-04/ ~/data/shared_autonomy/tri-hid-data-shared-autonomy/23-D-04/ --profile HID-Data-Shared-Autonomy-************`
          * Append the following to include only .mcap files: `--exclude "*" --include "*.mcap"`
    * Download unit test data by running
      * `./bash_scripts/get_unittest_data.sh`
      
      This will download data to `~/hid_data`. If you are creating a docker image copy content from `~/hid_data` to `data` folder of this repo before creating the build.
      * `cp -R ~/hid_data/* data`
          
    * More info on AWS SSO access, at the [confluence page](https://toyotaresearchinstitute.atlassian.net/wiki/spaces/IE/pages/482149393/AWS+Single+Sign-On+-+End+User+Documentation).
      
    * To use the data environment in jupyter notebook, run: `python -m ipykernel install --user --name=hid_commmon`
    * Open jupyter notebook:

    ```jupyter notebook ~/hid_common/data_sources/notebooks/run_data_environment.ipynb```

    * Open a browser based on the commandline instruction in the jupyter server. The URL should look something like `http://localhost:8888/?token=b24757c5ad86b03a24abf1cef64ca4a2caa938c430eb886c`.
    * In the jupyter notebook in the browser, update the folder names in the $parse_arguments$ call, e.g. `mcap-input-folder`, `compactsim-cache-folder`, or `track-map-csv`.
    * Play the jupyter notebook, update it to filter, visualize, etc.

* Data processing script:
    * See, `scripts/examples/visualize_spinouts.py` for an example that detects possible spinouts, visualizes them, and leaves an IPython command prompt as an interactive shell. To run, add the relevant paths/flags, e.g.:

  ```python ~/hid_common/scripts/examples/visualize_spinouts.py --mcap-input-folder ~/Downloads/tri-hid-data-shared-autonomy/unit_test_logs  --compactsim-cache-folder ~/intent/compactsim_cache --track-map-csv ~/Downloads/Thunderhill_data/track.csv```

    * See, `scripts/examples/visualize_overtake.py` for an example that detects possible overtakes, visualizes them, and leaves an IPython command prompt as an interactive shell. To run, add the relevant paths/flags, e.g.:

  ```python ~/hid_common/scripts/examples/visualize_overtake.py --mcap-input-folder ~/Downloads/tri-hid-data-shared-autonomy/unit_test_logs/overtake_left/  --compactsim-cache-folder ~/intent/compactsim_cache --track-map-csv ~/Downloads/Thunderhill_data/track.csv```

    * See, `scripts/examples/visualize_out_of_bounds.py` for an example that detects possible out-of-bound regions, visualizes them, and leaves an IPython command prompt as an interactive shell. To run, add the relevant paths/flags, e.g.:

  ```python ~/hid_common/scripts/examples/visualize_out_of_bounds.py --mcap-input-folder ~/Downloads/tri-hid-data-shared-autonomy/unit_test_logs/  --compactsim-cache-folder ~/intent/compactsim_cache --track-map-csv ~/Downloads/Thunderhill_data/track.csv```

* Cookbook
    * Examples of loading the data environment, running many of these scripts, as well as examples of visualizations lives in the `~/hid_common/data_sources/notebooks/cookbook.ipynb` jupyter notebook.

## [Annotation Data Unifier](https://docs.google.com/document/d/1AvOZK4CIeQUlVRe5bwleFxvIXItvSqO6lJCudjR9jLE/edit)
1. **Set up the environment**:
    - setup the hid_common environment
2. Download the following files from the specified S3 locations:
    - Annotated JSON files: `s3://tri-hid-data-shared-autonomy/manual_annotation/24-08-instruction_effect2/output/24-08-instruction-effect-400-data-objects/annotations/consolidated-annotation/consolidation-response/iteration-1/`
    - Annotated video source files: `s3://tri-hid-data-shared-autonomy/manual_annotation/24-08-instruction_effect2/input_data/24-D-05`
    - Trial files: `s3://tri-hid-data-shared-autonomy/24-D-05/trials_final/`
      - This script requires the trial files in Parquet format. Use the command below to download only the trial Parquet files.
      - `aws s3 cp s3://tri-hid-data-shared-autonomy/24-D-05/trials_final/ ~/trial_finals --recursive --exclude "*" --include "*.trial.parquet"`
`
3. **Run the Script**:
    - Use the command-line interface to pass the necessary arguments.
    - For example:
        
        ```bash
        python annotation_data_unifier.py \
          --track_map_path "test/resources/track.csv" \
          --trial_dir "/home/<USER>/trials_final/" \
          --output_dir "/home/<USER>/visualization_output/" \
          --video_dir "/home/<USER>/downloaded_videos/" \
        	--input_dir "/home/<USER>/input"
          --fps 5 \
          --dpi 300 \
          --time_window 10 \
          --trial_version 2
          --max_worker 4
          --max_worker_viz 2
          --job_id example
          --merge_viz
        ```
        
4. **View the Results**:
    - After the script finishes running, visualizations and parsed data will be saved to the `output_dir`.

## Data Introspection
Information about ros topic names, as well as keys in the dataframes produced by hid_common and the information that is to be found in each of those can be found on the following [confluence page](https://toyotaresearchinstitute.atlassian.net/wiki/spaces/HAIL/pages/3064823811/HID_Common+Data+Architecture).
