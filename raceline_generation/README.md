
# Track raceline generation

Metions the steps to follow in order to generate and visualise raceline(refline) for a given track.

##  Necessary installation steps
clone the following repo https://github.com/TUMRT/online_3D_racing_line_planning.git
Follow the steps below which are also mentioned in the READMe of the above repository.
* Install Acados following https://docs.acados.org/installation/#linux-mac.
* Install the Python interface following https://docs.acados.org/python_interface/index.html
* Install other used Python packages:
    ```
    pip install -r requirements.txt
    ```

## Convert existing track to epected format
* One has to run convert_track script in order to convert the track to required format. Run the below command with appropriate paths:
```
python raceline_generation/convert_track.py  --track path/to/track-csv --output-dir destination-dir

```
* Place the converted track csv in data/raw_track_data of online_3D_racing_line_planning respository.
* Activate the environment that you created while installing acados and its dependencies.
* Follow the Readme from this point -> https://github.com/TUMRT/online_3D_racing_line_planning?tab=readme-ov-file#:~:text=above%20scripts%20accordingly.-,2.%20Track%20Data,-To%20create%20a
* Make sure to open and edit all those python files which you run, as track name should be the same as your converted file.
