import argparse

import pandas as pd


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--track", type=str, required=True, help="Path to the track csv")
    parser.add_argument(
        "--output-dir", type=str, required=True, help="Path to destination directory"
    )
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()
    track_map = pd.read_csv(args.track)
    selected_columns = track_map[["inner_edge/x", "inner_edge/y", "outer_edge/x", "outer_edge/y"]]
    # rename the columns as per the required format
    selected_columns.columns = ["left_bound_x", "left_bound_y", "right_bound_x", "right_bound_y"]
    # set the z co-ordinates to 0 as they are not provided for inner and outer edges
    selected_columns["left_bound_z"] = 0
    selected_columns["right_bound_z"] = 0
    # save the new csv
    file_path = args.output_dir + "/mount_panorama_bounds_3d.csv"
    selected_columns.to_csv(file_path, index=False)
