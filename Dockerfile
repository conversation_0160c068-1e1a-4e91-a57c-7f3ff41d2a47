FROM mambaorg/micromamba:jammy

WORKDIR /opt/hid_common

# Copy test data
COPY data ~/hid_data

# Copy Micromamba environment yml
COPY environment.hid_common.yml .

COPY pyproject.toml .

RUN micromamba env create -n hid_common -f environment.hid_common.yml -c conda-forge -y
RUN micromamba install -n hid_common -y -c conda-forge git invoke
RUN micromamba run -n hid_common pip install -e .

RUN micromamba clean --all --yes

ARG MAMBA_DOCKERFILE_ACTIVATE=1
