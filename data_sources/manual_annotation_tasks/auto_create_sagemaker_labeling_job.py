import argparse
import os
from pathlib import Path
from typing import Any, Dict, <PERSON><PERSON>

import boto3
from envyaml import EnvYAM<PERSON>


def get_boto3_client(
    service_name: str, REGION_NAME: str, ACCESS_KEY: str, SECRET_KEY: str, SESSION_TOKEN: str
) -> Any:
    """Boto3 client is created for provided service name

    Args:
        service_name (str): Service name for which boto3 client will be created.
        REGION_NAME (str): AWS Region name
        ACCESS_KEY (str): AWS access key
        SECRET_KEY (str): AWS secret key
        SESSION_TOKEN (str): AWS session token.
    Returns:
        Any: Boto3 client.
    """
    client = boto3.client(
        service_name,
        region_name=REGION_NAME,
        aws_access_key_id=ACCESS_KEY,
        aws_secret_access_key=SECRET_KEY,
        aws_session_token=SESSION_TOKEN,
    )
    return client


def non_empty_string(value: str) -> str:
    """Custom type function to ensure the string is not empty."""
    if not value:
        raise argparse.ArgumentTypeError(f"Value cannot be empty.")
    return value


def make_template(save_fname: str = "instructions.template") -> str:
    """Create a UI template and store it to the file and return the file name.

    Args:
        save_fname: template file name.

    Returns:
        str: Template file name
    """
    template: str = """<script src="https://assets.crowd.aws/crowd-html-elements.js"></script>

        <script>
        document.addEventListener('DOMContentLoaded', function() {
            const yesRadio = document.querySelector('input[name="actionable_feedback"][value="Yes"]');
            const noRadio = document.querySelector('input[name="actionable_feedback"][value="No"]');
            
            const componentsToToggle = [
                'follow_advice_container',
                'student_action_container',
                'reaction_time_container',
                'type_of_advice_container'
            ];
            
            function toggleComponents(shouldShow) {
                componentsToToggle.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.style.display = shouldShow ? 'block' : 'none';
                    }
                });
            }
            
            function handleRadioChange() {
                if (yesRadio.checked) {
                    toggleComponents(true);
                } else {
                    toggleComponents(false);
                }
            }
            
            yesRadio.addEventListener('change', handleRadioChange);
            noRadio.addEventListener('change', handleRadioChange);
            
            // Initialize the components visibility based on default "No" selection
            handleRadioChange();
        });
        </script>
        
        <crowd-form>
          <div style="width: 1000px; margin: 0 auto;">
                <h1>Coaching Session Feedback</h1>
        
                <p>Your task is to identify and explain <strong>actionable feedback</strong> in a coaching session. Actionable feedback refers to coaching that can be followed <strong>in-the-moment</strong> (such as "Brake now!" or "Look up!"), not feedback that is abstract or long term ("On the next lap, remember this spot," or "That was great," or "That cone is there for a reason").</p>
                
                <p>Please review the following video and listen to the coach's instructions:</p>
                <div style="text-align: center; margin-bottom: 20px;">
                    <video controls style="width: 120%; ">
                        
                      <source src="{{ task.input.sourceRef | grant_read_access }}" type="video/mp4">
                        <!-- Your browser does not support the video tag. -->
                    </video>
        
                </div>
                
                <div style="text-align: center; margin-bottom: 20px;">
                  <p>{{ task.input.textRef }}</p>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Did the Coach provide actionable feedback to the student?</label>
                    <div>
                        <label style="margin-right: 20px;"><input type="radio" name="actionable_feedback" value="Yes"> Yes</label>
                        <label style="margin-right: 20px;"><input type="radio" name="actionable_feedback" value="No" checked> No</label>
                    </div>
                </div>
                
                <div id="type_of_advice_container" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">What type of advice did the Coach provide? Select all that apply:</label>
                    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                        <div style="border: 1px solid #000; padding: 10px;">
                            <label>Throttle</label><br>
                            <label><input type="checkbox" name="advice" value="Throttle on/Gas"> Throttle on/Gas</label><br>
                            <label><input type="checkbox" name="advice" value="Throttle off/Lift"> Throttle off/Lift</label>
                        </div>
                        <div style="border: 1px solid #000; padding: 10px;">
                            <label>Brake</label><br>
                            <label><input type="checkbox" name="advice" value="Brake on"> Brake on</label><br>
                            <label><input type="checkbox" name="advice" value="Brake off"> Brake off</label>
                        </div>
                        <div style="border: 1px solid #000; padding: 10px;">
                            <label>Turning</label><br>
                            <label><input type="checkbox" name="advice" value="Turn left"> Turn left</label><br>
                            <label><input type="checkbox" name="advice" value="Turn right"> Turn right</label>
                        </div>
                        <div style="border: 1px solid #000; padding: 10px;">
                            <label>Positioning</label><br>
                            <label><input type="checkbox" name="advice" value="Move left"> Move left</label><br>
                            <label><input type="checkbox" name="advice" value="Move right"> Move right</label><br>
                            <label><input type="checkbox" name="advice" value="Move to middle"> Move to middle</label>
                        </div>
                        <div style="border: 1px solid #000; padding: 10px;">
                            <label>Steering Adjustment</label><br>
                            <label><input type="checkbox" name="advice" value="Quicker steering"> Quicker steering</label><br>
                            <label><input type="checkbox" name="advice" value="Smoother steering"> Smoother steering</label><br>
                            <label><input type="checkbox" name="advice" value="Straight steering"> Straight steering</label><br>
                            <label><input type="checkbox" name="advice" value="Less steering"> Less steering</label><br>
                            <label><input type="checkbox" name="advice" value="More steering"> More steering</label>
                        </div>
                        <div style="border: 1px solid #000; padding: 10px;">
                            <label>Other</label><br>
                            <label><input type="checkbox" name="advice" value="Look up/peek"> Look up/peek</label>
                        </div>
                    </div>
                </div>
                
                <div id="follow_advice_container" style="margin-bottom: 15px;">
                  <label style="display: block; margin-bottom: 5px; font-weight: bold;">How well did the student follow the Coach's advice?</label>
                  <div style="display: flex; justify-content: space-between; width: 100%;">
                      <div style="text-align: center;">
                          <label>Not applicable</label><br>
                          <input type="radio" name="follow_advice" value="1">
                      </div>
        
                      <div style="text-align: center;">
                          <label>Not at all</label><br>
                          <input type="radio" name="follow_advice" value="1">
                      </div>
                      <div style="text-align: center;">
                          <label>Slightly</label><br>
                          <input type="radio" name="follow_advice" value="2">
                      </div>
                      <div style="text-align: center;">
                          <label>Somewhat</label><br>
                          <input type="radio" name="follow_advice" value="3">
                      </div>
                      <div style="text-align: center;">
                          <label>Moderately</label><br>
                          <input type="radio" name="follow_advice" value="4">
                      </div>
                      <div style="text-align: center;">
                          <label>Mostly</label><br>
                          <input type="radio" name="follow_advice" value="5">
                      </div>
                      <div style="text-align: center;">
                          <label>Very</label><br>
                          <input type="radio" name="follow_advice" value="6">
                      </div>
                      <div style="text-align: center;">
                          <label>Perfectly</label><br>
                          <input type="radio" name="follow_advice" value="7">
                      </div>
                  </div>
              </div>
                
                <div id="student_action_container" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">What did the student do?</label>
                    <input type="text" name="student_action" style="width: 100%; padding: 8px; margin-top: 5px;" placeholder="The student...">
                </div>
                
                <div id="reaction_time_container" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">How long did the student take to act according to the Coach's instruction?</label>
                    <input type="number" name="reaction_time" step="0.1" style="width: 100%; padding: 8px; margin-top: 5px;" placeholder="Time in seconds">
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <input type="submit" value="Submit" style="padding: 10px 20px; font-size: 16px; cursor: pointer;">
                </div>
            </div>
        </crowd-form>
    """
    with open(save_fname, "w") as f:
        f.write(template)


def parse_arguments() -> Any:
    """Parse the command line arguments.

    Returns:
        Any: Object of parsed argumets
    """
    parser = argparse.ArgumentParser(
        description="Auto Create Ground Truth Labeling Job Parameters."
    )

    # Following variables must be passed through either ENV or command line arguments.
    # If not found in any of the form, error will be raised.
    parser.add_argument(
        "--JOB_NAME",
        type=non_empty_string,
        default=os.getenv("JOB_NAME"),
        required=os.getenv("JOB_NAME") is None,
        help="Name of the job (must be provided either as an argument or an environment variable).",
    )

    parser.add_argument(
        "--S3_BUCKET_NAME",
        type=non_empty_string,
        default=os.getenv("S3_BUCKET_NAME"),
        required=os.getenv("S3_BUCKET_NAME") is None,
        help="S3 bucket name (must be provided either as an argument or an environment variable).",
    )

    parser.add_argument(
        "--ROLE_ARN",
        type=non_empty_string,
        default=os.getenv("ROLE_ARN"),
        required=os.getenv("ROLE_ARN") is None,
        help="Role ARN (must be provided either as an argument or an environment variable).",
    )

    parser.add_argument(
        "--WORK_TEAM_ARN",
        type=non_empty_string,
        default=os.getenv("WORK_TEAM_ARN"),
        required=os.getenv("WORK_TEAM_ARN") is None,
        help="Work team ARN (must be provided either as an argument or an environment variable).",
    )
    parser.add_argument(
        "--POST_LAMBDA_FUNC_ARN",
        type=non_empty_string,
        default=os.getenv("PRE_LAMBDA_FUNC_ARN"),
        required=os.getenv("PRE_LAMBDA_FUNC_ARN") is None,
        help="Post Lambda function ARN (must be provided either as an argument or an environment variable).",
    )
    parser.add_argument(
        "--PRE_LAMBDA_FUNC_ARN",
        type=non_empty_string,
        default=os.getenv("POST_LAMBDA_FUNC_ARN"),
        required=os.getenv("POST_LAMBDA_FUNC_ARN") is None,
        help="Pre Lambda function ARN",
    )

    # Following variables may be passed through command line arguments.
    # If not found in command line argument then created using job name.
    parser.add_argument(
        "--ATTRIBUTE_NAME",
        type=non_empty_string,
        required=False,
        help="Attribute name (default: {job_name}_attribute)",
    )

    parser.add_argument(
        "--TEMPLATE_FILE",
        type=non_empty_string,
        required=False,
        help="Template file (default: {job_name}_ui_instructions.template)",
    )

    parser.add_argument(
        "--TASK_KEY_WORDS",
        type=non_empty_string,
        required=False,
        help="Task key words (default: {job_name}_key_word)",
    )

    parser.add_argument(
        "--TASK_TITLE",
        type=non_empty_string,
        required=False,
        help="Title of the task. (default: {job_name}_task_title)",
    )

    parser.add_argument(
        "--TASK_DESCRIPTION",
        type=non_empty_string,
        required=False,
        help='Description of the task. (default: Custom job description for job "{job_name}")',
    )

    parser.add_argument(
        "--HUMAN_WORKERS_PER_DATA_OBJECT",
        type=int,
        required=False,
        default=1,
        help="Human workers per data object",
    )

    parser.add_argument(
        "--TASK_TIME_LIMIT",
        type=int,
        required=False,
        default=300,
        help="Task time limit. (In Seconds)",
    )

    parser.add_argument(
        "--TASK_AVAILABILITY_LIFE",
        type=int,
        required=False,
        default=3600 * 12,
        help="Task availability life. (In Seconds)",
    )

    parser.add_argument(
        "--MAX_CONCURRENT_TASK_COUNT",
        type=int,
        required=False,
        default=1,
        help="Maximum concurrent task count.",
    )

    args = parser.parse_args()

    # Generate the default value if value is non empty string.
    args.TEMPLATE_FILE = (
        args.TEMPLATE_FILE if args.TEMPLATE_FILE else f"{args.JOB_NAME}_ui_instructions.template"
    )
    args.ATTRIBUTE_NAME = (
        args.ATTRIBUTE_NAME if args.ATTRIBUTE_NAME else f"{args.JOB_NAME}-attribute"
    )
    job_name_wo_special_char = args.JOB_NAME.replace("-", "").replace("_", "")
    args.TASK_KEY_WORDS = (
        args.TASK_KEY_WORDS.split(",")
        if args.TASK_KEY_WORDS
        else [f"{job_name_wo_special_char}KeyWord"]
    )
    args.TASK_TITLE = args.TASK_TITLE if args.TASK_TITLE else f"{args.JOB_NAME}_task_title"
    args.TASK_DESCRIPTION = (
        args.TASK_DESCRIPTION
        if args.TASK_DESCRIPTION
        else f"Custom job description for job {args.JOB_NAME}"
    )

    return args


def load_labeling_job_config(config_file: str, parent_dir: str, **kwargs: Any) -> Dict:
    """
    Load labeling job configuration from a YAML file.

    This function constructs the path to the YAML configuration file using the provided `parent_dir`
    and `config_file` arguments, then loads and parses the YAML file. It returns the configuration
    dictionary, defaulting to an empty dictionary if the specified key is not found.

    Args:
        config_file (str): The name of the YAML configuration file.
        parent_dir (str): The parent directory where the configuration file is located.
        **kwargs (Any): Additional keyword arguments passed to `EnvYAML`.

    Returns:
        Dict: The configuration dictionary loaded from the YAML file. If the key "0" is not found,
              an empty dictionary is returned.
    """
    # Construct the full path to the configuration file
    yaml_config_path: str = str(Path(parent_dir).joinpath(config_file))

    # Load the YAML configuration
    input_obj: EnvYAML = EnvYAML(yaml_file=yaml_config_path, **kwargs)

    # Get the configuration dictionary for the key "0", defaulting to an empty dictionary
    config_dict: Dict = input_obj.get("0", {})

    return config_dict


def parse_s3_uri(s3_uri):
    # Split the path into bucket name and object key
    bucket_name, object_key = s3_uri.split("/", 1)

    return bucket_name, object_key


def create_ui_template_and_upload_to_s3(template_file: str, s3_bucket_uri: str) -> Tuple:
    # Prepare UI template and upload it to S3 bucket.
    make_template(save_fname=template_file)
    s3_client = get_boto3_client(
        "s3",
        os.getenv("AWS_REGION"),
        os.getenv("AWS_ACCESS_KEY"),
        os.getenv("AWS_SECRET_KEY"),
        os.getenv("AWS_SESSION_TOKEN"),
    )

    bucket_name, object_key = parse_s3_uri(s3_bucket_uri)

    object_key: str = f"{object_key}/ui-config/{template_file}"

    s3_client.upload_file(template_file, bucket_name, object_key)

    if os.path.exists(template_file):
        os.remove(template_file)

    return bucket_name, object_key


def generate_labeling_job():
    """Generate a custom labeling job in sagemaker ground truch."""

    # Collect the command line arguments.
    args = parse_arguments()

    labeling_job_config_dir: str = str(
        Path(os.getcwd()).joinpath("boto3_config").joinpath("labeling_job_config")
    )

    # Loading Input Configuration
    input_config = load_labeling_job_config(
        "input_config.yaml", labeling_job_config_dir, S3_BUCKET_NAME=args.S3_BUCKET_NAME
    )
    InputConfig = input_config.get("InputConfig")

    # Loading Output Configuration
    output_config = load_labeling_job_config(
        "output_config.yaml", labeling_job_config_dir, S3_BUCKET_NAME=args.S3_BUCKET_NAME
    )
    OutputConfig = output_config.get("OutputConfig")

    bucket_name, object_key = create_ui_template_and_upload_to_s3(
        args.TEMPLATE_FILE, args.S3_BUCKET_NAME
    )

    UI_TEMPLATE_URI: str = f"s3://{bucket_name}/{object_key}"

    # Loading Human Task Configuration
    human_task_config = load_labeling_job_config(
        "human_task_config.yaml",
        labeling_job_config_dir,
        UI_TEMPLATE_URI=UI_TEMPLATE_URI,
        WORK_TEAM_ARN=args.WORK_TEAM_ARN,
        PRE_LAMBDA_FUNC_ARN=args.PRE_LAMBDA_FUNC_ARN,
        TASK_KEY_WORDS=str(args.TASK_KEY_WORDS),
        TASK_TITLE=args.TASK_TITLE,
        TASK_DESCRIPTION=args.TASK_DESCRIPTION,
        POST_LAMBDA_FUNC_ARN=args.POST_LAMBDA_FUNC_ARN,
        TEMPLATE_FILE=args.TEMPLATE_FILE,
        HUMAN_WORKERS_PER_DATA_OBJECT=str(args.HUMAN_WORKERS_PER_DATA_OBJECT),
        TASK_TIME_LIMIT=str(args.TASK_TIME_LIMIT),
        TASK_AVAILABILITY_LIFE=str(args.TASK_AVAILABILITY_LIFE),
        MAX_CONCURRENT_TASK_COUNT=str(args.MAX_CONCURRENT_TASK_COUNT),
    )
    HumanTaskConfig = human_task_config.get("HumanTaskConfig")
    HumanTaskConfig["TaskKeywords"] = list(HumanTaskConfig["TaskKeywords"])

    # Loading Comman Variables Configuration
    common_var_config = load_labeling_job_config(
        "common_var.yaml",
        labeling_job_config_dir,
        JOB_NAME=args.JOB_NAME,
        ATTRIBUTE_NAME=args.ATTRIBUTE_NAME,
        ROLE_ARN=args.ROLE_ARN,
    )

    RoleArn = common_var_config.get("COMMON_VAR", {}).get("RoleArn")
    LabelingJobName = common_var_config.get("COMMON_VAR", {}).get("LabelingJobName")
    LabelAttributeName = common_var_config.get("COMMON_VAR", {}).get("LabelAttributeName")

    sagemaker_client = get_boto3_client(
        "sagemaker",
        os.getenv("AWS_REGION"),
        os.getenv("AWS_ACCESS_KEY"),
        os.getenv("AWS_SECRET_KEY"),
        os.getenv("AWS_SESSION_TOKEN"),
    )
    sagemaker_client.create_labeling_job(
        LabelingJobName=LabelingJobName,
        LabelAttributeName=LabelAttributeName,
        InputConfig=InputConfig,
        OutputConfig=OutputConfig,
        HumanTaskConfig=HumanTaskConfig,
        RoleArn=RoleArn,
    )


generate_labeling_job()
