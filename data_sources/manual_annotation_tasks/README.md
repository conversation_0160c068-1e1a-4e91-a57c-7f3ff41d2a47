# Types of custom annotation jobs

1. Instruction Effect
2. Instruction Category

In Amazon Sagemaker ground truth, Custom annotation jobs will use custom UI templates.

###### Instruction Effect - UI Template

* tasks/24-08-instruction_effect/annotation.html
 * **Ref. Job on Sagemaker:** prod-24-08-instruction-effect-1k-data-objects--1

###### Instruction Category - UI Template

* tasks/24-08-instruction-category/annotation.html
 * **Ref. Job on Sagemaker:** 24-08-check-instruction-category-val-duplication-test-4

Annotated data will be written at, **<output_bucket>/annotations/consolidated-annotation/**. The data will be written in two stage,

> 1. When user **Submit** the annotation, It will be written to **consolidated-request** folder under the

**consolidated-response**

> 2. Post lambda function will read the data from **consolidated-request** folder and convert it to user specific json and write to **consolidated-response** folder under the **consolidated-annotation** folder.

# Instruction Effect Task

##### Data Source:

Video clips with speedometer details and corresponding coach's instructions in text files. The **.mp4 and .txt** file names are the same. .mp4 video file must be encoded in **h264** format to make it compatible with ground truth jobs.

##### Task Overview:

The purpose of this task is to annotate the video, to check the coach's instruction effect visually. Also, get the information regarding the type of instruction, and the student's reaction to the coach's instruction. They also collect the reaction time if students follow the instruction.

##### Prepare annotation task:

Below are the steps to create instruction effect job:

1. Create a manifest file using the script, **tasks/24-08-instruction_effect/generate_labeling_job_manifest.py**. Execution details, and output manifest file structure are given in module Doc string.
2. Clone the existing reference job on sagemaker ground truth
3. Use the UI template or use it from a cloned job.
4. Select the worker team, [fma-pii-labelling-dev](https://us-east-1.console.aws.amazon.com/sagemaker/groundtruth?region=us-east-1#/labeling-workforces/private-details/fma-pii-labelling-dev)
5. Set **task timeout, task expiration time**, and **number of workers per dataset object**.
6. Create a job.

##### Analysis of annotation task:

Run the below script to generate aggregated details of annotation tasks. Read module level Doc to understand

execution and output details of script.

* **tasks/24-08-instruction_effect/generate_labeling_job_manifest.py**

# Instruction Category Task

##### Data Source:

Prepare subject and trial wise instruction category in CSV format. The instructions are distinguished using Type of instruction, category of instruction, and sub-category of the instruction.

##### Task Overview:

The purpose of this task is to categorize the instruction. The combination of Type, Category, and Subcategory will be chosen by the annotator to label the coach's instruction.

##### Prepare annotation task:

Below are the steps to create instruction effect job:

1. Now create a manifest file using the script, **tasks/24-08-instruction_category/generate_labeling_job_manifest.py**. Execution details, and output manifest file structure are given in module Doc string.
2. Follow 3-7 steps from Instruction Effect

##### Analysis of annotation task:

Run the below script to generate an analysis report for labeling tasks. Read module level Doc to understand

execution and output details of script.

* **tasks/24-08-instruction-category/analyse_labeled_data.py**

To generate analysis report for validation data, generate below script,

* **tasks/24-08-instruction-category/analyse_labeled_validation_data.py**