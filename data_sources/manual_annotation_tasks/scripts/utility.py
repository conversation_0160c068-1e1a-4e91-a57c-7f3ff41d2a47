import csv
import json
import os
import pathlib
from pathlib import Path
from typing import Dict, List

import pandas as pd

# Define the columns to include in the JSON and their names
# Column indices start from 0
columns_to_include: List = [
    "coach_subtitle",
    "start_time",
    "end_time",
    "Type",
    "Category",
    "Subcategory",
    "Sentence",
    "0",
]


def read_all_csv_from_directory(
    source_dir: str,
    columns_to_include: List,
    row_number: bool = True,
    source_file_name: bool = True,
    recursive: bool = False,
) -> pd.DataFrame:
    """Read all the CSVs from source directory and create pandas dataframe.

    Args:
        source_dir (str): Source directory containing CSVs
        columns_to_include (list): columns to be read from CSV file.
        row_number (bool): Add row/line number from CSV to JSON data.
        source_file_name (bool): Add source filename to JSON data.
        recursive (bool): Read all the csvs recursively.

    Returns:
        pd.DataFrame: Pandas dataframe containing all the data from all the CSVs

    """
    # Loop over all CSV files in the input folder
    verbose: bool = bool(os.environ.get("VERBOSE", False))
    ground_truth_data_list: List = []
    directory: pathlib.Path = Path(source_dir)

    # Default is non-recursive
    selected_iterator = directory.glob
    if recursive:
        selected_iterator = directory.rglob

    for filename in selected_iterator("*.csv"):
        if verbose:
            print(f"Reading {filename} ...")

        data: pd.DataFrame = pd.read_csv(filename, usecols=lambda x: x in columns_to_include)

        if source_file_name:
            data["file"] = Path(filename).name

        if row_number:
            data["row_number"] = range(len(data))

        ground_truth_data_list.append(data)

    return pd.concat(ground_truth_data_list, ignore_index=True)


def merge_all_worker_data(worker_wise_data: Dict) -> pd.DataFrame:
    """Merge the worker wise data and create dataframe from dictionary.

    Args:
        worker_wise_data (Dict): Worker to annotation map.

    Returns:
        pd.DataFrame: Merged dataframe.
    """
    all_worker_data: List = []
    for worker_id, worker_data in worker_wise_data.items():
        worker_data_df: pd.DataFrame = pd.DataFrame(worker_data)
        worker_data_df["worker_id"] = worker_id
        all_worker_data.append(worker_data_df)

    merged_annotated_data: pd.DataFrame = pd.concat(all_worker_data, ignore_index=True)

    return merged_annotated_data


def read_manifest_data(manifest_file: str) -> pd.DataFrame:
    """Read manifest file and return dataframe.

    Args:
        manifest_file (str): file path

    Returns:
        pd.DataFrame: Manifest data
    """

    data: List = []

    # Read the file line by line
    with open(manifest_file, "r", encoding="utf-8") as file:
        for line in file:
            # Parse each line as JSON and append to the data list
            data.append(json.loads(line))

    # Create a DataFrame from the list of dictionaries
    manifest_data_df: pd.DataFrame = pd.DataFrame(data)

    return manifest_data_df


def read_csv_to_json(csv_file, useful_column, filename):
    data = []
    with open(csv_file, mode="r", newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)

        for index, row in enumerate(reader):
            json_row = {}
            for column_name in useful_column:
                if column_name in row:
                    json_row[column_name] = (
                        str(row[column_name]).strip() if row[column_name] else row[column_name]
                    )
            source_ref = json_row.get("coach_subtitle")
            if not source_ref:
                source_ref = json_row.get("Sentence")
            if not source_ref:
                source_ref = json_row.get("0")
            json_row["file"] = filename
            json_row["uuid"] = filename.replace("_gpt_annotated.csv", "") + "--" + str(index)
            non_null_source_ref = "" if not source_ref else source_ref
            json_row["source-ref"] = "@" + json_row["uuid"] + "@" + non_null_source_ref
            try:
                json_row["true-label"] = row["true_label"]
            except:
                pass
            data.append(json_row)

    return data
