import json
from pathlib import Path
from typing import Any, Dict, List


def worker_to_annotation_map_from_annotation_request(data_path: str) -> Dict:
    """Read annotation request data form 'data_path' directory and create worker to annotation map.

    The annotation request JSONs are different from annotation response.

    annotation_request/iteration-1/: Folder contains the JSONs which is generated when worker submit the annotated data.
    annotation_response/iteration-1/: Folder contains the JSONs which is generated by post lambda function after
                                      processing annotation requests.

    Args:
        data_path (str): Local directory contains annotated data.

    Returns:
        Dict: Worker to annotation mapping.
    """
    data: List = []
    directory: Any = Path(data_path)
    for name in directory.glob("*.json"):
        with open(str(name), encoding="utf-8", mode="r") as currentFile:
            file_data: List = json.load(currentFile)
            data: List = data + file_data

    worker_to_labeling_map: Dict = {}
    for single_data in data:
        subtitle: str = single_data["dataObject"]["content"]
        annotations: Dict = single_data["annotations"]
        data_object_id: str = single_data["datasetObjectId"]

        for each_annotation_details in annotations:
            worker_id: str = each_annotation_details["workerId"]
            label_details: Dict = json.loads(each_annotation_details["annotationData"]["content"])
            label: str = label_details["label"]
            time_spent: float = label_details.get("time_spent", 0)
            worker_labeling_details: Dict = {
                "data_object_id": data_object_id,
                "worker_id": worker_id,
                "annotated_label": label.lower(),
                "sentence": subtitle,
                "time_spent": time_spent,
            }
            if worker_id in worker_to_labeling_map:
                collected_data: Any = worker_to_labeling_map[worker_id]
                collected_data.append(worker_labeling_details)
            else:
                worker_to_labeling_map[worker_id] = [worker_labeling_details]

    for w_id, worker_labels in worker_to_labeling_map.items():
        print(f"{w_id} :: Total label data: {len(worker_labels)}")

    return worker_to_labeling_map


def worker_to_annotation_map_from_annotation_response(data_path: str, labeling_job: str) -> Dict:
    """Read annotation response data form 'data_path' directory and create worker to annotation map.

    The annotation request JSONs are different from annotation response.

    annotation_request/iteration-1/: Folder contains the JSONs which is generated when worker submit the annotated data.
    annotation_response/iteration-1/: Folder contains the JSONs which is generated by post lambda function after
                                      processing annotation requests.

    Args:
        data_path (str): Local directory contains annotated data.
        labeling_job (str): labeling job name

    Returns:
        Dict: Worker to annotation mapping.
    """
    data: List = []
    directory: Any = Path(data_path)
    for name in directory.iterdir():
        if str(name).endswith(".json"):
            with open(str(name), encoding="utf-8", mode="r") as currentFile:
                file_data: List = json.load(currentFile)
                data: List = data + file_data

    worker_to_labeling_map: Dict = {}
    for single_data in data:
        data_object_id: str = single_data["datasetObjectId"]
        annotation_details: Dict = single_data["consolidatedAnnotation"]["content"]
        details: Dict = annotation_details[labeling_job]

        worker_id = details["workerId"]
        worker_labeling_details: Dict = {
            "data_object_id": data_object_id,
            "worker_id": details["workerId"],
            "annotated_label": details["labels"],
            "video_source": details["imageSource"]["s3Uri"],
        }

        if worker_id in worker_to_labeling_map:
            collected_data: Any = worker_to_labeling_map[worker_id]
            collected_data.append(worker_labeling_details)
        else:
            worker_to_labeling_map[worker_id] = [worker_labeling_details]

    for w_id, worker_labels in worker_to_labeling_map.items():
        print(f"{w_id} :: Total label data: {len(worker_labels)}")

    return worker_to_labeling_map
