"""For Instruction effect task, create the overlay annotation video to review the results of annotators.

The overlay video generation process will be done in two steps.
    1. Add white space padding to original video.
    2. Write/overlay the annotation details on white space and add subtitle at the bottom-center of the video.

Command:
    python scripts/visualize_annotations_over_video.py \
        --s3-uri "s3 uri where annotated json are stored. (location of consolidation-request/iteration-1/)" \
        --aws-profile "Aws profile to be used while downloading annotations" \
        <--sink-directory "local directory path where downloaded json from s3 uri will be stored.
                            default value: annotation_data"> \
        <--video-directory "Video directory where the original video will be downloaded and processed
                            overlay video will be stored. Default: annotation_videos">

Note: argument mentioned in "<>" are optional and have default values.
        
The above command will create two command file(Contains the list of ffmpeg commands), 
for each step mentioned above.
    For step 1 : commands_to_pad.txt (add white space padding)
    For step 2 : commands_to_add_text.txt (add annotation and subtitle and store in "final_video" directory.)

Note: If 5 annotator worked on single job with 400 video then number of ffmpeg commands will be 2000(5 * 400).
Also, each output file having worker id in its name.

Run the command files using bash script.

    --> ./bash_script/ffmpeg-parallel.sh commands_to_pad.txt \
            --use-gpu 1 \
            --gpu-device 0 \
            --process-count <number of parallel process default is 5>
    --> ./bash_script/ffmpeg-parallel.sh commands_to_add_text.txt \
            --use-gpu 1 \
            --gpu-device 0 -\
            -process-count <number of parallel process default is 5>

Note: If you have compiled FFMPEG with GPU, then us "--use-gpu 1" else use "--use-gpu 0".


"""

import argparse
import json
import textwrap
from pathlib import Path
from typing import Dict, List
from urllib.parse import urlparse

from util.s3_utility import download_s3_files, download_s3_files_recursively


def parse_arguments():
    """
    Parse command-line arguments and return the parsed arguments.

    :return: Parsed arguments
    """
    # Create the parser
    parser = argparse.ArgumentParser(description="Process some directories and file names.")

    # Define the command-line arguments
    parser.add_argument(
        "--s3-uri",
        type=str,
        required=True,
        help="""S3 bucket URI which contains the consolidated data for annotations. 
                Use 'exit' to skip this downloading step.""",
    )

    parser.add_argument(
        "--aws-profile",
        type=str,
        required=True,
        help="S3 bucket URI which contains the consolidated data for annotations.",
    )

    parser.add_argument(
        "--sink-directory",
        "-s",
        type=str,
        default="annotation_data",
        help="Directory where the downloaded files will be stored",
    )

    parser.add_argument(
        "--video-directory",
        "-v",
        type=str,
        default="annotation_videos",
        help="Directory where the downloaded videos will be stored",
    )

    # Parse the arguments
    all_args = parser.parse_args()

    return all_args


def add_padding_to_video(input_video: str, output_video: str) -> str:
    """Generate ffmpeg padding command for given input video with specified output file name.

    Args:
        input_video (str): Input videos file path.
        output_video (str): Output video file path.

    Returns:
        str: ffmpeg command.
    """
    command: List = ["ffmpeg"]

    command.extend(
        [
            "-y",  # overwrite the video if exist
            "-i",
            input_video,
            "-vf",
            '"pad=iw+300:ih:0:0:color=white"',
            "-c:v",
            "h264",
            "-codec:a",
            "copy",
            output_video,
        ]
    )

    return " ".join(command)


def add_text_to_video(
    input_video: str, video_txt: str, subtitle_txt: str, output_video: str
) -> str:
    """Generate ffmpeg padding command for given input video with specified output file name.

    Args:
        input_video (str): Input videos file path.
        video_txt (str): text file path contains annotation details.
        subtitle_txt (str): text file path contains subtitle details.
        output_video (str): Output video file path.

    Returns:
        str: ffmpeg command.
    """
    command: List = ["ffmpeg"]

    command.extend(
        [
            "-y",  # overwrite the video if exist
            "-i",
            input_video,
            "-vf",
            f"\"drawtext=textfile='{video_txt}':fontsize=16:fontcolor=black:x=W-tw-10:y=10,drawtext=textfile='{subtitle_txt}':fontsize=30:fontcolor=black:x=W/2-tw/2-10:y=H-th-10\"",
            "-c:v",
            "h264",
            "-codec:a",
            "copy",
            output_video,
        ]
    )

    return " ".join(command)


def extract_annotation_data(annotation: Dict, output_txt_path: str) -> None:
    """Generate ffmpeg padding command for given input video with specified output file name.

    Args:
        annotation (dict): Dictionary contains annotation details.
        output_txt_path (str): text file path contains annotation details.

    Returns:
        None
    """
    try:
        lines: List = ["Annotation Data:", "", ""]

        # Format extracted annotation data
        annotation_data: str = annotation.get("content", "{}")
        annotation_data: Dict = json.loads(annotation_data)

        coach_advices: List = []
        student_action: str = ""
        reaction_time: str = ""
        for key, value in annotation_data.items():
            if key == "actionable_feedback" and value["Yes"]:
                lines.append("--> Actionable Feedback")
                lines.append("")
            elif key == "actionable_feedback" and value["No"]:
                lines.append("--> Not Actionable Feedback")
                lines.append("")

            if key == "why_not_feedback_actionable" and value:
                lines.append(f"--> {value}")

            if key == "flawed_transcript_explanation" and value:
                lines.append(f"--> {value}")
                lines.append("")

            if key == "flawed_transcript" and value["Yes"]:
                lines.append("--> Transcript Flawed")
                lines.append("")
            elif key == "flawed_transcript" and value["No"]:
                lines.append("--> Transcript Not Flawed")
                lines.append("")

            if key == "throttle_advice" and value["Throttle off/Lift"]:
                coach_advices.append("          Throttle off/Lift")
            elif key == "throttle_advice" and value["Throttle on/Gas"]:
                coach_advices.append("          Throttle on/Gas")

            if key == "brake_advice" and value["Brake off"]:
                coach_advices.append("          Brake Off")
            elif key == "brake_advice" and value["Brake on"]:
                coach_advices.append("          Brake On")

            if key == "turn_advice" and value["Turn left"]:
                coach_advices.append("          Turn left")
            elif key == "turn_advice" and value["Turn right"]:
                coach_advices.append("          Turn right")

            if key == "position_advice" and value["Move left"]:
                coach_advices.append("          Move left")
            elif key == "position_advice" and value["Move right"]:
                coach_advices.append("          Move right")
            elif key == "position_advice" and value["Move to middle"]:
                coach_advices.append("          Move to middle")

            if key == "steer_advice" and value["Less steering"]:
                coach_advices.append("          Less steering")
            elif key == "steer_advice" and value["More steering"]:
                coach_advices.append("          More steering")
            elif key == "steer_advice" and value["Quicker steering"]:
                coach_advices.append("          Quicker steering")
            elif key == "steer_advice" and value["Smoother steering"]:
                coach_advices.append("          Smoother steering")
            elif key == "steer_advice" and value["Straight steering"]:
                coach_advices.append("          Straight steering")

            if key == "other_advice" and value["Aim for cone"]:
                coach_advices.append("          Aim for cone")
            elif key == "other_advice" and value["Aim for tower"]:
                coach_advices.append("          Aim for tower")
            elif key == "other_advice" and value["Look up/peek"]:
                coach_advices.append("          Look up/peek")
            elif key == "other_advice" and value["Other/Misc."]:
                coach_advices.append("          Other/Misc.")

            if key == "follow_advice" and value["0"]:
                lines.append("--> Follow Advice? Not applicable")
                lines.append("")
            elif key == "follow_advice" and value["1"]:
                lines.append("--> Follow Advice? Strongly Disagree")
                lines.append("")
            elif key == "follow_advice" and value["2"]:
                lines.append("--> Follow Advice? Disagree")
                lines.append("")
            elif key == "follow_advice" and value["3"]:
                lines.append("--> Follow Advice? Somewhat Disagree")
                lines.append("")
            elif key == "follow_advice" and value["4"]:
                lines.append("--> Follow Advice? Neutral")
                lines.append("")
            elif key == "follow_advice" and value["5"]:
                lines.append("  Follow Advice? Somewhat Agree")
                lines.append("")
            elif key == "follow_advice" and value["6"]:
                lines.append("--> Follow Advice? Agree")
                lines.append("")
            elif key == "follow_advice" and value["7"]:
                lines.append("--> Follow Advice? Strongly Agree")
                lines.append("")

            if key == "reaction_time" and value:
                reaction_time = value

            if key == "student_action" and value:
                # Wrap the text if it longer than 30 characters to fit the text the white padded space.
                student_action = textwrap.fill(value, 30, subsequent_indent="    ")

        if len(coach_advices) > 0:
            lines.append("--> Coach Advice:")
            lines.append("")
            for advice in coach_advices:
                lines.append(advice)
                lines.append("")

            lines.append("")

        if student_action:
            lines.append(f"--> {student_action}")
            lines.append("")

        if reaction_time:
            lines.append(f"--> Reaction Time: {reaction_time} Seconds")

        lines.append("")  # Blank line for separation

        # Write to .txt file
        with open(output_txt_path, "w") as f:
            f.write("\n".join(lines))

        print(f"Annotation data has been written to {output_txt_path}")

    except Exception as e:
        print(f"An error occurred: {e}")


def read_annotation_data(aws_profile: str, data_path: str, annotated_video_location: str) -> None:
    """Read the annotation data, grouped the data worker wise, download the original video file,
    and create ffmpeg commands.

    This function will write two ffmpeg command file, 'commands_to_pad.txt' and 'commands_to_add_text.txt'
    Also, write the worker wise annotation details in 'video_url_to_worker_annotation_aggregate.json' JSON.

    Args:
        aws_profile (str): Dictionary contains annotation details.
        data_path (str): text file path contains annotation details.
        annotated_video_location (str): video from annotation job are stored here.

    Returns:
        None
    """
    data: List = []
    video_to_annotation_map: Dict = dict()

    directory: Path = Path(data_path)
    for name in directory.glob("*.json"):
        with open(str(directory.joinpath(name)), encoding="utf-8", mode="r") as currentFile:
            file_data: List = json.load(currentFile)
            data: List = data + file_data

    for single_data in data:
        s3_uri: str = single_data["dataObject"]["s3Uri"]
        annotation_data: Dict = single_data["annotations"]
        if len(annotation_data) > 1:
            print("----> More than one annotation data is found.")

        video_to_annotation_map[s3_uri] = annotation_data

    # Create the directory, if it does not exist
    annotation_data_directory = Path(annotated_video_location)
    annotation_data_directory.mkdir(parents=True, exist_ok=True)

    for video_url, annotation_data in video_to_annotation_map.items():
        download_s3_files(aws_profile, video_url, annotated_video_location)
        subtitle_file: str = video_url.replace(".mp4", ".txt")
        download_s3_files(aws_profile, subtitle_file, annotated_video_location)

    final_video_dir: Path = annotation_data_directory.joinpath("final_videos")

    # Create the directory, if it does not exist
    final_video_dir.mkdir(parents=True, exist_ok=True)

    list_of_cmd_to_pad: List = []
    list_of_cmd_to_add_text: List = []
    data_object_to_aggregation: Dict = {}

    for video_url, annotation_data in video_to_annotation_map.items():
        parsed_uri = urlparse(video_url)
        name: str = parsed_uri.path.split("/")[-1]

        input_file: str = str(annotation_data_directory.joinpath(name))
        subtitle_txt: str = input_file.replace(".mp4", ".txt")
        partial_output_file: str = input_file.replace(".mp4", "_padded.mp4")

        if video_url not in data_object_to_aggregation:
            data_object_to_aggregation[video_url] = {}

        for annotation in annotation_data:
            worker_id: str = annotation["workerId"]
            worker_data: Dict = annotation["annotationData"]

            annotation_data: str = worker_data.get("content", "{}")
            annotation_data: Dict = json.loads(annotation_data)

            if worker_id not in data_object_to_aggregation[video_url]:
                data_object_to_aggregation[video_url][worker_id] = [annotation_data]
            else:
                current_data: List = data_object_to_aggregation[video_url][worker_id]
                current_data.append(annotation_data)
                data_object_to_aggregation[video_url][worker_id] = current_data

            txt_post_fix: str = f"_{worker_id}.txt"
            video_post_fix: str = f"_{worker_id}.mp4"
            video_txt: str = str(
                annotation_data_directory.joinpath(name.replace(".mp4", txt_post_fix))
            )

            output_file: str = str(final_video_dir.joinpath(name.replace(".mp4", video_post_fix)))

            extract_annotation_data(worker_data, video_txt)

            command_to_pad: str = add_padding_to_video(input_file, partial_output_file)
            list_of_cmd_to_pad.append(command_to_pad)
            command_to_add_text: str = add_text_to_video(
                partial_output_file, video_txt, subtitle_txt, output_file
            )
            list_of_cmd_to_add_text.append(command_to_add_text)

    with open("commands_to_pad.txt", "w") as f:
        for line in list_of_cmd_to_pad:
            f.write(f"{line}\n")

    with open("commands_to_add_text.txt", "w") as f:
        for line in list_of_cmd_to_add_text:
            f.write(f"{line}\n")

    # Open the file in write mode
    with open("video_url_to_worker_annotation_aggregate.json", "w") as file:
        # Use json.dump to write the dictionary to the file with indentation for pretty printing
        json.dump(data_object_to_aggregation, file, indent=4)


if __name__ == "__main__":
    args = parse_arguments()

    # Use s3_uri flag to "exit" if you want to this downloading step.
    download_s3_files_recursively(args.aws_profile, args.s3_uri, args.sink_directory)

    read_annotation_data(args.aws_profile, args.sink_directory, args.video_directory)
