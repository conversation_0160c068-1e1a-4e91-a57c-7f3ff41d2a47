<script src="https://assets.crowd.aws/crowd-html-elements.js"></script>

<script>
        document.addEventListener('DOMContentLoaded', function() {
        // Timer start
        const startTime = new Date();

        // Capture form submission and calculate elapsed time
        const form = document.querySelector('crowd-form');
        form.addEventListener('submit', function(event) {
            const endTime = new Date();
            const timeSpent = (endTime - startTime) / 1000; // Time in seconds

            // Append timeSpent to the form data
            const timeSpentInput = document.createElement('input');
            timeSpentInput.type = 'hidden';
            timeSpentInput.name = 'time_spent';
            timeSpentInput.value = timeSpent;
            form.appendChild(timeSpentInput);
        });

        const actionableYesRadio = document.querySelector('input[name="actionable_feedback"][value="Yes"]');
        const actionableNoRadio = document.querySelector('input[name="actionable_feedback"][value="No"]');

        const flawedYesRadio = document.querySelector('input[name="flawed_transcript"][value="Yes"]');
        const flawedNoRadio = document.querySelector('input[name="flawed_transcript"][value="No"]');

        const followAdviceCheckboxes = document.querySelectorAll('#follow_advice_container_1 .checkbox');

        const followAdviceRadios = document.querySelectorAll('input[name="follow_advice"]');

        const studentActionDetailsText = document.getElementById("student_action_text");

        const reactionTimeText = document.getElementById("reaction_time_text");

        const whyNotFeedbackActionableText = document.getElementById("why_not_feedback_actionable_text");

        const componentsToToggle = [
            'follow_advice_container',
            'student_action_container',
            'reaction_time_container',
            'follow_advice_container_1'
        ];

        const reverseCompToToggle = [
          'why_not_feedback_actionable'
        ];

        const flawedComponent = document.getElementById('flawed_transcript_explanation_container');

        function toggleComponents(shouldShow) {
            componentsToToggle.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.style.display = shouldShow ? 'block' : 'none';
                }
            });

            reverseCompToToggle.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.style.display = !shouldShow ? 'block' : 'none';
                }
            });
        }

        function toggleFlawedComponent(shouldShow) {
            if (flawedComponent) {
                flawedComponent.style.display = shouldShow ? 'block' : 'none';
            }

            if(!shouldShow){
              const element = document.getElementById("flawed_transcript_explanation_text");
              element.value = '';
            }
        }

        function handleActionableRadioChange() {
            if (actionableYesRadio.checked) {
                toggleComponents(true);
                whyNotFeedbackActionableText.value = '';
            } else {
                toggleComponents(false);
                // Reset the checkbox value to false
                followAdviceCheckboxes.forEach(checkbox => {
                  checkbox.checked = false;
                });
                // Reset radio value to false
                followAdviceRadios.forEach(radio => {
                  radio.checked = false;
                });

                studentActionDetailsText.value = '';
                reactionTimeText.value = '';
            }
        }

        function handleFlawedRadioChange() {
            if (flawedYesRadio.checked) {
                toggleFlawedComponent(true);
            } else {
                toggleFlawedComponent(false);
            }
        }

        actionableYesRadio.addEventListener('change', handleActionableRadioChange);
        actionableNoRadio.addEventListener('change', handleActionableRadioChange);

        flawedYesRadio.addEventListener('change', handleFlawedRadioChange);
        flawedNoRadio.addEventListener('change', handleFlawedRadioChange);

        // Initialize the components visibility based on default "No" selection
        handleActionableRadioChange();
        handleFlawedRadioChange();
    });
</script>

<crowd-form>
  <div style="width: 1000px; margin: 0 auto;">
        <h1>Coaching Session Feedback</h1>

        <p>Your task is to identify and explain <strong>actionable feedback</strong> in a coaching session. Actionable feedback refers to coaching that can be followed <strong>in-the-moment</strong> (such as "Brake now!" or "Look up!"), not feedback that is abstract or long term ("On the next lap, remember this spot," or "That was great," or "That cone is there for a reason").</p>

        <p>Please review the following video and listen to the coach's instructions:</p>
        <div style="text-align: center; margin-bottom: 20px;">
            <video controls style="width: 120%; ">

              <source src="{{ task.input.sourceRef | grant_read_access }}" type="video/mp4">
                <!-- Your browser does not support the video tag. -->
            </video>

        </div>

        <div style="text-align: center; margin-bottom: 20px;">
        <p>The coach said: <strong>{{ task.input.textRef }}</strong></p>
        </div>


        <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Is there a flaw in the transcript or video?</label>
            <div>
                <label style="margin-right: 20px;"><input type="radio" name="flawed_transcript" value="Yes"> Yes</label>
                <label style="margin-right: 20px;"><input type="radio" name="flawed_transcript" value="No" checked> No</label>
            </div>
        </div>
        <div id="flawed_transcript_explanation_container" style="margin-bottom: 15px; display: none;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Please describe the transcript/video issue:</label>
            <input type="text" id="flawed_transcript_explanation_text" name="flawed_transcript_explanation" style="width: 100%; padding: 8px; margin-top: 5px;" placeholder="">
        </div>
&nbsp
<hr class="solid">
&nbsp
        <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Did the Coach provide actionable feedback to the student?</label>
            <div>
                <label style="margin-right: 20px;"><input type="radio" name="actionable_feedback" value="Yes"> Yes</label>
                <label style="margin-right: 20px;"><input type="radio" name="actionable_feedback" value="No" checked> No</label>
            </div>
        </div>

        <div id="follow_advice_container_1" style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">What type of advice did the Coach provide? Select all that apply:</label>
                <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                    <div id='throttle_advice' style="border: 1px solid #000; padding: 10px;">
                        <label>Throttle</label><br>
                        <label><input type="checkbox" name="throttle_advice" class="checkbox" value="Throttle on/Gas"> Throttle on/Gas</label><br>
                        <label><input type="checkbox" name="throttle_advice" class="checkbox" value="Throttle off/Lift"> Throttle off/Lift</label>
                    </div>
                    <div style="border: 1px solid #000; padding: 10px;">
                        <label>Brake</label><br>
                        <label><input type="checkbox" name="brake_advice" class="checkbox" value="Brake on"> Brake on</label><br>
                        <label><input type="checkbox" name="brake_advice" class="checkbox" value="Brake off"> Brake off</label>
                    </div>
                    <div style="border: 1px solid #000; padding: 10px;">
                        <label>Turning</label><br>
                        <label><input type="checkbox" name="turn_advice" class="checkbox" value="Turn left"> Turn left</label><br>
                        <label><input type="checkbox" name="turn_advice" class="checkbox" value="Turn right"> Turn right</label>
                    </div>
                    <div style="border: 1px solid #000; padding: 10px;">
                        <label>Positioning</label><br>
                        <label><input type="checkbox" name="position_advice" class="checkbox" value="Move left"> Move left</label><br>
                        <label><input type="checkbox" name="position_advice" class="checkbox" value="Move right"> Move right</label><br>
                        <label><input type="checkbox" name="position_advice" class="checkbox" value="Move to middle"> Move to middle</label>
                    </div>
                    <div style="border: 1px solid #000; padding: 10px;">
                        <label>Steering Adjustment</label><br>
                        <label><input type="checkbox" name="steer_advice" class="checkbox" value="Quicker steering"> Quicker steering</label><br>
                        <label><input type="checkbox" name="steer_advice" class="checkbox" value="Smoother steering"> Smoother steering</label><br>
                        <label><input type="checkbox" name="steer_advice" class="checkbox" value="Straight steering"> Straight steering</label><br>
                        <label><input type="checkbox" name="steer_advice" class="checkbox" value="Less steering"> Less steering</label><br>
                        <label><input type="checkbox" name="steer_advice" class="checkbox" value="More steering"> More steering</label><br>
                    </div>
                    <div style="border: 1px solid #000; padding: 10px;">
                        <label>Other</label><br>
                        <label><input type="checkbox" name="other_advice" class="checkbox" value="Look up/peek"> Look up/peek</label><br>
                        <label><input type="checkbox" name="other_advice" class="checkbox" value="Aim for cone"> Aim for cone</label><br>
                        <label><input type="checkbox" name="other_advice" class="checkbox" value="Aim for tower"> Aim for tower</label><br>
                        <label><input type="checkbox" name="other_advice" class="checkbox" value="Other/Misc."> Other/Misc</label><br>
                    </div>
                </div>
            </div>


        <div id="follow_advice_container" style="margin-bottom: 15px;">
          <label style="display: block; margin-bottom: 5px; font-weight: bold;">Rate your agreement with the following statement: The driver followed the coach's advice.</label>
          <div style="display: flex; justify-content: space-between; width: 100%;">
              <div style="text-align: center;">
                  <label>Not applicable</label><br>
                  <input type="radio" name="follow_advice" value="0">
              </div>

              <div style="text-align: center;">
                  <label>Strongly Disagree</label><br>
                  <input type="radio" name="follow_advice" value="1">
              </div>
              <div style="text-align: center;">
                  <label>Disagree</label><br>
                  <input type="radio" name="follow_advice" value="2">
              </div>
              <div style="text-align: center;">
                  <label>Somewhat Disagree</label><br>
                  <input type="radio" name="follow_advice" value="3">
              </div>
              <div style="text-align: center;">
                  <label>Neutral</label><br>
                  <input type="radio" name="follow_advice" value="4">
              </div>
              <div style="text-align: center;">
                  <label>Somewhat Agree</label><br>
                  <input type="radio" name="follow_advice" value="5">
              </div>
              <div style="text-align: center;">
                  <label>Agree</label><br>
                  <input type="radio" name="follow_advice" value="6">
              </div>
              <div style="text-align: center;">
                  <label>Strongly Agree</label><br>
                  <input type="radio" name="follow_advice" value="7">
              </div>
          </div>
      </div>

        <div id="student_action_container" style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">What did the student do?</label>
            <input type="text" id="student_action_text" name="student_action" style="width: 100%; padding: 8px; margin-top: 5px;" placeholder="The student...">
        </div>

        <div id="reaction_time_container" style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">How long did the student take to act according to the Coach's instruction?</label>
            <input type="number" id="reaction_time_text" name="reaction_time" step="0.1" style="width: 100%; padding: 8px; margin-top: 5px;" placeholder="Time in seconds">
        </div>

        <div id="why_not_feedback_actionable" style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Why isn’t the feedback actionable?</label>
            <input type="text" id="why_not_feedback_actionable_text" name="why_not_feedback_actionable" style="width: 100%; padding: 8px; margin-top: 5px;" placeholder="Why not?...">
        </div>

        <div style="text-align: center; margin-top: 20px;">
            <input type="submit" value="Submit" style="padding: 10px 20px; font-size: 16px; cursor: pointer;">
        </div>
    </div>
</crowd-form>
