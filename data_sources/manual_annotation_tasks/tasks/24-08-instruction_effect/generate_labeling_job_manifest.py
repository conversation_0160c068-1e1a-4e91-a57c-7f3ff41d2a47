"""This script will loop over the video files available locally and generate manifest file
for instruct effect labeling job.

Prerequisites:
    1. Dataset is available locally.
    2. Input data must be in .mp4 file with respective
        .txt file containing coach's subtitle.

Command:
    python scripts/analyse_labeled_data.py \
        --data-location "Local folder containing the .mp4 and .txt files. (Files will not be read recursively)" \
        --s3-bucket-uri "Location of S3 bucket where all this file will be uploaded." \
        --manifest-file "The manifest file."

Output Structure: single data point/row structure in manifest file.

{
     # Video location on S3
    "source-ref": "s3://tri-hid-data-shared-autonomy/manual_annotation/24-08-instruction_effect2/input_data/24-D-05/P605_trial7_[1711056271.024521,1711056272.9407313]_[0.5,10].mp4",
    "text-ref": "be taking things a little bit faster now." # --> Coach's subtitle for that video.
}

Note :: 'source-ref' video will be loaded on labeling job and can be played while labeling.
        'text-ref' will be shown under the video in labeling job UI.
"""

import argparse
import json
from pathlib import Path


def generate_manifest(data_location, s3_bucket_uri, manifest_file):
    # Ensure the directory exists
    if not Path(data_location).is_dir():
        print(f"Error: The directory '{data_location}' does not exist.")
        return

    with open(manifest_file, "w") as f:
        directory: Path = Path(data_location)
        # Loop over all files in the directory
        for filename in directory.glob(".mp4"):
            # Construct the corresponding TXT file name
            txt_filename = str(filename.name).replace(".mp4", ".txt")
            txt_file_path = directory.joinpath(txt_filename)

            # Construct the MP4 file path
            s3_file_path = f"{s3_bucket_uri}/{filename.name}"

            manifest_entry = {"source-ref": s3_file_path}

            # Check if the TXT file exists
            if txt_file_path.is_file():
                # Read and print the content of the TXT file
                with open(str(txt_file_path), "r") as file:
                    content = file.read()
                    manifest_entry["text-ref"] = content
            else:
                print(f"TXT file for {filename.name} does not exist.")

            # Write the manifest entry to the file
            f.write(json.dumps(manifest_entry) + "\n")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Generate a manifest file from MP4 and TXT files in a specified directory."
    )
    parser.add_argument(
        "--data-location", "-d", type=str, help="The directory containing the MP4 and TXT files."
    )
    parser.add_argument("--s3-bucket-uri", "-s3", type=str, help="The base URI for the S3 bucket.")
    parser.add_argument(
        "--manifest-file",
        "-o",
        type=str,
        required=False,
        default="manifest.json",
        help="The output file where the manifest will be saved.",
    )

    args = parser.parse_args()
    generate_manifest(args.data_location, args.s3_bucket_uri, args.manifest_file)
