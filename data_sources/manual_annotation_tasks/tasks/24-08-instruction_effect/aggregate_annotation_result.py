"""Download the annotation data and create analysis report for instruct category labeling job data.

Note: Currently no analytics data is generated. We can create worker wise evaluation matrix in the output.

Command:
    python scripts/analyse_labeled_data.py \
        --s3-uri "s3 uri where annotated json are stored. (location of consolidation-request/iteration-1/)" \
        --annotation-job-name "Annotation job name used for annotation" \
        <--aws-profile "Aws profile to be used while downloading annotations. default value: 'default'"> \
        <--sink-directory "local directory path where downloaded json from s3 uri will be stored.
                            default value: 'annotation_data'"> \
        <--annotation-video "Directory where the downloaded video/text files will be stored.
                            default value: 'annotated_videos'"> \
        <--verbose/-v "Print the details of execution inbetween. default value: 'False'">
        <--output-csv/-o "output CSV file path. default value: 'aggregated_data_instruction_effect.csv'"

Note: The arguments shown in '<>' are optional and has default values.
"""

import argparse
import json
import os
from pathlib import Path
from typing import Dict, List

import pandas as pd

from data_sources.manual_annotation_tasks.scripts.parse_annotated_data import (
    worker_to_annotation_map_from_annotation_response,
)
from util.s3_utility import download_s3_files_recursively


def parse_arguments():
    """
    Parse command-line arguments and return the parsed arguments.

    :return: Parsed arguments
    """
    # Create the parser
    parser = argparse.ArgumentParser(description="Process some directories and file names.")

    # Define the command-line arguments
    parser.add_argument(
        "--s3-uri",
        type=str,
        required=True,
        help="""S3 bucket URI which contains the consolidated data for annotations.
             Use 'exit' to skip this downloading step.""",
    )

    parser.add_argument(
        "--aws-profile",
        type=str,
        default="default",
        help="S3 bucket URI which contains the consolidated data for annotations.",
    )

    parser.add_argument(
        "--sink-directory",
        "-s",
        type=str,
        default="annotation_data",
        help="Directory where the downloaded files will be stored",
    )

    parser.add_argument(
        "--annotation-job-name",
        "-j",
        type=str,
        required=True,
        help="Annotation job name to parse the labeled data",
    )

    parser.add_argument(
        "--annotation-video",
        type=str,
        default="annotated_videos",
        help="Directory where the downloaded vido/text files will be stored",
    )

    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")

    parser.add_argument(
        "--output-csv",
        "-o",
        type=str,
        default="aggregated_data_instruction_effect.csv",
        help="Output CSV file path",
    )

    # Parse the arguments
    all_args = parser.parse_args()

    return all_args


def extract_annotation_data(annotation_data: Dict) -> Dict:
    """Generate ffmpeg padding command for given input video with specified output file name.

    Args:
        annotation_data (dict): Dictionary contains annotation details.

    Returns:
        Dict: Extracted key-value pair.
    """
    try:
        extracted_details: Dict = {}
        throttle_advice: List = []
        brake_advice: List = []
        turn_advice: List = []
        position_advice: List = []
        steer_advice: List = []
        other_advice: List = []
        time_spent = 0

        for key, value in annotation_data.items():
            if key == "actionable_feedback" and value["Yes"]:
                extracted_details["actionable_feedback"] = "Yes"
            elif key == "actionable_feedback" and value["No"]:
                extracted_details["actionable_feedback"] = "No"

            # JSON dumps is used to handle special character in output CSVs.
            if key == "why_not_feedback_actionable" and value:
                extracted_details["why_not_feedback_actionable"] = json.dumps(value)

            if key == "flawed_transcript_explanation" and value:
                extracted_details["flawed_transcript_explanation"] = value

            if key == "flawed_transcript" and value["Yes"]:
                extracted_details["flawed_transcript"] = "Yes"
            elif key == "flawed_transcript" and value["No"]:
                extracted_details["flawed_transcript"] = "No"

            if key == "throttle_advice" and value["Throttle off/Lift"]:
                throttle_advice.append("Throttle off/Lift")
            if key == "throttle_advice" and value["Throttle on/Gas"]:
                throttle_advice.append("Throttle on/Gas")

            if key == "brake_advice" and value["Brake off"]:
                brake_advice.append("Brake Off")
            if key == "brake_advice" and value["Brake on"]:
                brake_advice.append("Brake On")

            if key == "turn_advice" and value["Turn left"]:
                turn_advice.append("Turn left")
            if key == "turn_advice" and value["Turn right"]:
                turn_advice.append("Turn right")

            if key == "position_advice" and value["Move left"]:
                position_advice.append("Move left")
            if key == "position_advice" and value["Move right"]:
                position_advice.append("Move right")
            if key == "position_advice" and value["Move to middle"]:
                position_advice.append("Move to middle")

            if key == "steer_advice" and value["Less steering"]:
                steer_advice.append("Less steering")
            if key == "steer_advice" and value["More steering"]:
                steer_advice.append("More steering")
            if key == "steer_advice" and value["Quicker steering"]:
                steer_advice.append("Quicker steering")
            if key == "steer_advice" and value["Smoother steering"]:
                steer_advice.append("Smoother steering")
            if key == "steer_advice" and value["Straight steering"]:
                steer_advice.append("Straight steering")

            if key == "other_advice" and value["Aim for cone"]:
                other_advice.append("Aim for cone")
            if key == "other_advice" and value["Aim for tower"]:
                other_advice.append("Aim for tower")
            if key == "other_advice" and value["Look up/peek"]:
                other_advice.append("Look up/peek")
            if key == "other_advice" and value["Other/Misc."]:
                other_advice.append("Other/Misc.")

            if key == "follow_advice" and value["0"]:
                extracted_details["follow_advice"] = "Not applicable"
            elif key == "follow_advice" and value["1"]:
                extracted_details["follow_advice"] = "Strongly Disagree"
            elif key == "follow_advice" and value["2"]:
                extracted_details["follow_advice"] = "Disagree"
            elif key == "follow_advice" and value["3"]:
                extracted_details["follow_advice"] = "Somewhat Disagree"
            elif key == "follow_advice" and value["4"]:
                extracted_details["follow_advice"] = "Neutral"
            elif key == "follow_advice" and value["5"]:
                extracted_details["follow_advice"] = "Somewhat Agree"
            elif key == "follow_advice" and value["6"]:
                extracted_details["follow_advice"] = "Agree"
            elif key == "follow_advice" and value["7"]:
                extracted_details["follow_advice"] = "Strongly Agree"
            elif key == "follow_advice":
                extracted_details["follow_advice"] = ""

            if key == "reaction_time" and value:
                extracted_details["reaction_time"] = value

            # JSON dumps is used to handle special character in output CSVs.
            if key == "student_action" and value:
                extracted_details["student_action"] = json.dumps(value)

            if key == "time_spent":
                time_spent = value

        if "why_not_feedback_actionable" not in extracted_details:
            extracted_details["why_not_feedback_actionable"] = ""

        if "flawed_transcript_explanation" not in extracted_details:
            extracted_details["flawed_transcript_explanation"] = ""

        if "student_action" not in extracted_details:
            extracted_details["student_action"] = ""

        if "reaction_time" not in extracted_details:
            extracted_details["reaction_time"] = ""

        # JSON dumps is used to handle special character in output CSVs.
        extracted_details["throttle_advice"] = json.dumps(",".join(throttle_advice))
        extracted_details["brake_advice"] = json.dumps(",".join(brake_advice))
        extracted_details["turn_advice"] = json.dumps(",".join(turn_advice))
        extracted_details["position_advice"] = json.dumps(",".join(position_advice))
        extracted_details["steer_advice"] = json.dumps(",".join(steer_advice))
        extracted_details["other_advice"] = json.dumps(",".join(other_advice))
        extracted_details["time_spent"] = time_spent

        return extracted_details
    except Exception as e:
        print(f"An error occurred: {e}")


def generate_aggregate_data(
    worker_to_annotation_map: Dict, annotated_video_location: str, aws_profile: str
) -> List[Dict]:
    """Loop over worker to annotation, download the subtitle and create aggregate data.

    Args:
        worker_to_annotation_map (str): worker to annotation map
        annotated_video_location (str): Directory where text file/annotation video will be downloaded.
        aws_profile (str): Aws profile to download subtitle.

    Returns:
        List[Dict]: List of aggregated data.
    """
    video_to_annotation_map: Dict = {}
    for video_url, annotation in worker_to_annotation_map.items():
        for each_annotation in annotation:
            video_source = each_annotation["video_source"]

            if video_source in video_to_annotation_map:
                current_annotated_data = video_to_annotation_map[video_source]
                current_annotated_data.append(each_annotation)
            else:
                video_to_annotation_map[video_source] = [each_annotation]

    annotated_video_directory: Path = Path(annotated_video_location)
    annotated_video_directory.mkdir(parents=True, exist_ok=True)

    for video_url, annotation_data in video_to_annotation_map.items():
        subtitle_file: str = video_url.replace(".mp4", ".txt")
        download_s3_files_recursively(aws_profile, subtitle_file, annotated_video_location)

    aggregate_data_list: List[Dict] = []
    for worker_id, annotations in worker_to_annotation_map.items():
        for each_annotation in annotations:
            label = each_annotation.pop("annotated_label", None)

            parsed_label: Dict = extract_annotation_data(label)
            each_annotation.update(parsed_label)

            video_source: str = each_annotation["video_source"]
            subtitle_path: str = video_source.replace(".mp4", ".txt")
            subtitle_file: str = Path(subtitle_path).name

            with open(str(annotated_video_directory.joinpath(subtitle_file)), "r") as file:
                subtitle: str = file.read()

            each_annotation["subtitle"] = json.dumps(subtitle)

            aggregate_data_list.append(each_annotation)

    return aggregate_data_list


if __name__ == "__main__":
    args = parse_arguments()

    os.environ["VERBOSE"] = str(args.verbose)

    # Download the data from S3.
    download_s3_files_recursively(args.aws_profile, args.s3_uri, args.sink_directory)

    # Read annotated data.
    worker_to_annotation_data: Dict = worker_to_annotation_map_from_annotation_response(
        args.sink_directory, args.annotation_job_name
    )

    # Parse and generate aggregated data
    aggregate_data: List[Dict] = generate_aggregate_data(
        worker_to_annotation_data, args.annotation_video, args.aws_profile
    )

    # Dump aggregated data to CSV files.
    aggregate_data_df = pd.DataFrame(aggregate_data)
    aggregate_data_df.to_csv(args.output_csv, index=False)
