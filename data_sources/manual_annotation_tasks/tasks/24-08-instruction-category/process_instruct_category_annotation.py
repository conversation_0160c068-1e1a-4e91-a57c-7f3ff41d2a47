"""Download the annotation data and create worker wise annotation analysis report for instruct category labeling job data.

Command:
    python data_sources/manual_annotation_tasks/tasks/24-08-instruction-category/process_instruct_category_annotation.py \
        --s3-uri "s3 uri where annotated json are stored.
                    (location of consolidation-request/iteration-1/)" \
        <--aws-profile "Aws profile to be used while downloading annotations.
                            default value: 'default'."> \
        <--sink-directory "local directory path where downloaded json from s3 uri will be stored.
                            default value: 'annotation_data'."> \
        --manifest/-m "path to manifest file default value: 'validation_data.manifest'." \
        <--verbose/-v "Print the details of execution inbetween. default value: 'False'.">
        <--output-csv/-o "output CSV file path. default value: 'annotation_details.csv'."
        <--source-data-location "source data location. default value: 'gpt_annotated_csvs_new'"
        <--source-data-with-label-csv "source data with labeling details.
                                        default value: 'source_data_with_worker_wise_label.csv'"

Note: The arguments shown in '<>' are optional and has default values.
"""

import argparse
from pathlib import Path
from typing import Dict, List

import pandas as pd

from data_sources.manual_annotation_tasks.scripts.parse_annotated_data import (
    worker_to_annotation_map_from_annotation_request,
)
from data_sources.manual_annotation_tasks.scripts.utility import (
    columns_to_include,
    merge_all_worker_data,
    read_csv_to_json,
    read_manifest_data,
)
from util.s3_utility import download_s3_files_recursively


def parse_arguments():
    """
    Parse command-line arguments and return the parsed arguments.

    :return: Parsed arguments
    """
    # Create the parser
    parser = argparse.ArgumentParser(description="Process some directories and file names.")

    # Define the command-line arguments
    parser.add_argument(
        "--s3-uri",
        type=str,
        required=True,
        help="""S3 bucket URI which contains the consolidated data for annotations.
             Use 'exit' to skip this downloading step.""",
    )

    parser.add_argument(
        "--aws-profile",
        type=str,
        default="default",
        help="S3 bucket URI which contains the consolidated data for annotations.",
    )

    parser.add_argument(
        "--sink-directory",
        "-s",
        type=str,
        default="annotation_data",
        help="Directory where the downloaded files will be stored",
    )

    parser.add_argument(
        "--manifest",
        "-m",
        type=str,
        required=True,
        help="Manifest file for validation dataset",
    )

    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")

    parser.add_argument(
        "--output-csv",
        "-o",
        type=str,
        default="annotation_details.csv",
        help="Output CSV file path",
    )

    parser.add_argument(
        "--source-data-location",
        type=str,
        default="gpt_annotated_csvs_new",
        help="source data folder location where all the csvs are located.",
    )

    parser.add_argument(
        "--source-data-with-label-csv",
        type=str,
        default="source_data_with_worker_wise_label.csv",
        help="source data folder location where all the csvs are located.",
    )

    # Parse the arguments
    all_args = parser.parse_args()

    return all_args


def df_util_count_unique_label(row: pd.Series) -> Dict:
    """Function used in DataFrame.apply. Function returns the unique label count for each sample

    Args:
        row (pd.Series) : Dataframe row contains the worker wise labeling details

    Returns:
        Dict: Dictionary of unique label count
    """
    value_count: Dict = {}
    for col, value in row.items():
        if col.startswith("private"):
            if value not in value_count:
                value_count[value] = 1
            else:
                value_count[value] = value_count[value] + 1
    return value_count


def process_instruct_category_annotation(
    annotated_data: pd.DataFrame, manifest_details: pd.DataFrame
) -> pd.DataFrame:
    """Process the annotated data and returns worker wise annotation for each sample with required stats.

    Args:
        annotated_data (pd.DataFrame) : Dataframe contains the details of worker wise annotation details.
        manifest_details (pd.DataFrame) : Dataframe used to extract suggested label
    Returns:
        pd.DataFrame: worker wise annotation for each sample with required stats
    """
    # Separate the utterance uid and utterance.
    annotated_data[["blank", "utterance_uid", "utterance"]] = annotated_data["sentence"].str.split(
        "@", expand=True
    )
    annotated_data.drop(columns=["sentence", "blank"], inplace=True)

    # Merge the dataframe with manifest to get suggested label
    annotated_data: pd.DataFrame = pd.merge(
        annotated_data, manifest_details, on="utterance_uid", how="inner"
    )

    # Pivot the table to convert from worker wise details to utterance wise details.
    annotated_pivot_data: pd.DataFrame = annotated_data.pivot_table(
        index=["data_object_id", "utterance_uid"],
        columns="worker_id",
        values="annotated_label",
        aggfunc="sum",
    ).reset_index()

    # Count the unique label count
    annotated_pivot_data["unique_annotation_count"] = annotated_pivot_data.apply(
        func=df_util_count_unique_label, axis=1
    )

    # Get the unique annotation count.
    annotated_pivot_data["unique_annotation"] = annotated_pivot_data[
        "unique_annotation_count"
    ].apply(lambda r: len(r.keys()))

    # Rearranging the columns in a new order
    primary_key_column: List = ["data_object_id"]
    new_column_order: List = ["utterance_uid", "unique_annotation", "unique_annotation_count"]
    annotated_pivot_data_reordered: pd.DataFrame = annotated_pivot_data[
        primary_key_column + new_column_order
    ]

    # Get the utterance details from annotated_data
    utterance_details: pd.DataFrame = annotated_data[
        primary_key_column + ["utterance", "suggested_label"]
    ]
    utterance_details: pd.DataFrame = utterance_details.drop_duplicates(
        subset=primary_key_column, keep="first"
    )
    semi_annotated_pivot_data: pd.DataFrame = pd.merge(
        utterance_details, annotated_pivot_data_reordered, on="data_object_id", how="inner"
    )

    # Drop the columns present in semi_annotated_pivot_data and keep
    # the worker annotation with respect to data_object_id
    annotated_pivot_data.drop(columns=new_column_order, inplace=True)

    # Merge the utterance and worker details in desired order.
    annotated_pivot_data_merged: pd.DataFrame = pd.merge(
        semi_annotated_pivot_data, annotated_pivot_data, on="data_object_id", how="inner"
    )
    annotated_pivot_data_merged.sort_values(by="unique_annotation", ascending=False, inplace=True)

    return annotated_pivot_data_merged


def include_annotation_details_in_source_data(
    processed_annotated_data: pd.DataFrame,
    source_data_with_label_csv: str,
    source_data_location: str,
) -> pd.DataFrame:
    """Merge the source data with process data of current batch.

    Args:
        processed_annotated_data (pd.DataFrame) : process annotated data.
        source_data_with_label_csv (str) : CSV contain source data with labeling details.
        source_data_location (str) : source data location required when process the first batch of label.
    Returns:
        pd.DataFrame: Source data with worker wise annotation details.
    """
    # If source data with label csv is not exists means we are
    # processing first batch of annotation and it need to read the source data.
    if not Path(source_data_with_label_csv).exists():
        all_data: List = []

        data_source: Path = Path(source_data_location)

        for filepath in data_source.glob("*.csv"):
            csv_file: str = str(filepath)
            print(f"Processing {csv_file}...")
            data = read_csv_to_json(csv_file, columns_to_include, filepath.name)
            all_data.extend(data)

        source_data: pd.DataFrame = pd.DataFrame(all_data)

        source_data: pd.DataFrame = source_data.rename(columns={"uuid": "utterance_uid"})
        source_data.drop(columns=["file", "source-ref"], inplace=True)
    else:
        source_data: pd.DataFrame = pd.read_csv(source_data_with_label_csv)

    annotator_names: List = [
        col for col in processed_annotated_data.columns if col.startswith("private")
    ]

    for index, row in processed_annotated_data.iterrows():
        located_at = source_data.loc[source_data["utterance_uid"] == row["utterance_uid"]].index

        for annotator in annotator_names:
            annotator_type_col_name: str = annotator + "_" + "Type"
            annotator_category_col_name: str = annotator + "_" + "Category"
            annotator_subcategory_col_name: str = annotator + "_" + "Subcategory"
            label_details: List = row[annotator].split(",")
            if len(label_details) > 1:
                type_: str = str(label_details[0]).strip()
                category_: str = str(label_details[1]).strip()
                subcategory_: str = str(label_details[2]).strip()
            else:
                type_: str = str(row[annotator]).strip()
                category_: str = str(row[annotator]).strip()
                subcategory_: str = str(row[annotator]).strip()
            source_data.loc[located_at, annotator_type_col_name] = type_
            source_data.loc[located_at, annotator_category_col_name] = category_
            source_data.loc[located_at, annotator_subcategory_col_name] = subcategory_

    return source_data


if __name__ == "__main__":
    args = parse_arguments()

    # Download data from S3 bucket.
    download_s3_files_recursively(args.aws_profile, args.s3_uri, args.sink_directory)

    # Create worker to annotation map
    s3_label_data: Dict = worker_to_annotation_map_from_annotation_request(args.sink_directory)

    # Create annotation dataframe
    annotated_details: pd.DataFrame = merge_all_worker_data(s3_label_data)

    manifest_data = read_manifest_data(args.manifest)

    manifest_data = manifest_data.rename(columns={"uuid": "utterance_uid"})
    manifest_data["suggested_label"] = (
        manifest_data["Type"]
        + ","
        + manifest_data["Category"]
        + ","
        + manifest_data["Subcategory"]
    )
    manifest_data = manifest_data[["utterance_uid", "suggested_label"]]

    processed_annotated_batch_data = process_instruct_category_annotation(
        annotated_details, manifest_data
    )
    # print(manifest_data)

    output_file_name = args.output_csv
    processed_annotated_batch_data.to_csv(output_file_name, index=False)

    source_data_with_labeling_details = include_annotation_details_in_source_data(
        processed_annotated_batch_data, args.source_data_with_label_csv, args.source_data_location
    )

    source_data_with_labeling_details.to_csv(args.source_data_with_label_csv, index=False)
