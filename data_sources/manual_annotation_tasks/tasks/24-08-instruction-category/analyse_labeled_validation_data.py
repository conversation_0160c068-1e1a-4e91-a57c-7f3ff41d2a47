"""Download the annotation data and create analysis report for instruct category labeling job data.

Assumption: We have ground truth data available for matching.

Command:
    python data_sources/manual_annotation_tasks/tasks/24-08-instruction-category/analyse_labeled_validation_data.py \
        --s3-uri "s3 uri where annotated json are stored.
                    (location of consolidation-request/iteration-1/)" \
        <--aws-profile "Aws profile to be used while downloading annotations.
                            default value: 'default'."> \
        <--sink-directory "local directory path where downloaded json from s3 uri will be stored.
                            default value: 'annotation_data'."> \
        <--ground-truth-data-dir "Directory contains the CSV files having ground truth data.
                                    default value: 'manually_annotated_examples/validation'>
        <--manifest/-m "path to manifest file default value: 'validation_data.manifest'."> \
        <--verbose/-v "Print the details of execution inbetween. default value: 'False'.">
        <--output-csv/-o "output CSV file path. default value: 'analyse_validation_80_20.csv'."
        <--wrong-label-csv/-w "Output file path for sorted wrong label details.
                                default value: 'wrong_label_sorted.csv'."
        <--ground-truth-file-sequence/-g "Maintain the order for ground truth validation file as
                                            it is used for manifest creation"

Note: The arguments shown in '<>' are optional and has default values.
"""

import argparse
import json
from pathlib import Path
from typing import Dict, Generator, List

import numpy as np
import pandas as pd
from pandas.core.groupby.generic import DataFrameGroupBy
from sklearn.model_selection import train_test_split

from data_sources.manual_annotation_tasks.scripts.parse_annotated_data import (
    worker_to_annotation_map_from_annotation_request,
)
from data_sources.manual_annotation_tasks.scripts.utility import (
    merge_all_worker_data,
    read_manifest_data,
)
from util.s3_utility import download_s3_files_recursively


def parse_arguments():
    """
    Parse command-line arguments and return the parsed arguments.

    :return: Parsed arguments
    """
    # Create the parser
    parser = argparse.ArgumentParser(description="Process some directories and file names.")

    # Define the command-line arguments
    parser.add_argument(
        "--s3-uri",
        type=str,
        required=True,
        help="""S3 bucket URI which contains the consolidated data for annotations.
             Use 'exit' to skip this downloading step.""",
    )

    parser.add_argument(
        "--aws-profile",
        type=str,
        default="default",
        help="S3 bucket URI which contains the consolidated data for annotations.",
    )

    parser.add_argument(
        "--sink-directory",
        "-s",
        type=str,
        default="annotation_data",
        help="Directory where the downloaded files will be stored",
    )

    parser.add_argument(
        "--ground-truth-data-dir",
        "-gt",
        type=str,
        default="manually_annotated_examples/validation",
        help="Directory where the ground truth data for validation set is stored.",
    )

    parser.add_argument(
        "--manifest",
        "-m",
        type=str,
        default="validation_data.manifest",
        help="Manifest file for validation dataset",
    )

    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    parser.add_argument(
        "--ground-truth-file-sequence",
        "-g",
        action="store_true",
        help="""Maintain order for validation ground truth files 
                (manifest are created in this order)""",
    )

    parser.add_argument(
        "--output-csv",
        "-o",
        type=str,
        default="analyse_validation_80_20.csv",
        help="Output CSV file path",
    )

    parser.add_argument(
        "--wrong-label-csv",
        "-w",
        type=str,
        default="wrong_label_sorted.csv",
        help="Output CSV file path",
    )

    # Parse the arguments
    all_args = parser.parse_args()

    return all_args


def generate_hash(subject_with_trial_number: str, row_index: str) -> str:
    """Concat string and create unique key

    This unique key will help to locate the data source using in annotation.

    Args:
        subject_with_trial_number (str): subject with trial number
        row_index (str): Row number from 'subject_with_trial_number.csv' file

    Returns:

    """
    # Concatenate trial_number and row_index as string
    unique_string: str = f"{subject_with_trial_number}-{row_index}"

    return unique_string


def read_ground_truth_data(directory_path: str, maintain_order: bool = False) -> pd.DataFrame:
    """Read ground truth data from input directory

    Args:
        directory_path (str): Ground truth data directory path.
        maintain_order (bool): Maintain order

    Returns:
        pd.DataFrame: Ground truth data.
    """
    all_data: List = []

    directory: Path = Path(directory_path)

    files_seq = [
        directory.joinpath("P606-trial_14.csv"),
        directory.joinpath("P608-trial_20.csv"),
        directory.joinpath("P611-trial_17.csv"),
        directory.joinpath("P611-trial_18.csv"),
    ]

    if maintain_order:
        # Read the validation data in same sequence to maintain the order with manifest file creation.
        file_iterator: List = files_seq
    else:
        file_iterator: Generator = directory.iterdir()

    # Load data from each file
    for file in file_iterator:
        data: pd.DataFrame = pd.read_csv(file)
        filename: str = Path(file).name
        trial_name: str = filename.split(".")[0]
        data["uuid"] = data.index.to_series().apply(lambda idx: generate_hash(trial_name, idx))
        all_data.append(data)

    # Combine all data into a single DataFrame
    combined_data_raw: pd.DataFrame = pd.concat(all_data, ignore_index=True)

    combined_data: pd.DataFrame = combined_data_raw.rename(
        columns={
            "0": "sentence",
        }
    )

    combined_data: pd.DataFrame = combined_data[
        ["sentence", "Type", "Category", "Subcategory", "uuid"]
    ]

    # First line of the few CSVs are having 'nan' values for all the columns.
    # Those data points are not used in creation of manifest and also UUID is missing
    # in first labeling job. So, to match exact order with ground truth data points,
    # We need this filter.
    combined_data: pd.DataFrame = combined_data[combined_data["sentence"].notna()]

    combined_data["Subcategory"] = combined_data["Subcategory"].fillna("None")
    combined_data["label"] = combined_data.apply(
        lambda r: f"{r['Type']}, {r['Category']}, {r['Subcategory']}", axis=1
    )

    combined_data["sentence"] = combined_data.apply(lambda r: f"{r['sentence']}", axis=1)

    # Separate features and labels
    features: pd.DataFrame = combined_data[["sentence", "uuid"]]
    labels: pd.DataFrame = combined_data[["Type", "Category", "Subcategory", "label"]]

    # We have to change the label for 20 % of validation data. That is why all validation set is split in two part.
    # and label for validation set is shuffled in suggestion.
    x_train, x_val, y_train, y_val = train_test_split(
        features, labels, test_size=0.2, random_state=42
    )

    # Combine features and labels back into DataFrames
    train_data: pd.DataFrame = pd.DataFrame(
        {
            "sentence": x_train["sentence"],
            "uuid": x_train["uuid"],
            "Type": y_train["Type"],
            "Category": y_train["Category"],
            "Subcategory": y_train["Subcategory"],
            "true_label": y_train["label"],
            "gpt_label": False,  # Wrong label in suggestion
        }
    )
    val_data: pd.DataFrame = pd.DataFrame(
        {
            "sentence": x_val["sentence"],
            "uuid": x_val["uuid"],
            "Type": y_val["Type"],
            "Category": y_val["Category"],
            "Subcategory": y_val["Subcategory"],
            "true_label": y_val["label"],
            "gpt_label": True,  # Wrong label in suggestion
        }
    )
    val_data: pd.DataFrame = val_data.drop_duplicates(subset="sentence")

    val_data["Type"] = np.random.RandomState(seed=42).permutation(val_data["Type"].values)
    val_data["Category"] = np.random.RandomState(seed=42).permutation(val_data["Category"].values)
    val_data["Subcategory"] = np.random.RandomState(seed=42).permutation(
        val_data["Subcategory"].values
    )

    merged_data_df: pd.DataFrame = pd.concat([train_data, val_data], ignore_index=True)
    merged_data_df: pd.DataFrame = merged_data_df.sample(frac=1, random_state=42).reset_index(
        drop=True
    )

    return merged_data_df


def extract_wrong_labels_in_order(
    annotated_data_with_ground_truth: pd.DataFrame, wrong_label_csv: str
) -> None:
    """Filter the wrong annotated data and sort it in such a way that
    maximum wrong data object will appear first.

    Args:
        annotated_data_with_ground_truth (pd.DataFrame): annotated data with ground truth details.
        wrong_label_csv (str): Output CSV file path.

    Returns:
        None
    """

    annotated_data_with_ground_truth["annotated_label"] = annotated_data_with_ground_truth[
        "annotated_label"
    ].str.replace("lookingahead", "looking ahead", regex=False)

    wrong_annotated_data: pd.DataFrame = annotated_data_with_ground_truth[
        annotated_data_with_ground_truth["annotated_label"]
        != annotated_data_with_ground_truth["ground_truth_label"]
    ]

    # Step 1: Group by 'sentence' and calculate count
    count_series: pd.Series = wrong_annotated_data["data_object_id"].value_counts()

    # Step 2: Map this count back to the original DataFrame
    wrong_annotated_data["Count"] = wrong_annotated_data["data_object_id"].map(count_series)

    wrong_annotated_grouped: DataFrameGroupBy = wrong_annotated_data.groupby("data_object_id")

    updated_wrong_annotated_data: List = []
    unique_worker_id: List = annotated_data_with_ground_truth["worker_id"].unique().tolist()

    # Fill the correct worker wise correct label using data_object_id.
    for data_object_id, group in wrong_annotated_grouped:
        new_row = {}
        for _, row in group.iterrows():
            w_id: str = str(row["worker_id"]).rsplit(".", maxsplit=1)[-1]
            new_row["data_object_id"] = data_object_id
            new_row["sentence"] = row["sentence"]
            new_row[f"annotated_label_{w_id}"] = row["annotated_label"].upper()
            new_row["ground_truth_label"] = row["ground_truth_label"]
            new_row["gpt_label/wrong_label"] = row["gpt_label/wrong_label"]
            new_row["count"] = row["Count"]

        for worker in unique_worker_id:
            w_id: str = worker.split(".")[-1]
            key: str = f"annotated_label_{w_id}"
            if key not in new_row:
                filtered_data: pd.DataFrame = annotated_data_with_ground_truth[
                    (
                        annotated_data_with_ground_truth["data_object_id"]
                        == new_row["data_object_id"]
                    )
                    & (annotated_data_with_ground_truth["worker_id"] == worker)
                ]
                if not filtered_data.empty:
                    new_row[key] = filtered_data["annotated_label"].iloc[0]
        updated_wrong_annotated_data.append(new_row)

    updated_wrong_annotated_data_df: pd.DataFrame = pd.DataFrame(updated_wrong_annotated_data)

    # Step 3: Sort the DataFrame by 'Count' in descending order
    updated_wrong_annotated_data_df_sorted: pd.DataFrame = (
        updated_wrong_annotated_data_df.sort_values(by="count", ascending=False)
    )

    column_seq: List = (
        ["data_object_id", "sentence"]
        + [f"annotated_label_{w_id.split('.')[-1]}" for w_id in unique_worker_id]
        + ["ground_truth_label", "gpt_label/wrong_label", "count"]
    )

    updated_wrong_annotated_data_df_sorted: pd.DataFrame = updated_wrong_annotated_data_df_sorted[
        column_seq
    ]
    # Write a data to CSV file.
    updated_wrong_annotated_data_df_sorted.to_csv(wrong_label_csv, index=False)


def merge_annotated_and_ground_truth_data(
    annotation_data_df: pd.DataFrame,
    ground_truth_df: pd.DataFrame,
    manifest_data_df: pd.DataFrame,
    output_csv: str,
) -> pd.DataFrame:
    """Merge the annotated data and ground truth data using data object id.

    Args:
        annotation_data_df (pd.DataFrame): annotation data
        ground_truth_df (pd.DataFrame): ground truth data
        manifest_data_df (pd.DataFrame): manifest data
        output_csv (str): Output CSV file.

    Returns:
        pd.DataFrame: Return merge data.
    """
    annotation_data_df["gpt_label/wrong_label"] = ""
    annotation_data_df["ground_truth_label"] = ""
    annotation_data_df["uuid"] = ""

    annotation_data_df.rename(columns={"label": "annotated_label"}, inplace=True)

    for index, row in annotation_data_df.iterrows():
        data_object_id: int = int(row["data_object_id"])
        ground_truth_row: pd.Series = ground_truth_df.iloc[data_object_id]
        gpt_label: str = ground_truth_row["gpt_label"]
        ground_truth_label: str = ground_truth_row["true_label"]

        manifest_row: pd.Series = manifest_data_df.iloc[data_object_id]

        annotation_data_df.at[index, "uuid"] = ground_truth_row["uuid"]
        if gpt_label:
            annotation_data_df.at[index, "gpt_label/wrong_label"] = (
                manifest_row["Type"]
                + ","
                + manifest_row["Category"]
                + ","
                + manifest_row["Subcategory"]
            )
            annotation_data_df.at[index, "ground_truth_label"] = ground_truth_label
        else:
            annotation_data_df.at[index, "ground_truth_label"] = ground_truth_label
        annotation_data_df.at[index, "ground_truth_sentence"] = ground_truth_row["sentence"]

    final_data: pd.DataFrame = annotation_data_df[
        [
            "data_object_id",
            "worker_id",
            "sentence",
            "uuid",
            "annotated_label",
            "gpt_label/wrong_label",
            "ground_truth_label",
        ]
    ]

    final_data["ground_truth_label"] = final_data["ground_truth_label"].str.lower()
    final_data["gpt_label/wrong_label"] = final_data["gpt_label/wrong_label"].str.lower()

    final_data.to_csv(output_csv, index=False)

    return final_data


def generate_analysis_report(annotated_data_with_ground_truth: pd.DataFrame) -> None:
    """Generate the worker wise accuracy and overall accuracy report.
    Also, create separate report for data points with wrong suggestions.

    Args:
        annotated_data_with_ground_truth (pd.DataFrame): dataframe having annotated and ground truth

    Returns:
        None
    """
    wrong_label_analysis: pd.DataFrame = annotated_data_with_ground_truth[
        annotated_data_with_ground_truth["gpt_label/wrong_label"] != ""
    ]

    # Calculate the accuracy for each worker along with the row count
    worker_analysis: pd.DataFrame = (
        annotated_data_with_ground_truth.groupby("worker_id")
        .agg(
            total_count=("worker_id", "size"),
            correct_count=(
                "annotated_label",
                lambda x: (
                    x == annotated_data_with_ground_truth.loc[x.index, "ground_truth_label"]
                ).sum(),
            ),
        )
        .reset_index()
    )

    # Calculate accuracy for each worker
    worker_analysis["accuracy"] = worker_analysis["correct_count"] / worker_analysis["total_count"]

    # Calculate overall accuracy
    overall_accuracy: float = (
        annotated_data_with_ground_truth["annotated_label"]
        == annotated_data_with_ground_truth["ground_truth_label"]
    ).mean()

    # Display the results
    print("Worker-wise Accuracy:")
    print(worker_analysis)

    print("\nOverall Accuracy:")
    print(overall_accuracy)

    worker_analysis["overall_accuracy"] = overall_accuracy

    worker_analysis.to_csv("worker_analysis.csv", index=False)

    # Calculate the accuracy for each worker along with the row count
    wrong_label_suggestion: pd.DataFrame = (
        wrong_label_analysis.groupby("worker_id")
        .agg(
            total_count=("worker_id", "size"),
            correct_count=(
                "annotated_label",
                lambda x: (x == wrong_label_analysis.loc[x.index, "ground_truth_label"]).sum(),
            ),
        )
        .reset_index()
    )

    # Calculate accuracy for each worker
    wrong_label_suggestion["accuracy"] = (
        wrong_label_suggestion["correct_count"] / wrong_label_suggestion["total_count"]
    )

    # Calculate overall accuracy
    wrong_label_overall_accuracy: float = (
        wrong_label_analysis["annotated_label"] == wrong_label_analysis["ground_truth_label"]
    ).mean()

    # Display the results
    print("Worker-wise Accuracy:")
    print(wrong_label_suggestion)

    print("\nOverall Accuracy:")
    print(wrong_label_overall_accuracy)

    wrong_label_suggestion["overall_accuracy"] = wrong_label_overall_accuracy
    wrong_label_suggestion.to_csv("wrong_label_analysis.csv", index=False)


if __name__ == "__main__":
    args = parse_arguments()

    # Download data from S3 bucket.
    download_s3_files_recursively(args.aws_profile, args.s3_uri, args.sink_directory)

    # Create worker to annotation map
    s3_label_data: Dict = worker_to_annotation_map_from_annotation_request(args.sink_directory)

    # Create annotation dataframe
    annotated_data: pd.DataFrame = merge_all_worker_data(s3_label_data)

    # Read ground truth data
    ground_truth_data: pd.DataFrame = read_ground_truth_data(
        args.ground_truth_data_dir, maintain_order=args.ground_truth_file_sequence
    )

    # Read manifest file.
    manifest_data: pd.DataFrame = read_manifest_data(args.manifest)

    # Merge annotation and ground truth data using data object id.
    merged_data: pd.DataFrame = merge_annotated_and_ground_truth_data(
        annotated_data, ground_truth_data, manifest_data, args.output_csv
    )

    # Generate analysis report (Currently it is printing in console.)
    generate_analysis_report(merged_data)

    # Dump wrongly annotated data in sorted order.
    extract_wrong_labels_in_order(merged_data, args.wrong_label_csv)
