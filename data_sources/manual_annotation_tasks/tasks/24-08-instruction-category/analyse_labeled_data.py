"""Download the annotation data and create analysis report for instruct category labeling job data.

Assumption: We have ground truth data available for matching.

Note: Currently no analytics data is generated. We can create worker wise evaluation matrix in the output.

Command:
    python scripts/analyse_labeled_data.py \
        --s3-uri "s3 uri where annotated json are stored. (location of consolidation-request/iteration-1/)" \
        <--aws-profile "Aws profile to be used while downloading annotations. default value: 'default'"> \
        <--sink-directory "local directory path where downloaded json from s3 uri will be stored.
                            default value: 'annotation_data'"> \
        <--ground-truth-data-dir "Directory contains the CSV files having ground truth data.
                                    default value: ''>
        <--verbose/-v "Print the details of execution inbetween. default value: 'False'">
        <--output-csv/-o "output CSV file path. default value: 'label_details_with_ground_truth.csv'"

Note: The arguments shown in '<>' are optional and has default values.
"""

import argparse
import os
from typing import Dict, List, Optional

import pandas as pd

from data_sources.manual_annotation_tasks.scripts.parse_annotated_data import (
    worker_to_annotation_map_from_annotation_request,
)
from data_sources.manual_annotation_tasks.scripts.utility import read_all_csv_from_directory
from util.s3_utility import download_s3_files_recursively

columns_to_include: List = ["Type", "Category", "Subcategory", "Sentence", "0", "couch_subtitle"]

source_ref_column_list: List = ["Sentence", "0", "couch_subtitle"]


def parse_arguments():
    """
    Parse command-line arguments and return the parsed arguments.

    :return: Parsed arguments
    """
    # Create the parser
    parser = argparse.ArgumentParser(
        description="Create analysis report for instruction category labeling job."
    )

    # Define the command-line arguments
    parser.add_argument(
        "--s3-uri",
        type=str,
        required=True,
        help="""S3 bucket URI which contains the consolidated data for annotations.
             Use 'exit' to skip this downloading step.""",
    )

    parser.add_argument(
        "--aws-profile",
        type=str,
        default="default",
        help="S3 bucket URI which contains the consolidated data for annotations.",
    )

    parser.add_argument(
        "--sink-directory",
        "-s",
        type=str,
        default="annotation_data",
        help="Directory where the downloaded files will be stored",
    )

    parser.add_argument(
        "--ground-truth-data-dir",
        "-gt",
        type=str,
        default="manually_annotated_examples",
        help="Directory where the downloaded videos will be stored",
    )

    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")

    parser.add_argument(
        "--output-csv",
        "-o",
        type=str,
        default="label_details_with_ground_truth.csv",
        help="Output CSV file path",
    )

    # Parse the arguments
    all_args = parser.parse_args()

    return all_args


def read_ground_truth_data(data_dir: str) -> pd.DataFrame:
    """Read the ground truth data from directory and return the combined dataframe.

    :param data_dir: ground truth data directory

    :return: (pd.DataFrame) combined ground truth dataframe.
    """
    # Reading true labels.
    ground_truth_data: pd.DataFrame = read_all_csv_from_directory(data_dir, columns_to_include)

    # Sentences may present under different column name. So, merge them and generate single column "sentence"
    combined_column: Optional[pd.Series] = None
    for col in source_ref_column_list:
        if col in ground_truth_data.columns:
            if combined_column is None:
                combined_column: pd.Series = ground_truth_data[col]
            else:
                combined_column: pd.Series = combined_column.combine_first(ground_truth_data[col])

    # Assign the combined column to the DataFrame
    if combined_column is not None:
        ground_truth_data["sentence"] = combined_column

    ground_truth_data: pd.DataFrame = ground_truth_data[
        ["sentence", "Type", "Category", "Subcategory", "file", "row_number"]
    ]

    # First line of the few CSVs are having 'nan' values for all the columns.
    # Those data points are not used in creation of manifest and also UUID is missing
    # in first labeling job. So, to match exact order with ground truth data points,
    # We need this filter.
    ground_truth_data: pd.DataFrame = ground_truth_data[ground_truth_data["sentence"].notna()]

    ground_truth_data["Subcategory"] = ground_truth_data["Subcategory"].fillna("None")

    ground_truth_data["label"] = ground_truth_data.apply(
        lambda row: f"{row['Type'].lower()}, {row['Category'].lower()}, {row['Subcategory'].lower()}",
        axis=1,
    )

    return ground_truth_data


def compare_labeled_data_with_ground_truth(
    worker_data: Dict, ground_truth_data: pd.DataFrame, output_file: str
) -> None:
    """Read annotation data form 'data_path' directory

    Args:
        worker_data (Dict): Directory contains annotated data.
        ground_truth_data (pd.DataFrame): Dataframe contains the ground truth data.
        output_file (str): Output CSV file name.

    Returns:
        None
    """
    updated_worker_id_to_labels_map: Dict = {}
    for w_id, worker_labels in worker_data.items():
        updated_worker_details: List = []
        for each_label in worker_labels:
            ground_truth_data_id: int = int(each_label["data_object_id"])
            # Fetch the ground truth data using data object id.
            ground_truth_data_object: pd.Series = ground_truth_data.iloc[ground_truth_data_id]

            true_label: str = ground_truth_data_object["label"]
            each_label["true_label"] = true_label

            trial_details: str = ground_truth_data_object["file"].split(".")[0]
            row_number: str = str(ground_truth_data_object["row_number"])
            unique_id: str = trial_details + "--" + row_number
            each_label["uuid"] = unique_id
            updated_worker_details.append(each_label)

        updated_worker_id_to_labels_map[w_id] = updated_worker_details

    all_worker_label_details: List = []
    for w_id, worker_labels in worker_data.items():
        worker_details_df: pd.DataFrame = pd.DataFrame(worker_labels)
        all_worker_label_details.append(worker_details_df)

    result: pd.DataFrame = pd.concat(all_worker_label_details, ignore_index=True)
    reorder_result: pd.DataFrame = result[
        ["data_object_id", "worker_id", "sentence", "annotated_label", "true_label", "uuid"]
    ]
    reorder_result.to_csv(output_file, index=False)


if __name__ == "__main__":
    args = parse_arguments()

    os.environ["VERBOSE"] = str(args.verbose)

    # Download the data from S3.
    download_s3_files_recursively(args.aws_profile, args.s3_uri, args.sink_directory)

    # Read annotated data.
    s3_label_data: Dict = worker_to_annotation_map_from_annotation_request(args.sink_directory)

    # Read ground truth data.
    ground_truth: pd.DataFrame = read_ground_truth_data(args.ground_truth_data_dir)

    # Compare annotated data with ground truth data.
    compare_labeled_data_with_ground_truth(s3_label_data, ground_truth, args.output_csv)
