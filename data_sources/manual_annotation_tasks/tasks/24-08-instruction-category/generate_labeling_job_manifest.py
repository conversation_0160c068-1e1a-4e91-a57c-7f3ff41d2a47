"""This script will read the CSVs available locally and generate manifest file
for instruct category labeling job.

Prerequisites:
    1. Dataset is downloaded and stored locally.
    2. Input data must be in CSV format.

Command:
    python data_sources/manual_annotation_tasks/tasks/24-08-instruction-category/generate_labeling_job_manifest.py \
        --data-location "Local folder containing the list of CSVs. (CSVs will not be read recursively)" \
        --manifest-file "The manifest file."

Output Structure: single data point/row structure in manifest file.

{
    "coach_subtitle": "full throttle", # --> column from CSV file.
    "start_time": "2838.16", # --> column from CSV file.
    "end_time": "2838.703", # --> column from CSV file.
    "Type": "instruction", # --> column from CSV file.
    "Category": "throttle", # --> column from CSV file.
    "Subcategory": "full", # --> column from CSV file.
    "file": "P607-trial_10_gpt_annotated.csv", # ---> file from which the data is read.
    "uuid": "P607-trial_10--0",   # ---> # subject_id-trial_<N>--<row number>
    "source-ref": "@P607-trial_10--0@full throttle"  # ---> Coach subtitle is pre-fixed with UUID.
}

Note :: 'source-ref' will be used in labeling job UI for labeling. Before showing on UI it will be trimmed
and showed only coach subtitle to annotators. This UUID will be helpful to reverse map the coach subtitle to
respective subject.
"""

import argparse
import csv
import json
from pathlib import Path
from typing import List

from data_sources.manual_annotation_tasks.scripts.utility import (
    columns_to_include,
    read_csv_to_json,
)


def write_json(output_json, data):
    with open(output_json, "w", encoding="utf-8") as jsonfile:
        for item in data:
            json.dump(item, jsonfile, ensure_ascii=False)
            jsonfile.write("\n")


def generate_manifest(input_folder, output_json):
    all_data = []

    data_source: Path = Path(input_folder)
    # Loop over all CSV files in the input folder
    for filepath in data_source.glob("*.csv"):
        csv_file = str(filepath)
        print(f"Processing {csv_file}...")
        data = read_csv_to_json(csv_file, columns_to_include, filepath.name)
        all_data.extend(data)

    print(f"Total number of data point written to the manifest is {len(all_data)}")
    # Write all collected data to the output JSON file
    write_json(output_json, all_data)
    print(f"Data has been written to {output_json}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Generate a manifest file from CSVs files in a specified directory."
    )
    parser.add_argument(
        "--data-location",
        "-d",
        type=str,
        help="The directory containing CSVs. (CSVs will not be read recursively)",
    )
    parser.add_argument(
        "--manifest-file",
        "-o",
        type=str,
        help="The output file where the manifest will be saved.",
    )

    args = parser.parse_args()

    generate_manifest(args.data_location, args.manifest_file)
