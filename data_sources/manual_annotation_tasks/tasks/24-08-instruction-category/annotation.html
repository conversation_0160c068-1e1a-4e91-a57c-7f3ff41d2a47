<script src="https://assets.crowd.aws/crowd-html-elements.js"></script>

<style>
    body {
        font-family: Arial, sans-serif;
        background-color: #F4F4F4;
        display: flex;
    }
    .container {
        display: flex; /* Use flexbox for layout */
        justify-content: space-between; /* Optional: adds space between items */
        margin: 40px; /* Space around container */
    }
    .item {
        padding: 20px;
        float: left;
        width: 50%; /* or any width you prefer */
        box-sizing: border-box;
        margin: 40px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        margin: 0 10px; /* Add horizontal margin for spacing */
        min-width: 800px;
    }
    .label {
        padding: 20px;
        float: left;
        width: 45%; /* or any width you prefer */
        box-sizing: border-box;
        margin: 40px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        margin: 0 10px; /* Add horizontal margin for spacing */
        min-width: 400px;
    }
    .disabled-input {
        display: none; /* Initially hidden */
    }
</style>

<crowd-form>
    
  <div class="container" name='category'>
    <div class="item">
        <h4>Your task is to correct the label below the sentence, if it is wrong.</h4>
        <p>The coach said: <strong>{{ task.input.sourceRef | remove:task.input.raw.uuid | remove:'@' | lstrip | rstrip }}<br /><br /></strong>Label: <strong>{{ task.input.raw.Type }}, {{ task.input.raw.Category }}, {{ task.input.raw.Subcategory }}</strong></p>
    </div>

    <div class="label" name='category'>
      <label>Choose The Option:</label>
      <select name="label" id='label' style="font-size: large; margin: 0 0 20px 0;" required onchange="toggleInput(this)">
        <option value="instruction, brake, off">instruction, brake, off</option>
        <option value="instruction, brake, on">instruction, brake, on</option>
        <option value="instruction, left, move">instruction, left, move</option>
        <option value="instruction, left, stay">instruction, left, stay</option>
        <option value="instruction, lookingahead, direction">instruction, lookingahead, direction</option>
        <option value="instruction, lookingahead, landmark">instruction, lookingahead, landmark</option>
        <option value="instruction, lookingahead, none">instruction, lookingahead, none</option>
        <option value="instruction, middle, move">instruction, middle, move</option>
        <option value="instruction, middle, stay">instruction, middle, stay</option>
        <option value="instruction, right, move">instruction, right, move</option>
        <option value="instruction, right, stay">instruction, right, stay</option>
        <option value="instruction, steering, alot">instruction, steering, alot</option>
        <option value="instruction, steering, alotless">instruction, steering, alotless</option>
        <option value="instruction, steering, landmark">instruction, steering, landmark</option>
        <option value="instruction, steering, less">instruction, steering, less</option>
        <option value="instruction, steering, little">instruction, steering, little</option>
        <option value="instruction, steering, position">instruction, steering, position</option>
        <option value="instruction, steering, quickness">instruction, steering, quickness</option>
        <option value="instruction, steering, smoothness">instruction, steering, smoothness</option>
        <option value="instruction, steering, stay">instruction, steering, stay</option>
        <option value="instruction, steering, straighten">instruction, steering, straighten</option>
        <option value="instruction, throttle, off">instruction, throttle, off</option>
        <option value="instruction, throttle, on">instruction, throttle, on</option>
        <option value="instruction, throttle, stay">instruction, throttle, stay</option>
        <option value="instruction, turn, left">instruction, turn, left</option>
        <option value="instruction, turn, more">instruction, turn, more</option>
        <option value="instruction, turn, none">instruction, turn, none</option>
        <option value="instruction, turn, right">instruction, turn, right</option>
        <option value="commentary, driving, explanation">commentary, driving, explanation</option>
        <option value="commentary, driving, preparation">commentary, driving, preparation</option>
        <option value="commentary, driving, recommendation">commentary, driving, recommendation</option>
        <option value="commentary, driving, reflection">commentary, driving, reflection</option>
        <option value="commentary, driving, summarization">commentary, driving, summarization</option>
        <option value="commentary, environment, explanation">commentary, environment, explanation</option>
        <option value="commentary, environment, reminder">commentary, environment, reminder</option>
        <option value="commentary, humanstate, none">commentary, humanstate, none</option>
        <option value="commentary, query, none">commentary, query, none</option>
        <option value="commentary, vehiclestate, none">commentary, vehiclestate, none</option>
        <option value="feedback, affirmation, none">feedback, affirmation, none</option>
        <option value="feedback, positive, none">feedback, positive, none</option>
        <option value="other, other, other">other, other, other</option>
        <option value="unsure">unsure</option>
      </select>

      <div class="disabled-input" id="unsure-ui" name='unsure-reason'>
        <label>Why unsure?</label>
        <input type="text" id="data-item" name='unsure-reason' placeholder="Please specify why you're unsure" />
      </div>
    </div>

  </div>

  <crowd-instructions link-text="View instructions" link-type="button">
	  <short-summary>
	    <h2>Annotation Task Specification:</h2>

      <p>The sentence shown on the screen was said by a car racing coach during a sim-racing coaching session. These sentences are short phrases.

      Your task is to annotate the sentence with a tuple of comma separated values (Type, Category, Subcategory). The possible tuples can be seen in the drop-down menu. Additionally, for each phrase there will be a (type, category, subcategory) <b>already shown</b> next to it. You need to correct the tuple if it is wrong.</p>
	  </short-summary>

		<detailed-instructions>
		    <h2>Annotation Guidelines for Sentence categorization annotation task:</h2>


<p>This guide provides guidelines for completing the sentence categorization annotation task successfully.</p>

<p>The guide contains:</p>

<ol>
  <ol type="1">
      <li>Specification of the annotation task.</li>
      <li>General guidelines and things to keep in mind</li>
      <li>Specific failure cases that have happened during the initial test phases and ways in which the accuracy of annotations needs to be improved.</li>
  </ol>
</ol>

<h2>General Guidelines:</h2>

<p>Before you start your annotation job, please get very familiar with the examples provided at <a href='https://drive.google.com/drive/folders/12KzPS-6RPQDPGBeklcjQHHokhbUlvMGJ' target="_blank">ground truth examples</a>. These examples cover a wide variety of sentences and their corresponding <b>correct ground truth</b> tuples.</p>

<p>Learn how (and why) different sentences are mapped to the corresponding tuples. Feel free to refer back to these examples while you are doing the annotation (if anything confuses you).</p>

<p>Strive your best to make an informed decision. Please be aware of mouse slips when choosing the options from the drop down menu. If you are completely unsure about which one to choose, please select the option <b>‘Unsure’</b>, and leave a reason why you are unsure in the text box.</p>

<h2>Specific failure cases and error mitigation strategies:</h2>

<p>Based on some of the initial tests that the annotators took we observed there are few types of commonly recurring errors. Here are some specific guidelines to minimize those errors. I will provide examples.</p>

  <ol>
      <li>“You got it”, “Then right”
          <ol type="a">
              <li>Ground truth: (feedback, positive, none), (instruction, right, stay) respectively</li>
              <li>Annotation Error: (Instruction Throttle On), (feedback, positive, none) respectively</li>
              <li>Possible reason for errors: This is likely because the annotator may have gotten distracted and hurried through the task. Or it could have been a mouse slip. In either case, <b>please make sure you take enough time to think it through before selecting the correct option</b></li>
          </ol>
      </li>
      <li>“Stay in it”
        <ol type="a">
          <li>Ground truth: (Instruction, throttle, <b>stay</b>)</li>
          <li>Annotation Errors: (Instruction Throttle On), (Commentary Driving Recommendation)</li>
          <li>Mitigation: Pay attention to the verb in the phrase. If it is “stay” then subcategory will be stay, if it is “move” then subcategory will be move. Since the phrase is fairly short, this is of type “instruction” and not “commentary”. In general, think of whether this phrase is uttered by the coach so that they want the student driver to act according to the phrase immediately or later. If immediately, then the phrase is an instruction</li>
        </ol>
      </li>
      <li>"Aim for the tower"
        <ol type="a">
          <li>Ground truth: (instruction, steering, landmark)</li>
          <li>Annotation Errors: (instruction, looking ahead, landmark.)</li>
          <li>Mitigation: Since the ‘verb’ is ‘aim for”, we will have ‘steering’ as the category. If the verb was “over to”, the category would be ‘steering’. If the verb was ‘look for’, or “find” then the category would be ‘looking ahead’.</li>
        </ol>
      </li>
      <li>I need you to turn a little earlier, no brakes on this right-hand turn,, okay?, you can still be a little quicker
        <ol type="a">
          <li>Ground truth: (commentary, driving, recommendation) respectively</li>
          <li>Annotation Errors: (instruction, turn, none), (instruction, brake, off), (instruction, steering, quickness) respectively</li>
          <li>Mitigation: Key is to look at the length of the sentence and make a judgment regarding whether the coach is expecting the student to act upon the phrase immediately or not. Similar to the mitigation strategy in 2c.</li>
        </ol>
      </li>
  </ol>

		  </detailed-instructions>
	</crowd-instructions>

  <!--<div id="feedback" style="margin-top: 20px;"></div>-->
</crowd-form>

<script>
  // Timer start
  const startTime = new Date();
  var suggestedLabel = '{{ task.input.raw.Type }}, {{ task.input.raw.Category }}, {{ task.input.raw.Subcategory }}';

  // Set the label as suggested label in select option
  var selectElement = document.getElementById('label');
  selectElement.value = suggestedLabel;

  function toggleInput(selectElement) {
      const inputField = document.getElementById('unsure-ui');
      const inputTitle = document.getElementById('data-item');
      console.log("Selected Label: ", selectElement.value)
      if (selectElement.value === 'unsure') {
          inputField.style.display = 'block'; // Show input field
          inputField.disabled = false; // Enable input field
      } else {
          inputField.style.display = 'none'; // Hide input field
          inputField.value = ''; // Clear input field
          inputField.disabled = true; // Disable input field
          inputTitle.value = '';
      }
  }

  document.querySelector('crowd-form').onsubmit = function(e) {
      const selectElement = document.querySelector('select[name="label"]');
        const inputField = document.getElementById('data-item');

        // Check if the selected value is "unsure"
        if (selectElement.value === 'unsure' && inputField.value.trim() === '') {
            e.preventDefault(); // Prevent form submission
            alert("Please specify why you're unsure."); // Alert user
        } else {
            const endTime = new Date();
            const timeSpent = (endTime - startTime) / 1000; // Time in seconds
            console.log('Time spent on task:', timeSpent, 'seconds');

            // Append timeSpent to the form data
            const timeSpentInput = document.createElement('input');
            timeSpentInput.type = 'hidden';
            timeSpentInput.name = 'time_spent';
            timeSpentInput.value = timeSpent;
            document.querySelector('crowd-form').appendChild(timeSpentInput);
        }
    }
</script>