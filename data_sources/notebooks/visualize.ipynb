#%%
import os
import pickle
import random
import matplotlib.pyplot as plt
import numpy as np
import collections
import matplotlib
#%%
synced_dfs_dir = "/home/<USER>/Data/Thunderhill/synced_data_frames_new_transcript_labels//"
all_synced_dfs = sorted(os.listdir(synced_dfs_dir))
all_synced_dfs = all_synced_dfs[:-1]  # remove the track.csv
print(all_synced_dfs, len(all_synced_dfs))
#%%
synced_dfs_to_be_skipped = []
for synced_df in all_synced_dfs:
    with open(os.path.join(synced_dfs_dir, synced_df), "rb") as fp:
        s_df = pickle.load(fp)
    if len(s_df) < 1000:
        synced_dfs_to_be_skipped.append(synced_df)

print(synced_dfs_to_be_skipped)
#%%
all_synced_dfs = [df for df in all_synced_dfs if df not in synced_dfs_to_be_skipped]
len(all_synced_dfs)
#%%
for synced_df in all_synced_dfs:
    print("LOADING synced df ", synced_df)
    with open(os.path.join(synced_dfs_dir, synced_df), "rb") as fp:
        s_df = pickle.load(fp)
    #     print(s_df['time'][2] -s_df['time'][1])

    d_all = [d / np.timedelta64(1, "s") for d in np.diff((s_df["time"].values))]
    #     print(np.mean(d_all), np.std(d_all))
    d_mean = np.mean(d_all)
    d_std = np.std(d_all)
    hz = 1.0 / d_mean
    if hz < 30:
        plt.hist(d_all, bins=20)
    print(f"{synced_df} {d_mean} {d_std} {hz}")
#%%
# new labels = 0-no sentence, 1-no action/other,  2-brake, 3-accelerate, 4-lateral-position, 5-left, 6-right, 7-straight 8-turn, 9-look,
LABELS_TO_INSTRUCTION_DICT = {
    0: "NO_SENTENCE",
    1: "NO_ACTION",
    2: "BRAKE",
    3: "ACCELERATE",
    4: "LATERAL-POSITION",
    5: "LEFT",
    6: "RIGHT",
    7: "STRAIGHT",
    8: "TURN",
    9: "LOOK",
}
synced_df = all_synced_dfs[0]
with open(os.path.join(synced_dfs_dir, synced_df), "rb") as fp:
    s_df = pickle.load(fp)
print(s_df["subtitle_label_sentence_level_cabin"].values)
print(collections.Counter(s_df["subtitle_label_sentence_level_cabin"].values))
print(sum(collections.Counter(s_df["subtitle_label_sentence_level_cabin"].values).values()))
plt.plot(s_df["subtitle_label_sentence_level_cabin"].values[3000:5000])
plt.figure()
plt.plot(s_df["v_forward"].values[3000:5000])
all_valid_subtitles = []
all_valid_subtitle_labels = []
for synced_df in all_synced_dfs:
    print("LOADING synced df ", synced_df)
    with open(os.path.join(synced_dfs_dir, synced_df), "rb") as fp:
        s_df = pickle.load(fp)
    valid_subtitle_labels = s_df["subtitle_label_sentence_level_cabin"].values
    valid_subtitles = s_df.subtitle_sentence_level_cabin.values
    print(len(valid_subtitles))
    all_valid_subtitle_labels.extend(list(valid_subtitle_labels))
    all_valid_subtitles.extend(list(valid_subtitles))
#     break
#%%
label_list = []
instruction_list = []
label_list_with_no_sentence = []
instruction_list_with_no_sentence = []

print(len(all_valid_subtitles))
print(type(all_valid_subtitle_labels))
for i, (subtitle, subtitle_label) in enumerate(
    zip(all_valid_subtitles, all_valid_subtitle_labels)
):
    if subtitle.content != "NA":
        #         print(subtitle.content, LABELS_TO_INSTRUCTION_DICT[all_valid_subtitles[i]])
        label_list.append(subtitle_label)
        instruction_list.append(LABELS_TO_INSTRUCTION_DICT[subtitle_label])
    label_list_with_no_sentence.append(subtitle_label)
    instruction_list_with_no_sentence.append(LABELS_TO_INSTRUCTION_DICT[subtitle_label])

print(collections.Counter(label_list))
print(collections.Counter(instruction_list))
print(collections.Counter(label_list_with_no_sentence))
print(collections.Counter(instruction_list_with_no_sentence))
#%%
import pandas as pd

track_path = "/home/<USER>/Data/Thunderhill/track.csv"
with open(track_path, "rb") as fp:
    map_data = pd.read_csv(fp)

refline_xy = np.array(list(zip(map_data["refline/x"].values, map_data["refline/y"].values)))
refline_v = np.array(map_data["refline/v"])
min_x_val = min(map_data["refline/x"].values) - 100
max_x_val = max(map_data["refline/x"].values) + 100
min_y_val = min(map_data["refline/y"].values) - 100
max_y_val = max(map_data["refline/y"].values) + 100
print(min_x_val, max_x_val, min_y_val, max_y_val)
# print(refline_xy.shape, refline_v.shape)
# print(torch.tensor(coord_xy_in_range).shape)
# torch.tensor(refline_xy).shape
#%%
import torch


def all_pairs_euclid_torch(A, B):
    sqrA = torch.sum(torch.pow(A, 2), 1, keepdim=True).expand(A.shape[0], B.shape[0])
    sqrB = torch.sum(torch.pow(B, 2), 1, keepdim=True).expand(B.shape[0], A.shape[0]).t()
    return torch.sqrt(sqrA - 2 * torch.mm(A, B.t()) + sqrB)
#%%
min_indices = torch.argmin(
    all_pairs_euclid_torch(torch.tensor(coord_xy_in_range), torch.tensor(refline_xy)), dim=1
)
np.array(refline_v[min_indices.tolist()]).shape
#%%
SKIP_NUM = 20
LAP_IDS = [2]
POS_SKIP_NUM = 20
PLOT_RANGE_START = 800
PLOT_RANGE_END = 800
MIN_L = 500
# TOTAL OF 206 laps of driving from 29 sessions
plt.figure(figsize=(16, 12))
visualization_dir = "/home/<USER>/Data/Thunderhill/Visualizations/Speed_Visualization_New_Transcript_DiffVel"
os.makedirs(visualization_dir, exist_ok=True)

for synced_df in all_synced_dfs[1:]:
    print("SYNCED_DF", synced_df[:-4])
    with open(os.path.join(synced_dfs_dir, synced_df), "rb") as fp:
        s_df = pickle.load(fp)

    # filter out timepoints where only no_sentence happened
    valid_subtitle_indices_and_subtitles = [
        (i, s)
        for i, s in enumerate(s_df.subtitle_sentence_level_cabin.values)
        if s.content != "NA"
    ]
    print(
        len(valid_subtitle_indices_and_subtitles),
        len(s_df.subtitle_sentence_level_cabin.values),
        len(valid_subtitle_indices_and_subtitles) / len(s_df.subtitle_sentence_level_cabin.values),
    )
    lap_nums = set(s_df["lap/num"].values)  # number of laps
    # create folder for the df
    if not os.path.exists(os.path.join(visualization_dir, synced_df[:-4])):
        os.makedirs(os.path.join(visualization_dir, synced_df[:-4]), exist_ok=True)
    for lapnum in lap_nums:
        print(f"PROCESSING LAP NUM {lapnum}")

        # exatrct points for lapnam
        all_valid_indices = [
            t[0]
            for t in valid_subtitle_indices_and_subtitles
            if s_df["lap/num"].values[t[0]] == lapnum
        ]
        #         print(len(valid_indices))

        if len(all_valid_indices) == 0:
            print(f"NOT ENOUGH VALID INDICES for LAP {lapnum} in {synced_df[:-4]}")
            print(len(valid_indices))
            continue

        total_len_of_all_valid_indices = len(all_valid_indices)
        # divide up a lap into chunks of length MIN_L
        num_chunks = total_len_of_all_valid_indices // MIN_L
        print("TOTAL LEN AND NUM CHUNKS", total_len_of_all_valid_indices, num_chunks)
        if num_chunks == 0:
            num_chunks = 1

        chunks = np.array_split(range(total_len_of_all_valid_indices), num_chunks)
        assert len(chunks) > 0  # make sure there are more than 1 chunk

        vel_at_xy_in_range = [
            s_df.v_forward.values[i]
            for i in list(range(all_valid_indices[0], all_valid_indices[-1]))
        ]

        max_vel_for_lap = max(
            [
                s_df.v_forward.values[i]
                for i in list(range(all_valid_indices[0], all_valid_indices[-1]))
            ]
        )
        min_vel_for_lap = min(
            [
                s_df.v_forward.values[i]
                for i in list(range(all_valid_indices[0], all_valid_indices[-1]))
            ]
        )

        max_racing_line_score_for_lap = max(
            [
                s_df["score/racing_line"].values[i]
                for i in list(range(all_valid_indices[0], all_valid_indices[-1]))
            ]
        )
        min_racing_line_score_for_lap = min(
            [
                s_df["score/racing_line"].values[i]
                for i in list(range(all_valid_indices[0], all_valid_indices[-1]))
            ]
        )

        coord_x_in_range = [
            s_df.coord_x.values[i]
            for i in list(range(all_valid_indices[0], all_valid_indices[-1]))
        ]
        coord_y_in_range = [
            s_df.coord_y.values[i]
            for i in list(range(all_valid_indices[0], all_valid_indices[-1]))
        ]
        coord_xy_in_range = np.array(list(zip(coord_x_in_range, coord_y_in_range)))
        # find ref line indices closest to the coord x y
        min_indices = torch.argmin(
            all_pairs_euclid_torch(torch.tensor(coord_xy_in_range), torch.tensor(refline_xy)),
            dim=1,
        )
        refline_vel = np.array(
            refline_v[min_indices.tolist()]
        )  # refline velocity at each trajectory point
        diff_vel = (
            np.array(refline_vel) - vel_at_xy_in_range
        )  # positive values mean that at driver is going too slow.
        min_diff_vel = min(diff_vel)
        max_diff_vel = max(diff_vel)

        for chunk_num, chunk in enumerate(chunks):
            print(f"PLOTTING CHUNK NUMBER {chunk_num} {len(chunk)}")

            # get indices for chunk
            valid_indices = [all_valid_indices[i] for i in chunk]
            subtitle_at_skipped_valid_indices = [
                s_df.subtitle_sentence_level_cabin.values[i] for i in valid_indices[::SKIP_NUM]
            ]
            subtitle_label_at_skipped_valid_indices = [
                LABELS_TO_INSTRUCTION_DICT[s_df.subtitle_label_sentence_level_cabin.values[i]]
                for i in valid_indices[::SKIP_NUM]
            ]
            coord_x_at_skipped_valid_indices = [
                s_df.coord_x.values[i] for i in valid_indices[::SKIP_NUM]
            ]
            coord_y_at_skipped_valid_indices = [
                s_df.coord_y.values[i] for i in valid_indices[::SKIP_NUM]
            ]
            racing_line_score = [
                s_df["score/racing_line"].values[i] for i in valid_indices[::SKIP_NUM]
            ]

            # plot anchor points. equally spaced from start and end (without ay skips)
            coord_x_in_range = [
                s_df.coord_x.values[i]
                for i in list(range(valid_indices[0], valid_indices[-1]))[::POS_SKIP_NUM]
            ]
            coord_y_in_range = [
                s_df.coord_y.values[i]
                for i in list(range(valid_indices[0], valid_indices[-1]))[::POS_SKIP_NUM]
            ]
            coord_xy_in_range = np.array(list(zip(coord_x_in_range, coord_y_in_range)))
            # find ref line indices closest to the coord x y
            min_indices = torch.argmin(
                all_pairs_euclid_torch(torch.tensor(coord_xy_in_range), torch.tensor(refline_xy)),
                dim=1,
            )
            refline_vel = np.array(
                refline_v[min_indices.tolist()]
            )  # refline velocity at each trajectory point

            vel_at_xy_in_range = [
                s_df.v_forward.values[i]
                for i in list(range(valid_indices[0], valid_indices[-1]))[::POS_SKIP_NUM]
            ]
            vel_at_xy_in_range = np.array(vel_at_xy_in_range)
            # normalize with respect to lap level score

            vel_at_xy_in_range = (np.array(vel_at_xy_in_range) - min_vel_for_lap) / (
                max_vel_for_lap - min_vel_for_lap
            )

            diff_vel_range = (
                np.array(refline_vel) - vel_at_xy_in_range
            )  # positive values mean that at driver is going too slow.
            diff_vel_range = (diff_vel_range - min_diff_vel) / (max_diff_vel - min_diff_vel)
            racing_line_score = [
                s_df["score/racing_line"].values[i]
                for i in list(range(valid_indices[0], valid_indices[-1]))[::POS_SKIP_NUM]
            ]
            racing_line_score = (np.array(racing_line_score) - min_racing_line_score_for_lap) / (
                max_racing_line_score_for_lap - min_racing_line_score_for_lap
            )
            #             colors = [[v]*3 for v in vel_at_xy_in_range]
            colors = [[v] * 3 for v in diff_vel_range]
            plt.figure()
            #             plt.plot(vel_at_xy_in_range)
            #         print(subtitle_label_at_skipped_valid_indices, len(coord_x_at_skipped_valid_indices))
            normalize = matplotlib.colors.Normalize(vmin=0.0, vmax=1)
            plt.scatter(
                coord_x_in_range, coord_y_in_range, c=diff_vel_range, cmap="jet", norm=normalize
            )
            for s, x, y in zip(
                subtitle_label_at_skipped_valid_indices,
                coord_x_at_skipped_valid_indices,
                coord_y_at_skipped_valid_indices,
            ):
                if s == "NO_ACTION":
                    s = ""
                plt.text(x, y, s, fontsize=7)

            plt.title(
                f"{synced_df[:-4]}_{lapnum}_{np.round(max_vel_for_lap,4)}_{np.round(min_vel_for_lap,4)}_lap_{lapnum}_chunk_{chunk_num}"
            )
            save_fig_path = os.path.join(
                visualization_dir, synced_df[:-4], f"lapnum_{lapnum}_chunk_{chunk_num}.png"
            )
            plt.savefig(save_fig_path)
            plt.close()
#             break
#             plt.show()
#         break
#     break

#


# plt.show()
#%%
s_df["v_forward"]
tuple([0.5] * 3)
print(len(s_df["score/racing_line"]))
print(len(s_df["subtitle_label_sentence_level_cabin"]))

l = 10000
min_l = 450
num_chunks = l // min_l
a = np.array_split(range(l), num_chunks)