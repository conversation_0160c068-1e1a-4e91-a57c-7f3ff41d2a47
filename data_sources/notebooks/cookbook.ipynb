#%%
from pathlib import Path
from IPython.display import Image

# %micromamba activate hid_common
#%%
# Set the following variables to be the proper paths on your system
# Directory containing MCAP logs to be read in
mcap_dir = "~/hid_data/mcap_logs/"

# Directory where cached MCAP log data (in the form of pickle files) should be read/saved
cache_dir = "~/hid_data/cache/"

# Directory where cached trial data should be read/saved
trials_dir = "~/hid_data/trials/"

# Path to csv containing the map of the track to be used (usually Thunderhill West)
csv_path = "~/hid_data/map.csv"
#%%
# To run the default set of SDM filters over a directory of logs, run the following command. If you would like to run over a single log,
# use the argument `--mcap-input-file` instead of `--mcap-inpout-folder`, followed by the path to the log
%run scripts/sdm/create_and_annotate_sdm_trials.py --mcap-input-folder {mcap_dir} --compactsim-cache-folder {cache_dir} --track-map-csv {csv_path} --trials-dir {trials_dir} --plot-animation true
#%%
# View the animation of the trial by opening the gif file(s) produced in the previous step.
# This line pulls all gifs contained in the trials directory and selects the very first one from the list.
selected_gif = sorted(Path(trials_dir).expanduser().resolve().rglob("*.gif"))[0]

# This line displays the selected gif
print(selected_gif)
display(Image(data=open(selected_gif, "rb").read(), format="gif", width="800"))
#%%
# Running AI Coaching filters is very similar to that for SDM filters
%run scripts/aic/create_and_annotate_aic_trials.py --mcap-input-folder {mcap_dir} --compactsim-cache-folder {cache_dir} --track-map-csv {csv_path} --trials-dir {trials_dir} --plot-animation true
#%%
# To load in trials and have access to their dataframes
from scripts.filter_params import DEFAULT_ANIM_FILTER, OVERTAKE_FILTER_1, SDM_TRAINING_FILTERS
from util.filters.trajectory_filters import DATA_DICT_KEY_OVERTAKE
from util.trial import Trial
from util.utility import (
    search_transitions,
    SEARCH_PARAMS_KEYS_ANNOTATION_KEY,
    SEARCH_PARAMS_KEYS_EPISODE_MARGIN,
)
import numpy as np

all_trials = Trial.read_all(trials_dir, auto_annotation_class=SDM_TRAINING_FILTERS)

# all_trials is a list containing the data for all the trials in the provided directory.
print(all_trials[0].data.keys())
deadzone = 1.0  # (s)

# Initialize with expected 32 scenarios in the SDM study (including videos and surveys)
cumulative_trial_overtakes = {str(x): 0 for x in range(32)}
for trial in all_trials:
    trial_dataframe = trial.get_dataframe()

    # This is currently assuming that only one overtake can occur per trial, which is a naive assumption
    # Remove any overtakes happening at the start of the trial
    search_params = {
        SEARCH_PARAMS_KEYS_ANNOTATION_KEY: DATA_DICT_KEY_OVERTAKE,
        SEARCH_PARAMS_KEYS_EPISODE_MARGIN: deadzone,
    }

    # search_transitions returns a dict containing the timestamps of the detected transitions, and their indices in the trial_dataframe.
    filtered_overtake = search_transitions(search_params, trial_dataframe)["detected_indices"]

    # look for the rising edge of overtakes. Prepend the first value so that indices line up with where the change occurred.
    if np.any(filtered_overtake):
        trial_num = trial.data["dataframe"]["start_scenario_name"].unique()[0].split("_")[0]
        if trial_num not in cumulative_trial_overtakes.keys():
            cumulative_trial_overtakes[trial_num] = 0
        cumulative_trial_overtakes[trial_num] += 1

print(cumulative_trial_overtakes)
#%%
# Plot number of overtakes and highlight trial type
import matplotlib.pyplot as plt

sorted_overtakes = sorted({int(k): v for k, v in cumulative_trial_overtakes.items()})

trial_names = list(cumulative_trial_overtakes.keys())
trial_overakes = list(cumulative_trial_overtakes.values())

plt.figure(figsize=(15, 10), dpi=80)
plt.bar(range(len(trial_names)), trial_overakes, tick_label=trial_names)
plt.xlabel("Trial")
plt.ylabel("Number of Overtakes")
plt.title("Overtakes per Trial")

# Highlight straight scenarios as blue and hairpin as orange
plt.axvspan(3.5, 4.5, color="blue", alpha=0.3)
plt.axvspan(5.5, 8.5, color="blue", alpha=0.3)
plt.axvspan(12.5, 13.5, color="orange", alpha=0.3)
plt.axvspan(14.5, 22.5, color="orange", alpha=0.3)
plt.axvspan(24.5, 26.5, color="blue", alpha=0.3)
plt.axvspan(26.5, 29.5, color="orange", alpha=0.3)
#%%

#%%
