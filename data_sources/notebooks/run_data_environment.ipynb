#%%
import argparse
import pickle as pickle
from datetimerange import DateTimeRange
import os

# Set work directory
%cd ~/hid_common/

from typing import Dict, List, Tuple, Optional
import pandas as pd
from datetime import time
from util.filters.trajectory_filters import detect_spinout, annotate_lat_long_coordinates, detect_deceleration
from data_sources.compact_sim.dataloading import load_and_cache_compactsim_data, DATA_DICT_KEY_MAP, DATA_DICT_KEY_EPISODES
from data_sources.compact_sim.mcap_parser import ProcessCompactSimMcap
from util.visualization.visualization import visualize_timepoint


#%%
from scripts.run_data_environment import parse_arguments

args = parse_arguments(
    args=[
        "--mcap-input-folder",
        "~/Downloads/tri-hid-data-shared-autonomy/Participants/",
        "--compactsim-cache-folder",
        "~/intent/compactsim_cache",
        "--track-map-csv",
        "~/Downloads/Thunderhill_data/track.csv",
    ]
)
#%%
# Load the data from the mcaps and map information.
data_dict = load_and_cache_compactsim_data(args, override=False)
#%%
# Do some processing

panda_frame = list(data_dict[DATA_DICT_KEY_EPISODES].values())[0]
track_map = data_dict[DATA_DICT_KEY_MAP]
timepoint = time(0, 0, 0, 0)
lat_long_coord = annotate_lat_long_coordinates(panda_frame, track_map)
deceleration_result = detect_deceleration(panda_frame, track_map)
spinout_result = detect_spinout(panda_frame, track_map)
#%%
timepoint = time(0, 30, 4, 0)
print(panda_frame)

# TODO(guy.rosman): fixme
visualize_timepoint(
    vehicle_data=panda_frame,
    reference_track_data=track_map,
    timepoint=timepoint,
    params={
        "fps": 10,
        "anim_output_dir": "./anim",
        "time_window": 10,
        "display": True,
        "show_columns": None,
    },
)
#%%
