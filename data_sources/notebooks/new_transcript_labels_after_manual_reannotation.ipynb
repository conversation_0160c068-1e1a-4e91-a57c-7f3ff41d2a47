#%%
import pandas as pd
import os
import glob
import pickle
#%%
transcript_output_xl = "/home/<USER>/Data/Thunderhill/transcript_output.xlsx"  # modified by hand to fix sentence labels
with open(transcript_output_xl, "rb") as fp:
    sheet = pd.read_excel(fp, sheet_name=None)
#%%
# xls = pd.ExcelFile(transcript_output_xl)
# str.strip(xls.sheet_names[0])

transcript_dir = "/home/<USER>/Data/Thunderhill/GoPro_Transcripts/"
all_transcripts = sorted(glob.glob(os.path.join(transcript_dir, "**/G*.srt"), recursive=True))
all_transcripts_cabin = [t[t.find("GoPro_Transcripts") :] for t in all_transcripts if "cabin" in t]
all_transcripts_cabin_09 = [t for t in all_transcripts_cabin if "11-09" in t]
all_transcripts_cabin_10 = [t for t in all_transcripts_cabin if "11-10" in t]
sheet_keys = list(sheet.keys())
sheet_keys_09 = [s for s in sheet_keys if "11-09" in s]
sheet_keys_10 = [s for s in sheet_keys if "11-10" in s]
# print(all_transcripts_cabin_10, len(all_transcripts_cabin_10))
# print(sheet_keys_09[:len(all_transcripts_cabin_09)], sheet_keys_10)
# all_transcripts_cabin[0][all_transcripts_cabin[0].find('GoPro_Transcripts'):]
#%%
num_transcripts_09 = len(all_transcripts_cabin_09)
map_from_srt_to_sheet_id_09 = dict(
    zip(all_transcripts_cabin_09, sheet_keys_09[:num_transcripts_09])
)
num_transcripts_10 = len(all_transcripts_cabin_10)
map_from_srt_to_sheet_id_10 = dict(
    zip(all_transcripts_cabin_10, sheet_keys_10[:num_transcripts_10])
)
map_from_srt_to_sheet_id_09.update(map_from_srt_to_sheet_id_10)
map_from_srt_to_sheet_id_09
#%%
transcript_label_pkl = "/home/<USER>/Data/Thunderhill/transcript_labels.pkl"
with open(transcript_label_pkl, "rb") as fp:
    transcript_label_dict = pickle.load(fp)
# transcript_label_dict['GoPro_Transcripts/2022-11-09/GoPro/Leia/cabin/GX015474.srt']
#%%
new_transcript_label_pkl = {}

for key, transcript_list in transcript_label_dict.items():
    print(key)
    print()
    if "front" in key:
        new_transcript_label_pkl[key] = transcript_list
    else:
        xl_sheet_key = map_from_srt_to_sheet_id_09[key]
        xl = sheet[xl_sheet_key]
        assert len(transcript_list) == len(xl.sentence.values)
        new_transcript_list = []
        for idx, v in enumerate(transcript_list):
            #         print(xl['manual category'].iloc[idx], v)
            if not pd.isna(xl["manual category"].iloc[idx]) and not pd.isna(
                xl["manual label"].iloc[idx]
            ):
                #             print(xl.iloc[idx], v)
                new_v = (
                    v[0],
                    v[1],
                    int(xl["manual label"].iloc[idx]),
                    xl["manual category"].iloc[idx],
                )
                print(v, new_v)
                new_transcript_list.append(new_v)
            else:
                new_transcript_list.append(v)

        assert len(transcript_list) == len(new_transcript_list)
        new_transcript_label_pkl[key] = new_transcript_list
    print()
#     break
#%%
new_transcript_pkl_path = "/home/<USER>/Data/Thunderhill/new_transcript_labels.pkl"
with open(new_transcript_pkl_path, "wb") as fp:
    pickle.dump(new_transcript_label_pkl, fp)
#%%
new_transcript_label_pkl["GoPro_Transcripts/2022-11-09/GoPro/Leia/cabin/GX015487.srt"]