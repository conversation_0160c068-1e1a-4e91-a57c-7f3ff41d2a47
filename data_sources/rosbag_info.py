import collections
from pathlib import Path
from typing import Dict, List

VALID_SUBJECT_IDS_24_D_05: List = [
    "P601",
    "P602",
    "P603",
    "P604",
    "P605",
    "P606",
    "P607",
    "P608",
    "P609",
    "P610",
    "P611",
    "P612",
    "P613",
    "P614",
    "P615",
]
VALID_SUBJECT_IDS_24_D_16: List = [
    "P1601",
    "P1602",
    "P1603",
    "P1604",
    "P1605",
    "P1606",
    "P1607",
    "P1608",
    "P1609",
    "P1610",
    "P1611",
    "P1612",
    "P1613",
    "P1614",
    "P1615",
]

SUBJECT_INFO_24_D_05 = {
    "P601": [("sim_bag_2024_03_18-16_50_45", 22)],  # doesn't have actor list
    "P602": [("sim_bag_2024_03_19-16_43_54", 23)],
    "P603": [("sim_bag_2024_03_20-13_19_16", 24)],
    "P604": [("sim_bag_2024_03_20-16_06_34", 22)],  # doesn't have actor list
    "P605": [
        ("sim_bag_2024_03_21-16_30_01", 15),
        ("sim_bag_2024_03_21-17_42_09", 1),
        ("sim_bag_2024_03_21-17_46_43", 11),
    ],
    "P606": [("sim_bag_2024_03_25-16_40_32", 23)],
    "P607": [("sim_bag_2024_03_26-16_40_39", 23)],
    "P608": [("sim_bag_2024_03_27-16_34_20", 26)],
    "P609": [("sim_bag_2024_03_28-16_53_57", 22)],
    "P610": [("sim_bag_2024_04_15-17_04_13", 21)],
    "P611": [("sim_bag_2024_04_16-16_46_59", 22)],
    "P612": [("sim_bag_2024_04_18-16_48_45", 24)],
    "P613": [("sim_bag_2024_04_22-16_51_33", 5), ("sim_bag_2024_04_22-17_14_27", 18)],
    "P614": [("sim_bag_2024_04_23-17_04_29", 21)],
    "P615": [("sim_bag_2024_04_24-16_49_25", 23)],
}

SUBJECT_INFO_24_D_16 = {
    "P1601": [("sim_bag_2024_09_24-11_10_29", 23)],
    "P1602": [("sim_bag_2024_09_24-10_45_12", 1), ("sim_bag_2024_09_24-11_12_24", 24)],
    "P1603": [("sim_bag_2024_09_24-14_04_22", 20)],
    "P1604": [("sim_bag_2024_09_24-14_04_28", 20)],
    "P1605": [("sim_bag_2024_09_25-10_05_50", 23)],
    "P1606": [("sim_bag_2024_09_25-10_06_16", 23)],
    "P1607": [("sim_bag_2024_09_25-13_04_27", 13), ("sim_bag_2024_09_25-14_13_34", 10)],
    "P1608": [("sim_bag_2024_09_25-13_03_16", 13), ("sim_bag_2024_09_25-14_04_38", 12)],
    "P1609": [("sim_bag_2024_09_25-16_01_08", 23)],
    "P1610": [("sim_bag_2024_09_25-16_01_25", 23)],
    "P1611": [("sim_bag_2024_09_26-10_08_28", 6)],
    "P1612": [("sim_bag_2024_09_26-10_08_21", 8), ("sim_bag_2024_09_26-10_44_34", 13)],
    "P1613": [("sim_bag_2024_09_26-13_10_58", 21)],
    "P1614": [("sim_bag_2024_09_26-15_59_31", 22)],
    "P1615": [("sim_bag_2024_09_26-15_57_32", 22)],
}


def get_24_d_05_subject_srt_info(subject_info: Dict) -> Dict:
    """Helper function to manually add study specific information regarding subject and srt association

    :param subject_info: subject info for experiment 24_D_05

    :rtype: Dict
    :return: subject info with srt details.
    """
    subject_srt_files_info = collections.OrderedDict()
    for subject_pid in subject_info.keys():
        subject_srt_files_info[subject_pid] = {}
        subject_srt_files_info[subject_pid]["coach"] = [f"{subject_pid}_coach.srt"]
        subject_srt_files_info[subject_pid]["driver"] = [f"{subject_pid}_driver.srt"]
        subject_srt_files_info[subject_pid]["srt_rosbag"] = [subject_info[subject_pid][0][0]]

    # specific modifications to P605 due to break in recording
    subject_srt_files_info["P605"]["coach"].append("P605a_coach.srt")
    subject_srt_files_info["P605"]["driver"].append("P605a_driver.srt")
    subject_srt_files_info["P605"]["srt_rosbag"].append(subject_info["P605"][-1][0])

    # specific modifications to P613 due to break in recording
    subject_srt_files_info["P613"]["coach"].append("P613a_coach.srt")
    subject_srt_files_info["P613"]["driver"].append("P613a_driver.srt")
    subject_srt_files_info["P613"]["srt_rosbag"].append(subject_info["P613"][-1][0])
    return subject_srt_files_info


def get_sim_bag_data(mcap_input_folder):
    # Initialize the dictionary to store the mapping
    participant_dict = {}

    # Walk through the participant directories
    for participant in Path(mcap_input_folder).iterdir():
        # Ensure the entry is a directory (participant directory)
        if participant.is_dir():
            sim_bag_data = []

            # Walk through the directories inside each participant directory
            for sim_bag in participant.iterdir():
                # Ensure the entry is a directory and follows the 'sim_bag_*' format
                if sim_bag.is_dir() and sim_bag.name.startswith("sim_bag_"):
                    # Find all the .mcap files inside the sim_bag directory
                    mcap_files = [f for f in sim_bag.iterdir() if f.suffix == ".mcap"]
                    num_mcaps = len(mcap_files)

                    # Append the sim_bag directory and number of .mcap files to the list
                    sim_bag_data.append((sim_bag.name, num_mcaps))

            # Add the sim_bag data to the participant entry in the dictionary
            if sim_bag_data:
                participant_dict[participant.name] = sim_bag_data

    return participant_dict
