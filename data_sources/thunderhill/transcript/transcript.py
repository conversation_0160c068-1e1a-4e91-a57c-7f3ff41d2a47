#!/usr/bin/env python
"""
This script transcribe audio from MP4 video files and output the results in srt format.
The script reads all MP4 files from input_folder (given by option -i),
and store the results in output_folder (given by option -o).

Example:
    $ python3 transcript.py -i path/to/input/folder -o path/to/output/folder

When input/output folder options are not given,
it uses inputs/outputs folders in the current directly.
"""

import argparse
import math
import typing
from collections import namedtuple
from pathlib import Path

import ffmpeg
import noisereduce as nr
import numpy as np
import whisper
from scipy.io import wavfile
from stable_whisper import load_model, tighten_timestamps
from tqdm import tqdm

HOUR_IN_SECONDS = 3600
MIN_IN_SECONDS = 60
SEC_IN_MILLISEC = 1000


def convert_sec_to_srt_time(time_in_seconds: float) -> str:
    """
    This function converts seconds to a formatted text that matches the srt format.
    e.g. 123.456 seconds is converted to 00:02:03,456

    Args:
        time_in_seconds (float): Time duration in seconds.

    Returns:
        A formatted string that matches srt file format.

    """
    hours = math.floor(time_in_seconds / HOUR_IN_SECONDS)
    mins = math.floor((time_in_seconds - hours * HOUR_IN_SECONDS) / MIN_IN_SECONDS)
    seconds = math.floor(time_in_seconds - hours * HOUR_IN_SECONDS - mins * MIN_IN_SECONDS)
    millisec = (time_in_seconds - math.floor(time_in_seconds)) * SEC_IN_MILLISEC
    return f"{hours:02.0f}:{mins:02.0f}:{seconds:02.0f},{millisec:03.0f}"


def write_single_srt_entry(
    entry_count: int,
    start_time: float,
    end_time: float,
    text: str,
    srt_file: typing.TextIO,
) -> None:
    """
    This function write a single entry to srt_file in srt format.
    e.g. A single entry looks like below.
        103
        00:00:01:002 --> 00:00:03:045
        Nice and smooth.

    Args:
        entry_count: Index number of the entry.
        start_time: Start time (sec) of the entry.
        end_time: End time (sec) of the entry.
        text: Transcript content of the entry.
        srt_file: The srt file.

    """
    # Escape blanks, newline characters, and timestamp arrows ('-->').
    escaped_text = text.strip().replace("-->", "->")

    # Print the srt entry.
    print(
        f"{entry_count}\n"
        f"{convert_sec_to_srt_time(start_time)} --> {convert_sec_to_srt_time(end_time)}\n"
        f"{escaped_text}\n",
        file=srt_file,
        flush=True,
    )


STEP_SIZE_SEC = 0.5
"""
An interval (seconds) to identify the silent segments.
"""
STEP_SIZE = int(whisper.audio.SAMPLE_RATE * STEP_SIZE_SEC)
"""
An interval (number of samples) to identify the silent segments.
"""
SILENCE_THRESH = 0.04
"""
A audio level threshold to determine if the section is silent.
Currently, it's an empirical value specific to the Thunderhill training videos.
TODO(hiro.yasuda): Determine the value based on the data.
"""


def extract_non_silent_segments(monaural_audio: np.array) -> list:
    """
    This function extracts non-silent segments from the given sequence of monaural audio.
    The audio sequence is broken down to segments of STEP_SIZE.
    When the audio level is always lower than SILENT_THRESH, the segment is estimated as silent.

    Args:
        monaural_audio (np.array): A monaural audio sequence as 1D array.

    Returns:
        A list of pairs that contains index of start and end of non-silent segments.

    """

    # Check the audio level.
    is_silent = abs(monaural_audio) < SILENCE_THRESH
    is_silent_segment = [
        all(is_silent[i : i + STEP_SIZE])
        for i in range(0, len(monaural_audio) - STEP_SIZE + 1, STEP_SIZE)
    ]

    # Extract start and end indices.
    segments = []
    consecutive_cnt = 0
    start_index = -1
    for i, silent in enumerate(is_silent_segment):
        index = i * STEP_SIZE + int(STEP_SIZE * 0.5)
        if not silent:
            if consecutive_cnt == 0:
                start_index = index
            consecutive_cnt += 1
        else:
            if consecutive_cnt > 0:
                segments.append((start_index, index))
            consecutive_cnt = 0

    return segments


PADDING_SIZE_SEC = 2.0
"""
A duration (seconds) of silence segments filled between non-silence segments.
"""
PADDING_SIZE = int(whisper.audio.SAMPLE_RATE * PADDING_SIZE_SEC)
"""
A duration (number of samples) of silence segments filled between non-silence segments.
"""


def combine_segments(segments: list, audio: np.array) -> (np.array, list):
    """
    This function extract non-silent segments,
    and concatenate them with fixed size silence segments with PADDING_SIZE.

    Args:
        segments: A list of pairs contains index of start and end of non-silent segments.
        audio: A monaural audio sequence as 1D array.

    Returns:
        new_audio: A generated monaural audio sequence as 1D array.
        new_segments:
            A list of pairs contains index of start and end of non-silent segments in the new_audio.

    """

    # Prepare a space for the new audio.
    non_silent_section_total = sum(end - start for start, end in segments)
    new_audio = np.zeros(non_silent_section_total + len(segments) * PADDING_SIZE, dtype=np.float32)

    # Concatenate the non-silent segments.
    new_segments = []
    last_end = 0
    for start, end in segments:
        new_start = last_end + PADDING_SIZE
        new_end = new_start + (end - start)
        new_audio[new_start:new_end] = audio[start:end]
        new_segments.append((new_start, new_end))
        last_end = new_end

    return new_audio, new_segments


def index_to_sec(index: int) -> float:
    """
    This function converts sample index to time (seconds).
    Args:
        index (int): An index for the monaural audio sequence.

    Returns:
        Elapsed time from the beginning of the audio in seconds.

    """
    return index / whisper.audio.SAMPLE_RATE


def sec_to_index(second: float) -> int:
    """
    This function converts elapsed time form the beginning of audio sequence to a sample index.
    Args:
        second (float): Elapsed time from the beginning of the audio in seconds.

    Returns:
        An index for the monaural audio sequence.

    """
    return int(second * whisper.audio.SAMPLE_RATE)


MIN_FILE_SIZE = 100e6
"""
A minimum size (bytes) of the video file to be processed.
"""


def check_video_file(file_path) -> bool:
    """
    This function check the validity of the input file_path and the video content.

    Args:
        file_path: A path of the input video file.

    Returns:
        Boolean whether if the file should be proceed for the transcription.

    """

    # Skip if the video does not exist.
    if not file_path.exists():
        print(f"The video does not exist. {file_path}")
        return False

    # Skip if the video size is too small.
    if file_path.stat().st_size < MIN_FILE_SIZE:
        print(f"The video size is too small. {file_path}")
        return False

    # Skip if the video does not contain audio.
    p = ffmpeg.probe(str(file_path), select_streams="a")
    if not p["streams"]:
        print(f"The video does not contain audio. {file_path}")
        return False

    return True


AMPLIFY_SCALE = 3.162  # 10 db
"""
A scale value to amplification after de-noising.
Currently, it is an empirical value.
"""


def load_audio(input_path: Path) -> np.array:
    """
    This function loads raw audio from the video in input_path.
    The audio is de-noised for improving the transcript precision.
    Note that the de-noising is critical for detecting the silent segments.

    Args:
        input_path: A file path to the input video file.

    Returns:
        denoised_audio: A monaural audio sequence as 1D array.

    """
    # Load the raw audio from the video.
    audio_data = whisper.load_audio(str(input_path))

    # De-noise the audio.
    denoised_audio = nr.reduce_noise(y=audio_data, sr=whisper.audio.SAMPLE_RATE, n_jobs=-1)

    # Amplification since de-noised audio tends to be quieter than original.
    denoised_audio *= AMPLIFY_SCALE

    return denoised_audio


Transcript = namedtuple("Transcript", ["start", "end", "text"])


def remap_transcript_timestamps(
    transcripts: list, non_silent_segments: list, original_non_silent_segments: list
) -> list:
    """
    This function converts the timestamps of the transcripts of the modified audio,
    which is a concatenation of non-silent segments, to the associated time in the original audio.

    Args:
        transcripts: Transcripts of the modified audio that is a concatenation of non-silent segments.
        non_silent_segments: A list of (start, end) indices of non-silent segments of the modified audio.
        original_non_silent_segments: A list of (start, end) indices of non-silent segments of the original audio.

    Returns:
        A list of Transcripts with start and end timestamps that are mapped back to the original audio.

    """

    # Define utility functions that return start/end index of the specified transcript.
    def get_transcript_end(index):
        return sec_to_index(transcripts[index]["end"])

    def get_transcript_start(index):
        return sec_to_index(transcripts[index]["start"])

    # Output the result in srt format.
    transcript_index = 0
    remapped_transcripts = []
    for non_silent_segment_index, (non_silent_start, non_silent_end) in enumerate(
        non_silent_segments
    ):
        # Skip results before the current non-silent section.
        while (
            transcript_index != len(transcripts)
            and get_transcript_end(transcript_index) <= non_silent_start
        ):
            transcript_index += 1
        if transcript_index == len(transcripts):
            break

        # Retrieve the original start and end indices of the current non-silent segment.
        (
            original_non_silent_start,
            original_non_silent_end,
        ) = original_non_silent_segments[non_silent_segment_index]

        while (
            transcript_index != len(transcripts)
            and get_transcript_start(transcript_index) < non_silent_end
        ):
            # Clamp the start & end indices within the non-silent section.
            transcript_start = get_transcript_start(transcript_index)
            transcript_end = get_transcript_end(transcript_index)
            clamped_transcript_start = max(transcript_start, non_silent_start)
            clamped_transcript_end = min(transcript_end, non_silent_end)

            # If the transcript has larger overlap with the next non-silent segment,
            # go to next non-silent segment.
            if non_silent_segment_index + 1 < len(non_silent_segments):
                next_non_silent_start, next_non_silent_end = non_silent_segments[
                    non_silent_segment_index + 1
                ]
                if (non_silent_end - transcript_start) < (
                    min(transcript_end, next_non_silent_end) - next_non_silent_start
                ):
                    break

            # Convert the start & end time back to the associated time in the original audio.
            transcript_start_time = index_to_sec(
                clamped_transcript_start - non_silent_start + original_non_silent_start
            )
            transcript_end_time = index_to_sec(
                clamped_transcript_end - non_silent_end + original_non_silent_end
            )

            # Register the current transcript.
            remapped_transcripts.append(
                Transcript(
                    transcript_start_time,
                    transcript_end_time,
                    transcripts[transcript_index]["text"],
                )
            )

            transcript_index += 1
        if transcript_index == len(transcripts):
            break

    return remapped_transcripts


def transcribe(input_path: Path, output_path: Path, model: "Whisper", output_wav: Path) -> None:
    """
    This function loads a single .MP4 file in input_path,
    transcribe the video using the model, and store the result in srt format in output_path.
    If -w option of this script is specified, a wav file of de-noised audio is saved as well.

    Args:
        input_path: Path to the input video file.
        output_path: Path to the output srt file.
        model: The Whisper model.
        output_wav: Path to the output wav file of de-noised audio.

    """
    print(f"Processing {input_path}...")

    # Check the input video file.
    if not check_video_file(input_path):
        # The file_path/content is not valid, yet create an empty file for the record.
        output_path.touch()
        return

    # Skip if the transcript file already exists.
    if output_path.exists():
        print(f"The file already exist. {output_path}")
        return

    # Create the parent folder if it does not exist.
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # Load the audio.
    denoised_audio = load_audio(input_path)
    print("De-noised audio is loaded.")

    # Extract non silent segments.
    original_non_silent_segments = extract_non_silent_segments(denoised_audio)
    print("Non-silent segments are extracted.")

    # Combine the segments with fixed size of silence in between.
    modified_audio, non_silent_segments = combine_segments(
        original_non_silent_segments, denoised_audio
    )

    # Output de-noised audio as wav file.
    if output_wav:
        wav_output_path = output_path.with_suffix(".wav")
        wavfile.write(str(wav_output_path), whisper.audio.SAMPLE_RATE, denoised_audio)
        print(f"Finished writing de-noised audio.{wav_output_path}")

    # Transcribe the modified audio.
    results = model.transcribe(modified_audio, language="en", pbar=True)
    results = tighten_timestamps(results, end_before_period=True)
    if not results["segments"]:
        return
    transcripts = results["segments"]

    # Remap the timestamp back to the associated time in the original audio.
    remapped_transcripts = remap_transcript_timestamps(
        transcripts, non_silent_segments, original_non_silent_segments
    )

    # Output the results.
    with open(str(output_path), "w", encoding="utf-8") as srt_file:
        for count, transcript in enumerate(remapped_transcripts):
            write_single_srt_entry(
                count + 1, transcript.start, transcript.end, transcript.text, srt_file
            )


def main():
    """
    This function loads .MP4 files from input_folder, generate transcripts in srt format,
    and store the results in output_folder.
    Note that the outputs maintain the subfolder structure under the input_folder.

    """

    # Get argments.
    psr = argparse.ArgumentParser()
    psr.add_argument(
        "-o",
        "--output_folder",
        default="outputs",
        help='A folder where the transcripts will be stored. The default is "outputs" folder.',
    )
    psr.add_argument(
        "-i",
        "--input_folder",
        default="inputs",
        help='A folder that contains input MP4 files. The default is "inputs" folder.',
    )
    psr.add_argument(
        "-w",
        "--wav_output",
        action="store_true",
        default=False,
        help="Output de-noised audio as a wav file.",
    )
    args = psr.parse_args()

    # Check arguments.
    input_folder = Path(args.input_folder)
    assert input_folder.exists()
    assert input_folder.is_dir()
    inputs = input_folder.glob("**/*.MP4")

    output_folder = Path(args.output_folder)
    output_wav = args.wav_output

    # Load the Whisper model.
    model_name = "large-v2"
    model = load_model(model_name, device="cuda")
    print(f"Whisper model, {model_name}, is loaded.")

    for input_path in tqdm(list(inputs)):
        output_path = (
            output_folder
            / input_path.parent.relative_to(input_folder)
            / input_path.with_suffix(".srt").name
        )
        transcribe(input_path, output_path, model, output_wav=output_wav)


if __name__ == "__main__":
    main()
