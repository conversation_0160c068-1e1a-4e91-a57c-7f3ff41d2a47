# Transcript audio from MP4 files using Whisper
This script transcribe audio from MP4 video files and output the results in [srt format](https://docs.fileformat.com/video/srt/).

The timestamps of the transcripts using the original [Whisper](https://github.com/openai/whisper), and even its [stable-timestamp version](https://github.com/jianfch/stable-ts), sometimes have large offsets from the actual timing of the utterances. This script is to reduce the artifacts by the following steps;
1. Extract the audio from MP4 file.
1. Remove noise from the audio.
1. Extract non-silent sections based on the volume level.
1. Concatenate the extracted non-silent sections with a fixed size silent sections.
1. Apply Whiser ([stable timestamp version](https://github.com/jianfch/stable-ts))
1. Convert the timestamp back to the associated time in the original audio.
1. Export the results.

Note that the process and parameters are very specific to the GoPro recordings from Thunderhill training Nov 2022, and may not work for other data.

## Prerequisite
Install conda environment with dependancies following the instruction [here](https://github.shared-services.aws.tri.global/tri-projects/hid_common/).
## How to run the script
The script reads all MP4 files from `input_folder` (given by an option `-i`/`--input_folder`), and store the results in `output_folder` (given by an option `-o`/`--output_folder`).
An example of the command is;
```sh 
python3 transcript.py -i path/to/input/folder -o path/to/output/folder
```
When `input_folder` and/or `output_folder` options are not given, it trys to read a folder named `inputs` in the current directly and creates `outputs` folder in the current directly.
Within the process of transcription, the script creates a modified audio with removed noise. You can use `-w`/`--wav_output` option to output the audio as `wav` file.
