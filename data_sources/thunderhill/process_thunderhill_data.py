#!/usr/bin/env python3
# This script is a master script that parses video data (GoPro), Rosbag data extracted into pkl files, reads a sessions csv file contained the data association information between
# participants, date, car, and video data, and primarily performs the time synchronization between different channels of data
# This scripts goes over a folder of MP4s, (once done should), reads a sessions csv file, and matches the MP4s to sessions / participants IDs.
# The script will start an IPython session with the parsed and synchronized data for the user to analyze based on their interests.


import argparse
import collections
import csv
import datetime
import glob
import math
import os
import pickle
import re
from typing import Dict, List, Optional, Tuple

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import pytz
import srt
import torch
import tqdm
import yaml
from datetimerange import DateTimeRange

from data_sources.thunderhill.data_readers import read_map_csv
from model_zoo.sentence_embedding import create_sentence_embedding_model, load_classifier

KEY_STR_PID = "participant_ID"
KEY_STR_SESSION = "session_ID"
# TODO (deepak.gopinath). Maybe changes instance of PST to LOCAL for all dict keys as well as variables
KEY_STR_START_TIME_PST = "start_time_pst"
KEY_STR_END_TIME_PST = "end_time_pst"
KEY_STR_START_TIME_UTC = "start_time_utc"
KEY_STR_END_TIME_UTC = "end_time_utc"
KEY_STR_VEHICLE = "vehicle_name"
KEY_STR_LOCATION = "location"

KEY_UTTERANCE_SUBTITLE = "subtitle"
KEY_UTTERANCE_LABEL = "subtitle_label"
KEY_UTTERANCE_START_TIMEPOINT_UTC = "start_timepoint_utc"
KEY_UTTERANCE_START_TIMEPOINT_PST = "start_timepoint_pst"
KEY_UTTERANCE_END_TIMEPOINT_UTC = "end_timepoint_utc"
KEY_UTTERANCE_END_TIMEPOINT_PST = "end_timepoint_pst"
KEY_UTTERANCE_VIDEO = "video_info"
KEY_UTTERANCE_VIDEO_KEY = "video_key"
KEY_AUX_UTTERANCES = "utterances"
KEY_AUX_SCRIPT_TO_VIDEO = "script_to_video_map"

KEY_VID_INFO = "video_file_info"
KEY_VID_START_TIME_UTC = "video_start_time_utc"
KEY_VID_END_TIME_UTC = "video_end_time_utc"
KEY_VID_START_TIME_PST = "video_start_time_pst"
KEY_VID_END_TIME_PST = "video_end_time_pst"
KEY_VID_DURATION = "video_duration"
KEY_VID_FILENAME = "video_filename"
KEY_VID_SESSION_CHUNK_START_TIME_UTC = "video_session_chunk_start_time_utc"
KEY_VID_SESSION_CHUNK_END_TIME_UTC = "video_session_chunk_end_time_utc"
KEY_MEDIAINFO_FILENAME = "mediainfo_filename"
KEY_VID_SESSION = "video_session_info"
KEY_VID_ID = "video_id"
KEY_VID_SESSION_CHUNK_ASSOC = "video_session_chunk_assoc"


KEY_VEH_DATASHEET_PID = "PID"
KEY_VEH_DATASHEET_SESSION = "Session"
KEY_VEH_DATASHEET_LOCATION = "Location"
KEY_VEH_DATASHEET_TIME = "Time"

KEY_START_TIME_UTC_ROSBAG = "start_time_utc_rosbag"
KEY_END_TIME_UTC_ROSBAG = "end_time_utc_rosbag"
KEY_START_TIME_PST_ROSBAG = "start_time_pst_rosbag"
KEY_END_TIME_PST_ROSBAG = "end_time_pst_rosbag"

# the index in the tuple from transcripts_labels.pkl at which the label associated with the utterance
# 0th index corresponds to the utterance, 1st index corresponds to non-remapped label, and 3rd index corresponds to the predicted instruction category name as a string. 0,1, and 3 are unused
UTTERANCE_LABEL_IDX = 2

ALL_VEHICLE_NAMES = ["FR-S", "Supra", "Leia"]
ALL_DATES = [datetime.datetime(2022, 11, d) for d in range(7, 11)]
ALL_TRACK_DATES = [datetime.datetime(2022, 11, d) for d in range(9, 11)]

DT_TOLERANCE = 2.0  # max tolerance (in seconds) between transcript timestamps and ros time stamp.
# Since the ros sampling rate is fairly high it is usually guaranteed that there is a ros timestamp within
# DT_TOLERANCE of a transcript timestamp. However, ros bag processing introduces dead time (due to stopped vehicle)
# or participant switch etc which could vary from ~5 seconds to 700 seconds.
NANOS_TO_S_FACTOR = 10**9


def parse_arguments():
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument(
        "--sentence-level-transcript-input-dir",
        type=str,
        help="Folder containing the sentence level transcripts from the GoPro files",
        default="./",
    )
    parser.add_argument(
        "--word-level-transcript-input-dir",
        type=str,
        help="Folder containing the word level transcripts from the GoPro files",
        default="./",
    )
    parser.add_argument(
        "--mediainfo-input-dir",
        type=str,
        help="Input folder containing the txt files with mediainfo outputs for all gopro videos. ",
        default="./",
    )
    parser.add_argument(
        "--solo2-input-dir",
        type=str,
        help="Input Solo2 folder to read",
        default="./",
    )
    parser.add_argument(
        "--rosbag-input-dir",
        type=str,
        help="Input rosbag folder to read",
        default="./",
    )
    parser.add_argument(
        "--session-xlsx",
        type=str,
        help="Input .xlsx file to read, based on the Thunderhill data gdoc",
        default="./",
    )
    parser.add_argument(
        "--track-map-csv",
        type=str,
        help="Input .csv file to read for the track map.",
        default="./",
    )
    parser.add_argument(
        "--transcript-label-pkl",
        type=str,
        help="Path to the pkl file containing the dict which has labels to all transcripts",
        default="./",
    )
    parser.add_argument(
        "--save-model-location",
        type=str,
        help="Path to folder for saving the models",
        default="./saved_models",
    )
    parser.add_argument("--output-dir", type=str, help="Output jsons files to write", default="./")
    parser.add_argument(
        "--dataset-label", type=str, help="Tag/label for the dataset", default="default"
    )
    parser.add_argument("-v", "--visualize", action="store_true", help="Visualize data statistics")

    result = parser.parse_args()
    return vars(result)


def extract_video_time_and_duration_from_mediainfo(
    mediainfo_filename: str,
) -> Tuple[str, str, float]:
    """
    Reads the metadata of a video using the mediainfo unix utility function and returns the date and the duration of the video

    Args:
        filename: str - full path to the video file.
    Returns:
        file_last_modified_pst_date_str: str - time in PST at which the video was last modified
        file_last_modified_utc_date_str: str - time in UTC at which the video was last modified
        duration: float - duration of the video file in seconds.

    """
    # from all the metadata, we seek to extract the date string (date_str) and the duration of the video
    file_last_modified_pst_date_str = None
    file_last_modified_utc_date_str = None
    duration = None
    with open(mediainfo_filename) as f:
        mediainfo_txt = f.read()

    # TODO (deepak.gopinath) mediainfo return multiple duration fields and multiple encoded date fields
    # Not all duration fields have the identical durations.
    # Understand why there are different duration values for each video.
    line_num = 0
    for str_ln in mediainfo_txt.splitlines():
        if line_num == 0:
            assert str_ln.lower() == "general"
            line_num = -1

        # (deepak.gopinath) Note that "encoded date" in mediainfo does not give you the start time of the specific clip
        if (
            "file last modification date" in str_ln.lower()
            and file_last_modified_utc_date_str is None
        ):
            file_last_modified_utc_date_str = str_ln[str_ln.find("UTC") :]
        if (
            "file last modification date (local)" in str_ln.lower()
            and file_last_modified_pst_date_str is None
        ):
            file_last_modified_pst_date_str = str_ln[str_ln.find(":") :]
        if "duration" in str_ln.lower() and duration is None:  #
            m = re.search(r"\d", str_ln)
            duration_str = str_ln[m.start() :]
            duration = float(duration_str) / 1000  # convert milliseconds to seconds.

    return file_last_modified_pst_date_str, file_last_modified_utc_date_str, duration


def extract_rosbag_to_session_chunk_association(
    start_end_time_for_rosbag_dict: Dict, session_chunks_table: List
) -> Dict:
    """Function that computes which valid session chunks (5 min chunks in each row of the excel sheet) corresponds
    to the rosbags is media_info_filename. Uses a DateTimeRange object to compute overlap between the
    timerange spanned by the rosbag and the timerange of a session chunk. If there is overlap, then it means some part of the session chunk is in the video

    Args:
    start_end_time_for_rosbag_dict: Dict - dictionary containing the mapping between the source rosbag name and the start and end
        datetime objects both in utc and pst
    session_chunks_table: List - list of dictionaries each of which is info regarding a session chunk. Information include, vehicle name, participant id,
        start and end time datetime objects both in utc and pst and session ids.

    Returns:
        rosbag_to_session_chunk_association_dict: Dict - dictionary containing the mapping between rosbag names and the corresponding list of session chunks
    """
    rosbag_to_session_chunk_association_dict = {}
    for rosbag_key in start_end_time_for_rosbag_dict.keys():
        start_end_time_for_rosbag = start_end_time_for_rosbag_dict[rosbag_key]
        start_time_utc_rosbag = start_end_time_for_rosbag[KEY_START_TIME_UTC_ROSBAG]
        end_time_utc_rosbag = start_end_time_for_rosbag[KEY_END_TIME_UTC_ROSBAG]
        rosbag_time_range_utc = DateTimeRange(start_time_utc_rosbag, end_time_utc_rosbag)
        rosbag_to_session_chunk_association_dict[rosbag_key] = []

        for session_chunk in session_chunks_table:
            if session_chunk["vehicle_name"] != "Leia":
                continue
            else:
                session_chunk_start_time_utc = session_chunk["start_time_utc"]
                session_chunk_end_time_utc = session_chunk["end_time_utc"]
                session_chunk_time_range = DateTimeRange(
                    session_chunk_start_time_utc, session_chunk_end_time_utc
                )
                if rosbag_time_range_utc.is_intersection(session_chunk_time_range):
                    rosbag_to_session_chunk_association_dict[rosbag_key].append(session_chunk)

    return rosbag_to_session_chunk_association_dict


def extract_video_to_session_chunk_association(
    media_info_filename: str, session_chunks_table: List
) -> Dict:
    """
    Function that computes which valid session chunks (5 min chunks in each row of the excel sheet) corresponds
    to the video whose mediainfo output is media_info_filename. Uses a DateTimeRange object to compute overlap between the
    timerange spanned by the video and the timerange of a session chunk. If there is overlap, then it means some part of the session chunk is in the video

    Args:
        media_info_filename: str -  full path to the file containing the output of the mediainfo unix utility for a video
        session_chunks_table: List - list containing all the info regarding the sessions chunks (each 5 min row in the data association excel sheet)

    Returns:
        video_to_session_chunk_association_dict: Dict - dictionary containing the association between a given video and all the chunks that overlap with the video

    """
    # extract the last modified date str (both in utc and pst) and duration from the mediainfo filename.
    (
        file_last_modified_pst_date_str,
        file_last_modified_utc_date_str,
        duration,
    ) = extract_video_time_and_duration_from_mediainfo(media_info_filename)
    print(
        media_info_filename,
        file_last_modified_pst_date_str,
        file_last_modified_utc_date_str,
        duration,
    )
    print()
    # extract datetime objects both in utc and pst for video end time and compute the video start time datetime objects
    video_end_time_utc = datetime.datetime.strptime(
        file_last_modified_utc_date_str, "UTC %Y-%m-%d %H:%M:%S"
    )  # naive
    video_end_time_utc = pytz.utc.localize(video_end_time_utc)  # utc timezone aware
    video_start_time_utc = video_end_time_utc - datetime.timedelta(0, duration)  # aware

    video_end_time_pst = datetime.datetime.strptime(
        file_last_modified_pst_date_str, ": %Y-%m-%d %H:%M:%S"
    )  # naive
    video_end_time_pst = pytz.timezone("US/Pacific").localize(
        video_end_time_pst
    )  # US pacific timezone aware
    video_start_time_pst = video_end_time_pst - datetime.timedelta(0, duration)  # aware

    # check if the transformed datetime objects matchup properly.
    assert video_end_time_utc == video_end_time_pst.astimezone(pytz.utc)
    assert video_start_time_utc == video_start_time_pst.astimezone(pytz.utc)

    # create video filename path from mediainfo filename

    video_filename = os.path.splitext(media_info_filename)[0] + ".MP4"
    video_filename = video_filename.replace(
        "GoPro_MediaInfo", "GoPro"
    )  # fix parent video folder name

    # extract vehicle name from the folder path
    gopro_location_path, video_name = os.path.split(video_filename)
    vehicle_name_path, gopro_location = os.path.split(gopro_location_path)
    vehicle_name = os.path.split(vehicle_name_path)[1]
    assert vehicle_name in ALL_VEHICLE_NAMES

    # dictionary to hold the mapping from video to the session chunk
    video_to_session_chunk_association_dict = {}
    video_to_session_chunk_association_dict[KEY_VID_INFO] = {
        KEY_VID_FILENAME: video_filename[video_filename.find("GoPro") :],
        KEY_VID_START_TIME_PST: video_start_time_pst,
        KEY_VID_END_TIME_PST: video_end_time_pst,
        KEY_VID_START_TIME_UTC: video_start_time_utc,
        KEY_VID_END_TIME_UTC: video_end_time_utc,
        KEY_VID_DURATION: duration,
        KEY_MEDIAINFO_FILENAME: media_info_filename[media_info_filename.find("GoPro_MediaInfo") :],
    }
    video_to_session_chunk_association_dict[KEY_VID_SESSION] = []

    # create video time range in utc.
    video_time_range_utc = DateTimeRange(video_start_time_utc, video_end_time_utc)

    for session_chunk in session_chunks_table:
        # create timerange for session chunk
        session_chunk_time_range_utc = DateTimeRange(
            session_chunk[KEY_STR_START_TIME_UTC], session_chunk[KEY_STR_END_TIME_UTC]
        )

        # check if session chunk timerange overlaps with video and if the vehicle matches. If yes, then this session chunk has some association with the video
        if (
            video_time_range_utc.is_intersection(session_chunk_time_range_utc)
            and vehicle_name == session_chunk[KEY_STR_VEHICLE]
        ):
            video_to_session_chunk_association_dict[KEY_VID_SESSION].append(
                {
                    KEY_STR_VEHICLE: session_chunk[KEY_STR_VEHICLE],
                    KEY_STR_SESSION: session_chunk[KEY_STR_SESSION],
                    KEY_STR_PID: session_chunk[KEY_STR_PID],
                    KEY_VID_FILENAME: video_filename[video_filename.find("GoPro") :],
                    KEY_VID_SESSION_CHUNK_START_TIME_UTC: session_chunk[KEY_STR_START_TIME_UTC],
                    KEY_VID_SESSION_CHUNK_END_TIME_UTC: session_chunk[KEY_STR_END_TIME_UTC],
                }
            )

    return video_to_session_chunk_association_dict


def read_transcripts(transcript_files: List) -> Dict:
    """
    Function to extract the transcripts from each .srt and organize them into a dict

    Args:
        transcript_files: List - list of full paths to the transcripts (word-level or sentence-level) files

    Returns:
        transcript_dict: Dict - Dictionary containing mapping between transcripts_paths (local dependency removed) and the extracted transcripts stored as a list of Subtitle objects.
    """
    transcript_dict = {}
    for transcript_file in transcript_files:
        # remove local computer path dependency in the keys.
        transcript_file_key = transcript_file[transcript_file.find("GoPro_Transcripts") :]
        print(transcript_file)
        with open(transcript_file, "r", encoding="latin-1") as fp:
            srt_text = fp.read()
            new_srt_gen = srt.parse(srt_text)
            # new_srt is a list of Subtitle objects.
            # For example of a subtitle object in the list can look like
            # Subtitle(index=5, start=datetime.timedelta(seconds=96), end=datetime.timedelta(seconds=106), content='Thank you.', proprietary='')
            new_srt = list(new_srt_gen)
            # NOTE : It seems like OpenAI's Whisper can hallucinate transcriptions. Beware! Need to have some QC on this, before using it for model training
            transcript_dict[transcript_file_key] = new_srt
    return transcript_dict


def get_videos_to_video_id_map(
    videos_to_sessions_chunks_association_list: List,
) -> Dict:
    """
    Function to create a dictionary map between video_paths and the video_ids and video-->session_chunk association dictionary
    Args:
        videos_to_sessions_chunks_association_list: List - List of dictionaries each of which is a mapping between a video and all the session chunks associated with the video

    Returns:
        videos_to_video_id_dict: Dict - dictionary mapping between video_pathname and video_ids and video_session_chunk_association dict
    """
    videos_to_video_id_dict = {}
    for video_session_chunk_assoc_dict in videos_to_sessions_chunks_association_list:
        if video_session_chunk_assoc_dict is not None:
            video_pathname = video_session_chunk_assoc_dict[KEY_VID_INFO][KEY_VID_FILENAME]
            video_id = os.path.splitext(os.path.split(video_pathname)[1])[
                0
            ]  # for example, GX015389
            # use full path (video pathname) as key instead of video_id because video_id is non-unique and can be overwritten
            videos_to_video_id_dict[video_pathname] = {
                KEY_VID_ID: video_id,
                KEY_VID_SESSION_CHUNK_ASSOC: video_session_chunk_assoc_dict,
            }
    return videos_to_video_id_dict


def get_solo2_csv(solo2_csv_files: List):
    # fix solo2 data parsing
    for pathname in solo2_csv_files:
        with open(pathname, newline="") as csvfile:
            reader = csv.reader(csvfile)
            solo2_time = None
            time_cell = None
            solo_session = None
            video_start_time = None
            for i_row, row in enumerate(reader):
                if len(row) > 1:
                    if i_row == 2 and row[0] == "Vehicle":
                        vehicle_name = row[1]
                    elif row[0].lower() == "date":
                        date_cell = row[1]
                    elif row[0].lower() == "session" and solo_session is None:
                        solo_session = row[1]
                        parent_folder = os.path.split(os.path.split(pathname)[0])[0]
                        mov_files = list(glob.glob(os.path.join(parent_folder, "*.mov")))
                        if len(solo_session) > 0 and len(mov_files):
                            duration = 0
                            for filename in mov_files:
                                # TODO(guy.rosman): either restore the function or remove completely.
                                date_str_, duration_ = read_file_mediainfo(filename)
                                if duration_ > duration:
                                    date_str = date_str_
                                    duration = duration_
                                video_start_time = datetime.datetime.strptime(
                                    date_str, "UTC %Y-%m-%d %H:%M:%S"
                                ).replace(tzinfo=datetime.timezone.utc)
                            # import IPython;
                            # IPython.embed(header=f"Session: {solo_session}")
                    elif row[0].lower() == "time" and time_cell is None:
                        time_cell = row[1]
                        print(f"row[1]={row[1]}")
                        solo2_time = datetime.datetime.strptime(
                            date_cell + ", " + time_cell, "%A, %B %d, %Y, %I:%M %p"
                        )
                    elif i_row >= 18:
                        delta_time_cell = row[0]
                        gps_lat_deg = row[12]
                        gps_lon_deg = row[13]
                        # TODO(guy.rosman): convert coordinates to metric coordinates
                        if video_start_time is not None:
                            gps_sample_time = video_start_time + datetime.timedelta(
                                0, float(delta_time_cell)
                            )
                            import IPython

                            IPython.embed(header=f"{pathname}")
                        else:
                            # probably wrong..
                            gps_sample_time = solo2_time + datetime.timedelta(
                                0, float(delta_time_cell)
                            )
                        # TODO(guy.rosman): add the rest, save samples


def read_rosbag_pkl_files(rosbag_folder: str, rosbags_to_omit: List = []) -> Tuple[Dict, Dict]:
    """
    This function reads the pkl files extracted from the individual rosbags and extracts necessarily fields of data (location information)
    and time information. Uses the metadata.yaml associated with each rosbag to express time coordinate in global time.

    Args:
        rosbag_folder: str: path to the folder containing the pkl files extracted from ROSbags
        rosbags_to_omit: list: list of rosbag files to be omitted
    Returns:
        ros_pkl_pandas_dict: Dict - dictionary containing the mapping between pkl file paths
                            (local dependency removed) and the pandas data frame conatining all the
                            recorded ros msgs for the corresponding pkl files

        ros_pkl_processed_data_timestamps_dict: Dict - dictionary containing the mapping between pkl file paths
                            (local dependency removed) and a subset of extracted data fields and datetime objects both in utc and pst
                            for all the extracted messages
    """
    pkl_file_list = glob.glob(os.path.join(rosbag_folder, "**/*.pkl"), recursive=True)
    ros_pkl_pandas_dict = {}
    ros_pkl_processed_data_timestamps_dict = {}

    for pkl_file in sorted(
        pkl_file_list
    ):  # sorting so that the parsed pkl files are temporally contiguous
        parent, filename = os.path.split(pkl_file)
        grandparent, parent = os.path.split(parent)
        older, grandparent = os.path.split(grandparent)

        # Note: For the time being only processing the track data from November trip. The format in which data is stored depends on whether
        # it is from track or skid pad. Especially wrt the metrics and the track map etc.
        if (
            (parent == "save")
            and (grandparent.startswith("run"))
            and (filename.startswith("all_processed"))
            and any([date.strftime("%m-%d") in older for date in ALL_TRACK_DATES])
            and not any([rosbag_to_omit in pkl_file for rosbag_to_omit in rosbags_to_omit])
        ):
            with open(pkl_file, "rb") as fp:
                pkl_content = pickle.load(fp)  # load pkl file containing the pandas dataframe.
                # Remove local computer dependency, for example, 'ROS/2022-11-07/ROS/run_002/save/run_002_000.pkl'
                pkl_file_key = pkl_file[pkl_file.find("ROS") :]
                print(pkl_file)
                # all_processed_data.pkl file contains, a list which contains
                # 1) dataframe for the entire rosbag
                # 2) track.csv as dataframe
                # 3) and all 10 track segments

                # store the contents of the pkl file in the results dictionary
                ros_pkl_pandas_dict[pkl_file_key] = pkl_content

                if type(pkl_content) is list:
                    ros_data, track_map_df, track_seg_df_list = pkl_content
                else:
                    # to deal with run_018 on 11-10. track map is not generated for this rosbag
                    ros_data = pkl_content

                # grab all fields in the dataframe and convert into dictionary
                rd_dict = ros_data.to_dict(orient="list")
                assert "pos_e" in rd_dict and "pos_n" in rd_dict  # make sure position data exists
                assert list(rd_dict.keys()) == list(ros_data.keys())  # make sure keys match

                # Rename keys for clarity
                rd_dict["coord_x"] = rd_dict.pop("pos_e")
                rd_dict["coord_y"] = rd_dict.pop("pos_n")

                # extract the time coordinate of the message
                coord_dt = np.array(
                    rd_dict["t"]
                )  # in seconds. t=0 is the beginning of rosbag recording

                bag_metadata_name = os.path.join(older, grandparent, "bag/metadata.yaml")
                # check if metadata for each bag exists
                assert os.path.exists(
                    bag_metadata_name
                ), "Please ensure that the metadata.yaml corresponding to every rosbag is present"
                print(bag_metadata_name)

                with open(bag_metadata_name, "r") as fp2:
                    # load the yaml containing the metadata for the rosbag. Needed to extract the start time of the rosbag recording
                    bag_metadata = yaml.load(fp2, Loader=yaml.Loader)
                    # start time of the rosbag recording in nanoseconds since the beginning of unix epoch
                    nanoseconds_since_epoch = bag_metadata["rosbag2_bagfile_information"][
                        "starting_time"
                    ]["nanoseconds_since_epoch"]

                    # note that coord_dt is the time at which the coord_x, coord_y was recorded with respect to the beginning of the ROSBAG recording
                    # from which the pkl was extracted with t=0 as the beginning.
                    coord_t_since_epoch = (
                        coord_dt + float(nanoseconds_since_epoch) / 1.0e9
                    )  # in secs
                    coord_t_since_epoch_datetime_utc = [
                        datetime.datetime.fromtimestamp(t).astimezone(pytz.utc)
                        for t in coord_t_since_epoch
                    ]
                    coord_t_since_epoch_datetime_pst = [
                        datetime.datetime.fromtimestamp(t).astimezone(pytz.timezone("US/Pacific"))
                        for t in coord_t_since_epoch
                    ]
                    # add additional timestamp related info in the dataframe
                    rd_dict["coord_t_relative"] = coord_dt
                    rd_dict["coord_t_absolute"] = coord_t_since_epoch
                    rd_dict["coord_t_datetime_utc"] = coord_t_since_epoch_datetime_utc
                    rd_dict["coord_t_datetime_pst"] = coord_t_since_epoch_datetime_pst
                    rd_dict["source_rosbag_start_ns_since_epoch"] = nanoseconds_since_epoch
                    ros_pkl_processed_data_timestamps_dict[pkl_file_key] = rd_dict

    return ros_pkl_pandas_dict, ros_pkl_processed_data_timestamps_dict


def get_transcript_video_association(
    transcripts: Dict,
    transcript_parent_folder: str,
    video_to_video_id_dict: Dict,
    transcript_to_label_dict: Optional[Dict] = None,
) -> Dict:
    """
    Function that computes the datetime objects (both in utc and pst) for each utterance in the transcript
    by referencing the datetime objects of the corresponding file

    Args:
        transcripts: Dict - dictionary containing mapping between transcripts_paths (local dependency removed) and the extracted transcripts stored as a list of Subtitle objects.
        transcript_parent_folder: str - Name of the parent folder that needs to be used for creating file path from GoPro video file path
        video_to_video_id_dict: Dict - dictionary mapping between video_pathname and video_ids and video_session_chunk_association dict
        transcript_to_label_dict: Dict - optional dictionary containing mapping from transcript to a label
    Returns:
        transcript_timestamps_and_video_association_dict: Dict -  dictionary containing mappings from utterances in a transcript to datetime
        objects both in utc and pst computed wrt the video timestamps
    """

    transcript_timestamps_and_video_association_dict = {
        KEY_AUX_UTTERANCES: {},
        KEY_AUX_SCRIPT_TO_VIDEO: {},
    }

    for transcript_key in transcripts:  # each transcript key is the full path to transcript file
        transcript = transcripts[transcript_key]
        # create corresponding video key from transcript key. This is guaranteed to be matched.
        assert transcript_parent_folder is not None
        video_key = (os.path.splitext(transcript_key)[0] + ".MP4").replace(
            transcript_parent_folder, "GoPro"
        )
        if video_key in video_to_video_id_dict.keys():
            video_info = video_to_video_id_dict[video_key]
            transcript_timestamps_and_video_association_dict[KEY_AUX_SCRIPT_TO_VIDEO][
                transcript_key
            ] = video_key
            transcript_timestamps_and_video_association_dict[KEY_AUX_UTTERANCES][
                transcript_key
            ] = []
            if transcript_to_label_dict is not None and transcript_key in transcript_to_label_dict:
                assert len(transcript_to_label_dict[transcript_key]) == len(transcript)

            video_start_time_utc = video_info[KEY_VID_SESSION_CHUNK_ASSOC][KEY_VID_INFO][
                KEY_VID_START_TIME_UTC
            ]
            video_start_time_pst = video_info[KEY_VID_SESSION_CHUNK_ASSOC][KEY_VID_INFO][
                KEY_VID_START_TIME_PST
            ]
            assert video_start_time_utc == video_start_time_pst.astimezone(pytz.utc)
            for utterance_idx, utterance in enumerate(transcript):
                if (
                    transcript_to_label_dict is not None
                    and transcript_key in transcript_to_label_dict
                ):
                    # ensure that the string in transcript to label dict matches the one extracted from the transcript
                    assert (
                        utterance.content
                        == transcript_to_label_dict[transcript_key][utterance_idx][0]
                    )
                    # utterance label is an int in [0, N-1]. for N class
                    utterance_label = transcript_to_label_dict[transcript_key][utterance_idx][
                        UTTERANCE_LABEL_IDX
                    ]
                else:
                    utterance_label = -1  # dummy label

                transcript_timestamps_and_video_association_dict[KEY_AUX_UTTERANCES][
                    transcript_key
                ].append(
                    {
                        KEY_UTTERANCE_SUBTITLE: utterance,
                        KEY_UTTERANCE_LABEL: utterance_label,
                        KEY_UTTERANCE_START_TIMEPOINT_UTC: video_start_time_utc + utterance.start,
                        KEY_UTTERANCE_END_TIMEPOINT_UTC: video_start_time_utc + utterance.end,
                        KEY_UTTERANCE_START_TIMEPOINT_PST: video_start_time_pst + utterance.start,
                        KEY_UTTERANCE_END_TIMEPOINT_PST: video_start_time_pst + utterance.end,
                        KEY_UTTERANCE_VIDEO_KEY: video_key,
                    }
                )

        else:
            print(f"filename {video_key} not found in video_to_video_id_dict")

    return transcript_timestamps_and_video_association_dict


def get_start_end_time_for_each_ros_bag(
    rosbag_to_rospkl_files_dict: Dict, ros_pkl_processed_data_timestamps_dict: Dict
) -> Dict:
    """
    Functions that computes the start and end datetime objects both in utc and pst for the source bag files

    Args:
        rosbag_to_rospkl_files_dict: Dict -  dictionary containing the mapping between a source rosbag files and all the pkl files extracted from it
        ros_pkl_processed_data_timestamps_dict: Dict - dictionary containing the mapping between pkl file paths
        (local dependency removed) and extracted data fields and datetime objects both in utc and pst
        for all the extracted messages

    Returns:
        start_end_time_for_rosbag_dict: Dict - dictionary containing the mapping between the source rosbag name and the start and end
        datetime objects both in utc and pst
    """
    start_end_time_for_rosbag_dict = {}
    for source_rosbag_path, pkl_file_paths_list in rosbag_to_rospkl_files_dict.items():
        # assumes that the pkl_file_paths_list is sorted temporally. Also works when the pkl_file_paths_list contain only
        # one pkl file. This is the case when we are processing all_processed_data.pkl.
        first_pkl_file_path = pkl_file_paths_list[0]
        last_pkl_file_path = pkl_file_paths_list[-1]

        # first timestamp (in utc) of the first pkl file extracted from the source rosbag
        start_time_utc_ros_bag = ros_pkl_processed_data_timestamps_dict[first_pkl_file_path][
            "coord_t_datetime_utc"
        ][0]
        # last timestamp (in utc) of the last pkl file extarcted from the source rosbag
        end_time_utc_ros_bag = ros_pkl_processed_data_timestamps_dict[last_pkl_file_path][
            "coord_t_datetime_utc"
        ][-1]

        # first timestamp (in pst) of the first pkl file extracted from the source rosbag
        start_time_pst_ros_bag = ros_pkl_processed_data_timestamps_dict[first_pkl_file_path][
            "coord_t_datetime_pst"
        ][0]
        # last timestamp (in pst) of the last pkl file extarcted from the source rosbag
        end_time_pst_ros_bag = ros_pkl_processed_data_timestamps_dict[last_pkl_file_path][
            "coord_t_datetime_pst"
        ][-1]
        start_end_time_for_rosbag_dict[source_rosbag_path] = {
            KEY_START_TIME_UTC_ROSBAG: start_time_utc_ros_bag,
            KEY_END_TIME_UTC_ROSBAG: end_time_utc_ros_bag,
            KEY_START_TIME_PST_ROSBAG: start_time_pst_ros_bag,
            KEY_END_TIME_PST_ROSBAG: end_time_pst_ros_bag,
        }

    return start_end_time_for_rosbag_dict


def get_rosbag_to_videos_association(
    start_end_time_for_rosbag_dict: Dict, videos_to_video_id_dict: Dict
) -> Dict:
    """
    Function that computes which videos are associated with a source rosbag by checking
    if the start and end datetime objects of the rosbag and the videos overlap

    Args:
        start_end_time_for_rosbag_dict: Dict - dictionary containing the mapping between the source rosbag name and the start and end
        datetime objects both in utc and pst
        video_to_video_id_dict: Dict - dictionary mapping between video_pathname and video_ids and video_session_chunk_association dict

    Returns:
        rosbag_to_videos_association_dict: Dict - dictionary containing the mapping between source rosbag key and all the videos that have any time overlap with the rosbag

    """
    rosbag_to_videos_association_dict = {}
    for rosbag_key in start_end_time_for_rosbag_dict.keys():
        start_end_time_for_rosbag = start_end_time_for_rosbag_dict[rosbag_key]
        start_time_utc_rosbag = start_end_time_for_rosbag[KEY_START_TIME_UTC_ROSBAG]
        end_time_utc_rosbag = start_end_time_for_rosbag[KEY_END_TIME_UTC_ROSBAG]
        rosbag_time_range = DateTimeRange(start_time_utc_rosbag, end_time_utc_rosbag)
        rosbag_to_videos_association_dict[rosbag_key] = []
        for video_key in videos_to_video_id_dict.keys():
            if "Leia" not in video_key:  # Doing this to speed up processing a bit.
                continue

            video_start_time_utc = videos_to_video_id_dict[video_key][KEY_VID_SESSION_CHUNK_ASSOC][
                KEY_VID_INFO
            ][KEY_VID_START_TIME_UTC]
            video_end_time_utc = videos_to_video_id_dict[video_key][KEY_VID_SESSION_CHUNK_ASSOC][
                KEY_VID_INFO
            ][KEY_VID_END_TIME_UTC]

            video_time_range = DateTimeRange(video_start_time_utc, video_end_time_utc)

            if rosbag_time_range.is_intersection(video_time_range):
                print(" VIDEO KEY FOR ROSBAG", video_key, rosbag_key)
                rosbag_to_videos_association_dict[rosbag_key].append(video_key)

    return rosbag_to_videos_association_dict


def parse_data_association_excel_sheet(sessions_xlsx_filename: str) -> List:
    """
    Function to parse the data association excel sheet and create a list containing info of each session shunk. Each element in the list is a session chunk (5 min)

    Args:
        sessions_xlsx_filename: str - path to the data association excel sheet

    Returns:
        session_chunks_table: List - list of dictionaries each of which is info regarding a session chunk. Information include, vehicle name, participant id,
        start and end time datetime objects both in utc and pst and session ids.
    """
    with open(sessions_xlsx_filename, "rb") as fp:
        # extract the vehicle and date specific spreadsheet names separately.
        raw_vehicle_timesheet_keys = []
        for raw_vehicle_name in ALL_VEHICLE_NAMES:
            for date in ALL_DATES:
                # note that %m and %d on the datetime object returns the month and the date
                data_sheet_key = raw_vehicle_name + " " + date.strftime("%m-%d")
                raw_vehicle_timesheet_keys.append(data_sheet_key)

        # extract vehicle-date specific spreadsheets as pandas dataframe.
        vehicle_date_specific_spreadsheets_dict = pd.read_excel(
            fp, sheet_name=raw_vehicle_timesheet_keys
        )

        # each 'session' refers to the ~20min segment that a subject got to experience. Each session is broken down into 5 minute chunks in the excel sheet

        session_chunks_table = []
        for raw_vehicle_name in ALL_VEHICLE_NAMES:
            for date in ALL_DATES:
                vehicle_date_data_sheet_key = raw_vehicle_name + " " + date.strftime("%m-%d")
                # vehicle_date_data_sheet is a pandas dataframe containing all the information from the excel sheet correspnding to raw_vehicle_name and date
                vehicle_date_data_sheet = vehicle_date_specific_spreadsheets_dict[
                    vehicle_date_data_sheet_key
                ]

                # get the datasheet corresponding to vehicle whose name is raw_vehicle_name fot date=date

                for row_idx in range(
                    len(vehicle_date_data_sheet[KEY_VEH_DATASHEET_PID])
                ):  # iterate over all rows
                    # check if row is non empty. If empty, then dataframe will contain nan.
                    if not np.isnan(vehicle_date_data_sheet[KEY_VEH_DATASHEET_SESSION][row_idx]):
                        # session_chunk_id indicates the nth session associated with a particular participant
                        session_chunk_id = vehicle_date_data_sheet[KEY_VEH_DATASHEET_SESSION][
                            row_idx
                        ]
                        participant_id = vehicle_date_data_sheet[KEY_VEH_DATASHEET_PID][row_idx]
                        location = vehicle_date_data_sheet[KEY_VEH_DATASHEET_LOCATION][row_idx]
                        # already a datetime object in PST
                        start_time = vehicle_date_data_sheet[KEY_VEH_DATASHEET_TIME][row_idx]
                        try:
                            # TODO(guy.rosman): read as a parameter, or get end time from next session chunk
                            # each session is broken down into 5 minutes=300s #note that this is approximate
                            step_time = 300
                            # absolute start time and date for the session chunk
                            start_datetime_naive = datetime.datetime.combine(
                                date, start_time
                            )  # naive
                            # localize to US/Pacific timezone
                            start_datetime_pst = pytz.timezone("US/Pacific").localize(
                                start_datetime_naive
                            )
                            # create utc timeobject for the same
                            start_datetime_utc = start_datetime_pst.astimezone(pytz.utc)

                            # create end datetime object for each session chunk
                            end_datetime_pst = start_datetime_pst + datetime.timedelta(
                                0, step_time
                            )
                            end_datetime_utc = start_datetime_utc + datetime.timedelta(
                                0, step_time
                            )
                            session_chunks_table.append(
                                {
                                    KEY_STR_PID: int(participant_id),
                                    KEY_STR_SESSION: int(session_chunk_id),
                                    KEY_STR_VEHICLE: raw_vehicle_name,
                                    KEY_STR_LOCATION: location,
                                    KEY_STR_START_TIME_PST: start_datetime_pst,
                                    KEY_STR_END_TIME_PST: end_datetime_pst,
                                    KEY_STR_START_TIME_UTC: start_datetime_utc,
                                    KEY_STR_END_TIME_UTC: end_datetime_utc,
                                    # TODO (deepak.gopinath) Maybe add gopro full video path in box?
                                }
                            )
                        except:
                            print(
                                f"Failed to load {raw_vehicle_name},{date},[{session_chunk_id}],[{participant_id}]"
                            )

    return session_chunks_table


def create_subtitle_data_frame_for_rosbag(
    transcript_files_associated_with_rosbag: List,
    transcript_timestamps_and_video_association_dict: Dict,
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Function to parse the transcript files (either the sentence level or the word level into pandas dataframe)

    Args:
    transcript_files_associated_with_rosbag - List - List of transcript files associated with a given rosbag
    transcript_timestamps_and_video_association_dict - Dict - dictionary containing mappings from utterances in a transcript to datetime
        objects both in utc and pst computed wrt the video timestamps

    Returns:
    (df_subtitle_cabin, df_subtitle_front) - pd.DataFrame - dataframe containing the transcripts from the (cabin, front) gopro for a given rosbag

    """
    df_subtitle_dicts = collections.OrderedDict()
    df_subtitle_dicts["cabin"] = None
    df_subtitle_dicts["front"] = None
    for transcript_file_for_rosbag in transcript_files_associated_with_rosbag:
        for camera_type, df_subtitle in df_subtitle_dicts.items():
            if camera_type in transcript_file_for_rosbag:
                if df_subtitle is None:
                    df_subtitle_dicts[camera_type] = pd.DataFrame.from_dict(
                        transcript_timestamps_and_video_association_dict[KEY_AUX_UTTERANCES][
                            transcript_file_for_rosbag
                        ]
                    )
                else:
                    df_subtitle_dicts[camera_type] = pd.concat(
                        [
                            df_subtitle_dicts[camera_type],
                            pd.DataFrame.from_dict(
                                transcript_timestamps_and_video_association_dict[
                                    KEY_AUX_UTTERANCES
                                ][transcript_file_for_rosbag]
                            ),
                        ],
                        axis=0,
                    )

    return tuple(df_subtitle_dicts.values())


def convert_to_s_ms(t: float) -> Tuple[int, int]:
    """
    Utility function to convert time in seconds (any float) into rounded seconds and milliseconds

    Args:
    t: float - time value to be converted into seconds and milliseconds

    Returns:
    (s, ms): Tuple - contain the equivalent seconds and milliseconds.

    """
    ms, s = math.modf(t)
    return int(round(s)), int(round(ms * 1000))


def get_transcript_ros_time_synced_data(
    rosbag_to_rospkl_files_dict: Dict,
    rosbag_to_sentence_level_transcripts_association_dict: Dict,
    rosbag_to_word_level_transcripts_association_dict: Dict,
    ros_pkl_processed_data_timestamps_dict: Dict,
    sentence_level_transcript_timestamps_and_video_association_dict: Dict,
    word_level_transcript_timestamps_and_video_association_dict: Dict,
    rosbag_to_session_chunk_association_dict: Dict,
    gopro_cabin_to_ros_offset: Dict,
) -> Dict:
    # TODO (deepak.gopinath) Modularize this function further to make it less of a monolith
    """
    Function to merge ros data and subtitle data from cabin and front videos into a single pandas dataframe
    merged according to the datetime-objects.

    Args:
        rosbag_to_rospkl_files_dict: Dict - dictionary mapping between a rosbag to all the pkl files extracted from it.
        rosbag_to_sentence_level_transcripts_association_dict: Dict - dictionary mapping between a rosbag and all the sentence level transcript files associated with it
        rosbag_to_word_level_transcripts_association_dict: Dict - dictionary mapping between a rosbag and all word level transcript files associated with it
        ros_pkl_processed_data_timestamps_dict: Dict - dictionary containing the mapping between pkl file paths
        (local dependency removed) and a subset of extracted data fields and datetime objects both in utc and pst
        for all the extracted messages
        sentence_level_transcript_timestamps_and_video_association_dict: Dict - dictionary containing the timestamp information (datetime objects both in utc and pst) of
        all sentence-level transcripts
        word_level_transcript_timestamps_and_video_association_dict: Dict - dictionary containing the timestamp information (datetime objects both in utc and pst) of
        all word-level transcripts
        rosbag_to_session_chunk_association_dict: Dict - dictionary containing the session chunks corresponding to each rosbag
        gopro_cabin_to_ros_offset: Dict - dictionary containing the time offset between gopro cabin videos and ROSbag according to rosbag key.
    Returns:
        ros_subtitle_merged_dfs_dict - Dict - dictionary containing the mapping between rosbags and merged dataframe containing
        ros data, subtitle (both sentence-level and word-level) data from cabin videos and front videos. Note that multiple videos of the same type (cabin/front) are concantenated temporally in the correct order
    """
    ros_subtitle_merged_dfs_dict = {}
    for rosbag_key, ros_pkl_list in rosbag_to_rospkl_files_dict.items():
        print()
        print(" Processing merging of data for ", rosbag_key)
        # data frame initilaization for holding the rosbag data
        ros_bag_df = None
        # combine the data from all pkl files associated with a rosbag into a combined (along the time axis) pandas dataframe

        for pkl in ros_pkl_list:
            if ros_bag_df is None:
                ros_bag_df = pd.DataFrame.from_dict(ros_pkl_processed_data_timestamps_dict[pkl])
            else:
                # vertically concatenate all data along the time axis.
                ros_bag_df = pd.concat(
                    [
                        ros_bag_df,
                        pd.DataFrame.from_dict(ros_pkl_processed_data_timestamps_dict[pkl]),
                    ],
                    axis=0,
                )

        # get the sentence_level_transcript files associated with rosbag
        sentence_level_transcript_files_associated_with_rosbag = (
            rosbag_to_sentence_level_transcripts_association_dict[rosbag_key]
        )
        word_level_transcript_files_associated_with_rosbag = (
            rosbag_to_word_level_transcripts_association_dict[rosbag_key]
        )

        # separate dataframes for transcripts from cabin videos and front videos. most likely transcripts from only one of them will be useful.
        # all front videos will be contiguous and all cabin videos will be made temporally contiguous by sorting the values
        # according to the start_timepoint_utc
        # furthermore the timestamps of cabin and front videos are in some sense parallel (as both videos are getting recorded almost at the same time)

        # get dataframes for sentence and word level subtitles
        dfs_dict_for_rosbag = collections.OrderedDict()
        dfs_dict_for_rosbag["sentence_level"] = collections.OrderedDict()
        dfs_dict_for_rosbag["word_level"] = collections.OrderedDict()
        dfs_dict_for_rosbag["sentence_level"]["cabin"] = None
        dfs_dict_for_rosbag["sentence_level"]["front"] = None
        dfs_dict_for_rosbag["word_level"]["cabin"] = None
        dfs_dict_for_rosbag["word_level"]["front"] = None

        (
            dfs_dict_for_rosbag["sentence_level"]["cabin"],
            dfs_dict_for_rosbag["sentence_level"]["front"],
        ) = create_subtitle_data_frame_for_rosbag(
            sentence_level_transcript_files_associated_with_rosbag,
            sentence_level_transcript_timestamps_and_video_association_dict,
        )

        (
            dfs_dict_for_rosbag["word_level"]["cabin"],
            dfs_dict_for_rosbag["word_level"]["front"],
        ) = create_subtitle_data_frame_for_rosbag(
            word_level_transcript_files_associated_with_rosbag,
            word_level_transcript_timestamps_and_video_association_dict,
        )

        if (
            dfs_dict_for_rosbag["sentence_level"]["cabin"] is None
            or dfs_dict_for_rosbag["sentence_level"]["front"] is None
            or dfs_dict_for_rosbag["word_level"]["cabin"] is None
            or dfs_dict_for_rosbag["word_level"]["front"] is None
        ):
            print("subtitle dfs are empty. skipping merge process for rosbag", rosbag_key)
            ros_subtitle_merged_dfs_dict[rosbag_key] = {}
            continue
        else:
            # create dummy 'time' column in rosbag dataframe for merging purposes
            ros_bag_df["time"] = ros_bag_df.loc[:, "coord_t_datetime_utc"]
            ts_deltas = [float(v) / NANOS_TO_S_FACTOR for v in np.diff(ros_bag_df["time"].values)]
            ts_deltas = [(i, t) for i, t in enumerate(ts_deltas) if t > DT_TOLERANCE]
            print(
                "TEMPORAL DISCONTINUITIES",
                ts_deltas,
                [
                    (
                        ros_bag_df["time"].values[i],
                        ros_bag_df["time"].values[i - 1],
                        ros_bag_df["time"].values[i + 1],
                    )
                    for (i, t) in ts_deltas
                ],
            )
            df_num = 0

            for level in dfs_dict_for_rosbag.keys():
                for camera_type in dfs_dict_for_rosbag[level].keys():
                    if camera_type == "cabin":
                        if rosbag_key in gopro_cabin_to_ros_offset:
                            gopro_cabin_to_ros_offset_for_rosbagkey = gopro_cabin_to_ros_offset[
                                rosbag_key
                            ]
                        else:
                            gopro_cabin_to_ros_offset_for_rosbagkey = None
                    # sort the subtitle dataframes according to datetime object if the subtitle dataframes were successfully created
                    dfs_dict_for_rosbag[level][camera_type] = dfs_dict_for_rosbag[level][
                        camera_type
                    ].sort_values(by="start_timepoint_utc", ascending=True)

                    if (
                        gopro_cabin_to_ros_offset_for_rosbagkey is not None
                        and camera_type == "cabin"
                    ):
                        # convert the offset into seconds and milliseconds. Assumes that the offset is always expressed as seconds.
                        s, ms = convert_to_s_ms(gopro_cabin_to_ros_offset_for_rosbagkey)

                        dfs_dict_for_rosbag[level][camera_type].loc[:, "start_timepoint_utc"] = (
                            dfs_dict_for_rosbag[level][camera_type].loc[:, "start_timepoint_utc"]
                            + np.timedelta64(s, "s")
                            + np.timedelta64(ms, "ms")
                        )
                        dfs_dict_for_rosbag[level][camera_type].loc[:, "end_timepoint_utc"] = (
                            dfs_dict_for_rosbag[level][camera_type].loc[:, "end_timepoint_utc"]
                            + np.timedelta64(s, "s")
                            + np.timedelta64(ms, "ms")
                        )
                        dfs_dict_for_rosbag[level][camera_type].loc[:, "start_timepoint_pst"] = (
                            dfs_dict_for_rosbag[level][camera_type].loc[:, "start_timepoint_pst"]
                            + np.timedelta64(s, "s")
                            + np.timedelta64(ms, "ms")
                        )
                        dfs_dict_for_rosbag[level][camera_type].loc[:, "end_timepoint_pst"] = (
                            dfs_dict_for_rosbag[level][camera_type].loc[:, "end_timepoint_pst"]
                            + np.timedelta64(s, "s")
                            + np.timedelta64(ms, "ms")
                        )

                    # create a new dataframe (empty initially) whose time axis matches the time axis of ros_bag_df
                    # eventually new_df will contain subtitle information which will then be merged with ros_bag_df
                    new_df = pd.DataFrame(ros_bag_df["time"].copy())
                    for key in dfs_dict_for_rosbag[level][camera_type].keys():
                        new_df[key] = pd.Series(
                            dtype=dfs_dict_for_rosbag[level][camera_type][key].dtype
                        )
                    # add dummy subtitle entries for 'subtitle' column
                    new_df["subtitle"] = [
                        srt.Subtitle(index=0, start=0, end=0, content="NA")
                    ] * len(new_df)
                    new_df["subtitle_label"] = [0] * len(new_df)  # default label for no-sentence

                    assert len(new_df) == len(ros_bag_df)

                    # get closest time index in rosbag to the transcript time index
                    minindices = []
                    for valid_transcript_idx, transcript_timestamp_utc in enumerate(
                        dfs_dict_for_rosbag[level][camera_type]["start_timepoint_utc"]
                    ):
                        if (
                            transcript_timestamp_utc >= new_df.time[0]
                            and transcript_timestamp_utc <= new_df.time[len(new_df) - 1]
                        ):  # ensure that the transcript time stamp is within the start and end of the rosbag
                            delta_t = (transcript_timestamp_utc - new_df.time).abs()
                            min_dt_idx = delta_t.idxmin()  # this is a time index in the rosbag
                            min_dt = delta_t[min_dt_idx]
                            # check if the delta t between transcript and ros timestamp is greater than tolerance.
                            # this can happen when transcript timestamp is in the deadzone for rosbag frame. deadzones
                            # in rosbag happens when vehicle is stopped (for example, during a session on track) and there is temporal discontinuity
                            if not float(min_dt.value) / NANOS_TO_S_FACTOR > DT_TOLERANCE:
                                minindices.append((valid_transcript_idx, min_dt_idx))

                    ts_alignment = []
                    subtitle_lens = collections.OrderedDict()
                    intra_subtitle_time_lens = collections.OrderedDict()
                    for (t_i, _), (tp1_i, _) in zip(minindices[:-1], minindices[1:]):
                        # time gaps between the start point of each subtitle
                        t_start = dfs_dict_for_rosbag[level][camera_type][
                            "start_timepoint_utc"
                        ].iloc[t_i]
                        t_end = dfs_dict_for_rosbag[level][camera_type]["end_timepoint_utc"].iloc[
                            t_i
                        ]
                        t_dur = (t_end - t_start).seconds + (t_end - t_start).microseconds * (
                            (10 ** (-6))
                        )  # time duraction dervied from utc

                        tp1_start = dfs_dict_for_rosbag[level][camera_type][
                            "start_timepoint_utc"
                        ].iloc[tp1_i]
                        intra_subtitle_time_lens[t_i] = (tp1_start - t_start).total_seconds()

                        # length of each subtitle.
                        subtitle_at_t_i = dfs_dict_for_rosbag[level][camera_type]["subtitle"].iloc[
                            t_i
                        ]
                        subtitle_start = subtitle_at_t_i.start
                        subtitle_end = subtitle_at_t_i.end
                        subtitle_len = (subtitle_end - subtitle_start).seconds + (
                            subtitle_end - subtitle_start
                        ).microseconds * (10 ** (-6))
                        subtitle_lens[t_i] = subtitle_len

                        # sanity check to see if the length of subtitle are logged consistently

                        # print(t_dur, subtitle_len)
                        assert t_dur == subtitle_len

                        # sanity check to see if the end time of subtitle at t is before the start time of subtitle at t + 1
                        assert (
                            (tp1_start - t_end).seconds
                            + (tp1_start - t_end).microseconds * (10**-6)
                        ) >= 0.0

                    # add last transcript idx subtitle len
                    last_transcript_idx = minindices[-1][0]
                    subtitle_at_transcript_idx = dfs_dict_for_rosbag[level][camera_type][
                        "subtitle"
                    ].iloc[last_transcript_idx]
                    subtitle_start = subtitle_at_transcript_idx.start
                    subtitle_end = subtitle_at_transcript_idx.end
                    subtitle_len = (subtitle_end - subtitle_start).seconds + (
                        subtitle_end - subtitle_start
                    ).microseconds * (10 ** (-6))
                    subtitle_lens[last_transcript_idx] = subtitle_len

                    print(
                        f"INTRA SUBTITLE",
                        np.mean(list(intra_subtitle_time_lens.values())),
                        np.std(list(intra_subtitle_time_lens.values())),
                    )
                    print(
                        f"SUBTITLE LENS",
                        np.mean(list(subtitle_lens.values())),
                        np.std(list(subtitle_lens.values())),
                    )

                    subtitle_lens_used = collections.OrderedDict()
                    frequency_for_df = 1.0 / (
                        float(np.mean(np.diff(new_df.time.values))) * (10**-9)
                    )
                    for transcript_idx, min_dt_idx in minindices:
                        # accumulate the delta between the nearest timestamps in both dataframes. /10*9 to convert nanoseconds to seconds
                        ts_alignment.append(
                            abs(
                                float(
                                    dfs_dict_for_rosbag[level][camera_type][
                                        "start_timepoint_utc"
                                    ].values[transcript_idx]
                                    - new_df["time"].values[min_dt_idx]
                                )
                                / NANOS_TO_S_FACTOR
                            )
                        )

                        # extract time range of subtitle at transcript idx
                        subtitle_at_transcript_idx = dfs_dict_for_rosbag[level][camera_type][
                            "subtitle"
                        ].iloc[transcript_idx]

                        # subtitle_lens[transcript_idx] = subtitle_len
                        # target_time_in_new_df = new_df.at[min_dt_idx, "time"] + subtitle_end - subtitle_start
                        # target_time_idx_in_new_df = (target_time_in_new_df - new_df.time).abs().idxmin()
                        if transcript_idx in subtitle_lens:
                            subtitle_len = subtitle_lens[transcript_idx]  # in seconds
                            target_time_idx_in_new_df = min_dt_idx + round(
                                subtitle_len * frequency_for_df
                            )
                            subtitle_lens_used[transcript_idx] = round(
                                subtitle_len * frequency_for_df
                            )
                        # if transcript_idx in intra_subtitle_time_lens:
                        #     time_distance_to_next_subtitle = intra_subtitle_time_lens[transcript_idx]  # in sec

                        #     target_time_idx_in_new_df = min_dt_idx + min(
                        #         round(3 * frequency_for_df), round(time_distance_to_next_subtitle * frequency_for_df)
                        #     )  # 3 seconds at around ~50Hz
                        #     if round(time_distance_to_next_subtitle * frequency_for_df) < round(3 * frequency_for_df):
                        #         print(
                        #             "Next subtitle happens in {} steps".format(
                        #                 round(time_distance_to_next_subtitle * frequency_for_df)
                        #             )
                        #         )
                        #     subtitle_lens_used[transcript_idx] = min(
                        #         round(3 * frequency_for_df), round(time_distance_to_next_subtitle * frequency_for_df)
                        #     )

                        # else:
                        #     # TODO (deepak.gopinath) when is it that this block is entered. is it only for the last transcript idx?
                        #     target_time_idx_in_new_df = min_dt_idx + round(3 * frequency_for_df)
                        #     subtitle_lens_used[transcript_idx] = round(3 * frequency_for_df)

                        if target_time_idx_in_new_df > len(new_df):
                            target_time_idx_in_new_df = len(new_df) - 1
                        # copy the all columns at the corresponding closest row from transcript_df to new_df
                        for key in dfs_dict_for_rosbag[level][camera_type].keys():
                            # copy the column into the time range spanned by start and end of the subtitle.
                            new_df.loc[
                                min_dt_idx:target_time_idx_in_new_df, key
                            ] = dfs_dict_for_rosbag[level][camera_type][key].iloc[transcript_idx]
                    # plt.figure()
                    # plt.hist(list(subtitle_lens.values()))
                    # plt.title(
                    #     f"SUBTITLE LEN mean and std {np.mean(list(subtitle_lens.values()))} {np.std(list(subtitle_lens.values()))}"
                    # )
                    # plt.xlabel("Seconds")
                    # rbgk = rosbag_key.replace("/", "_")
                    # plt_title = os.path.join(
                    #     "/home/<USER>/Data/Thunderhill/New_Visualizations",
                    #     f"SUBTITLE_LENS_{level}_{camera_type}_{rbgk}",
                    # )
                    # plt.savefig(plt_title)
                    # plt.close()

                    print(
                        "AVERAGE and STD ALIGNMENT FOR ",
                        rosbag_key,
                        level,
                        camera_type,
                        np.mean(ts_alignment),
                        np.std(ts_alignment),
                        "seconds",
                    )

                    # merge new_df with ros_bag_df
                    if df_num == 0:
                        ros_subtitle_df_merged = pd.merge(ros_bag_df, new_df, on="time")
                        df_num += 1
                    else:
                        ros_subtitle_df_merged = pd.merge(
                            ros_subtitle_df_merged, new_df, on="time"
                        )

                    # rename dataframe keys to avoid clashes
                    ros_subtitle_df_merged = ros_subtitle_df_merged.rename(
                        columns={
                            "start_timepoint_utc": "start_timepoint_utc_"
                            + level
                            + "_"
                            + camera_type,
                            "end_timepoint_utc": "end_timepoint_utc_" + level + "_" + camera_type,
                            # note that when parsed into dataframe utc and pst became the same.
                            # TODO (deepak.gopinath) fix pst being same as utc issue.
                            "start_timepoint_pst": "start_timepoint_pst_"
                            + level
                            + "_"
                            + camera_type,
                            "end_timepoint_pst": "end_timepoint_pst_" + level + "_" + camera_type,
                            "subtitle": "subtitle_" + level + "_" + camera_type,
                            "subtitle_label": "subtitle_label_" + level + "_" + camera_type,
                            "video_key": "video_key_" + level + "_" + camera_type,
                        }
                    )

            # break dataframe into separate dataframes at temporal discontinuites points
            if len(ts_deltas) > 0:
                break_points = [-1]  # -1 because we want to start with 0-index
                break_points.extend([t[0] for t in ts_deltas])
                break_points.extend([len(new_df) - 1])
                partial_ros_subtitle_merged_df_list = []
                for start, end in zip(break_points[:-1], break_points[1:]):
                    partial_ros_subtitle_merged_df_list.append(
                        ros_subtitle_df_merged.iloc[start + 1 : end + 1]
                    )

                # sanity checks
                assert len(partial_ros_subtitle_merged_df_list) == len(ts_deltas) + 1
                assert sum([len(s) for s in partial_ros_subtitle_merged_df_list]) == len(
                    ros_subtitle_df_merged
                )

            else:
                partial_ros_subtitle_merged_df_list = [ros_subtitle_df_merged]

            participant_ids_rosbag = list(
                set(
                    [
                        c["participant_ID"]
                        for c in rosbag_to_session_chunk_association_dict[rosbag_key]
                    ]
                )
            )

            # TODO (deepak.gopinath) when is len(participant_ids_rosbag) == 0

            # initialize participant ID, session_ID and location
            for partial_ros_subtitle_df_merged in partial_ros_subtitle_merged_df_list:
                partial_ros_subtitle_df_merged.loc[:, "participant_ID"] = np.nan
                partial_ros_subtitle_df_merged.loc[:, "session_ID"] = np.nan
                partial_ros_subtitle_df_merged.loc[:, "location"] = np.nan

            if len(participant_ids_rosbag) != 0:
                for partial_ros_subtitle_df_merged in partial_ros_subtitle_merged_df_list:
                    for chunk in rosbag_to_session_chunk_association_dict[rosbag_key]:
                        start_time = chunk["start_time_utc"]
                        nearest_start_index = partial_ros_subtitle_df_merged.iloc[
                            (partial_ros_subtitle_df_merged["time"] - start_time)
                            .abs()
                            .argsort()[:1]
                        ].index[0]
                        end_time = chunk["end_time_utc"]
                        nearest_end_index = partial_ros_subtitle_df_merged.iloc[
                            (partial_ros_subtitle_df_merged["time"] - end_time).abs().argsort()[:1]
                        ].index[0]

                        # ensure that when participants switch within a ros (happens only once) wrong pid is not assigned
                        # if (
                        #     not ((partial_ros_subtitle_df_merged["time"] - start_time).abs()[nearest_start_index].value)
                        #     / NANOS_TO_S_FACTOR
                        #     > 700
                        # ):
                        # TODO (deepak.gopinath). Fix nearest time point search when multiple participants are present in a
                        # single rosbag
                        partial_ros_subtitle_df_merged.loc[
                            nearest_start_index:nearest_end_index, "participant_ID"
                        ] = int(chunk["participant_ID"])
                        partial_ros_subtitle_df_merged.loc[
                            nearest_start_index:nearest_end_index, "session_ID"
                        ] = int(chunk["session_ID"])
                        partial_ros_subtitle_df_merged.loc[
                            nearest_start_index:nearest_end_index, "location"
                        ] = chunk["location"]
            else:
                import IPython

                IPython.embed(banner1="empty pid")
                participant_id = -1
                session_id = -1
                location_id = "NA"
                for partial_ros_subtitle_df_merged in partial_ros_subtitle_merged_df_list:
                    # repeat pid, session id and location for each row of the data frame
                    partial_ros_subtitle_df_merged["participant_ID"] = participant_id
                    partial_ros_subtitle_df_merged["session_ID"] = session_id
                    partial_ros_subtitle_df_merged["location"] = location_id

            ros_subtitle_merged_dfs_dict[rosbag_key] = partial_ros_subtitle_merged_df_list

    return ros_subtitle_merged_dfs_dict


def add_encoded_sentences(
    sentence_level_transcript_timestamps_and_video_association_dict: Dict,
    model_location: str,
):
    """
    Function to add sentence classification vectors itn the utterances.

    Args:
        sentence_level_transcript_timestamps_and_video_association_dict: Dict - dictionary containing "utterances", to capture all utterances in the scripts.
        model_location: string - the folder that contains the saved model .pth and .pkl files.

    Returns:
        updates sentence_level_transcript_timestamps_and_video_association_dict in-place.
    """
    model_location = os.path.expandvars(os.path.expanduser(model_location))
    mlp_classifier, ldict = load_classifier(model_location)
    sentence_model_id, sentence_embedding_model = create_sentence_embedding_model()
    for srt_file in tqdm.tqdm(
        sentence_level_transcript_timestamps_and_video_association_dict["utterances"]
    ):
        sentences = []
        try:
            for itm_i, itm in enumerate(
                sentence_level_transcript_timestamps_and_video_association_dict["utterances"][
                    srt_file
                ]
            ):
                text = itm["subtitle"].content
                sentences.append(text)
            if len(sentences) > 0:
                sentence_emb = sentence_embedding_model.encode(sentences)
                sentence_vectors = mlp_classifier(torch.Tensor(sentence_emb))
        except:
            sentence_emb = None
            sentence_vectors = None
            import IPython

            IPython.embed()

        for itm_i, itm in enumerate(
            sentence_level_transcript_timestamps_and_video_association_dict["utterances"][srt_file]
        ):
            itm["encoding"] = sentence_vectors[itm_i, :]


def main():
    args = parse_arguments()
    # extract all arguments corresponding to different data directories
    sentence_level_transcript_input_dir = os.path.expandvars(
        os.path.expanduser(args["sentence_level_transcript_input_dir"])
    )
    word_level_transcript_input_dir = os.path.expandvars(
        os.path.expanduser(args["word_level_transcript_input_dir"])
    )
    mediainfo_input_dir = os.path.expandvars(os.path.expanduser(args["mediainfo_input_dir"]))
    # solo2_input_dir = os.path.expandvars(os.path.expanduser(args["solo2_input_dir"]))
    sessions_xlsx_filename = os.path.expandvars(os.path.expanduser(args["session_xlsx"]))
    map_csv_filename = os.path.expandvars(os.path.expanduser(args["track_map_csv"]))
    output_dir = os.path.expandvars(os.path.expanduser(args["output_dir"]))
    rosbag_input_dir = os.path.expandvars(os.path.expanduser(args["rosbag_input_dir"]))
    sentence_transcript_to_label_dict_path = os.path.expandvars(
        os.path.expanduser(args["transcript_label_pkl"])
    )
    # dataset_label = args["dataset_label"]
    # assert dataset_label is not None, "dataset label is not set, either use one, or set explicitly to an empty string"

    # extract the data files that are present in the specified data paths.

    sentence_level_transcript_files = sorted(
        glob.glob(
            os.path.join(sentence_level_transcript_input_dir, "**/G*.srt"),
            recursive=True,
        )
    )
    word_level_transcript_files = sorted(
        glob.glob(os.path.join(word_level_transcript_input_dir, "**/G*.srt"), recursive=True)
    )

    mediainfo_files = sorted(
        glob.glob(os.path.join(mediainfo_input_dir, "**/G*.txt"), recursive=True)
    )

    # sanity check. ensure that there are equal number of files in transcripts and mediainfo lists
    # Muting the following assert because we only have latest transcripts from track days.
    # assert len(sentence_level_transcript_files) == len(mediainfo_files)
    # TODO (deepak.gopinath). Add check to match every transcript to a corresponding mediainfo file

    # remove transcripts that had collapsed transcription issues or driving irrelevant parts from Whisper.
    transcripts_to_be_excluded = [
        # "2022-11-09/GoPro/Leia/cabin/GX015481.srt",
        # "2022-11-09/GoPro/Leia/cabin/GX025475.srt",
        "2022-11-09/GoPro/Leia/cabin/GX015487.srt",
        # "2022-11-09/GoPro/Leia/cabin/GX025486.srt",  # irrelevant chit chat all "others 1"
        # "2022-11-09/GoPro/Leia/cabin/GX025487.srt",  # irrelevant chit chat all "others 1"
        # "2022-11-10/GoPro/Leia/cabin/GX015493.srt",
        # "2022-11-10/GoPro/Leia/cabin/GX015489.srt",
        # "2022-11-10/GoPro/Leia/cabin/GX015500.srt", # all other
    ]
    full_path_transcripts_to_be_excluded = [
        os.path.join(sentence_level_transcript_input_dir, t) for t in transcripts_to_be_excluded
    ]
    sentence_level_transcript_files = [
        s for s in sentence_level_transcript_files if s not in full_path_transcripts_to_be_excluded
    ]
    # solo2_csv_files = sorted(glob.glob(os.path.join(solo2_input_dir, "**/PID*/Session*/csv/*.csv"), recursive=True))

    # read track map
    track_map = read_map_csv(map_csv_filename)

    base_count = 0
    os.makedirs(output_dir, exist_ok=True)

    # read the data from the pkl files extracted from rosbag and organize the processed data
    # Read all fields in the pkl files as dictionaries and add processed timestamps as well.
    rosbags_to_omit = [
        "ROS/2022-11-09/ROS/Leia/run_001",
        "ROS/2022-11-10/ROS/Leia/run_016",
        "ROS/2022-11-10/ROS/Leia/run_018",
    ]
    ros_pkl_pandas_dict, ros_pkl_processed_data_timestamps_dict = read_rosbag_pkl_files(
        rosbag_input_dir, rosbags_to_omit
    )

    # make a dict mapping a rosbag to all the pkl files extracted from it.
    rosbag_to_rospkl_files_dict = collections.defaultdict(list)
    for pkl_file_path in ros_pkl_pandas_dict.keys():
        source_rosbag_path, _ = os.path.split(os.path.split(pkl_file_path)[0])
        rosbag_to_rospkl_files_dict[source_rosbag_path].append(pkl_file_path)

    # extract the start and end datetime objects for each rosbag
    start_end_time_for_rosbag_dict = get_start_end_time_for_each_ros_bag(
        rosbag_to_rospkl_files_dict, ros_pkl_processed_data_timestamps_dict
    )

    # TODO (deepak.gopinath) Check the total time of ros message recorded (the ending time in last pkl file extracted from a rosbag) and the total length of the video from the mediainfo
    # parse data association excel sheet organized as a list of session chunks (each chunk is 5 minutes and correspond to a row in the excel)
    session_chunks_table = parse_data_association_excel_sheet(sessions_xlsx_filename)
    # read all the sentence_level transcript files corresponding to the go pro videos
    sentence_level_transcripts = read_transcripts(sentence_level_transcript_files)
    # read all the word level transcript files corresponding to the go pro videos
    word_level_transcripts = read_transcripts(word_level_transcript_files)
    # Read dictionary containing the labels for the sentence level transcripts
    with open(sentence_transcript_to_label_dict_path, "rb") as fp:
        sentence_transcript_to_label_dict = pickle.load(fp)

    # create list of dicts, each of which is a map from video to all sessions chunks that have overlap.
    videos_to_sessions_chunks_association_list = []

    for mediainfo_file in tqdm.tqdm(mediainfo_files, desc="reading media info files"):
        print(f"parsing {mediainfo_file}")
        video_to_session_chunk_association_dict = extract_video_to_session_chunk_association(
            mediainfo_file, session_chunks_table
        )
        videos_to_sessions_chunks_association_list.append(video_to_session_chunk_association_dict)

    # create a dict that maps rosbags to all session chunks from the data association excel sheet that corresponds to the rosbag
    rosbag_to_session_chunk_association_dict = extract_rosbag_to_session_chunk_association(
        start_end_time_for_rosbag_dict, session_chunks_table
    )

    # get the list of videos with no valid student sessions associated with (could be instructor demos as well as junk video, for exampl video that was kept on during lunch breaks)
    # videos_with_no_session_associated = [
    #     v for v in videos_to_sessions_chunks_association_list if v[KEY_VID_SESSION] == []
    # ]

    videos_to_video_id_dict = get_videos_to_video_id_map(
        videos_to_sessions_chunks_association_list
    )
    assert len(videos_to_video_id_dict.keys()) == len(videos_to_sessions_chunks_association_list)

    # get the timestamps (in utc and pst) of transcript events and the associated videos
    sentence_level_transcript_timestamps_and_video_association_dict = (
        get_transcript_video_association(
            sentence_level_transcripts,
            "GoPro_Transcripts",
            videos_to_video_id_dict,
            sentence_transcript_to_label_dict,
        )
    )
    word_level_transcript_timestamps_and_video_association_dict = get_transcript_video_association(
        word_level_transcripts,
        "GoPro_Transcripts_Word-Level",
        videos_to_video_id_dict,
    )

    # solo2_parsed = get_solo2_csv(solo2_csv_files) #temporarily commented out
    # TODO(guy.rosman): process solo2 CSVs

    # check if the timestamps for a single rosbag is continuous (no temporal breaks due to missing _00x.pkl)
    for source_rosbag_path, pkl_file_paths_list in rosbag_to_rospkl_files_dict.items():
        coord_t_rosbag = []
        for pkl_file_path in pkl_file_paths_list:
            if type(ros_pkl_pandas_dict[pkl_file_path]) is list:
                coord_t_rosbag.extend(list(ros_pkl_pandas_dict[pkl_file_path][0].t.values))
            else:
                coord_t_rosbag.extend(list(ros_pkl_pandas_dict[pkl_file_path].t.values))
        print(
            "ROSBAG rate for {}".format(source_rosbag_path),
            1.0 / np.mean(np.diff(coord_t_rosbag)),  # sampling frequency, 1/dt
            np.mean(np.diff(coord_t_rosbag)),  # sampling interval
            np.std(np.diff(coord_t_rosbag)),  # std of sampling interval
        )  # 1/dt

    # using the datetime information (start and end of rosbag) identify which videos correspond to the rosbag
    rosbag_to_videos_association_dict = get_rosbag_to_videos_association(
        start_end_time_for_rosbag_dict, videos_to_video_id_dict
    )

    # compute the mapping from rosbag to which transcripts it contains and a map from transcript to which rosbag it is associated with.
    # can use rosbag_to_videos_association_dict to do this as there is a
    # one-to-one mapping between videos and transcripts
    rosbag_to_sentence_level_transcripts_association_dict = {}
    sentence_level_transcripts_to_rosbag_association_dict = {}

    rosbag_to_word_level_transcripts_association_dict = {}
    word_level_transcripts_to_rosbag_association_dict = {}

    partial_transcript_paths = [
        os.path.join("GoPro_Transcripts", p) for p in transcripts_to_be_excluded
    ]
    for rosbagkey, video_list in rosbag_to_videos_association_dict.items():
        sentence_level_transcript_paths_for_rosbag = []
        word_level_transcript_paths_for_rosbag = []
        for v in video_list:
            sentence_level_transcript_path = v.replace("GoPro", "GoPro_Transcripts", 1)
            sentence_level_transcript_path = sentence_level_transcript_path.replace("MP4", "srt")
            print(sentence_level_transcript_path)
            if not sentence_level_transcript_path in partial_transcript_paths:
                sentence_level_transcript_paths_for_rosbag.append(sentence_level_transcript_path)
                # TODO (deepak.gopinath) assumes that there is utmost one video. Check if this is indeed the case
                sentence_level_transcripts_to_rosbag_association_dict[
                    sentence_level_transcript_path
                ] = rosbagkey

            word_level_transcript_path = v.replace("GoPro", "GoPro_Transcripts_Word-Level", 1)
            word_level_transcript_path = word_level_transcript_path.replace("MP4", "srt")
            word_level_transcript_paths_for_rosbag.append(word_level_transcript_path)
            # TODO (deepak.gopinath) assumes that there is utmost one video. Check if this is indeed the case
            word_level_transcripts_to_rosbag_association_dict[
                word_level_transcript_path
            ] = rosbagkey

        rosbag_to_sentence_level_transcripts_association_dict[
            rosbagkey
        ] = sentence_level_transcript_paths_for_rosbag
        rosbag_to_word_level_transcripts_association_dict[
            rosbagkey
        ] = word_level_transcript_paths_for_rosbag

    # create gopro_to_ros_offset dictionary (the current values are based on manual syncing are appoximate)

    gopro_cabin_to_ros_offset = {
        "ROS/2022-11-09/ROS/Leia/run_001": None,  # skipped because this is instructor driving.
        "ROS/2022-11-09/ROS/Leia/run_002": -6.5,
        "ROS/2022-11-09/ROS/Leia/run_003": -6.5,
        "ROS/2022-11-09/ROS/Leia/run_004": -6.5,
        "ROS/2022-11-09/ROS/Leia/run_005": -6.5,
        "ROS/2022-11-09/ROS/Leia/run_006": -6.5,
        "ROS/2022-11-09/ROS/Leia/run_007": -7,
        "ROS/2022-11-09/ROS/Leia/run_008": -7.5,
        "ROS/2022-11-09/ROS/Leia/run_009": -7.5,
        "ROS/2022-11-09/ROS/Leia/run_010": -7.5,
        "ROS/2022-11-09/ROS/Leia/run_012": -6.5,
        "ROS/2022-11-09/ROS/Leia/run_013": -8,
        "ROS/2022-11-09/ROS/Leia/run_014": -7,
        "ROS/2022-11-10/ROS/Leia/run_004": -10.5,
        "ROS/2022-11-10/ROS/Leia/run_005": -11,
        "ROS/2022-11-10/ROS/Leia/run_006": -11.5,
        "ROS/2022-11-10/ROS/Leia/run_007": -12,
        "ROS/2022-11-10/ROS/Leia/run_008": -11,
        "ROS/2022-11-10/ROS/Leia/run_009": -12,
        "ROS/2022-11-10/ROS/Leia/run_012": -11.5,
        "ROS/2022-11-10/ROS/Leia/run_013": -12,
        "ROS/2022-11-10/ROS/Leia/run_014": -12,
        "ROS/2022-11-10/ROS/Leia/run_015": -11.5,
        "ROS/2022-11-10/ROS/Leia/run_016": None,
        "ROS/2022-11-10/ROS/Leia/run_017": -12,
        "ROS/2022-11-10/ROS/Leia/run_018": None,
    }

    # add_encoded_sentences(sentence_level_transcript_timestamps_and_video_association_dict, args["save_model_location"])

    # TODO (deepak.gopinath). Add manual offset for "front" videos

    rosbag_to_transcript_timesync_dict = get_transcript_ros_time_synced_data(
        rosbag_to_rospkl_files_dict,
        rosbag_to_sentence_level_transcripts_association_dict,
        rosbag_to_word_level_transcripts_association_dict,
        ros_pkl_processed_data_timestamps_dict,
        sentence_level_transcript_timestamps_and_video_association_dict,
        word_level_transcript_timestamps_and_video_association_dict,
        rosbag_to_session_chunk_association_dict,
        gopro_cabin_to_ros_offset,
    )

    # save the synced dataframes for each rosbag

    # create folder in Data folder for saving the synced dataframes
    synced_dfs_dir = os.path.join(
        os.path.split(os.path.split(rosbag_input_dir)[0])[0],
        "synced_data_frames_new_transcript_labels",
    )
    # make directory
    os.makedirs(synced_dfs_dir, exist_ok=True)

    # save each value in the synced data dict as seperate pkl files
    for rosbag_key, df_list in rosbag_to_transcript_timesync_dict.items():
        ros_filename = rosbag_key.replace("/", "_")
        for i, df in enumerate(df_list):
            print("Saving time synced data for ", rosbag_key, "dataframe id", i)
            filename = ros_filename + "_" + str(i) + ".pkl"
            file_path = os.path.join(synced_dfs_dir, filename)
            with open(file_path, "wb") as fp:
                pickle.dump(df, fp)

    import IPython

    IPython.embed(header="handle results")


if __name__ == "__main__":
    main()
