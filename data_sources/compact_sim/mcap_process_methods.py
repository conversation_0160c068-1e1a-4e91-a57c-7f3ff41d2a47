import json
from collections import defaultdict
from pathlib import Path

import cv2
import numpy as np
import pandas as pd

from util.utility import build_bbox, check_intersect, flatten_bbox


def process_carla_actor_ids(msgs, extract_ego_from_actor_list=False):
    """Method for extracting IDs of ego and ado agents"""
    id_dict = defaultdict(list)
    start_time = 0
    if not extract_ego_from_actor_list:
        msgs_hero_vehicle_info = msgs["/carla/hero/vehicle_info"]
        # parse ego id and rolename from /carla_hero/vehicle_info
        ego_info = msgs_hero_vehicle_info[0]
        ego_id = ego_info.ros_msg.id
        ego_rolename = ego_info.ros_msg.rolename
        id_dict[ego_rolename].append(ego_id)

    # take care of other agents and grab the info for those from carla/actor_list
    # TODO (deepak.gopinath) When extract_ego_from_actor_list=False, then the only topic present in msgs is /carla/hero/vehicle_info
    # and therefore the next line adds a new key ("/carla/actor_list") with an empty [] because msgs is a defaultdict(list)
    # This doesn't break anything but later on when process_methods is called on /carla/hero/vehicle_info one extra topic is passed with an empty
    # list. This is not clean. Option is to separate out the retreival of start_time to a separate function which will be invoked when the topic is /carla/actor_list

    msgs_actor_list = msgs["/carla/actor_list"]
    for ind, m in enumerate(msgs_actor_list):
        for a in m.ros_msg.actors:
            if a.rolename == "hero" and not extract_ego_from_actor_list:
                continue
            if a.id not in id_dict[a.rolename]:
                id_dict[a.rolename].append(a.id)

        if "hero" in id_dict.keys() and "autopilot" in id_dict.keys():
            if start_time <= 0:
                start_time = ind

    return id_dict, start_time


def process_carla_actor_list(msgs, **kwargs):
    """Method for extracting basic info about ALL carla actors"""

    data = defaultdict(list)

    actor_count = 0
    for ind, m in enumerate(msgs):
        for a in m.ros_msg.actors:
            try:
                index = data["carla_actor_id"].index(a.id)
            except:
                index = -1

            if index == -1:
                data["carla_actor_id"].append(a.id)
                data["carla_actor_type"].append(a.type)
                data["carla_actor_rolename"].append(a.rolename)
                data["carla_actor_first_appearance log time"].append(m.log_time_ns / 1e9)
                data["carla_actor_number_appearances"].append(1)
                actor_count += 1
            else:
                data["carla_actor_number_appearances"][index] += 1

    return dict(data)


def process_carla_start_times(msgs):
    """Method for extracting timestamps of scenario starts"""
    start_times = []
    for ind, m in enumerate(msgs):
        if m.ros_msg.condition == "start gate passed":
            start_times.append(m.log_time_ns / 1e9)
    return start_times


def append_object_info(data, agent, agent_prefix):
    """
    Helper function to append information about an agent to the data dictionary
    """
    data[f"{agent_prefix}_x"].append(agent.pose.position.x)
    data[f"{agent_prefix}_y"].append(agent.pose.position.y)
    data[f"{agent_prefix}_z"].append(agent.pose.position.z)
    data[f"{agent_prefix}_orientation_x"].append(agent.pose.orientation.x)
    data[f"{agent_prefix}_orientation_y"].append(agent.pose.orientation.y)
    data[f"{agent_prefix}_orientation_z"].append(agent.pose.orientation.z)
    data[f"{agent_prefix}_orientation_w"].append(agent.pose.orientation.w)
    data[f"{agent_prefix}_vx"].append(agent.twist.linear.x)
    data[f"{agent_prefix}_vy"].append(agent.twist.linear.y)
    data[f"{agent_prefix}_vz"].append(agent.twist.linear.z)
    data[f"{agent_prefix}_wx"].append(agent.twist.angular.x)
    data[f"{agent_prefix}_wy"].append(agent.twist.angular.y)
    data[f"{agent_prefix}_wz"].append(agent.twist.angular.z)


def create_agent_dict(agent, agent_prefix):
    """
    Helper function to create a dictionary containing information about an agent
    """
    return {
        f"{agent_prefix}_x": agent.pose.position.x,
        f"{agent_prefix}_y": agent.pose.position.y,
        f"{agent_prefix}_z": agent.pose.position.z,
        f"{agent_prefix}_orientation_x": agent.pose.orientation.x,
        f"{agent_prefix}_orientation_y": agent.pose.orientation.y,
        f"{agent_prefix}_orientation_z": agent.pose.orientation.z,
        f"{agent_prefix}_orientation_w": agent.pose.orientation.w,
        f"{agent_prefix}_vx": agent.twist.linear.x,
        f"{agent_prefix}_vy": agent.twist.linear.y,
        f"{agent_prefix}_vz": agent.twist.linear.z,
        f"{agent_prefix}_wx": agent.twist.angular.x,
        f"{agent_prefix}_wy": agent.twist.angular.y,
        f"{agent_prefix}_wz": agent.twist.angular.z,
        # The length of the bbox dimensions are the full length of each side
        # of the bounding box.
        f"{agent_prefix}_bbox_x": agent.shape.dimensions[0],
        f"{agent_prefix}_bbox_y": agent.shape.dimensions[1],
        f"{agent_prefix}_bbox_z": agent.shape.dimensions[2],
    }


def extract_aligned_carla_objects(msgs, id_dict, data, ado_agents):
    """
    Method for processing /carla/objects
    Given a set of ros messages, this will extract ego and ado objects. If one ever appears without
    the other, the missing object will be nan-padded
    """
    ego_data = {}
    ado_data = {}
    other_objects = []
    for m in msgs:
        if len(m.ros_msg.objects) != 0:  # there should be at least some objects
            # if there are objects only add timestamp when ego is present.
            for agent in m.ros_msg.objects:
                if agent.id in id_dict["hero"]:
                    data["timestamp"].append(
                        m.ros_msg.header.stamp.sec + m.ros_msg.header.stamp.nanosec / 1e9
                    )
                    data["carla_objects log time"].append((m.log_time_ns) / 1e9)
                    break

        for i, agent in enumerate(m.ros_msg.objects):
            if agent.id in id_dict["hero"]:
                append_object_info(data, agent, "ego")
                ego_data = create_agent_dict(agent, "ego")

            # This check will only grab the first autopilot ado encountered in each message. We've
            # deemed this an acceptable risk since there are only ever 2 ado vehicles briefly when
            # one happens to spawn slightly before the previous ado is destroyed. When the time
            # comes that we want multple ado vehicles at a time this whole code block will have to
            # be changed anyway.
            elif ado_agents is not None and agent.id in id_dict["autopilot"] and not ado_data:
                append_object_info(ado_agents, agent, "ado")
                ado_data = create_agent_dict(agent, "ado")

            else:
                # Grab the bounding box from any other objects. These could contain cones, barriers,
                #  or other spawned objects used for the study.
                other_objects.append(create_agent_dict(agent, "object"))

        # Check for collisions
        ado_collision = False
        object_collision = False
        if ego_data:
            ego_quat = np.expand_dims(
                np.array(
                    [
                        ego_data["ego_orientation_x"],
                        ego_data["ego_orientation_y"],
                        ego_data["ego_orientation_z"],
                        ego_data["ego_orientation_w"],
                    ]
                ),
                axis=0,
            )
            ego_bbox_sides = np.expand_dims(
                np.array([ego_data["ego_bbox_x"], ego_data["ego_bbox_y"], ego_data["ego_bbox_z"]]),
                axis=0,
            )
            ego_bbox_vertices = flatten_bbox(
                build_bbox(
                    np.expand_dims(np.array([ego_data["ego_x"]]), axis=0),
                    np.expand_dims(np.array([ego_data["ego_y"]]), axis=0),
                    np.expand_dims(np.array([ego_data["ego_z"]]), axis=0),
                    ego_quat,
                    ego_bbox_sides,
                )
            )
            if ado_data:
                ado_quat = np.expand_dims(
                    np.array(
                        [
                            ado_data["ado_orientation_x"],
                            ado_data["ado_orientation_y"],
                            ado_data["ado_orientation_z"],
                            ado_data["ado_orientation_w"],
                        ]
                    ),
                    axis=0,
                )
                ado_bbox_sides = np.expand_dims(
                    np.array(
                        [ado_data["ado_bbox_x"], ado_data["ado_bbox_y"], ado_data["ado_bbox_z"]]
                    ),
                    axis=0,
                )
                ado_bbox_vertices = flatten_bbox(
                    build_bbox(
                        np.expand_dims(np.array([ado_data["ado_x"]]), axis=0),
                        np.expand_dims(np.array([ado_data["ado_y"]]), axis=0),
                        np.expand_dims(np.array([ado_data["ado_z"]]), axis=0),
                        ado_quat,
                        ado_bbox_sides,
                    )
                )

                ado_collision = np.any(check_intersect(ego_bbox_vertices, ado_bbox_vertices))
            if other_objects:
                for obj in other_objects:
                    obj_quat = np.expand_dims(
                        np.array(
                            [
                                obj["object_orientation_x"],
                                obj["object_orientation_y"],
                                obj["object_orientation_z"],
                                obj["object_orientation_w"],
                            ]
                        ),
                        axis=0,
                    )
                    obj_bbox_sides = np.expand_dims(
                        np.array(
                            [obj["object_bbox_x"], obj["object_bbox_y"], obj["object_bbox_z"]]
                        ),
                        axis=0,
                    )
                    obj_bbox_vertices = flatten_bbox(
                        build_bbox(
                            np.expand_dims(np.array([obj["object_x"]]), axis=0),
                            np.expand_dims(np.array([obj["object_y"]]), axis=0),
                            np.expand_dims(np.array([obj["object_z"]]), axis=0),
                            obj_quat,
                            obj_bbox_sides,
                        )
                    )
                    object_collision = np.any(
                        check_intersect(ego_bbox_vertices, obj_bbox_vertices)
                    )
                    if object_collision:
                        break
            # Append whether the ego collided with an ado or other object only if ego is present
            data["ado_collision"].append(ado_collision)
            data["object_collision"].append(object_collision)

        # If the ego agent appeared but not other agents, pad the data here
        if ego_data:
            if ado_agents is not None and not ado_data:
                for k in ado_agents.keys():
                    ado_agents[k].append(np.nan)
        # If the ado agent appeared but not the other agents, pad the data here
        elif ado_data:
            for k in data.keys():
                # Skip timestep/log-time keys, only pad the ego object b/c that's what's missing.
                if "ego" in k:
                    data[k].append(np.nan)

        # reset the data objects for next message and to use less memory.
        ego_data = {}
        ado_data = {}
        other_objects = []
    if ado_agents:
        data.update(ado_agents)

    return data


def extract_carla_starts(msgs, id_dict, start_times, data, ado_agents):
    """
    Extract the start positions/orientations of ego and ado agents
    Args:
        msgs: ROS messages from /carla/objects
        id_dict: dictionary mapping agent names to lists of known ids
                 ({'hero': [1000010], 'autopilot': [1000060, 1000085, 1000109, 1000133]}
        start_times: timestamps when hero (ego) crossed start gate line
        data: dictionary to fill out with ego data
        ado_agents: dictionary to fill out with ado data (or None if no ados)

    Returns:
        dictionary of parsed data
    """
    start_time_index = 0
    next_start = start_times[start_time_index]

    def _pad():
        if (
            not got_ego
        ):  # If there was no ego, pad with nans and pad start times with current timestamp
            for k in data.keys():
                data[k].append(np.nan)
        if ado_agents is not None and not got_ado:  # If there was no ado, pad with nans
            for k in ado_agents.keys():
                ado_agents[k].append(np.nan)

    got_ego = False
    got_ado = False
    for m in msgs:
        # Skip this step if it is before the known start and we already have the agents we want
        step_time = m.log_time_ns / 1e9
        if step_time < next_start:
            continue
        # If we got here that means that:
        # (A) step_time is > next_start (new start point) AND/OR
        # (B) we're still missing an agent from the last start
        for i, agent in enumerate(m.ros_msg.objects):
            if agent.id in id_dict["hero"]:
                if not got_ego:
                    data["ego_x"].append(agent.pose.position.x)
                    data["ego_y"].append(agent.pose.position.y)
                    data["ego_z"].append(agent.pose.position.z)
                    data["ego_orientation_x"].append(agent.pose.orientation.x)
                    data["ego_orientation_y"].append(agent.pose.orientation.y)
                    data["ego_orientation_z"].append(agent.pose.orientation.z)
                    data["ego_orientation_w"].append(agent.pose.orientation.w)
                    data["carla_objects log time"].append(step_time)
                    data["timestamp"].append(
                        m.ros_msg.header.stamp.sec + m.ros_msg.header.stamp.nanosec / 1e9
                    )
                    got_ego = True
            elif ado_agents is not None and agent.id in id_dict["autopilot"]:
                if not got_ado:
                    # WARNING -- Ado can show up _very_ late.
                    # Design decision if we want to pad with nans if it isn't in the same start
                    # frame as the ego.
                    ado_agents["ado_x"].append(agent.pose.position.x)
                    ado_agents["ado_y"].append(agent.pose.position.y)
                    ado_agents["ado_z"].append(agent.pose.position.z)
                    ado_agents["ado_orientation_x"].append(agent.pose.orientation.x)
                    ado_agents["ado_orientation_y"].append(agent.pose.orientation.y)
                    ado_agents["ado_orientation_z"].append(agent.pose.orientation.z)
                    ado_agents["ado_orientation_w"].append(agent.pose.orientation.w)
                    got_ado = True

        if step_time >= next_start:
            # If the message's time is after the start-gate:
            if start_time_index < (
                len(start_times) - 1
            ):  # If we aren't done parsing yet (still more starts ahead)
                # if we haven't gotten ego+ado and haven't passed the next start gate, keep
                # looking for initial position of ego or ado for the most recent start gate.
                if step_time >= start_times[start_time_index + 1] or (
                    got_ado and got_ego
                ):  # But if we have already passed the next start:
                    # If we have already passed the _next_ start time, we need to move on. so pad
                    # with nans, move our start-time, and set our flags to false
                    _pad()

                    start_time_index += 1
                    next_start = start_times[start_time_index]  # Move onto next start time
                    got_ego = False
                    got_ado = False
            else:
                if got_ego and got_ado:
                    # If we are past the last known start-time and we got both agents, we're done.
                    break

    _pad()
    if ado_agents:
        data.update(ado_agents)
    return data


def process_carla_objects(msgs, **kwargs):
    """Method for processing /carla/objects"""
    id_dict = kwargs["id_dict"]
    start_times = kwargs["start_times"]
    data = {
        "timestamp": [],
        "carla_objects log time": [],
        "ego_x": [],
        "ego_y": [],
        "ego_z": [],
        "ego_orientation_x": [],
        "ego_orientation_y": [],
        "ego_orientation_z": [],
        "ego_orientation_w": [],
        "ego_vx": [],
        "ego_vy": [],
        "ego_vz": [],
        "ego_wx": [],
        "ego_wy": [],
        "ego_wz": [],
        "ado_collision": [],
        "object_collision": [],
    }
    ado_agents = None
    # TODO: currently only looks for 1 autopilot (ado) -- handle more if necessary
    if "autopilot" in id_dict.keys():
        ado_agents = {
            "ado_x": [],
            "ado_y": [],
            "ado_z": [],
            "ado_orientation_x": [],
            "ado_orientation_y": [],
            "ado_orientation_z": [],
            "ado_orientation_w": [],
            "ado_vx": [],
            "ado_vy": [],
            "ado_vz": [],
            "ado_wx": [],
            "ado_wy": [],
            "ado_wz": [],
        }

    if start_times is not None:
        return extract_carla_starts(msgs, id_dict, start_times, data, ado_agents)
    else:
        return extract_aligned_carla_objects(msgs, id_dict, data=data, ado_agents=ado_agents)


def process_all_carla_objects(msgs, **kwargs):
    """
    Method for extracting pose info for ALL objects in /carla/objects
    e.g. for gaze association to objects as in DD/CD study
    """

    data = defaultdict(list)
    for m in msgs:
        for o in m.ros_msg.objects:
            data["carla_objects log time"].append(m.log_time_ns / 1e9)
            data["carla_objects_id"].append(o.id)
            data["carla_objects_pose_x"].append(o.pose.position.x)
            data["carla_objects_pose_y"].append(o.pose.position.y)
            data["carla_objects_pose_z"].append(o.pose.position.z)
            data["carla_objects_pose_orientation_x"].append(o.pose.orientation.x)
            data["carla_objects_pose_orientation_y"].append(o.pose.orientation.y)
            data["carla_objects_pose_orientation_z"].append(o.pose.orientation.z)
            data["carla_objects_pose_orientation_w"].append(o.pose.orientation.w)
            data["carla_objects_twist_linear_vx"].append(o.twist.linear.x)
            data["carla_objects_twist_linear_vy"].append(o.twist.linear.y)
            data["carla_objects_twist_linear_vz"].append(o.twist.linear.z)
            data["carla_objects_twist_angular_wx"].append(o.twist.angular.x)
            data["carla_objects_twist_angular_wy"].append(o.twist.angular.y)
            data["carla_objects_twist_angular_wz"].append(o.twist.angular.z)
            data["carla_objects_bbox_x"].append(o.shape.dimensions[0])
            data["carla_objects_bbox_y"].append(o.shape.dimensions[1])
            data["carla_objects_bbox_z"].append(o.shape.dimensions[2])

    return dict(data)


def process_hero_status(msgs, **kwargs):
    """Method for processing /carla/hero/vehicle_status"""

    data = defaultdict(list)
    for m in msgs:
        data["throttle"].append(m.ros_msg.control.throttle)
        data["brake"].append(m.ros_msg.control.brake)
        data["steering"].append(m.ros_msg.control.steer)
        data["orientation_x"].append(m.ros_msg.orientation.x)
        data["orientation_y"].append(m.ros_msg.orientation.y)
        data["orientation_z"].append(m.ros_msg.orientation.z)
        data["orientation_w"].append(m.ros_msg.orientation.w)
        data["carla_hero_status log time"].append(m.log_time_ns / 1e9)

    return dict(data)


def process_speedometer(msgs, **kwargs):
    """Method for processing /ic/speedometer"""

    data = defaultdict(list)
    for m in msgs:
        data["speedometer"].append(m.ros_msg.data)
        data["speedometer log time"].append(m.log_time_ns / 1e9)

    return dict(data)


def process_scenario_timing_info(msgs, **kwargs):
    """Method for processing /experiment/shared_autonomy/scenario_timing_info"""

    data = defaultdict(list)
    for m in msgs:
        data["condition"].append(m.ros_msg.condition)
        data["timing_value"].append(m.ros_msg.timing_value)
        data["scenario_timing log time"].append(m.log_time_ns / 1e9)

    return dict(data)


def process_start_scenario(msgs, **kwargs):
    """Method for processing /experiment/study/ctrl/start_scenario"""

    data = defaultdict(list)
    for m in msgs:
        data["start_scenario_name"].append(m.ros_msg.data)
        data["start_scenario log time"].append(m.log_time_ns / 1e9)

    return dict(data)


def process_stop_scenario(msgs, **kwargs):
    """Method for processing /experiment/study/ctrl/stop_scenario"""

    data = defaultdict(list)
    for m in msgs:
        data["stop_scenario_name"].append(m.ros_msg.data)
        data["stop_scenario log time"].append(m.log_time_ns / 1e9)

    return dict(data)


def process_telemetry(msgs, **kwargs):
    """Method for processing /telemetry/velocity"""

    data = defaultdict(list)
    for m in msgs:
        data["velocity"].append(m.ros_msg.data)
        data["velocity log time"].append(m.log_time_ns / 1e9)

    return dict(data)


def process_sound_name(msgs, **kwargs):
    data = defaultdict(list)
    for m in msgs:
        data["sound_name"].append(m.ros_msg.data.replace("standard", "").replace("_", " ").strip())
        data["sound_name log time"].append(m.log_time_ns / 1e9)
    return dict(data)


def process_soss_namedlocation(msgs, **kwargs):
    """Method for processing /soss/playatnamedlocation"""

    data = defaultdict(list)
    for m in msgs:
        data["sound_name"].append(m.ros_msg.sound.sound_name)
        data["sound_location"].append(m.ros_msg.location)
        data["sound_name log time"].append(m.log_time_ns / 1e9)

    return dict(data)


def process_soss_soundfinished(msgs, **kwargs):
    """Method for processing /soss/soundfinished"""

    data = defaultdict(list)
    for m in msgs:
        data["sound_name_finished"].append(m.ros_msg.sound.sound_name)
        data["sound_name_finished log time"].append(m.log_time_ns / 1e9)

    return dict(data)


def process_rosout(msgs, **kwargs):
    data = defaultdict(list)
    for m in msgs:
        if "RecordLap: ego passed the start gate" in m.ros_msg.msg:
            print(f"rosout msg, {m.ros_msg.msg}")
            data["condition"].append("start gate passed")
            data["timing_value"].append(m.ros_msg.stamp.sec + m.ros_msg.stamp.nanosec / 1e9)
            data["scenario_timing log time"].append(m.log_time_ns / 1e9)
        if "RecordLap: ego passed the end gate" in m.ros_msg.msg:
            data["condition"].append("finish gate passed")
            print(f"rosout msg, {m.ros_msg.msg}")
            data["timing_value"].append(m.ros_msg.stamp.sec + m.ros_msg.stamp.nanosec / 1e9)
            data["scenario_timing log time"].append(m.log_time_ns / 1e9)
        if "Scenario total time: " in m.ros_msg.msg:
            data["condition"].append("scenario ended")
            print(f"rosout msg, {m.ros_msg.msg}")
            data["timing_value"].append(m.ros_msg.stamp.sec + m.ros_msg.stamp.nanosec / 1e9)
            data["scenario_timing log time"].append(m.log_time_ns / 1e9)

    return dict(data)


def process_carla_chase_cam_info(msgs, **kwargs):
    data = defaultdict(list)
    for m in msgs:
        data["chase_cam_size_height"].append(m.ros_msg.height)
        data["chase_cam_size_width"].append(m.ros_msg.width)
        data["chase_cam log time"].append(m.log_time_ns / 1e9)

    return dict(data)


def process_carla_chase_image(msgs, **kwargs):
    data = defaultdict(list)
    for m in msgs:
        data["chase_cam_image_data"].append(m.ros_msg.data)
        data["chase_cam_image log time"].append(m.log_time_ns / 1e9)

    return dict(data)


def process_carla_spectator_front_cam_info(msgs, **kwargs):
    data = defaultdict(list)
    for m in msgs:
        data["spectator_cam_size_height"].append(m.ros_msg.height)
        data["spectator_cam_size_width"].append(m.ros_msg.width)
        data["spectator_cam log time"].append(m.log_time_ns / 1e9)

    return dict(data)


def process_carla_spectator_front_image(msgs, **kwargs):
    data = defaultdict(list)
    for m in msgs:
        data["spectator_cam_image_data"].append(m.ros_msg.data)
        data["spectator_cam_image log time"].append(m.log_time_ns / 1e9)
    return dict(data)


def process_gaze_data(msgs, **kwargs):
    """Method for processing Tobii Spark gaze tracking data: /experiment/tobii/frame"""

    data = defaultdict(list)

    for m in msgs:
        data["tobii log time"].append(m.log_time_ns / 1e9)
        data["tobii_left_eye_gaze_origin_in_trackbox_x"].append(
            m.ros_msg.left_eye.gaze_origin_in_trackbox_coordinate_system.x
        )
        data["tobii_left_eye_gaze_origin_in_trackbox_y"].append(
            m.ros_msg.left_eye.gaze_origin_in_trackbox_coordinate_system.y
        )
        data["tobii_left_eye_gaze_origin_in_trackbox_z"].append(
            m.ros_msg.left_eye.gaze_origin_in_trackbox_coordinate_system.z
        )
        data["tobii_left_eye_gaze_origin_in_user_x"].append(
            m.ros_msg.left_eye.gaze_origin_in_user_coordinate_system.x
        )
        data["tobii_left_eye_gaze_origin_in_user_y"].append(
            m.ros_msg.left_eye.gaze_origin_in_user_coordinate_system.y
        )
        data["tobii_left_eye_gaze_origin_in_user_z"].append(
            m.ros_msg.left_eye.gaze_origin_in_user_coordinate_system.z
        )
        data["tobii_left_eye_gaze_origin_validity"].append(m.ros_msg.left_eye.gaze_origin_validity)
        data["tobii_left_eye_gaze_pt_in_user_x"].append(
            m.ros_msg.left_eye.gaze_point_in_user_coordinate_system.x
        )
        data["tobii_left_eye_gaze_pt_in_user_y"].append(
            m.ros_msg.left_eye.gaze_point_in_user_coordinate_system.y
        )
        data["tobii_left_eye_gaze_pt_in_user_z"].append(
            m.ros_msg.left_eye.gaze_point_in_user_coordinate_system.z
        )
        data["tobii_left_eye_gaze_pt_in_display_x"].append(
            m.ros_msg.left_eye.gaze_point_on_display_area.x
        )
        data["tobii_left_eye_gaze_pt_in_display_y"].append(
            m.ros_msg.left_eye.gaze_point_on_display_area.y
        )
        data["tobii_left_eye_gaze_pt_validity"].append(m.ros_msg.left_eye.gaze_point_validity)
        data["tobii_left_eye_pupil_diameter"].append(m.ros_msg.left_eye.pupil_diameter)
        data["tobii_left_eye_pupil_validity"].append(m.ros_msg.left_eye.pupil_validity)
        data["tobii_right_eye_gaze_origin_in_trackbox_x"].append(
            m.ros_msg.right_eye.gaze_origin_in_trackbox_coordinate_system.x
        )
        data["tobii_right_eye_gaze_origin_in_trackbox_y"].append(
            m.ros_msg.right_eye.gaze_origin_in_trackbox_coordinate_system.y
        )
        data["tobii_right_eye_gaze_origin_in_trackbox_z"].append(
            m.ros_msg.right_eye.gaze_origin_in_trackbox_coordinate_system.z
        )
        data["tobii_right_eye_gaze_origin_in_user_x"].append(
            m.ros_msg.right_eye.gaze_origin_in_user_coordinate_system.x
        )
        data["tobii_right_eye_gaze_origin_in_user_y"].append(
            m.ros_msg.right_eye.gaze_origin_in_user_coordinate_system.y
        )
        data["tobii_right_eye_gaze_origin_in_user_z"].append(
            m.ros_msg.right_eye.gaze_origin_in_user_coordinate_system.z
        )
        data["tobii_right_eye_gaze_origin_validity"].append(
            m.ros_msg.right_eye.gaze_origin_validity
        )
        data["tobii_right_eye_gaze_pt_in_user_x"].append(
            m.ros_msg.right_eye.gaze_point_in_user_coordinate_system.x
        )
        data["tobii_right_eye_gaze_pt_in_user_y"].append(
            m.ros_msg.right_eye.gaze_point_in_user_coordinate_system.y
        )
        data["tobii_right_eye_gaze_pt_in_user_z"].append(
            m.ros_msg.right_eye.gaze_point_in_user_coordinate_system.z
        )
        data["tobii_right_eye_gaze_pt_in_display_x"].append(
            m.ros_msg.right_eye.gaze_point_on_display_area.x
        )
        data["tobii_right_eye_gaze_pt_in_display_y"].append(
            m.ros_msg.right_eye.gaze_point_on_display_area.y
        )
        data["tobii_right_eye_gaze_pt_validity"].append(m.ros_msg.right_eye.gaze_point_validity)
        data["tobii_right_eye_pupil_diameter"].append(m.ros_msg.right_eye.pupil_diameter)
        data["tobii_right_eye_pupil_validity"].append(m.ros_msg.right_eye.pupil_validity)
        data["tobii_device_time_stamp"].append(m.ros_msg.device_time_stamp)
        data["tobii_system_time_stamp"].append(m.ros_msg.system_time_stamp)

    return dict(data)


def process_tobii_calib_data(msgs, **kwargs):
    """
    Method for processing tobii calibration data
    """

    data = defaultdict(list)

    for m in msgs:
        data["tobii_calib_device_time_stamp"].append(m.ros_msg.device_time_stamp)
        data["tobii_calib_system_time_stamp"].append(m.ros_msg.system_time_stamp)
        data["tobii_calib_mode"].append(m.ros_msg.mode)
        data["tobii_calib_shown_target_point_x"].append(m.ros_msg.shown_target_point[0])
        data["tobii_calib_shown_target_point_y"].append(m.ros_msg.shown_target_point[1])
        data["tobii_calib_shown_target_idx"].append(m.ros_msg.shown_target_idx)
        data["tobii_calib_animation_step"].append(m.ros_msg.animation_step)
        data["tobii_calib_num_calib"].append(m.ros_msg.num_calib)
        data["tobii_calib_num_test"].append(m.ros_msg.num_test)
        data["tobii_calib log time"].append(m.log_time_ns / 1e9)

    return dict(data)


def process_tobii_test_result(msgs, **kwargs):
    """
    Method for processing tobii test results, saves test json in kwargs["output_dir"]
    """
    assert kwargs["output_dir"] is not None and Path(kwargs["output_dir"]).exists()
    test_results = []
    for m in msgs:
        test_result = {}
        test_result["log time"] = m.log_time_ns / 1e9
        test_result["rmses"] = m.ros_msg.rmses
        test_result["mean_rmse"] = m.ros_msg.mean_rmse
        test_result["num_calib"] = m.ros_msg.num_calib
        test_result["num_test"] = m.ros_msg.num_test
        test_results.append(test_result)
    test_result_path = Path(kwargs["output_dir"]) / "tobii_test_results.json"
    with open(test_result_path, "w") as json_file:
        json.dump(test_results, json_file, indent=4)
    return None


def process_audio_data(msgs, **kwargs):
    """
    Method for processing audio_stamped data, saves an mp3 in kwargs["output_dir"]
    """
    # https://github.com/ros-drivers/audio_common/issues/1
    mic_name = msgs[0].channel.topic.split("/")[1]
    if not mic_name.endswith("audio"):
        mic_name += "_audio"
    assert kwargs["output_dir"] is not None and Path(kwargs["output_dir"]).exists()
    mp3_path = Path(kwargs["output_dir"]) / (mic_name + ".mp3")
    mp3_file = open(mp3_path, "wb")
    frames = defaultdict(list)
    byte_count = 0
    for m in msgs:
        if m.channel.topic.split("/")[-1] == "audio_stamped":
            mp3_file.write(m.ros_msg.audio.data)
            frames[mic_name + " log time"].append(m.log_time_ns / 1e9)
            frames[mic_name + "_byte_count"].append(byte_count)
            byte_count += len(m.ros_msg.audio.data)
    mp3_file.close()

    return dict(frames)


def create_process_simple_data(test_name):
    """
    Factory function for simple data topics
    """

    def process_simple_data(msgs, **kwargs):
        """
        Method for processing string data from simple topics such as stationary tests
        e.g.
            /experiment/gazetrackingtest/event_log
            /ic/speedometer
        """
        data = defaultdict(list)
        for m in msgs:
            data[f"{test_name}_data"].append(m.ros_msg.data)
            data[f"{test_name} log time"].append(m.log_time_ns / 1e9)
        return dict(data)

    return process_simple_data
