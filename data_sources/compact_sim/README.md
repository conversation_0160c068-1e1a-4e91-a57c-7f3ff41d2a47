# README: Multi-Processing Enhancements for Data Loading and mcap Parsing

## Overview
This update integrates multi-processing into the `dataloading.py` and `mcap_parser_multi_processing.py` scripts to improve the efficiency of handling large mcap files. By leveraging multiple CPU cores, we ensure faster execution and better scalability, especially for CPU-intensive operations like parsing and transforming mcap data into pandas DataFrames.

---

## Key Enhancements

### 1. Multi-Processing in `dataloading.py`
- **What Changed:**
  - Replaced sequential processing(reading mcaps and pid's) of mcaps files with a multi-processing implementation using Python's `ProcessPoolExecutor`.
  - Each mcap file is processed in parallel, and the results are aggregated into the final data dictionary.
  - Independent processes ensure optimal CPU utilization without interference.

- **How It Works:**
  - The outer loop iterating through mcap files is parallelized.
  - Each file is handled by a separate process, which:
    1. Checks for a cached version of the file.
    2. Parses the mcap file into a pandas DataFrame using `mcap_to_dataframe`.
    3. Saves the DataFrame to a cache (`.parquet` format) for future use.

- **Why Multi-Processing:**
  - True parallelism for CPU-bound tasks (e.g., mcap parsing and data transformation).
  - Avoids the limitations of Python's Global Interpreter Lock (GIL).

---

### 2. Multi-Processing in `mcap_parser.py`
- **What Changed:**
  - The `mcap_to_dataframe` method uses multi-processing to process topics within a single mcap file concurrently.
  - Topics are divided among worker processes using `ProcessPoolExecutor`, where each process handles a subset of topics and merges the results.

- **How It Works:**
  - For each topic in the mcap file:
    1. Messages are read and processed using `process_topic_parallel`.
    2. Each topic's processing is delegated to a worker process.
    3. Results are merged into a unified DataFrame using `merge_asof` for temporal alignment.

- **Why Multi-Processing:**
  - Speeds up topic-level processing by parallelizing across multiple cores.
  - Efficient for large files with many topics or complex processing logic.

---
