import collections
import traceback
from concurrent.futures import <PERSON><PERSON><PERSON><PERSON>xecutor
from hashlib import sha512
from pathlib import Path
from typing import Dict

import numpy as np
import pandas as pd

from data_sources.compact_sim.mcap_parser import ProcessCompactSimMcap
from data_sources.thunderhill.data_readers import read_map_csv

DATA_DICT_KEY_EPISODES = "episodes"
DATA_DICT_KEY_MAP = "map"


def process_file(
    input_file_full_path,
    process_mcap,
    cache_folder,
    additional_topics_dict,
    verbose,
    max_message_count,
    override,
    use_multiprocessing,
):
    """
    Process a single MCAP file, including caching logic.

    args:
        input_file_full_path (Path): Path to the input MCAP file to process.
        process_mcap (ProcessCompactSimMcap): Instance of ProcessCompactSimMcap for MCAP processing.
        cache_folder (Path): Directory path where processed data will be cached.
        additional_topics_dict (Dict): Dictionary containing additional topics to process and their
            corresponding processing methods.
        verbose (bool): If True, prints processing progress information.
        max_message_count (int): Maximum number of messages to process from the MCAP file.
        override (bool): If True, forces reprocessing even if cached data exists.
        use_multiprocessing (bool): Whether to use parallel processing for file processing. Defaults to True.

    returns:
        tuple: A tuple containing:
            - input_filename (str): The stem (filename without extension) of the input file
            - panda_frame (pd.DataFrame): Processed data as a pandas DataFrame
    """
    # Hash file name for cache
    mcap_file_name = input_file_full_path.name
    input_filename = input_file_full_path.stem
    output_filename = sha512(mcap_file_name.encode()).hexdigest() + ".parquet"
    output_pathname = cache_folder / output_filename

    if output_pathname.exists() and not override:
        print(f"Cache found for {input_file_full_path}. Loading...")
        panda_frame = pd.read_parquet(output_pathname)
    else:
        panda_frame = process_mcap.mcap_to_dataframe(
            log_path=input_file_full_path,
            additional_topics_dict=additional_topics_dict,
            reference_topic="/carla/objects",
            verbose=verbose,
            max_message_count=max_message_count,
            use_multiprocessing=use_multiprocessing,
        )
        if panda_frame is None:
            raise RuntimeError(f"Failed to process {input_file_full_path}")
        if output_pathname:
            panda_frame.to_parquet(output_pathname, engine="pyarrow", compression="snappy")
    return input_filename, panda_frame


def process_file_wrapper(args):
    """
    Wrapper function to unpack arguments and call `process_file`.
    """
    return process_file(*args)


def load_and_cache_compactsim_data(
    args: Dict,
    override: bool = False,
    additional_topics_dict: Dict = None,
    verbose: bool = False,
    per_subject_mcap_dict: Dict = None,
    max_message_count=np.inf,
    include_map: bool = True,
    use_multiprocessing: bool = True,
    num_workers: int = 4,
) -> Dict:
    """Load mcap data, parse, and cache it. Load the map data. Return them as a single data dictionary.

    Params
    ----------
    args : Dict
        Arguments from the commandline for scenario loading. Should include track_map_csv, compactsim_cache_folder, mcap_input_file or mcap_input_folder (not implemented yet)
    override : bool
        Sets whether to override the cache.
    additional_topics_dict (Dict):
        Dict containing two keys: 'topics' and 'topic_process_methods'.
        additional_topics_dict['topics'] is List of additional topics to append to default list to read from log.
        additional_topics_dict['topic_process_methods'] is a Dict containing a mapping between each additional topic in additional_topics_dict['topics']
        and the corresponding process method to be used
    per_subject_mcap_dict (Dict):
        Dict containing the mapping between subjects and the list of mcaps associated with each subject. Should not be None,
        if args['is_multiple_mcaps_per_subject'] is True.
    verbose (bool):
        If true, print number of messages interpreted every 10k.
    max_message_count (integer):
        A maximum size message count, to allow limit parsing of each file, for debugging purposes.
    include_map (bool):
        Should we include the map in the data_dict that is returned? Defaults to True.
    use_multiprocessing (bool):
        Whether to use parallel processing for file processing. Defaults to True.
    Returns
    num_workers (integer): Number of workers.
    -------
    Dict
        A data dictionary with: 'map' -> a loaded map pandas object. 'episodes' -> a map from track name to a pandas object representing the track/episode.

    """
    # whether to override cache files
    data_dict = {}  # output data object

    if include_map:
        # Read track map
        map_csv_filename = str(Path(args["track_map_csv"]).expanduser().resolve())
        track_map = read_map_csv(map_csv_filename)
        data_dict[DATA_DICT_KEY_MAP] = track_map

    # Read track episodes
    process_mcap = ProcessCompactSimMcap()

    data_dict[DATA_DICT_KEY_EPISODES] = {}
    cache_folder = Path(args["compactsim_cache_folder"]).expanduser().resolve()

    if args["mcap_input_file"]:
        input_files = [Path(args["mcap_input_file"]).expanduser().resolve()]
    elif args["mcap_input_folder"]:
        input_files = sorted(
            Path(args["mcap_input_folder"]).expanduser().resolve().rglob("*.mcap")
        )
    else:
        raise "Either mcap_input_file or mcap_input_folder should be populated."

    if args["is_multiple_mcaps_per_subject"]:
        assert per_subject_mcap_dict is not None
        organized_input_files = collections.defaultdict(list)
        # organize mcaps for each subject in chronological order
        for pid, pid_mcap_list in per_subject_mcap_dict.items():
            for pid_mcap in pid_mcap_list:
                for input_file in input_files:
                    if input_file.stem == pid_mcap:
                        organized_input_files[pid].append(input_file)
    else:
        organized_input_files = {"DUMMY_ID": input_files}

    cache_folder.mkdir(parents=True, exist_ok=True)
    # Extract the data files that are present in the specified data paths.
    # Generally the organization is according to pid and mcaps associated with the pid.
    # By default if args['is_multiple_mcaps_per_subject'] is False, then a dummy pid is given and the entire input_files list
    # is associated with the dummy pid

    # Multi-processing for file processing
    all_input_files = [
        (
            input_file_full_path,
            process_mcap,
            cache_folder,
            additional_topics_dict,
            verbose,
            max_message_count,
            override,
        )
        for pid_files in organized_input_files.values()
        for input_file_full_path in pid_files
    ]

    all_results = []
    for pid, pid_files in organized_input_files.items():
        print(f"Processing PID: {pid}")
        start_idx = 0
        if args["is_multiple_mcaps_per_subject"]:
            print(f"RESETTING ID DICT FOR {pid}")
            process_mcap.reset_id()
            # Process the first file single-threaded
            print(f"Processing first file single-threaded: {pid_files[0]}")
            try:
                input_filename, panda_frame = process_file(
                    pid_files[start_idx],
                    process_mcap,
                    cache_folder,
                    additional_topics_dict,
                    verbose,
                    max_message_count,
                    override,
                    use_multiprocessing=use_multiprocessing,
                )
                all_results.append((input_filename, panda_frame))
                start_idx = 1
            except Exception as e:
                error_traceback = traceback.format_exc()
                print(f"Error processing first file for PID {pid}: {e}\n{error_traceback}")
                continue

        # Process remaining files with multiprocessing
        all_input_files = [
            (
                input_file_full_path,
                process_mcap,
                cache_folder,
                additional_topics_dict,
                verbose,
                max_message_count,
                override,
                use_multiprocessing,
            )
            for input_file_full_path in pid_files[start_idx:]
        ]

        if use_multiprocessing:
            print("Starting parallel file processing...")
            with ProcessPoolExecutor(max_workers=num_workers) as executor:
                try:
                    results = executor.map(process_file_wrapper, all_input_files)
                    all_results.extend(results)
                except Exception as e:
                    error_traceback = traceback.format_exc()
                    print(
                        f"Error processing remaining files for PID {pid}: {e}\n{error_traceback}"
                    )
        else:
            print("Starting with single threaded processing...")
            # Process files sequentially if multiprocessing is disabled
            for args in all_input_files:
                try:
                    result = process_file_wrapper(args)
                    all_results.append(result)
                except Exception as e:
                    error_traceback = traceback.format_exc()
                    print(f"Error processing file: {e}\n{error_traceback}")
    # Update data dictionary with results
    for input_filename, panda_frame in all_results:
        data_dict[DATA_DICT_KEY_EPISODES][input_filename] = panda_frame

    return data_dict


def read_all_track_maps(directory_path):
    """
    Reads all `.csv` files in a directory and constructs a dictionary mapping
    track names (derived from file names) to their corresponding map data.

    Args:
        directory_path (str): Path to the directory containing track map `.csv` files.

    Returns:
        dict: A dictionary where keys are track names (file names without extensions)
              and values are the track map data read from the files.
    """
    # Dictionary to hold track name to track data mappings
    track_map_dict = {}

    # Resolve the directory path
    directory = Path(directory_path).expanduser().resolve()

    # Check if the directory exists
    if not directory.is_dir():
        raise FileNotFoundError(f"The directory {directory} does not exist or is not a directory.")

    # Iterate through `.csv` files in the directory
    for csv_file in directory.glob("*.csv"):
        # Extract the track name (file name without extension)
        track_name = csv_file.stem

        # Read the track map using the existing `read_map_csv` function
        track_data = read_map_csv(str(csv_file))

        # Store the track data in the dictionary
        track_map_dict[track_name] = track_data

    return track_map_dict
