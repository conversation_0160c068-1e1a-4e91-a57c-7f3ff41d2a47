import argparse
import glob
import os
from typing import Dict

import openai
import pandas as pd
import tqdm


def parse_args() -> argparse.Namespace:
    """
    Parse command line arguments for running GPT-3 ICL Classification.

    Returns:
        argparse.Namespace: Parsed command line arguments.
    """
    parser = argparse.ArgumentParser(description="Run GPT-3 ICL Classification")
    parser.add_argument(
        "--input-folder",
        type=str,
        default="~/data/24-D-05/csvs_of_concurrent_feedback/",
        help="Input folder",
    )
    parser.add_argument(
        "--output-folder",
        type=str,
        default="~/data/24-D-05/csvs_of_concurrent_feedback_processed",
        help="Output folder",
    )
    parser.add_argument("--api-key", type=str, default="Fill out key")
    parser.add_argument("--examples-file", type=str, default=None, help="CSV with taxonomy")
    return parser.parse_args()


default_examples = {
    "BRAKE": ["Hit the brakes!", "Slow down"],
    "ACCELERATE": ["Speed up", "Go faster"],
    "STAY_LEFT": ["Stay on the left", "Move to the left"],
    "STAY_RIGHT": ["Stay on the right", "Move to the right"],
    "MISSING_MODALITIES": [
        "Go towards the cone",
        "Eyes up",
        "Look over there",
        "Aim at the tower",
    ],
    "UNRELATED": ["Perfect", "How are you doing", "Nice weather today", "I am hungry"],
}


def classify_sentence(examples: Dict, query: str) -> str:
    """
    Classifies the given sentence according to the type of coach comment it is.

    Args:
        examples (dict): A dictionary containing examples for coach comment types.
        query (str): The sentence to be classified.

    Returns:
        str: The category of the coach comment.

    Example:
        examples = {
            "Positive": "Great job!",
            "Negative": "You need to improve.",
            "Neutral": "Keep up the good work."
        }
        query = "Well done!"
        result = classify_sentence(examples, query)
        print(result)  # Output: "Positive"
    """
    cmd = "Can you please classify the following text according to the type of coach comment it is? Return just the category. examples for coach comment types are: "
    model = "gpt-3.5-turbo"
    instruction_str = cmd
    for key in examples:
        instruction_str += f"{key}: {str(examples[key])}\n"
    instruction_str += f"Query: {query}"

    response = openai.chat.completions.create(
        model=model,
        temperature=0.9,
        top_p=0.3,
        messages=[{"role": "user", "content": instruction_str}],
    )
    result = response.choices[0].message.content
    return result


def read_examples_csv(examples_file: str) -> Dict:
    """
    Reads the examples from a CSV file.

    Args:
        examples_file (str): The path to the CSV file containing examples.

    Returns:
        dict: A dictionary containing examples for coach comment types.
    """
    examples = {}
    df = pd.read_csv(examples_file)
    for index, row in df.iterrows():
        cat_examples = df.iloc[index][1:]
        cat_examples = [x for x in cat_examples if str(x) != "nan"]
        examples[row["Category"]] = cat_examples
        print(f"Category: {row['Category']}, Examples: {cat_examples}")
    return examples


if __name__ == "__main__":
    # Test GPT-3 ICL Classification
    args = parse_args()
    openai.api_key = args.api_key
    print(f"API Key: {args.api_key}")
    if args.examples_file:
        examples = read_examples_csv(args.examples_file)
    else:
        examples = default_examples
    input_folder = args.input_folder
    input_folder = os.path.expanduser(os.path.expandvars(input_folder))
    for filename in tqdm.tqdm(glob.glob(os.path.join(input_folder, "*.csv"))):
        output_folder = args.output_folder
        output_folder = os.path.expanduser(os.path.expandvars(output_folder))
        os.makedirs(output_folder, exist_ok=True)
        trial = pd.read_csv(filename)
        queries = ["go go go!", "Stick to the right", "Slow down"]
        categories = []
        for i, query in enumerate(trial["0"]):
            result = classify_sentence(examples, query)
            print(f"Query: {query},Category: {result}")
            categories.append(result)
        trial["category"] = categories
        output_filename = os.path.join(output_folder, os.path.basename(filename))
        trial.to_csv(output_filename, index=False)
