#!/usr/bin/env python3
import argparse
import subprocess
from pathlib import Path


def parse_arguments():
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument(
        "--log-dir",
        type=str,
        help="Folder containing the logs to process and upload",
        default="./",
    )
    parser.add_argument(
        "--mcap-tool-path",
        type=str,
        help="Path to mcap CLI executable",
        default="~/mcap",
    )
    parser.add_argument(
        "--upload",
        action="store_true",
        help="flag to upload",
    )
    parser.add_argument(
        "--s3-bucket",
        type=str,
        help="s3 bucket to which to upload",
    )
    return parser.parse_args()


def _convert_to_mcap(mcap_tool_path, input_log_path, destination_path):
    return subprocess.run(
        [mcap_tool_path, "convert", input_log_path, destination_path], check=True
    )


def _upload_to_s3(local_path, s3_path):
    return subprocess.run(["aws", "s3", "sync", local_path, s3_path], check=True)


def main():
    args = parse_arguments()
    # Get list of logs matching desired structure
    input_paths = sorted(Path(args.log_dir).expanduser().resolve().rglob("P*.db3"))
    if not input_paths:
        print(f"No log files found in the indicated folder: {args.log_dir}")
        return
    # Create list of mcap log paths based on input logs
    output_paths = [p.parent / f"{p.stem}.mcap" for p in input_paths]
    # Convert logs
    mcap_tool_path = Path(args.mcap_tool_path).expanduser().resolve()
    if not Path(mcap_tool_path).is_file():
        print(f"MCAP CLI tool not found at {mcap_tool_path}. Exiting.")
        return

    for input, output in zip(input_paths, output_paths):
        _convert_to_mcap(mcap_tool_path, input, output)

    # Upload all directories to s3
    if not args.upload:
        print("Upload flag not set, skipping upload.")
        return
    if not args.s3_bucket:
        print("No s3 url provided so cannot upload")
        return
    if not args.s3_bucket.endswith("/"):
        args.s3_bucket += "/"
    for o in output_paths:
        parent = f"{o.parent}/"
        s3_path = args.s3_bucket + o.parent.name + "/"
        _upload_to_s3(parent, s3_path)


if __name__ == "__main__":
    main()
