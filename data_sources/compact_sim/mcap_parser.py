#!/usr/bin/env python3
import multiprocessing as mp
import os
import traceback

from mcap_ros2.reader import read_ros2_messages

from data_sources.compact_sim.mcap_process_methods import *


class ProcessCompactSimMcap:
    """
    Class for handling data from mcap logs collected with the compact simulators.
    Contains methods for reading the data from the log, creating, saving, loading, and merging pandas dataframes.
    """

    def __init__(self):
        self.default_topics_to_read = [
            "/carla/objects",
            "/ic/speedometer",
            "/experiment/study/ctrl/start_scenario",
            "/experiment/study/ctrl/stop_scenario",
            "/carla/actor_list",
            "/carla/hero/vehicle_status",
            "/carla/hero/vehicle_info",
            "/experiment/shared_autonomy/scenario_timing_info",
        ]

        self.default_topic_process_methods_dict = {
            "/carla/objects": process_carla_objects,
            "/ic/speedometer": process_speedometer,
            "/experiment/study/ctrl/start_scenario": process_start_scenario,
            "/experiment/study/ctrl/stop_scenario": process_stop_scenario,
            "/carla/actor_list": None,
            "/carla/hero/vehicle_status": process_hero_status,
            "/carla/hero/vehicle_info": None,
            "/experiment/shared_autonomy/scenario_timing_info": process_scenario_timing_info,
        }

        self.merged_df = None
        self.reference_col = "carla_objects log time"
        self.id_dict = {}
        self.first_timestep = 0

    def reset_id(self):
        """
        Reset function to reset id dict. To be used when there are multiple mcaps associated with a subject
        """
        self.id_dict = {}
        self.first_timestep = 0

    @staticmethod
    def process_topic_parallel(input_vars):
        """
        Worker function to process a single topic in parallel
        """
        (
            log_path,
            topic,
            id_dict,
            first_timestep,
            max_message_count,
            topic_process_methods_dict,
            start_times,
            wait_for_ado,
            verbose,
        ) = input_vars
        process_compact_sim_topic = ProcessCompactSimMcap()
        process_compact_sim_topic.id_dict = id_dict
        process_compact_sim_topic.first_timestep = first_timestep

        # Reading msgs from mcap
        msgs = process_compact_sim_topic.read_from_mcap(
            log_path=log_path,
            topics=[topic],
            max_message_count=max_message_count,
            verbose=verbose,
        )

        # Extracted features for topic
        extracted_features = process_compact_sim_topic.extract_features(
            msgs,
            topic_process_methods_dict=topic_process_methods_dict,
            start_times=start_times,
            wait_for_ado=wait_for_ado,
        )
        return extracted_features

    def mcap_to_dataframe(
        self,
        log_path,
        additional_topics_dict=None,
        reference_topic="/carla/objects",
        max_message_count=np.inf,
        initial_only=False,
        wait_for_ado=False,
        verbose=False,
        n_workers=None,
        use_multiprocessing=True,
    ):
        """
        Convert MCAP ROS log into pandas dataframe.
        Inputs:
            log_path (string): Path to the mcap log to read in.
            additional_topics_dict (Dict): Dict containing two keys: 'topics' and 'topic_process_methods'.
                additional_topics_dict['topics'] is List of additional topics to append to default list to read from log.
                additional_topics_dict['topic_process_methods'] is a Dict containing a mapping between each additional topic in additional_topics_dict['topics']
                and the corresponding process method to be used
            reference_topic (string): ROS topic to use as the reference when merging topic dataframes together.
            max_message_count (integer): A maximum size message count, to allow limit parsing of each file, for debugging purposes.
            initial_only (bool): If true, only record the first timestep of each scenario
            wait_for_ado (bool): If true, ignore all data before the ado materializes
            verbose (bool): If true, print number of messages interpreted every 10k.
            n_workers (int): Number of worker processes to use for parallel processing.
            use_multiprocessing (bool): Whether to use parallel processing for file processing. Defaults to True.
        Output:
            pandas dataframe containing features from ROS topics, synchronized to the reference topic log time.
        """
        if not os.path.isfile(log_path):
            print(f"File not found at {log_path}. Exiting.")
            return

        # Clear merged_df to clear RAM before starting to read in another log.
        self.merged_df = None

        topics = self.default_topics_to_read
        topic_process_methods_dict = self.default_topic_process_methods_dict
        if additional_topics_dict is not None:
            # TODO (Deepak.gopinath) Fail gracefully. remove asserts
            assert len(additional_topics_dict["topics"]) == len(
                additional_topics_dict["topic_process_methods"]
            )
            assert [
                t in additional_topics_dict["topic_process_methods"]
                for t in additional_topics_dict["topics"]
            ], "Process method corresponding to additional topic is missing"
            topics = topics + additional_topics_dict["topics"]

            # update process methods dict
            for (
                additional_topic,
                additional_topic_process_method,
            ) in additional_topics_dict["topic_process_methods"].items():
                topic_process_methods_dict[additional_topic] = additional_topic_process_method

        if reference_topic not in topics:
            print(
                f'The selected reference topic "{reference_topic}" is not in the topics to read. Adding the reference topic to the list of topics to read.'
            )
            topics.append(reference_topic)

        msg_dicts = {}
        if initial_only:
            topic = "/experiment/shared_autonomy/scenario_timing_info"
            msgs = self.read_from_mcap(log_path, [topic], max_message_count, verbose=verbose)
            start_times = process_carla_start_times(msgs[topic])
            # Since we've read in the scenario_timing_info messages, process them
            # while we have them to avoid reading them again. Then remove the topic
            # from the list of topics to read.
            if topic in topics:
                msg_dicts.update(
                    self.extract_features(
                        msgs,
                        topic_process_methods_dict=topic_process_methods_dict,
                        start_times=start_times,
                        wait_for_ado=wait_for_ado,
                    )
                )
                topics.remove(topic)
            msgs = None
        else:
            start_times = None

        # Sort topics alphabetically and check that if "/carla/actor_list" is to be
        # read that it is the first topic in the list. This is important as the
        # information from it is used to process other messages.
        topics.sort()
        if "/carla/actor_list" in topics and topics[0] != "/carla/actor_list":
            topics.remove("/carla/actor_list")
            topics.insert(0, "/carla/actor_list")

        special_topics = ["/carla/actor_list", "/carla/hero/vehicle_info"]
        special_topics_present = [t for t in special_topics if t in topics]

        # Process special topics first to get id_dict
        if special_topics_present:
            for special_topic in special_topics_present:
                if not self.id_dict and special_topic == "/carla/actor_list":
                    msgs = self.read_from_mcap(
                        log_path, [special_topic], max_message_count, verbose=verbose
                    )
                    self.id_dict, self.first_timestep = process_carla_actor_ids(
                        msgs, extract_ego_from_actor_list=True
                    )
                    topics.remove(special_topic)

                elif not self.id_dict and special_topic == "/carla/hero/vehicle_info":
                    msgs = self.read_from_mcap(
                        log_path, [special_topic], max_message_count, verbose=verbose
                    )
                    print(f"msg for vehicle info:{msgs}")
                    self.id_dict, self.first_timestep = process_carla_actor_ids(
                        msgs, extract_ego_from_actor_list=False
                    )
                    topics.remove(special_topic)

        if not self.id_dict:
            print("Error: No carla actor list or hero vehicle info found. id_dict not populated.")
            raise ValueError
        # Choose between multiprocessing and single-threaded processing
        if use_multiprocessing:
            # Setup parallel processing
            if n_workers is None:
                n_workers = max(1, mp.cpu_count() - 1)  # Leave one CPU free

            # Prepare arguments for parallel processing
            multiprocess_input = [
                (
                    log_path,
                    topic,
                    self.id_dict,
                    self.first_timestep,
                    max_message_count,
                    topic_process_methods_dict,
                    start_times,
                    wait_for_ado,
                    verbose,
                )
                for topic in topics
            ]

            # Process topics in parallel
            with mp.Pool(processes=n_workers) as pool:
                for msg in pool.imap_unordered(self.process_topic_parallel, multiprocess_input):
                    msg_dicts.update(msg)
        else:
            # Single-threaded processing
            for topic in topics:
                # Reading msgs from mcap
                msgs = self.read_from_mcap(
                    log_path=log_path,
                    topics=[topic],
                    max_message_count=max_message_count,
                    verbose=verbose,
                )
                # Extract features for topic
                msg_dicts.update(
                    self.extract_features(
                        msgs,
                        topic_process_methods_dict=topic_process_methods_dict,
                        start_times=start_times,
                        wait_for_ado=wait_for_ado,
                    )
                )

        if not list(msg_dicts[reference_topic].values()):
            print(
                "No messages found for reference topic. Please make sure the log contains messages for that topic."
            )
            return None

        reference_df = pd.DataFrame.from_dict(msg_dicts[reference_topic])
        merged_dfs = reference_df
        self.reference_col = [i for i in msg_dicts[reference_topic].keys() if "log time" in i][0]

        # Use merge_asof to find the nearest (from the past) time match using the reference topic
        # log time as the reference. This means other dataframes will be down/up sampled to match
        # the number of rows as the reference topic.
        for topic in msg_dicts.keys():
            if topic == reference_topic:
                continue
            elif not msg_dicts[topic]:
                continue
            merge_col = [i for i in msg_dicts[topic].keys() if "log time" in i][0]

            merged_dfs = pd.merge_asof(
                merged_dfs,
                pd.DataFrame.from_dict(msg_dicts[topic]),
                left_on=self.reference_col,
                right_on=merge_col,
                # Backward direction makes sure that the rows in carla objects would be aligned
                # with the nearest data point in topic that has already occurred.
                direction="backward",
            )

        # merge_asof can fill in nans if the right_on column doesn't have a value less than or equal to the left_on
        # (usually happens right at the beginning). In this case just backfill (use the next valid value) to fill in the nans.
        # Do this column wise.
        for key in merged_dfs.keys():
            # check if nans are present in columns
            nan_series_k = pd.isna(merged_dfs[key].values)
            if nan_series_k.any():
                # if nans exists, get where they exist
                nan_index = np.argwhere(nan_series_k == True)
                # if there is only 1 nan in the column and that nan is the first row, do backfill.
                # usually this is the case for any type of continuous signal.
                # In the unusual case of maybe having more than 1 nans (maybe first few rows) for a continuous
                # signal we will have to deal with it (so far this hasn't happened in the datasets we have processed).
                # generally ros msgs corresponding to sparse events have nans for longer blocks and we don't want to backfill them
                if nan_index.shape[0] == 1 and int(nan_index[0][0]) == 0:
                    merged_dfs[key] = merged_dfs[key].bfill()

        # TODO (deepak.gopinath) consider alternate approach in which the channels to be backfilled are specified by the user,
        # However this assumes that the user already knows the channels present in the mcaps.

        self.merged_df = merged_dfs
        return merged_dfs

    @staticmethod
    def read_from_mcap(log_path, topics, max_message_count, verbose=False):
        """
        Read mcap messages into dict
        Inputs:
            log_path (string): Path to the mcap log to read.
            topics (list of strings): List of topics to read from log.
            verbose (bool, optional): Whether to log that we're still reading messages every 10k reads. Defaults to False
        Output:
            Dictionary of messages corresponding to input topics.
        """
        msgs = defaultdict(list)  # dictionary to hold the messages
        print(f"Loading in topic: {topics}")

        for msg_i, msg in enumerate(read_ros2_messages(log_path, topics=topics)):
            if msg is None:
                continue
            if msg_i > max_message_count:
                break
            topic = msg.channel.topic
            msgs[topic].append(msg)
            if msg_i % 10000 == 0 and verbose:
                print(f"read {msg_i} messages.")
        print(f"all topics: {msgs.keys()}")
        return msgs

    def extract_features(
        self,
        msgs,
        topic_process_methods_dict,
        start_times=None,
        wait_for_ado=False,
    ):
        """
        Process messages dictionary for desired features (such as poses, speeds etc)
        Input:
            msgs (dict): Dictionary of messages with topic names as keys.
            topic_process_methods_dict (dict): Dictionary of process_methods with topic names as keys
            initial_only (bool): If true, only record the first timestep of each scenario
            wait_for_ado (bool): If true, ignore all data before the ado materializes
            id_dict (dict): Dictionary mapping actor names to their IDs
            first_timestep (int): Time stamp to start processing from

        Output:
            dict of dicts containing the extracted features from each topic's messages.
        """
        msg_dict = {}
        if not wait_for_ado or start_times:
            self.first_timestep = 0
            if start_times and wait_for_ado:
                print(
                    f"Ignoring wait-for-ado because we need to search all messages to find start points properly."
                )

        for topic in list(msgs.keys()):
            topic_process_method = topic_process_methods_dict[topic]
            msg_dict[topic] = process_msg_data(
                topic,
                topic_process_method,
                msgs,
                self.id_dict,
                self.first_timestep,
                start_times,
            )

        return msg_dict

    @staticmethod
    def load_from_csv(csv_path):
        """
        Wrapper for pandas.read_csv()
        Input:
            csv_path (string): Path to csv to load as dataframe.
        Output:
            dataframe containing contents of csv.
        """
        return pd.read_csv(csv_path)

    def save_to_csv(self, csv_path, df=None):
        """
        Wrapper for pandas.Dataframe.to_csv()
        Inputs:
            csv_path (string): Destination path for csv.
            df (Dataframe): Dataframe to save to csv. If None, defaults to the merged dataframe created by the class.
        Output:
            None (creates a csv file at the specified path.)
        """
        if df is None:
            df = self.merged_df
        df.to_csv(csv_path)

    def merge_csv_and_dataframe(self, csv_path, df=None, reference_col=None, merge_col=None):
        """
        Load a csv into a dataframe and merge it with an existing dataframe.
        Inputs:
            csv_path (string): Path to csv to load as dataframe.
            df (Dataframe): Dataframe to merge with csv. If None, defaults to the merged dataframe created by the class.
            reference_col (string): Column title in the provided dataframe to use as a reference when synchronizing rows at merge. Defaults to class reference if None.
            merge_col (string): Column title in the dataframe loaded from csv to use when synchronizing rows at merge. Defaults to the first column title containing "log time" if None.
        Output:
            Dataframe containing synchronized rows from the provided dataframe and the one loaded from csv.
        """
        if df is None:
            df = self.merged_df

        new_df = pd.read_csv(csv_path)

        if not merge_col:
            merge_col = [i for i in new_df.columns if "log time" in i][0]
        if not reference_col:
            reference_col = self.reference_col

        return pd.merge_asof(
            df, new_df, left_on=reference_col, right_on=merge_col, direction="nearest"
        )


def process_msg_data(
    topic,
    topic_process_method,
    msgs,
    names_to_ids,
    initial_timestep: int = 0,
    start_times=None,
):
    """
    Method for extracting desired features from ROS topics.
    In order to process a topic, a processing method must be added below for that topic.
    Inputs:
        topic (string): The topic to process.
        msgs (dict): Dictionary of messages with topics as the keys.
        names_to_ids (dict): Dictionary mapping carla actor names to their IDs
        initial_timestep (int): First timestep to start recording (when all agents are on the scene)
    Output:
        dict with features as keys and lists of feature values as values.
    """
    if not msgs[topic]:
        print(f"No messages found for {topic}.")
        return None

    # Trim all messages to only consider when all objects exist
    msgs[topic] = msgs[topic][initial_timestep:]

    if topic_process_method is not None:
        print(f"TOPIC: {topic}, PROCESS_METHOD: {topic_process_method}")
        return topic_process_method(msgs[topic], id_dict=names_to_ids, start_times=start_times)
    else:
        print(f"TOPIC_PROCESS_METHOD for {topic} is None")
        return None


if __name__ == "__main__":
    """
    Example run:
    python mcap_parser.py --filename /home/<USER>/Data/test_mcap/P001_2023_11_02-14_47_57_0.mcap --wait_for_ado
    """
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument("--filename", type=str, default="../data/P001_2023_11_02-14_47_57_0.mcap")
    parser.add_argument(
        "--wait_for_ado",
        help="Should we ignore all data until the ado agent exists?",
        action="store_true",
    )
    parser.add_argument(
        "--initial_only",
        help="Only record initial timesteps for each run?",
        action="store_true",
    )
    parser.add_argument(
        "--save_fn",
        help="Filename to save the csv into",
        type=str,
        default="mcap_data.csv",
    )
    parser.add_argument("--verbose", help="Print debug messages", action="store_true")

    args = parser.parse_args()
    process_mcap = ProcessCompactSimMcap()
    panda_frame = process_mcap.mcap_to_dataframe(
        log_path=args.filename,
        initial_only=args.initial_only,
        wait_for_ado=args.wait_for_ado,
        max_message_count=150_000,
        verbose=args.verbose,
        n_workers=4,
    )
    process_mcap.save_to_csv(csv_path=f"{args.save_fn}", df=panda_frame)
