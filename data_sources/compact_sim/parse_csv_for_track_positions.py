import argparse

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd


def get_nearest_track_coordinate(track_x_coords, track_y_coords, x_coordinate, y_coordinate):
    """
    Given (aligned) lists of track coordinates and a new set of coordinates, find their nearest neighbor on the track
    (using euclidean distance)
    Args:
        track_x_coords: list of all x positions on the track (list[float])
        track_y_coords: list of all y positions on the track (list[float])
        x_coordinate: x coordinate of new point (float)
        y_coordinate: y coordinate of new point (float)

    Returns:
        integer index of the nearest neighbor on the track (int)
    """
    x_diff = (x_coordinate - track_x_coords) ** 2
    y_diff = (y_coordinate - track_y_coords) ** 2
    return np.argmin(np.sqrt(x_diff + y_diff))


if __name__ == "__main__":
    """
    Example run:
    python parse_csv_for_track_positions.py --save_fn ./mcap_data.csv
    """

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--save_fn",
        help="Filename to save the csv into",
        type=str,
        default="mcap_data.csv",
    )
    parser.add_argument(
        "--reference_fn",
        help="Filename of the track csv",
        type=str,
        default="~/shared-decision-making/track.csv",
    )
    args = parser.parse_args()

    our_locations = pd.read_csv(args.save_fn)
    reference_csv = pd.read_csv(args.reference_fn)
    print(our_locations.columns)
    print(reference_csv.columns)
    compare_key_x = "refline/x"
    compare_key_y = "refline/y"
    start_loc_dict = {
        "straight_ego": [],
        "straight_ado": [],
        "curve_ego": [],
        "curve_ado": [],
    }
    all_track_x_coords = reference_csv[compare_key_x]
    all_track_y_coords = reference_csv[compare_key_y]
    for index in range(len(our_locations["ego_x"])):
        ego_startline = get_nearest_track_coordinate(
            track_x_coords=all_track_x_coords,
            track_y_coords=all_track_y_coords,
            x_coordinate=our_locations["ego_x"][index],
            y_coordinate=our_locations["ego_y"][index],
        )
        ado_startline = get_nearest_track_coordinate(
            track_x_coords=all_track_x_coords,
            track_y_coords=all_track_y_coords,
            x_coordinate=our_locations["ado_x"][index],
            y_coordinate=our_locations["ado_y"][index],
        )
        print(f"Nearest track.csv indices for ego x/y: {ego_startline}")
        print(f"Nearest track.csv indices for ado x/y: {ado_startline}")

        if abs(ego_startline - 2370) < 10:
            start_loc_dict["straight_ego"].append(
                [our_locations["ego_x"][index], our_locations["ego_y"][index]]
            )
        elif abs(ego_startline - 1525) < 10:
            start_loc_dict["curve_ego"].append(
                [our_locations["ego_x"][index], our_locations["ego_y"][index]]
            )

        if abs(ado_startline - 1535) < 10:
            start_loc_dict["curve_ado"].append(
                [our_locations["ado_x"][index], our_locations["ado_y"][index]]
            )
        elif abs(ado_startline - 2370) < 10:
            start_loc_dict["straight_ado"].append(
                [our_locations["ado_x"][index], our_locations["ado_y"][index]]
            )

        print(
            f'Raw ego x/y: ({our_locations["ego_x"][index]}, '
            f'{our_locations["ego_y"][index]}) with heading {our_locations["ego_heading"][index]}'
        )
        print(
            f'Raw ado x/y: ({our_locations["ado_x"][index]}, '
            f'{our_locations["ado_y"][index]}) with heading {our_locations["ado_heading"][index]}'
        )
    for k, v in start_loc_dict.items():
        mean_start_loc = np.mean(v, axis=0)
        print(f"Mean {k} start location: {mean_start_loc}")
        x_d = (mean_start_loc[0] - reference_csv[compare_key_x]) ** 2
        y_d = (mean_start_loc[1] - reference_csv[compare_key_y]) ** 2
        nn = np.argmin(np.sqrt(x_d + y_d))
        print(f"Nearest neighbor to average point: {nn}")
