#!/bin/bash
#####
# This is a script to help convert participant logs with the name format P*.db3 to mcap
# and then upload them to a specified s3 bucket
# The optional args that can be provided to the script are the following:
# (All paths can be relative or absolute)
# 1) The path to the directory containing the logs to be converted and uploaded
# 2) The path to the mcap CLI tool
# 3) A flag to enable uploading (any text will serve here)
# 4) The s3 bucket url to upload to
# example: ./mcap_convert_upload.sh ../log-dir/ ~/mcap y s3://dest_bucket/url/
#####

# Print output of command (-x) and exit if command fails (-e)
exe(){
    set -e
    "$@"
    { set +e; } 2>/dev/null
}

catch_kill(){
    return
}

trap 'catch_kill' SIGINT

SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )
RELEASE_VERSION="0.17.0"
HMI_WS_SRC_DIR="motion_sim_hmi_scenarios"
# Source ROS
exe source /opt/ros/humble/setup.bash

# Source the motion sim release
exe source /opt/tri-runner-0-storage/motion-sim-$RELEASE_VERSION/install/setup.bash
exe source $HOME/$HMI_WS_SRC_DIR/install/setup.bash

# Set AWS credentials (get temporary keys and add them here.)
exe export AWS_ACCESS_KEY_ID=
exe export AWS_SECRET_ACCESS_KEY=
exe export AWS_SESSION_TOKEN=

LOG_DIRECTORY="$HOME/motion_sim_hmi_scenarios/"
if [ ! -z "$1" ]
    then
        LOG_DIRECTORY=$1
fi
MCAP_TOOL_PATH=""
if [ ! -z "$2" ]
    then
        MCAP_TOOL_PATH="--mcap-tool-path=$2"
fi
UPLOAD=""
if [ ! -z "$3" ]
    then
        UPLOAD="--upload"
fi
S3_URL=""
if [ ! -z "$4" ]
    then
        S3_URL="--s3-bucket=$4"
fi

# Run conversion and upload script
exe python3 $SCRIPT_DIR/mcap_convert_upload.py --log-dir=$LOG_DIRECTORY $MCAP_TOOL_PATH $UPLOAD $S3_URL
