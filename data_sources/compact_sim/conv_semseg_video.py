import argparse

import cv2
import numpy as np


def convert_segmentation_frame(frame):
    height, width = frame.shape
    output_frame = np.zeros((height, width, 3), dtype=np.uint8)

    # https://carla.readthedocs.io/en/latest/ref_sensors/#semantic-segmentation-camera
    classes = {
        0: (0, 0, 142),  # unlabeled (in practice, some parts of kerb)
        1: (128, 64, 128),  # road
        2: (244, 35, 232),  # sidewalk
        3: (70, 70, 70),  # building
        4: (102, 102, 156),  # wall
        5: (190, 153, 153),  # fence
        6: (153, 153, 153),  # pole
        7: (250, 170, 30),  # traffic light
        8: (220, 220, 0),  # traffic sign
        9: (107, 142, 35),  # vegetation
        10: (152, 251, 152),  # terrain
        11: (70, 130, 180),  # sky
        12: (220, 20, 60),  # pedestrian
        13: (255, 0, 0),  # rider
        14: (0, 0, 142),  # car
        15: (0, 0, 70),  # truck
        16: (0, 60, 100),  # bus
        17: (0, 80, 100),  # train
        18: (0, 0, 230),  # motorcycle
        19: (119, 11, 32),  # bicycle
        20: (110, 190, 160),  # static immovable elements - bin/bollards/signs
        21: (170, 120, 50),  # dynamic elements - movable trash bins, buggies
        22: (55, 90, 80),  # other - everything that does not belong to any other category
        23: (45, 60, 150),  # water
        24: (157, 234, 50),  # road lines / markings
        25: (81, 0, 81),  # other horizontal ground level structures, e.g. roundabouts
        26: (150, 100, 100),  # bridge
        27: (230, 150, 140),  # rail track
        28: (180, 165, 180),  # guard rail
    }

    for label, color in classes.items():
        output_frame[np.where(frame == label)] = color[::-1]
    return output_frame


def convert_segmentation_video(input_path, output_path):
    # Open the input video
    cap = cv2.VideoCapture(input_path)
    if not cap.isOpened():
        print(f"Error: Unable to open video file {input_path}")
        return

    # Get video properties
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)

    # Use lossy MJPG codec and create VideoWriter object
    fourcc = cv2.VideoWriter_fourcc(*"MJPG")
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break

        frame = frame[:, :, 2]

        # Convert the segmentation frame
        bgr_frame = convert_segmentation_frame(frame)

        # Write the frame to the output video
        out.write(bgr_frame)

    # Release on completion
    cap.release()
    out.release()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "input_video_path",
        type=str,
        default="/data/motion-simulator-logs-processed/Test/20240724T095321_cam_synced_60/cam_seg/video.mkv",
        help="path to input video",
    )
    parser.add_argument(
        "output_video_path",
        type=str,
        default="/tmp/colorized_semseg_video.mkv",
        help="path to output video",
    )
    args = parser.parse_args()

    convert_segmentation_video(args.input_video_path, args.output_video_path)
    print(f"Semseg video generation complete: {args.output_video_path}")
