#!/usr/bin/env python
# Train/finetune a sentence embedder/classifier. Commandline:
# ```python ./sentence_embedding.py --coded-segments-filepathname ~/Downloads/coded_segments.csv --sentence-level-transcript-input-dir ~/Downloads/Thunderhill_data/GoPro_Transcripts/2022-11-09/GoPro/ --other-sentences-filepathname ~/misc_sentences.txt'''
#
import argparse
import glob
import os
import pickle as pkl
from typing import Dict, List, Tuple

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import pytorch_lightning as pl
import scipy.stats
import srt
import torch
import tqdm
import umap
from sentence_transformers import SentenceTransformer, util
from torch import nn

"""
Example Usage:

python sentence_embedding.py --coded_segments_pathname PATH_TO_CODED_SEGMENTS_CSV_FILE

"""


def get_class_idxs():
    class_idxs = {}

    class_idxs["brake"] = [
        "Vehicle > Brake > Medium brake",
        "Vehicle > Brake",
        "Vehicle > Brake > Hard brake",
        "Vehicle > Hard brake",
        "Vehicle > Brake > Hold brake",
        "Vehicle > Light brake",
        "Vehicle > Brake > Brake Preparation",
        "Vehicle > Speed > Slow Down",
    ]

    class_idxs["accelerate"] = [
        "Vehicle > Speed > Accelerate > Accelerate - Little",
        "Vehicle > Speed > Accelerate",
        "Vehicle > Speed > Slow Down",
        "Vehicle > Speed > Speed Limit",
        "Vehicle > Speed",
    ]

    class_idxs["lateral-position"] = [
        "Vehicle > Lateral Position > Car Lateral Position - Tight to Edge",
        "Vehicle > Lateral Position > Car Lateral Position - Edge to Edge",
        "Vehicle > Lateral Position > Car Lateral Position - Mid-track",
        "Vehicle > Lateral Position > Car Lateral Position - Apex",
    ]

    class_idxs["left"] = ["Vehicle > Lateral Position > Car Lateral Position - Left"]

    class_idxs["right"] = ["Vehicle > Lateral Position > Car Lateral Position - Right"]

    class_idxs["straight"] = ["Vehicle > Lateral Position > Car Lateral Position - Straight"]

    class_idxs["turn"] = [
        "Vehicle > Turn (+)",
        "Vehicle > Turn (+) > Turn - Squeeze",
        "Vehicle > Turn (+) > Turn - Preparation",
        "Vehicle > Turn (+) > Turn - Left",
        "Vehicle > Turn (+) > U turn",
    ]

    class_idxs["look"] = [
        "Driver > Look (+)",
        "Driver > Look (+) > Look - Left",
        "Driver > Look (+) > Look - Right",
        "Driver > Look (+) > Look - Up",
        "Driver > Look (+) > Look - Ahead",
    ]

    return class_idxs


def get_codes_labels_valid_items(class_idxs, data, other_sentences):
    sentences = data["Segment"]
    codes = data["Code"]
    for key in class_idxs:
        for subkey in class_idxs[key]:
            codes = [key if i.lower() == subkey.lower() else i for i in codes]

    codes = np.array(codes)

    # List of booleans which assigns False or True to each item depending on whether they are present in the classes considered and not nan
    # valid_items = [list(class_idxs.keys()).count(cd) > 0 for cd in codes]
    valid_items = [
        list(class_idxs.keys()).count(cd) > 0 and not type(s) == float
        for cd, s in zip(codes, list(sentences)[:])
    ]
    # grab data from rows that are valid.
    valid_codes = codes[valid_items]
    # labels correspond to the index of the row of the code (from raw data) in code_classes
    labels = [pd.Index(class_idxs).get_loc(cd) for cd in valid_codes]
    labels += [len(class_idxs) for i in range(len(other_sentences))]
    OTHER_KEY = "other"
    valid_codes = np.concatenate(
        [valid_codes, np.array([OTHER_KEY for i in range(len(other_sentences))])]
    )
    class_idxs[OTHER_KEY] = [OTHER_KEY]
    tgts = torch.Tensor(labels).long()
    label_id_to_class_name = pd.DataFrame(pd.Index(class_idxs)).to_dict()[0]
    return (
        sentences,
        codes,
        valid_items,
        valid_codes,
        labels,
        class_idxs,
        tgts,
        label_id_to_class_name,
    )


def get_train_test_split(labels, sentences, other_sentences, valid_items):
    indices = np.random.permutation(len(labels))
    # 70-30 split for training and validation
    training_size = int(np.ceil(len(indices) * 0.7))
    training_idx, val_idx = indices[:training_size], indices[training_size:]
    valid_sentences = list(sentences[valid_items]) + other_sentences
    return indices, training_size, training_idx, val_idx, valid_sentences


def str2bool(v):
    if isinstance(v, bool):
        return v
    if v.lower() in ("yes", "true", "t", "y", "1"):
        return True
    elif v.lower() in ("no", "false", "f", "n", "0"):
        return False
    else:
        raise argparse.ArgumentTypeError("Boolean value expected.")


def run_action_detector(action_detector_dict: Dict, sentence_level_transcripts: Dict):
    """
    Run the action detector created in action_detector_dict on all sentences in sentence_level_transcripts.

    Args:
        action_detector_dict: Dict - dictionary with 'sentence_embedding' and 'classifier_mlp' for the trained classifier. Assumes the last class is the "nonaction" class.

    Returns:
        detected_actions: Dict - the sentences that were considered "actions"
        detected_nonactions: Dict - the sentences that were considered "non-actions"
    """
    sentence_level_embedding = action_detector_dict["sentence_embedding"]
    classifier_mlp = action_detector_dict["classifier_mlp"]
    detected_actions = {}
    detected_nonactions = {}
    NONACTION_BIAS = 2.0
    for key in tqdm.tqdm(sentence_level_transcripts):
        sentences = sentence_level_transcripts[key]
        if len(sentences) == 0:
            continue
        sentence_texts = [sentence.content for sentence in sentences]
        sentence_emb = sentence_level_embedding.encode(sentence_texts)
        sentence_class_vec = classifier_mlp(torch.Tensor(sentence_emb))
        other_class_idx = sentence_class_vec.shape[1] - 1
        sentence_class_vec[:, other_class_idx] += NONACTION_BIAS
        actions = (
            ((sentence_class_vec.max(1)[1] == other_class_idx).logical_not())
            .nonzero()
            .detach()
            .cpu()
            .numpy()
        )
        non_actions = (
            ((sentence_class_vec.max(1)[1] == other_class_idx)).nonzero().detach().cpu().numpy()
        )
        detected_actions[key] = np.array(sentences)[np.array(actions)]
        detected_nonactions[key] = np.array(sentences)[np.array(non_actions)]
    return detected_actions, detected_nonactions


def parse_arguments():
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument(
        "--coded-segments-pathname",
        type=str,
        help="Path to filename for coded segments",
        default="./",
    )
    parser.add_argument(
        "--other-sentences-pathname",
        type=str,
        help="Path to filename for other sentences",
        default="./",
    )
    parser.add_argument(
        "--visualize-embedding",
        type=str2bool,
        help="If true, visualizes the embeddings and opens a prompt",
        default="false",
    )
    parser.add_argument(
        "--sentence-level-transcript-input-dir",
        type=str,
        help="Path to filename for full transcript sentences",
        default="./",
    )
    parser.add_argument(
        "--save-model-location",
        type=str,
        help="Path to folder for saving the models",
        default="./saved_models",
    )
    result = parser.parse_args()
    return vars(result)


class MLPClassifier(pl.LightningModule):
    def __init__(self, input_dim=32, num_cls=10):
        super().__init__()
        self.input_dim = input_dim
        self.num_code_classes = num_cls
        # TODO (deepak.gopinath) Make network architecture configurable
        self.features_module = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Dropout(),
            nn.Linear(128, 32),
            # nn.ReLU(),
            # nn.Dropout(),
            # nn.Linear(128, 32),
        )
        self.classifier_layer = nn.Sequential(nn.ReLU(), nn.Dropout(), nn.Linear(32, num_cls))
        self.ce = nn.CrossEntropyLoss()

    def learned_features(self, x):
        return self.features_module(x)

    def forward(self, x):
        return self.classifier_layer(self.features_module(x))

    def training_step(self, batch, batch_idx):
        x, y = batch
        x = x.view(x.size(0), -1)
        y_hat = self.classifier_layer(self.features_module(x))
        loss = self.ce(y_hat, y)
        self.log("train_loss", loss)
        return loss

    def configure_optimizers(self):
        optimizer = torch.optim.Adam(self.parameters(), lr=1e-4, weight_decay=0.005)
        return optimizer


def create_sentence_embedding_model():
    """Return a sentence embedding model.

    :return: A pytorch model that supports .encode(), from a list of string to tensor of sentence embeddings.
    """
    sentence_model_id = "paraphrase-MiniLM-L6-v2"
    return sentence_model_id, SentenceTransformer(sentence_model_id)


def create_general_sentence_dataset(pathname, size):
    res = []
    with open(pathname, "r") as f:
        for row in f:
            res.append(row.strip())

    return res


def save_classifier(text_classification_results_dict: Dict, save_model_location: str):
    if save_model_location is not None:
        os.makedirs(save_model_location, exist_ok=True)
        classifier_mlp = text_classification_results_dict["classifier_mlp"]
        save_dict = {
            k: text_classification_results_dict[k]
            for k in ["class_idxs", "labels", "embedding_dimension"]
        }
        classifier_path = os.path.join(save_model_location, "classifier.pth")
        overall_dict_path = os.path.join(save_model_location, "training.pkl")
        torch.save(classifier_mlp.state_dict(), classifier_path)
        with open(overall_dict_path, "wb") as fp:
            pkl.dump(save_dict, fp)


def load_classifier(save_model_location: str):
    if save_model_location is not None:
        classifier_path = os.path.join(save_model_location, "classifier.pth")
        overall_dict_path = os.path.join(save_model_location, "training.pkl")
        with open(overall_dict_path, "rb") as fp:
            overall_dict = pkl.load(fp)
        embedding_dimension = overall_dict["embedding_dimension"]
        class_idxs = overall_dict["class_idxs"]
        model = MLPClassifier(input_dim=embedding_dimension, num_cls=len(class_idxs))
        model.load_state_dict(torch.load(classifier_path))
    return model, overall_dict


def classify_using_custom_model(
    data, sentence_level_transcripts, other_sentences, visualize_embedding: bool = False
):
    """
    Function for creating and training a sentence embedding that can classify.

    Args:
        data: Dict - dictionary with 'sentences' and 'code' to capture the text sentences in the data, and the class code for each of them (currently no "null class" as it's taken from coded_segments.csv)

    Returns:
        results - Dict - dictionary containing 'sentence_embedding' (raw, before any training), 'classifier_mlp' (does an intermediate embedding+classification), 'valid_items' (items that were trained on), 'labels' (used for training).
    """

    # code_classes = codes.unique()  # total number of unique codes - 50 in coded_segments.csv

    class_idxs = get_class_idxs()
    (
        sentences,
        codes,
        valid_items,
        valid_codes,
        labels,
        class_idxs,
        tgts,
    ) = get_codes_labels_valid_items(class_idxs, data, other_sentences)

    # initialize a sentence transformer model for creating embedding of the instructions
    (
        sentence_embedding_model_id,
        sentence_embedding_model,
    ) = create_sentence_embedding_model()

    # encode ALL sentences
    sentence_embeddings = sentence_embedding_model.encode(sentences)
    other_sentence_embeddings = sentence_embedding_model.encode(other_sentences)
    # grab embeddings from the valid items under consideration
    embeddings = np.concatenate(
        [sentence_embeddings[valid_items, :], other_sentence_embeddings]
    )  # each embedding is a 384-D vector
    # Train a classifier on the embeddings generated.

    pl.seed_everything(42)
    embedding_dimension = np.shape(embeddings)[1]

    mlp = MLPClassifier(input_dim=embedding_dimension, num_cls=len(class_idxs))
    trainer = pl.Trainer(
        auto_scale_batch_size="power",
        gpus=0,
        deterministic=False,
        max_epochs=5000,
        gradient_clip_val=1.0,
    )
    (
        indices,
        training_size,
        training_idx,
        val_idx,
        valid_sentences,
    ) = get_train_test_split(labels, sentences, other_sentences, valid_items)

    print("TRAINING SET SIZE", len(training_idx), "VALIDATION SET SIZE", len(val_idx))

    # codes corresponding to the validation and training set
    validation_codes = [list(valid_codes)[id] for id in val_idx]
    training_codes = [list(valid_codes)[id] for id in training_idx]

    # create pytorch datasets
    train_dataset = torch.utils.data.TensorDataset(
        torch.Tensor(embeddings[training_idx, :]), tgts[training_idx]
    )
    val_dataset = torch.utils.data.TensorDataset(
        torch.Tensor(embeddings[val_idx, :]), tgts[val_idx]
    )

    # create dataloaders
    train_dataloader = torch.utils.data.DataLoader(train_dataset, batch_size=512, shuffle=True)
    val_dataloader = torch.utils.data.DataLoader(val_dataset, batch_size=512)

    # train the mlp classifier model.
    trainer.fit(mlp, train_dataloader, val_dataloader)

    # reload the sentence embedding since the pytorch lightning training may collide.
    # sentence_embedding_model = create_sentence_embedding_model()

    # initialize lists to accumulate results and learned features on training and validation results.
    train_res = []
    validation_res = []
    validation_intermediates = []
    training_intermediates = []
    per_class_validation_res = {}
    per_class_train_res = {}
    training_class_sentences = {}
    # results on training dataset
    for idx in tqdm.tqdm(range(len(train_dataset)), desc="train_data"):
        (train_embedding, train_label) = train_dataset[idx]
        prediction = mlp(train_embedding)
        # check if prediction matches the ground truth
        intermediate_feature = mlp.learned_features(train_embedding)  # 32 D
        training_intermediates.append(intermediate_feature.expand(1, -1))
        train_res.append(prediction.argmax() == train_label)
        key = train_label.detach().cpu().item()
        if key not in per_class_train_res:
            per_class_train_res[key] = []
            training_class_sentences[key] = []
        per_class_train_res[key].append(prediction.argmax() == train_label)
        training_class_sentences[key].append(list(valid_sentences)[training_idx[idx]])

    # results on validation set

    for idx in tqdm.tqdm(range(len(val_dataset)), desc="val_data"):
        (val_embedding, val_label) = val_dataset[idx]
        prediction = mlp(val_embedding)
        intermediate_feature = mlp.learned_features(val_embedding)  # 32 D
        validation_intermediates.append(intermediate_feature.expand(1, -1))
        validation_res.append(prediction.argmax() == val_label)
        key = val_label.detach().cpu().item()
        if key not in per_class_validation_res:
            per_class_validation_res[key] = []
        per_class_validation_res[key].append(prediction.argmax() == val_label)

    validation_learned_classifier_embeddings = torch.cat(validation_intermediates)
    training_learned_classifier_embeddings = torch.cat(training_intermediates)
    validation_classifier_embedding_dists = util.cos_sim(
        validation_learned_classifier_embeddings,
        validation_learned_classifier_embeddings,
    )
    trained_classifier_embedding_dists = util.cos_sim(
        training_learned_classifier_embeddings, training_learned_classifier_embeddings
    )

    # Umap for validation and training
    dim_red = umap.UMAP(n_neighbors=8, n_components=2, metric="cosine").fit(
        training_learned_classifier_embeddings.detach().cpu()
    )

    umap_validation_classifier_embeddings = dim_red.transform(
        validation_learned_classifier_embeddings.detach().cpu()
    )
    umap_training_classifier_embeddings = dim_red.transform(
        training_learned_classifier_embeddings.detach().cpu()
    )
    for key in per_class_validation_res:
        print(f"Validation accuracy for class {key}: {np.average(per_class_validation_res[key])}")
    print(
        f"Validation accuracy: {np.average(validation_res)}, Training accuracy: {np.average(train_res)}"
    )

    # PLOT VALIDATION DATA EMBEDDINGS
    # get index of rows whose code match the given class label
    tidx = {}
    vidx = {}

    for key in class_idxs:
        tidx[key] = [i for i in range(len(list(training_codes))) if list(training_codes)[i] == key]
        vidx[key] = [
            i for i in range(len(list(validation_codes))) if list(validation_codes)[i] == key
        ]

    if visualize_embedding:
        cmap = plt.cm.get_cmap("hsv", len(class_idxs))
        plt.plot(
            umap_validation_classifier_embeddings[:, 0],  # plot all points are dots
            umap_validation_classifier_embeddings[:, 1],
            ".",
        )
        for key_i, key in enumerate(class_idxs):
            plt.plot(
                # overlay color on points frpom classes of interest
                umap_validation_classifier_embeddings[vidx[key], 0],
                umap_validation_classifier_embeddings[vidx[key], 1],
                ".",
                c=cmap(key_i),
            )
        plt.title(
            f"Learned classifier feature embedding for validation set (size = {len(val_idx)})"
        )
        plt.figure()

        plt.plot(
            umap_training_classifier_embeddings[:, 0],  # plot all points are dots
            umap_training_classifier_embeddings[:, 1],
            ".",
        )
        # overlay color on points frpom classes of interest
        for key_i, key in enumerate(class_idxs):
            plt.plot(
                # overlay color on points from classes of interest
                umap_training_classifier_embeddings[tidx[key], 0],
                umap_training_classifier_embeddings[tidx[key], 1],
                ".",
                c=cmap(key_i),
            )

        plt.title(
            f"Learned classifier feature embedding for training set (size = {len(training_idx)})"
        )

        if sentence_level_transcripts is not None:
            raw_transcripts_res = []
            for transcript in sentence_level_transcripts.values():
                for sentence in transcript:
                    # print(transcript)
                    content = sentence.content
                    raw_transcripts_res.append(content)
            # sentence_embedding_model = create_sentence_embedding_model()
            embedded_raw_transcripts = sentence_embedding_model.encode(raw_transcripts_res)
            fig, ax = plt.subplots()

            raw_transcript_classifier_embeddings = mlp.learned_features(
                torch.Tensor(embedded_raw_transcripts)
            )
            cls_prob = (
                torch.softmax(mlp.forward(torch.Tensor(embedded_raw_transcripts)), 1)
                .max(1)[0]
                .detach()
                .cpu()
                .numpy()
            )
            # probably UMAP should be used differently -- cannot embed based on the noisy data, need to get a transformation based on the clean data
            # and use on the noisy data.
            umap_transcript_classifier_embeddings = dim_red.transform(
                raw_transcript_classifier_embeddings.detach().cpu()
            )

            # For a heatmap w/ KDE
            m1 = umap_transcript_classifier_embeddings[:, 0]
            m2 = umap_transcript_classifier_embeddings[:, 1]
            xmin = m1.min()
            xmax = m1.max()
            ymin = m2.min()
            ymax = m2.max()

            # mgrid: "if the step length is a complex number (e.g. 5j), then the integer part of its magnitude is interpreted
            # as specifying the number of points to create between the start and stop values"
            X, Y = np.mgrid[xmin:xmax:100j, ymin:ymax:100j]
            positions = np.vstack([X.ravel(), Y.ravel()])
            values = np.vstack([m1, m2])
            kernel = scipy.stats.gaussian_kde(values)
            Z = np.reshape(kernel(positions).T, X.shape)

            #
            ax.imshow(np.rot90(Z), cmap=plt.cm.gist_earth_r, extent=[xmin, xmax, ymin, ymax])
            ax.plot(m1, m2, "k.", markersize=2)

            ax.set_xlim([xmin, xmax])
            ax.set_ylim([ymin, ymax])

            plt.plot(
                umap_transcript_classifier_embeddings[:, 0],
                umap_transcript_classifier_embeddings[:, 1],
                "b.",
                umap_training_classifier_embeddings[:, 0],  # plot all points are dots
                umap_training_classifier_embeddings[:, 1],
                "k.",
            )

            plt.title(
                f"Learned classifier feature embedding for raw instructor sentences (size = {umap_transcript_classifier_embeddings.shape[0]})"
            )
        plt.show()
    results = {
        "sentence_embedding": sentence_embedding_model,
        "sentence_embedding_id": sentence_embedding_model_id,
        "classifier_mlp": mlp,
        "valid_items": valid_items,
        "labels": labels,
        "class_idxs": class_idxs,
        "embedding_dimension": embedding_dimension,
    }
    return results


# TODO(guy.rosman): import from process_thunderhill_data
def read_transcripts(transcript_files: List) -> Dict:
    """
    Function to extract the transcripts from each .srt and organize them into a dict

    Args:
        transcript_files: List - list of full paths to the transcripts (word-level or sentence-level) files

    Returns:
        transcript_dict: Dict - Dictionary containing mapping between transcripts_paths (local dependency removed) and the extracted transcripts stored as a list of Subtitle objects.
    """
    transcript_dict = {}
    for transcript_file in transcript_files:
        # remove local computer path dependency in the keys.
        transcript_file_key = transcript_file[transcript_file.find("GoPro_Transcripts") :]
        print(transcript_file)
        with open(transcript_file, "r", encoding="latin-1") as fp:
            srt_text = fp.read()
            new_srt_gen = srt.parse(srt_text)
            # new_srt is a list of Subtitle objects.
            # For example of a subtitle object in the list can look like
            # Subtitle(index=5, start=datetime.timedelta(seconds=96), end=datetime.timedelta(seconds=106), content='Thank you.', proprietary='')
            new_srt = list(new_srt_gen)
            # NOTE : It seems like OpenAI's Whisper can hallucinate transcriptions. Beware! Need to have some QC on this, before using it for model training
            transcript_dict[transcript_file_key] = new_srt
    return transcript_dict


def main():
    args = parse_arguments()
    coded_segements_filepath = os.path.expandvars(
        os.path.expanduser(args["coded_segments_pathname"])
    )
    other_sentences_filepath = os.path.expandvars(
        os.path.expanduser(args["other_sentences_pathname"])
    )
    save_model_location = os.path.expandvars(os.path.expanduser(args["save_model_location"]))
    # Read coded segments csv into a pandas dataframe
    other_sentences_data = create_general_sentence_dataset(other_sentences_filepath, 100)
    sentence_level_transcript_input_dir = os.path.expandvars(
        os.path.expanduser(args["sentence_level_transcript_input_dir"])
    )
    sentence_level_transcript_files = sorted(
        glob.glob(
            os.path.join(sentence_level_transcript_input_dir, "**/G*.srt"),
            recursive=True,
        )
    )
    sentence_level_transcripts = read_transcripts(sentence_level_transcript_files)
    data = pd.read_csv(coded_segements_filepath)
    text_classification_results_dict = classify_using_custom_model(
        data,
        sentence_level_transcripts,
        other_sentences_data,
        args["visualize_embedding"],
    )

    sentence_embedding_model = text_classification_results_dict["sentence_embedding"]
    classifier_mlp = text_classification_results_dict["classifier_mlp"]
    action_detections, nonaction_detections = run_action_detector(
        text_classification_results_dict, sentence_level_transcripts
    )

    save_classifier(text_classification_results_dict, save_model_location)
    lmodel, ldict = load_classifier(save_model_location)

    if args["visualize_embedding"]:
        import IPython

        IPython.embed()


if __name__ == "__main__":
    main()
