#!/usr/bin/env python
# Train/finetune a sentence embedder/classifier. Commandline:
# python setfit_classification.py --coded-segments-pathname ~/Data/Thunderhill/coded_segments.csv
# --other-sentences-pathname ~/Data/Thunderhill/other_sentences.txt --sentence-level-transcript-input-dir
# ~/Data/Thunderhill/GoPro_Transcripts/ --visualize-embedding True --use-full-train-set True

import argparse
import collections
import glob
import os
import pickle
from typing import Dict, List, Tuple

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import torch
import tqdm
import umap

# hugging face imports
from datasets import Dataset, DatasetDict
from sentence_embedding import (
    create_general_sentence_dataset,
    get_class_idxs,
    get_codes_labels_valid_items,
    get_train_test_split,
    read_transcripts,
    str2bool,
)
from sentence_transformers.losses import CosineSimilarityLoss
from setfit import SetFitModel, SetFitTrainer, sample_dataset

# original labels = 0-brake, 1-accelerate, 2-lateral-position, 3-left, 4-right, 5-staright, 6-turn, 7-look, 8-no action/other
# new labels = 0-no sentence, 1-no action/other,  2-brake, 3-accelerate, 4-lateral-position, 5-left, 6-right, 7-straight 8-turn, 9-look,
REMAPPED_LABELS = {0: 2, 1: 3, 2: 4, 3: 5, 4: 6, 5: 7, 6: 8, 7: 9, 8: 1}


def parse_arguments():
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument(
        "--coded-segments-pathname",
        type=str,
        help="Path to filename for coded segments",
        default="./",
    )
    parser.add_argument(
        "--other-sentences-pathname",
        type=str,
        help="Path to filename for other sentences",
        default="./",
    )
    parser.add_argument(
        "--visualize-embedding",
        type=str2bool,
        help="If true, visualizes the embeddings and opens a prompt",
        default="false",
    )
    parser.add_argument(
        "--use-full-train-set",
        type=str2bool,
        help="If true, Uses the full available train set for constrastive training of sentence transformer",
        default="false",
    )
    parser.add_argument(
        "--sentence-level-transcript-input-dir",
        type=str,
        help="Path to filename for full transcript sentences",
        default="./",
    )
    result = parser.parse_args()
    return vars(result)


def per_class_results(dataset, predictions):
    unique_labels = np.unique(dataset["label"])
    per_class_accuracy_dataset = {}

    for label in unique_labels:
        indices_for_labels = np.where(np.array(dataset["label"]) == label)[0]
        res_for_label = (
            predictions.detach().cpu().numpy()[indices_for_labels]
            == np.array(dataset["label"])[indices_for_labels]
        )
        per_class_accuracy_dataset[label] = np.average(res_for_label)

    return per_class_accuracy_dataset


def print_per_class_results(per_class_res_ds, tag="train"):
    for key in per_class_res_ds:
        print(
            f"Validation accuracy for class {key} for {tag} dataset",
            per_class_res_ds[key],
        )


def plot_classifier_embeddings(
    classifier_embeddings,
    dataset,
    plot_colors=True,
    tag="train",
    create_new_fig=True,
    plot_style=".",
    with_title=True,
):
    if create_new_fig:
        plt.figure()

    plt.plot(
        classifier_embeddings[:, 0],  # plot all points are dots
        classifier_embeddings[:, 1],
        plot_style,
    )
    if plot_colors:
        idx = {}
        cmap = plt.cm.get_cmap("hsv", len(np.unique(dataset["label"])))
        for label in np.unique(dataset["label"]):
            idx[label] = [i for i, l in enumerate(dataset["label"]) if l == label]

        for label, label_inds in idx.items():
            plt.plot(
                # overlay color on points frpom classes of interest
                classifier_embeddings[label_inds, 0],
                classifier_embeddings[label_inds, 1],
                plot_style,
                c=cmap(label),
            )
    if with_title:
        plt.title(
            f"Learned classifier feature embedding for {tag} set (size = {len(dataset['label'])})"
        )


def classify_using_setfit(
    data,
    sentence_level_transcripts,
    other_sentences,
    use_full_train_set: bool = True,
    visualize_embedding: bool = False,
):
    class_idxs = get_class_idxs()
    (
        sentences,
        codes,
        valid_items,
        valid_codes,
        labels,
        class_idxs,
        tgts,
        label_id_to_class_name,
    ) = get_codes_labels_valid_items(class_idxs, data, other_sentences)

    (
        indices,
        training_size,
        training_idx,
        val_idx,
        valid_sentences,
    ) = get_train_test_split(labels, sentences, other_sentences, valid_items)

    print("TRAINING SET SIZE", len(training_idx), "VALIDATION SET SIZE", len(val_idx))

    # codes corresponding to the validation and training set
    validation_codes = [list(valid_codes)[id] for id in val_idx]
    training_codes = [list(valid_codes)[id] for id in training_idx]

    # prepare data for huggingface Dataset
    train_text_list = [t for i, t in enumerate(valid_sentences) if i in training_idx]
    valid_text_list = [t for i, t in enumerate(valid_sentences) if i in val_idx]

    train_labels = [l for i, l in enumerate(labels) if i in training_idx]
    val_labels = [l for i, l in enumerate(labels) if i in val_idx]

    train_pd = pd.DataFrame.from_dict({"sentence": train_text_list, "label": train_labels})
    val_pd = pd.DataFrame.from_dict({"sentence": valid_text_list, "label": val_labels})

    # Create huggingface Dataset from pandas dataframe
    train_dataset = Dataset.from_pandas(train_pd)
    valid_dataset = Dataset.from_pandas(val_pd)

    # create a Huggingface dataset dict
    dataset_dict = DatasetDict()
    dataset_dict["train"] = train_dataset
    dataset_dict["valid"] = valid_dataset
    print(
        "Distribution of train dataset",
        collections.Counter(train_dataset["label"]),
    )
    print(
        "Distribution of validation dataset",
        collections.Counter(valid_dataset["label"]),
    )
    # default num_samples per class is 8 examples
    sampled_train_dataset = sample_dataset(dataset_dict["train"], num_samples=24)

    print(
        "Distribution of sampled train dataset",
        collections.Counter(sampled_train_dataset["label"]),
    )

    # set up setfit training
    num_classes = len(sampled_train_dataset.unique("label"))
    print("NUM CLASSES", num_classes)
    # model_id = "sentence-transformers/paraphrase-mpnet-base-v2"  # default choice in paper
    model_id = "paraphrase-MiniLM-L6-v2"  # our choice
    model = SetFitModel.from_pretrained(
        model_id,
        use_differentiable_head=True,
        head_params={"out_features": num_classes, "use_multilayer": True},
    )

    # plot
    if use_full_train_set:
        print(" USING FULL TRAIN SET")
        trainer = SetFitTrainer(
            model=model,
            train_dataset=train_dataset,
            eval_dataset=valid_dataset,
            loss_class=CosineSimilarityLoss,
            num_iterations=20,
            column_mapping={"sentence": "text", "label": "label"},
        )
    else:
        print(" USING SUB SAMPLED TRAIN SET")
        trainer = SetFitTrainer(
            model=model,
            train_dataset=sampled_train_dataset,
            eval_dataset=valid_dataset,
            loss_class=CosineSimilarityLoss,
            num_iterations=20,
            column_mapping={"sentence": "text", "label": "label"},
        )
    # train body contrastive loss
    trainer.freeze()
    trainer.train(body_learning_rate=1e-5, num_epochs=1)

    # train the classifier head
    trainer.unfreeze(keep_body_frozen=True)
    trainer.train(learning_rate=1e-2, num_epochs=50)

    metrics = trainer.evaluate()  # on validation set
    print("ACCURACY ON VALIDATION SET", metrics)

    # sentence transfomer embeddings, classifier_feature_embeddings and predictions on sampled_train_set

    (
        sampled_train_dataset_st_embeddings,
        sampled_train_dataset_classifer_embeddings,
        sampled_train_dataset_predictions,
    ) = trainer.compute_embeddings_and_predictions(sampled_train_dataset)

    sampled_train_res = sampled_train_dataset_predictions.detach().cpu().numpy() == np.array(
        sampled_train_dataset["label"]
    )
    per_class_results_sampled_train_dataset = per_class_results(
        sampled_train_dataset, sampled_train_dataset_predictions
    )
    print_per_class_results(per_class_results_sampled_train_dataset, "sampled_train")
    print("SAMPLED TRAIN DATASET OVERALL ACCURACY ", np.average(sampled_train_res))
    print("              ")

    # sentence transfomer embeddings, classifier_feature_embeddings and predictions on full_train_set
    (
        train_dataset_st_embeddings,
        train_dataset_classifer_embeddings,
        train_dataset_predictions,
    ) = trainer.compute_embeddings_and_predictions(train_dataset)
    # sentence transfomer embeddings, classifier_feature_embeddings and predictions on validation_set
    train_res = train_dataset_predictions.detach().cpu().numpy() == np.array(
        train_dataset["label"]
    )
    per_class_results_train_dataset = per_class_results(train_dataset, train_dataset_predictions)
    print_per_class_results(per_class_results_train_dataset, "train")
    print("TRAIN DATASET OVERALL ACCURACY ", np.average(train_res))

    print("              ")
    (
        valid_dataset_st_embeddings,
        valid_dataset_classifer_embeddings,
        valid_dataset_predictions,
    ) = trainer.compute_embeddings_and_predictions(valid_dataset)

    valid_res = valid_dataset_predictions.detach().cpu().numpy() == np.array(
        valid_dataset["label"]
    )
    per_class_results_valid_dataset = per_class_results(valid_dataset, valid_dataset_predictions)
    print_per_class_results(per_class_results_valid_dataset, "valid")
    print("VALIDATION DATASET OVERALL ACCURACY ", np.average(valid_res))

    # umap stuff

    if sentence_level_transcripts is not None:
        raw_transcripts_res = []
        raw_transcripts_keys = []
        for transcript_key, transcript in sentence_level_transcripts.items():
            for sentence in transcript:
                # print(transcript)
                content = sentence.content
                raw_transcripts_res.append(content)
                raw_transcripts_keys.append(transcript_key)
        # sentence transfomer embeddings, classifier_feature_embeddings and predictions on all_transcripts

        fake_labels = [0] * len(
            raw_transcripts_res
        )  # needed otherwise validating column mappings will fail
        transcript_pd = pd.DataFrame.from_dict(
            {"sentence": raw_transcripts_res, "label": fake_labels}
        )
        transcript_dataset = Dataset.from_pandas(transcript_pd)
        (
            transcript_dataset_st_embeddings,
            transcript_dataset_classifer_embeddings,
            transcript_dataset_predictions,
        ) = trainer.compute_embeddings_and_predictions(transcript_dataset)

    if visualize_embedding:
        # Umap for validation and training
        dim_red = umap.UMAP(n_neighbors=8, n_components=2, metric="cosine").fit(
            sampled_train_dataset_classifer_embeddings.detach().cpu()
        )

        umap_sampled_train_classifier_embeddings = dim_red.transform(
            sampled_train_dataset_classifer_embeddings.detach().cpu()
        )
        umap_train_classifier_embeddings = dim_red.transform(
            train_dataset_classifer_embeddings.detach().cpu()
        )
        umap_valid_classifier_embeddings = dim_red.transform(
            valid_dataset_classifer_embeddings.detach().cpu()
        )

        plot_classifier_embeddings(
            umap_sampled_train_classifier_embeddings,
            sampled_train_dataset,
            tag="sampled_train",
            create_new_fig=True,
        )
        plot_classifier_embeddings(
            umap_train_classifier_embeddings,
            train_dataset,
            tag="train",
            create_new_fig=True,
        )

        plot_classifier_embeddings(
            umap_valid_classifier_embeddings,
            valid_dataset,
            tag="valid",
            create_new_fig=True,
        )

        if sentence_level_transcripts is not None:
            umap_transcript_classifier_embeddings = dim_red.transform(
                transcript_dataset_classifer_embeddings.detach().cpu()
            )
            plot_classifier_embeddings(
                umap_transcript_classifier_embeddings,
                transcript_dataset,
                plot_colors=False,
                tag="transcript",
                plot_style="b.",
            )
            plot_classifier_embeddings(
                umap_train_classifier_embeddings,
                train_dataset,
                plot_colors=True,
                create_new_fig=False,
                with_title=False,
                plot_style="k.",
            )

            plt.show()

    organized_results_raw_transcripts = collections.defaultdict(list)

    if sentence_level_transcripts is not None:
        for raw_transcript, raw_transcripts_key, transcript_dataset_prediction in zip(
            raw_transcripts_res,
            raw_transcripts_keys,
            list(transcript_dataset_predictions.detach().cpu().numpy()),
        ):
            organized_results_raw_transcripts[raw_transcripts_key].append(
                (
                    raw_transcript,
                    transcript_dataset_prediction,
                    REMAPPED_LABELS[transcript_dataset_prediction],
                    label_id_to_class_name[transcript_dataset_prediction],
                )
            )

    results = {
        "sentence_transformer_model": trainer.model.model_body,
        "classifier_head_model": trainer.model.model_head,
        "predicted_classes_for_raw_transcripts": organized_results_raw_transcripts,
    }
    return results


def main():
    args = parse_arguments()
    coded_segments_filepath = os.path.expandvars(
        os.path.expanduser(args["coded_segments_pathname"])
    )
    other_sentences_filepath = os.path.expandvars(
        os.path.expanduser(args["other_sentences_pathname"])
    )
    # Read coded segments csv into a pandas dataframe
    other_sentences_data = create_general_sentence_dataset(other_sentences_filepath, 100)
    sentence_level_transcript_input_dir = os.path.expandvars(
        os.path.expanduser(args["sentence_level_transcript_input_dir"])
    )
    sentence_level_transcript_files = sorted(
        glob.glob(
            os.path.join(sentence_level_transcript_input_dir, "**/G*.srt"),
            recursive=True,
        )
    )
    # TODO (deepak.gopinath) Test training setfit without collapsed transcriptions.
    # Uncomment the following to remove collapsed transcriptions from setfit classification.
    # # remove transcripts that had collapsed transcription issues from Whisper.
    # transcripts_to_be_excluded = [
    #     "2022-11-09/GoPro/Leia/cabin/GX015481.srt",
    #     "2022-11-09/GoPro/Leia/cabin/GX025475.srt",
    #     "2022-11-10/GoPro/Leia/cabin/GX015493.srt",
    #     "2022-11-10/GoPro/Leia/cabin/GX015489.srt",
    # ]
    # full_path_transcripts_to_be_excluded = [
    #     os.path.join(sentence_level_transcript_input_dir, t) for t in transcripts_to_be_excluded
    # ]
    # sentence_level_transcript_files = [
    #     s for s in sentence_level_transcript_files if s not in full_path_transcripts_to_be_excluded
    # ]
    sentence_level_transcripts = read_transcripts(sentence_level_transcript_files)
    data = pd.read_csv(coded_segments_filepath)

    embedding_results_dict = classify_using_setfit(
        data,
        sentence_level_transcripts,
        other_sentences_data,
        use_full_train_set=args["use_full_train_set"],
        visualize_embedding=args["visualize_embedding"],
    )
    path_to_excel_file = os.path.join(
        os.path.split(os.path.split(sentence_level_transcript_input_dir)[0])[0],
        "transcript_output_test.xlsx",
    )
    writer = pd.ExcelWriter(path=path_to_excel_file)
    for key, d in embedding_results_dict["predicted_classes_for_raw_transcripts"].items():
        sheet_name = key.replace("/", "_")
        df = pd.DataFrame(d, columns=["sentence", "label_int", "remapped_label_int", "category"])
        df.to_excel(writer, sheet_name=sheet_name)
    writer.close()

    # Save the predicted labels on transcripts into a pkl file for consumption by process_thunderhill_data.py
    path_to_embedding_results_pkl = os.path.join(
        os.path.split(os.path.split(sentence_level_transcript_input_dir)[0])[0],
        "transcript_labels_test.pkl",
    )
    with open(path_to_embedding_results_pkl, "wb") as fp:
        pickle.dump(embedding_results_dict["predicted_classes_for_raw_transcripts"], fp)

    import IPython

    IPython.embed(banner1="check")


if __name__ == "__main__":
    main()
