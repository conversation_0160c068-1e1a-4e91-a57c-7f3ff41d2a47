"""
Run these commands from the repository root.
Example for building new images for both accounts
```bash
# We grant cross-account to TRI build role, but not users for now, so use rad profile
# Use temporary credentials in rad profile
AWS_PROFILE=rad inv build-images
AWS_PROFILE=rad inv push-images
# Or copy and paste SSO passcode from the okta -> AWS SSO -> RAD account -> Command line or programmatic access
export AWS_ACCESS_KEY_ID="*******"
inv build-images
inv push-images
```
"""


import sys

import invoke
from typeguard import typechecked


@invoke.task
def lint(c, check=False):
    """Run black and isort code formatters, optionally just check if they need to run"""
    check_error_msg = "Run `inv -r intent/multiagents format` to reformat the code"
    check_arg = "--check" if check else ""

    failed = False  # catch failures and exit with 1 after all checks
    black_cmd = f"black {check_arg} ."
    if not c.run(black_cmd, warn=True, echo=True) and check:
        print(check_error_msg)
        failed = True

    isort_cmd = f"isort {check_arg} ."
    if not c.run(isort_cmd, warn=True, echo=True) and check:
        print(check_error_msg)
        failed = True

    if failed:
        sys.exit(1)


@typechecked
def as_collection() -> invoke.Collection:
    namespace = invoke.Collection("hail")
    namespace.add_task(lint)
    return namespace
