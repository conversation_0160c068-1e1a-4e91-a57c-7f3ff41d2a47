"""
Run these commands from the repository root.

Example for building new images for both accounts

```bash
# We grant cross-account to TRI build role, but not users for now, so use rad profile
# Use temporary credentials in rad profile
AWS_PROFILE=rad inv build-images
AWS_PROFILE=rad inv push-images

# Or copy and paste SSO passcode from the okta -> AWS SSO -> RAD account -> Command line or programmatic access
export AWS_ACCESS_KEY_ID="*******"

inv build-images
inv push-images

```
"""
import copy
import json
import logging
import os
import pathlib
import sys
from typing import Dict, Tuple

import invoke
from typeguard import typechecked

AWS_REGION = "us-east-1"
ENV_VERSION = os.environ.get("ENV_VERSION", "py11")
RAD_ACCOUNT = "************"
RAD_IMAGE = "hid-common"
TAG_BASE = ENV_VERSION
TAG_INTEGRATION = f"{TAG_BASE}-integration"


@typechecked
def read_aws_credentials(c: invoke.Context) -> Tuple[str, str]:
    """Get local AWS credentials to pass to docker as build arguments"""
    try:
        access_key_id = c.run("aws configure get aws_access_key_id", hide="stdout").stdout.strip(
            "\n"
        )
        secret_access_key = c.run(
            "aws configure get aws_secret_access_key", hide="stdout"
        ).stdout.strip("\n")
    except invoke.exceptions.UnexpectedExit as err:
        logging.error(str(err))
        raise
    return access_key_id, secret_access_key


@typechecked
def ecr_repo_url(account: str) -> str:
    # https://console.aws.amazon.com/ecr/repositories/private/************/risk-aware-driving?region=us-east-1
    return f"{account}.dkr.ecr.{AWS_REGION}.amazonaws.com"


@typechecked
def path_to_ecr_cred() -> str:
    path_to_cred = os.path.join(pathlib.Path.home(), ".docker/config.json")
    return path_to_cred


@typechecked
def clear_ecr_cred(path_to_cred: str, account: str) -> None:
    """Clear credentials to make sure we do not clobber the new one when dual-populating"""
    if not os.path.exists(path_to_cred):
        return
    with open(path_to_cred) as f:
        data = json.load(f)
        data["auths"] = {k: v for k, v in data["auths"].items() if account not in k}
    with open(path_to_cred, "w") as f:
        json.dump(data, f)


@typechecked
def populate_ecr_cred(path_to_cred: str, account: str) -> None:
    """
    Add credentials to both plain and https endpoints because aws and docker don't align on all platforms.
    This function assumes the credentials have been cleared before logging in so only one version is present.
    """
    with open(path_to_cred) as f:
        data: Dict = json.load(f)
        full_data = copy.deepcopy(data)
        for endpoint in data["auths"]:
            if account not in endpoint:
                continue
            if endpoint.startswith("https"):
                chopped = len("https://")
                other_endpoint = endpoint[chopped:]
            else:
                other_endpoint = f"https://{endpoint}"
            full_data["auths"][other_endpoint] = data["auths"][endpoint]
    with open(path_to_cred, "w") as f:
        json.dump(full_data, f)


@invoke.task
def ecr_login(c, account=RAD_ACCOUNT):
    """Get token for ECR repo"""
    path_to_cred = path_to_ecr_cred()
    clear_ecr_cred(path_to_cred, account)

    docker_pwd_cmd = f"aws ecr get-login-password --region {AWS_REGION}"
    docker_login_cmd = "docker login --username AWS --password-stdin"
    repo_url = ecr_repo_url(account)
    c.run(f"{docker_pwd_cmd} | {docker_login_cmd} {repo_url}")

    populate_ecr_cred(path_to_cred, account)


@typechecked
def _pull_image(
    c: invoke.Context, image: str, tag: str, repo_url: str, warn: bool = False
) -> None:
    cmd = f"docker pull {repo_url}{'/' if repo_url else ''}{image}:{tag}"
    print(cmd)
    c.run(cmd, warn=warn)


@invoke.task
def pull_image(c, account=RAD_ACCOUNT, image=RAD_IMAGE, tag=TAG_INTEGRATION):
    """Pull a docker image, logging in to a private ECR repo if account is provided"""
    # Always login to RAD account to pull base image
    ecr_login(c, account=account)
    repo_url = ecr_repo_url(account) if account else ""
    _pull_image(c, image=image, tag=tag, repo_url=repo_url, warn=True)


@typechecked
def tag_image(
    c: invoke.Context,
    local_image: str,
    local_tag: str,
    remote_image: str,
    remote_tag: str,
    repo_url: str,
) -> None:
    cmd = f"docker tag {local_image}:{local_tag} {repo_url}/{remote_image}:{remote_tag}"
    print(cmd)
    c.run(cmd)


@typechecked
def build_image(
    c,
    dockerfile: str,
    image: str,
    tag: str,
    env_version: str,
    repo_url: str,
    use_access_key: bool,
    progress: str,
) -> None:
    """
    Build a docker image and tag it

    If use_access_key is False, try to use instance metadata on ECS or EC2, eg when building images in CI
    """
    # Buildkit is required for
    #   1) overriding ENV vars
    #   2) Not baking ARG secrets into the image
    cmd = f"DOCKER_BUILDKIT=1 docker build -t {image}:{tag} --progress {progress} --build-arg ENV_VERSION={env_version} --file {dockerfile} ."
    if use_access_key:
        access_key_id, secret_access_key = read_aws_credentials(c)
        cmd = f"{cmd} --build-arg AWS_ACCESS_KEY_ID='{access_key_id}' --build-arg AWS_SECRET_ACCESS_KEY='{secret_access_key}'"
    else:
        uri = "AWS_CONTAINER_CREDENTIALS_RELATIVE_URI"
        cmd = f"{cmd} --build-arg AWS_DEFAULT_REGION=$AWS_DEFAULT_REGION --build-arg {uri}=${uri}"
    c.run(
        cmd,
        echo=True,
        pty=True,
    )
    tag_image(
        c, local_image=image, local_tag=tag, remote_image=image, remote_tag=tag, repo_url=repo_url
    )


@invoke.task
def build_images(c, account=RAD_ACCOUNT, use_access_key=True, pull_first=False, progress="auto"):
    """Build and tag the unit and integration test docker images"""
    # Always login to RAD account to pull base image
    ecr_login(c, account=RAD_ACCOUNT)
    if account != RAD_ACCOUNT:
        ecr_login(c, account=account)

    repo_url = ecr_repo_url(account)
    # Pull the child image first to minimize layers built, typically not locally because it has been built already
    if pull_first:
        _pull_image(c, image=RAD_IMAGE, tag=TAG_BASE, repo_url=repo_url, warn=True)
    build_image(
        c,
        dockerfile="Dockerfile",
        image=RAD_IMAGE,
        tag=TAG_BASE,
        env_version=ENV_VERSION,
        repo_url=repo_url,
        use_access_key=use_access_key,
        progress=progress,
    )


@typechecked
def push_image(c: invoke.Context, image: str, tag: str, repo_url: str) -> None:
    c.run(f"docker push {repo_url}/{image}:{tag}", pty=True, echo=True)


@invoke.task
def push_images(c, account=RAD_ACCOUNT):
    """Push the integration test docker image to ECR"""
    repo_url = ecr_repo_url(account)
    ecr_login(c, account=account)
    push_image(c, image=RAD_IMAGE, tag=TAG_BASE, repo_url=repo_url)


@invoke.task
def lint(c, check=False):
    """Run black and isort code formatters, optionally just check if they need to run"""
    check_error_msg = "Run `inv -r intent/multiagents format` to reformat the code"
    check_arg = "--check" if check else ""

    failed = False  # catch failures and exit with 1 after all checks
    black_cmd = f"black {check_arg} ."
    if not c.run(black_cmd, warn=True, echo=True) and check:
        print(check_error_msg)
        failed = True

    isort_cmd = f"isort {check_arg} ."
    if not c.run(isort_cmd, warn=True, echo=True) and check:
        print(check_error_msg)
        failed = True

    if failed:
        sys.exit(1)


@typechecked
def as_collection() -> invoke.Collection:
    namespace = invoke.Collection("hid")
    namespace.add_task(ecr_login)
    namespace.add_task(build_images)
    namespace.add_task(push_images)
    namespace.add_task(lint)
    return namespace
