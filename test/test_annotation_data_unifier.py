from pathlib import Path
from unittest.mock import MagicMock, patch

import pandas as pd
import pytest

from scripts.annotation_data_unifier import AnnotationDataUnifier


@pytest.fixture
def sample_config():
    """
    Fixture for a sample configuration dictionary.

    Returns:
        dict: A dictionary containing sample configuration data.
    """
    return {
        "track_map_path": "test/resources/track.csv",
        "trial_dir": "/test/trials_final/",
        "output_dir": "/test/output_dir/",
        "video_dir": "/test/video_dir/",
        "fps": 5,
        "dpi": 300,
        "time_window": 10,
        "input_dir": "/test/input_dir",
        "trial_version": 2,
    }


@pytest.fixture
def sample_s3_uri():
    """
    Fixture for a sample S3 URI string.

    Returns:
        str: A sample S3 URI for testing.
    """
    return "s3://test-bucket/test_video/P001_trial1_[100.5,200.5]_[5,5].mp4"


@pytest.fixture
def annotation_data_unifier(sample_config: dict, sample_s3_uri: str):
    """
    Fixture to create an instance of AnnotationDataUnifier.

    Args:
        sample_config (dict): Sample configuration fixture.
        sample_s3_uri (str): Sample S3 URI fixture.

    Returns:
        AnnotationDataUnifier: Instance of the AnnotationDataUnifier class.
    """
    return AnnotationDataUnifier(sample_s3_uri, sample_config)


@patch("pandas.read_csv")
def test_mapping_annotation_videos(mock_read_csv, annotation_data_unifier: AnnotationDataUnifier):
    """
    Test mapping of annotation videos to trial data.

    Args:
        mock_read_csv (MagicMock): Mock for pandas' read_csv function.
        annotation_data_unifier (AnnotationDataUnifier): Instance of AnnotationDataUnifier to test.
    """
    # Mocking the video source DataFrame
    mock_read_csv.return_value = pd.DataFrame({"video_source": [annotation_data_unifier.s3_uri]})

    # Creating a sample trial DataFrame
    trial_df = pd.DataFrame(
        {"carla_objects log time": [10, 101, 150, 200], "other_data": [0, 10, 20, 30]}
    )

    annotation_data_unifier.trial_df = trial_df
    annotation_data_unifier.config["end_at_coaching"] = False

    # Parsing the s3 URI to get the timestamps and other details
    annotation_data_unifier.parse_s3_uri()
    start_time, end_time = annotation_data_unifier.get_trajectory_window()

    annotation_data_unifier.filtered_trajectory_data = (
        annotation_data_unifier.fetch_trajectory_data(start_time, end_time)
    )

    assert len(annotation_data_unifier.filtered_trajectory_data) == 3
    assert (
        annotation_data_unifier.filtered_trajectory_data.iloc[0]["carla_objects log time"] == 101
    )


def test_incomplete_s3_uri(annotation_data_unifier: AnnotationDataUnifier):
    """
    Test case to handle an incomplete S3 URI.

    Args:
        annotation_data_unifier (AnnotationDataUnifier): Instance of AnnotationDataUnifier to test.
    """
    incomplete_s3_uri = "s3://test-bucket/test_video/P001_trial1_[100.5]_[]_.mp4"
    annotation_data_unifier.s3_uri = incomplete_s3_uri

    with pytest.raises(Exception):
        annotation_data_unifier.parse_s3_uri()


def test_start_end_times_out_of_bounds(annotation_data_unifier: AnnotationDataUnifier):
    """
    Test case to handle out-of-bounds start and end times in the S3 URI.

    Args:
        annotation_data_unifier (AnnotationDataUnifier): Instance of AnnotationDataUnifier to test.
    """
    start_end_times_out_of_bounds_uri = (
        "s3://test-bucket/test_video/P001_trial1_[10,20]_[10,50].mp4"
    )
    annotation_data_unifier.s3_uri = start_end_times_out_of_bounds_uri

    annotation_data_unifier.parse_s3_uri()

    # Setting trial DataFrame with times out of bounds
    annotation_data_unifier.trial_df = pd.DataFrame(
        {"carla_objects log time": [200, 300], "same_data": [30, 40]}
    )
    annotation_data_unifier.config["end_at_coaching"] = False

    # Getting the trajectory window from the parsed URI
    start_time, end_time = annotation_data_unifier.get_trajectory_window()

    # Fetching the filtered trajectory data
    annotation_data_unifier.filtered_trajectory_data = (
        annotation_data_unifier.fetch_trajectory_data(start_time, end_time)
    )

    # Assertion: Filtered data should be empty for out-of-bounds times
    assert (
        annotation_data_unifier.filtered_trajectory_data.empty
    ), "Filtered data should be empty for out-of-bounds start/end times"


def test_missing_start_time(annotation_data_unifier: AnnotationDataUnifier):
    """
    Test case to handle a missing start time in the S3 URI.

    Args:
        annotation_data_unifier (AnnotationDataUnifier): Instance of AnnotationDataUnifier to test.
    """
    missing_start_uri = "s3://test-bucket/test_video/P001_trial1_[10,20]_[].mp4"
    annotation_data_unifier.s3_uri = missing_start_uri

    # Expecting an exception due to missing start time in the S3 URI
    with pytest.raises(Exception):
        annotation_data_unifier.parse_s3_uri()


def test_generate_parquet_path(annotation_data_unifier: AnnotationDataUnifier):
    """
    Test case to verify the correct generation of a parquet path from the S3 URI.

    Args:
        annotation_data_unifier (AnnotationDataUnifier): Instance of AnnotationDataUnifier to test.
    """

    annotation_data_unifier.parse_s3_uri()

    # Expceted path format
    expected_parquet_path = (
        Path(annotation_data_unifier.config["trial_dir"])
        / "P001-trial_1"
        / "P001-trial_1_v2_with_all_annotations_and_metrics_with_instruction_category.trial.parquet"
    )

    # Assertion to check if the generated path matches the expected path
    assert annotation_data_unifier.generate_parquet_path() == expected_parquet_path
