import sys
from pathlib import Path

import pytest

import scripts.aic.create_and_annotate_aic_23d04
import scripts.sdm.create_and_annotate_sdm_trials


class TestCreateAndAnnotateSDMScripts:
    def test_sdm_script_run_without_errors(self):
        # Run the script and assure no errors
        current_dir = Path(__file__).resolve().parent
        hid_common_dir = current_dir.parent
        sys.argv = [
            "scripts/sdm/create_and_annotate_sdm_trials.py",
            "--mcap-input-folder",
            "test/resources/sdm_trial",
            "--track-map-csv",
            "test/resources/track.csv",
            "--plot-animation",
            "False",
            "--trials-dir",
            f"{hid_common_dir}/shared_autonomy/sdm_trials/",
            "--process-trials",
            "False",
        ]
        try:
            scripts.sdm.create_and_annotate_sdm_trials.main()
        except Exception as e:
            pytest.fail("script resulted in an error while running")

    def test_sdm_script_run_without_errors_with_cache(self):
        # Run the script and assure no errors
        current_dir = Path(__file__).resolve().parent
        hid_common_dir = current_dir.parent
        sys.argv = [
            "scripts/sdm/create_and_annotate_sdm_trials.py",
            "--mcap-input-folder",
            "test/resources/sdm_trial",
            "--track-map-csv",
            "test/resources/track.csv",
            "--plot-animation",
            "True",
            "--trials-dir",
            f"{hid_common_dir}/shared_autonomy/sdm_trials/",
        ]
        try:
            scripts.sdm.create_and_annotate_sdm_trials.main()
        except Exception as e:
            pytest.fail("script resulted in an error while running")

    def test_sdm_destination_files(self):
        current_dir = Path(__file__).resolve().parent
        hid_common_dir = current_dir.parent
        folderpath = (
            hid_common_dir / "shared_autonomy" / "sdm_trials" / "split_sdm-trial_0"
        ).expanduser()
        files = [
            "split_sdm-trial_0_anim_v1.mp4",
            "split_sdm-trial_0--spinout_v2_2fc6a131.parquet",
            "split_sdm-trial_0--ego_ado_proximity_v1_0973eea9.parquet",
            "split_sdm-trial_0--map_segment_ids_v1_a3b7564d.parquet",
            "split_sdm-trial_0_v2.trial.parquet",
            "split_sdm-trial_0_ego_center_anim_v1.mp4",
            "split_sdm-trial_0--overtake_v1_da2697cb.parquet",
        ]

        if not folderpath.exists():
            pytest.fail("Expected destination not created")

        missing_files = []
        for file_name in files:
            file_path = folderpath / file_name
            if not file_path.exists():
                missing_files.append(file_name)

        if missing_files:
            pytest.fail(f"Following files are missing {' '.join(missing_files)}")
