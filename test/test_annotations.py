import pandas as pd
import pytest

from util.annotations import (
    EgoAdoProximityAnnotation,
    EgoCenterAnimationAnnotation,
    LatLongAnnotation,
    MapSegmentAnnotation,
    OutOfBoundsAnnotation,
    OvertakeAnnotation,
    PlotAnimationAnnotation,
    SpinoutAnnotation,
)
from util.trial import Trial


class TestOvertakeAnnotationClass:
    def setup_method(self):
        self.obj = OvertakeAnnotation()
        self.trial = Trial()
        self.trial.read("test/resources/P506_lime2023_12_05-10_03_38_0-trial_0_v2.trial.parquet")
        self.track_map = pd.read_csv("test/resources/track.csv")

    def test_filter_name(self):
        assert self.obj.FILTER_NAME == "overtake"

    def test_get_hash_param_keys(self):
        params = {"key": "value"}
        keys = self.obj._get_hash_param_keys(params)
        assert keys == ["dist_threshold"]

    def test_filter_impl_safe_run(self):
        params = {"dist_threshold": 8.0}
        try:
            result = self.obj._filter_impl(self.trial, self.track_map, params)
        except Exception as e:
            pytest.fail(f"Unexpected error while running a filter {e}")


class TestEgoAdoProximityAnnotationClass:
    def setup_method(self):
        self.obj = EgoAdoProximityAnnotation()
        self.trial = Trial()
        self.trial.read("test/resources/P113_2023_10_30-08_18_22_0-trial_0_v2.trial.parquet")
        self.track_map = pd.read_csv("test/resources/track.csv")

    def test_filter_name(self):
        assert self.obj.FILTER_NAME == "ego_ado_proximity"

    def test_get_hash_param_keys(self):
        params = {"key": "value"}
        keys = self.obj._get_hash_param_keys(params)
        assert keys == ["dist_threshold"]

    def test_filter_impl_safe_run(self):
        params = {"dist_threshold": 8.0}
        try:
            result = self.obj._filter_impl(self.trial, self.track_map, params)
        except Exception as e:
            pytest.fail("Unexpected error while running a filter")


class TestMapSegmentAnnotationClass:
    def setup_method(self):
        self.obj = MapSegmentAnnotation()
        self.trial = Trial()
        self.trial.read("test/resources/P113_2023_10_30-08_18_22_0-trial_0_v2.trial.parquet")
        self.track_map = pd.read_csv("test/resources/track.csv")

    def test_filter_name(self):
        assert self.obj.FILTER_NAME == "map_segment_ids"

    def test_get_hash_param_keys(self):
        params = {"key": "value"}
        keys = self.obj._get_hash_param_keys(params)
        assert keys == ["map_name"]

    def test_filter_impl_safe_run(self):
        params = {"map_name": "thunderhill_west"}
        try:
            result = self.obj._filter_impl(self.trial, self.track_map, params)
        except Exception as e:
            pytest.fail("Unexpected error while running a filter")


class TestLatLongAnnotationClass:
    def setup_method(self):
        self.dataframe = pd.DataFrame(
            {
                "ego_x": [0, 2, 3, 3, 4],
                "ego_y": [2, 2, 3, 5, 6],
                "carla_objects log time": [1, 2, 3, 4, 5],
            }
        )
        self.obj = LatLongAnnotation()
        self.trial = Trial(dataframe=self.dataframe)
        self.track_map = pd.read_csv("test/resources/track.csv")

    def test_filter_name(self):
        assert self.obj.FILTER_NAME == "lat_long_coordinates"

    def test_get_hash_param_keys(self):
        params = {"key": "value"}
        keys = self.obj._get_hash_param_keys(params)
        assert keys == ["map_name"]

    def test_filter_impl_safe_run(self):
        params = {"map_name": "thunderhill_west"}
        try:
            result = self.obj._filter_impl(self.trial, self.track_map, params)
        except Exception as e:
            pytest.fail("Unexpected error while running a filter")


class TestOutOfBoundsAnnotationClass:
    def setup_method(self):
        self.obj = OutOfBoundsAnnotation()
        self.trial = Trial()
        self.trial.read("test/resources/P506_lime2023_12_05-10_03_38_0-trial_0_v2.trial.parquet")
        self.track_map = pd.read_csv("test/resources/track.csv")

    def test_filter_name(self):
        assert self.obj.FILTER_NAME == "out_of_bounds"

    def test_get_hash_param_keys(self):
        params = {"key": "value"}
        keys = self.obj._get_hash_param_keys(params)
        assert keys == ["map_name"]

    def test_filter_impl_safe_run(self):
        params = {"map_name": "thunderhill_west"}
        try:
            result = self.obj._filter_impl(self.trial, self.track_map, params)
        except Exception as e:
            pytest.fail("Unexpected error while running a filter")


class TestSpinoutAnnotationClass:
    def setup_method(self):
        self.obj = SpinoutAnnotation()
        self.trial = Trial()
        self.trial.read("test/resources/P506_lime2023_12_05-10_03_38_0-trial_0_v2.trial.parquet")
        self.track_map = pd.read_csv("test/resources/track.csv")

    def test_filter_name(self):
        assert self.obj.FILTER_NAME == "spinout"

    def test_get_hash_param_keys(self):
        params = {"key": "value"}
        keys = self.obj._get_hash_param_keys(params)
        assert keys == ["slip_thresh"]

    def test_filter_impl_safe_run(self):
        params = {"slip_thresh": 0.5}
        try:
            result = self.obj._filter_impl(self.trial, self.track_map, params)
        except Exception as e:
            pytest.fail("Unexpected error while running a filter")


class TestPlotAnimationAnnotationClass:
    def setup_method(self):
        self.obj = PlotAnimationAnnotation()
        self.trial = Trial()
        self.trial.read("test/resources/P506_lime2023_12_05-10_03_38_0-trial_0_v2.trial.parquet")
        self.trial.set_dataframe(
            self.trial.dataframe.head(1000)
        )  # Taking 1000 rows for making a viz
        self.track_map = pd.read_csv("test/resources/track.csv")

    def test_filter_name(self):
        assert self.obj.FILTER_NAME == "animation"

    def test_get_hash_param_keys(self):
        params = {"key": "value"}
        keys = self.obj._get_hash_param_keys(params)
        assert keys == ["fps", "dpi"]

    def test_filter_without_cones(self):
        params = {"fps": 5, "dpi": 300, "anim_suffix": "mp4"}
        try:
            result = self.obj.filter(self.trial, self.track_map, params, {})
        except Exception as e:
            pytest.fail("Unexpected error while running a filter without cones")

    def test_filter_with_cones(self):
        params = {"fps": 5, "dpi": 300, "anim_suffix": "mp4", "plot_cones": True}
        try:
            result = self.obj.filter(self.trial, self.track_map, params, {})
        except Exception as e:
            pytest.fail("Unexpected error while running a filter with cones")


class TestEgoCenterAnimationAnnotationClass:
    def setup_method(self):
        self.obj = EgoCenterAnimationAnnotation()
        self.trial = Trial()
        self.trial.read("test/resources/P601-trial_1_v2.trial.parquet")
        self.trial.set_dataframe(
            self.trial.dataframe.head(1000)
        )  # Taking 1000 rows for making a viz
        self.track_map = pd.read_csv("test/resources/track.csv")

    def test_filter_name(self):
        assert self.obj.FILTER_NAME == "ego_centric_animation"

    def test_get_hash_param_keys(self):
        params = {"key": "value"}
        keys = self.obj._get_hash_param_keys(params)
        assert keys == ["fps", "dpi"]

    def test_filter_without_cones(self):
        params = {"fps": 5, "dpi": 300, "anim_suffix": "mp4", "visualize_subtitles": True}
        try:
            result = self.obj.filter(self.trial, self.track_map, params, {})
        except Exception as e:
            pytest.fail("Unexpected error while running a filter without cones")

    def test_filter_with_cones(self):
        params = {
            "fps": 5,
            "dpi": 300,
            "anim_suffix": "mp4",
            "plot_cones": True,
            "visualize_subtitles": True,
        }
        try:
            result = self.obj.filter(self.trial, self.track_map, params, {})
        except Exception as e:
            pytest.fail("Unexpected error while running a filter with cones")
