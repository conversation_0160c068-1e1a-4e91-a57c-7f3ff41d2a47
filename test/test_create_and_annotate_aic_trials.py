import sys
from pathlib import Path

import pytest

import scripts.aic.create_and_annotate_aic_23d04
import scripts.sdm.create_and_annotate_sdm_trials


class TestCreateAndAnnotateAICScripts:
    def test_aic_script_run_without_errors(self):
        # Run the script and assure no errors
        current_dir = Path(__file__).resolve().parent
        hid_common_dir = current_dir.parent
        sys.argv = [
            "scripts/aic/create_and_annotate_aic_23d04.py",
            "--mcap-input-folder",
            "test/resources/aic_trial/create_cache",
            "--track-map-csv",
            "test/resources/track.csv",
            "--plot-animation",
            "False",
            "--trials-dir",
            f"{hid_common_dir}/shared_autonomy/aic_trials_large/",
            "--process-trials",
            "False",
        ]
        try:
            scripts.aic.create_and_annotate_aic_23d04.main()
            # Change this cache file details when input MCAP file name/location is changed.
            cache_file = "log_cache/d9ef5a0e452ba2e1aee8a93a305f7534cafba1d7aeddbebc22fb9b920fe67067106cda9e0e524202bf088c58e18a4bc40d02e3b5ac35f7c20f980d8938cf8424.parquet"
            assert Path(cache_file).exists()
        except Exception as e:
            pytest.fail("script resulted in an error while running")

    def test_aic_script_run_without_errors_with_cache(self):
        # Run the script and assure no errors
        current_dir = Path(__file__).resolve().parent
        hid_common_dir = current_dir.parent
        sys.argv = [
            "scripts/aic/create_and_annotate_aic_23d04.py",
            "--mcap-input-folder",
            "test/resources/aic_trial/process_trials",
            "--track-map-csv",
            "test/resources/track.csv",
            "--plot-animation",
            "False",
            "--trials-dir",
            f"{hid_common_dir}/shared_autonomy/aic_trials_large/",
        ]
        try:
            scripts.aic.create_and_annotate_aic_23d04.main()
        except Exception as e:
            pytest.fail("script resulted in an error while running")

    def test_aic_destination_files(self):
        current_dir = Path(__file__).resolve().parent
        hid_common_dir = current_dir.parent
        for folderpath in ["split_aic-trial_0"]:
            path = (
                hid_common_dir / "shared_autonomy" / "aic_trials_large" / folderpath
            ).expanduser()
            files = [
                f"{folderpath}--spinout_v2_2fc6a131.parquet",
                f"{folderpath}--map_segment_ids_v1_a3b7564d.parquet",
                f"{folderpath}_v2.trial.parquet",
                f"{folderpath}--lat_long_coordinates_v1_a3b7564d.parquet",
                f"{folderpath}--out_of_bounds_v1_a3b7564d.parquet",
            ]

            if not path.exists():
                pytest.fail("Expected destination not created")

            missing_files = []
            for file_name in files:
                file_path = path / file_name
                if not file_path.exists():
                    missing_files.append(file_name)

            if missing_files:
                pytest.fail(f"Following files are missing {' '.join(missing_files)}")
