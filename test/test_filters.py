import numpy as np
import pandas as pd
import pytest

from util.filters.trajectory_filters import (
    MAP_SEGMENT_ID_KEY,
    RESULT_KEY_TIMESTAMPS,
    annotate_lat_long_coordinates,
    detect_deceleration,
    detect_ego_ado_proximity,
    detect_out_of_bounds,
    detect_overtake,
    detect_spinout,
    mark_map_segment_ids,
    mark_trial_ids,
)


class TestDetectOvertake:
    def setup_method(self):
        self.vehicle_data_straight = pd.DataFrame(
            {
                "ego_x": [1.0, 1.99, 3.0, 4.0],
                "ego_y": [1.0, 2.0, 2.0, 2.0],
                "carla_objects log time": [1, 2, 3, 4],
                "ego_orientation_x": [0.0, 0.0, 0.0, 0.0],
                "ego_orientation_y": [0.0, 0.0, 0.0, 0.0],
                "ego_orientation_z": [0.0, 0.0, 0.0, 0.0],
                "ego_orientation_w": [1.0, 1.0, 1.0, 1.0],
                "ado_x": [2.0, 2.0, 2.0, 2.0],
                "ado_y": [1.0, 1.0, 1.0, 1.0],
            }
        )
        # Vehicle orientation at 3pi/4 (or 135 deg)
        self.vehicle_data_diagonal = pd.DataFrame(
            {
                "ego_x": [0.0, -0.99, -2.0, -3.0],
                "ego_y": [0.0, 1.0, 2.0, 3.0],
                "carla_objects log time": [1, 2, 3, 4],
                "ego_orientation_x": [0.0, 0.0, 0.0, 0.0],
                "ego_orientation_y": [0.0, 0.0, 0.0, 0.0],
                "ego_orientation_z": [0.92, 0.92, 0.92, 0.92],
                "ego_orientation_w": [0.38, 0.38, 0.38, 0.38],
                "ado_x": [-4.0, -4.0, -4.0, -4.0],
                "ado_y": [1.0, 1.0, 1.0, 1.0],
            }
        )

    def test_overtake_right_straight(self):
        annotation_df = pd.DataFrame(
            {
                RESULT_KEY_TIMESTAMPS: self.vehicle_data_straight["carla_objects log time"].values,
                "overtake": [False, False, True, True],
                "overtake_side": ["", "", "right", "right"],
            }
        )
        expected_result = {"annotation_df": annotation_df, "additional_dict": {}}
        result = detect_overtake(self.vehicle_data_straight, dist_threshold=8.0)
        self.check_results_equal(result, expected_result)

    def test_overtake_left_straight(self):
        vehicle_data_right = self.vehicle_data_straight
        vehicle_data_right["ego_y"] = [1.0, 0.0, 0.0, 0.0]
        annotation_df = pd.DataFrame(
            {
                RESULT_KEY_TIMESTAMPS: self.vehicle_data_straight["carla_objects log time"].values,
                "overtake": [False, False, True, True],
                "overtake_side": ["", "", "left", "left"],
            }
        )
        expected_result = {"annotation_df": annotation_df, "additional_dict": {}}
        result = detect_overtake(vehicle_data_right, dist_threshold=8.0)
        self.check_results_equal(result, expected_result)

    def test_overtake_left_diagonal(self):
        annotation_df = pd.DataFrame(
            {
                RESULT_KEY_TIMESTAMPS: self.vehicle_data_diagonal["carla_objects log time"].values,
                "overtake": [False, False, False, True],
                "overtake_side": ["", "", "", "left"],
            }
        )
        expected_result = {"annotation_df": annotation_df, "additional_dict": {}}
        result = detect_overtake(self.vehicle_data_diagonal, dist_threshold=8.0)
        self.check_results_equal(result, expected_result)

    def test_overtake_right_diagonal(self):
        vehicle_data_right = self.vehicle_data_diagonal
        vehicle_data_right["ado_x"] = [-1.0, -1.0, -1.0, -1.0]
        vehicle_data_right["ado_y"] = [4.0, 4.0, 4.0, 4.0]
        annotation_df = pd.DataFrame(
            {
                RESULT_KEY_TIMESTAMPS: self.vehicle_data_diagonal["carla_objects log time"].values,
                "overtake": [False, False, False, True],
                "overtake_side": ["", "", "", "right"],
            }
        )
        expected_result = {"annotation_df": annotation_df, "additional_dict": {}}
        result = detect_overtake(vehicle_data_right, dist_threshold=8.0)
        self.check_results_equal(result, expected_result)

    def test_no_overtake(self):
        vehicle_data_no_overtake = self.vehicle_data_straight
        vehicle_data_no_overtake["ado_x"] = [2.0, 3.0, 4.0, 5.0]
        annotation_df = pd.DataFrame(
            {
                RESULT_KEY_TIMESTAMPS: self.vehicle_data_straight["carla_objects log time"].values,
                "overtake": [False, False, False, False],
                "overtake_side": ["", "", "", ""],
            }
        )
        expected_result = {"annotation_df": annotation_df, "additional_dict": {}}
        result = detect_overtake(vehicle_data_no_overtake, dist_threshold=8.0)
        self.check_results_equal(result, expected_result)

    def test_detect_overtake_missing_ado_data(self):
        # Remove 'ado_x' and 'ado_y' columns
        vehicle_data_missing = self.vehicle_data_straight.drop(["ado_x", "ado_y"], axis=1)
        expected_result = {"annotation_df": pd.DataFrame(), "additional_dict": {}}
        result = detect_overtake(vehicle_data_missing, dist_threshold=8.0)
        assert expected_result["annotation_df"].equals(result["annotation_df"])
        assert expected_result["additional_dict"] == result["additional_dict"]

    def check_results_equal(self, result, expected_result):
        assert result["additional_dict"] == expected_result["additional_dict"]
        assert np.array_equal(
            result["annotation_df"][RESULT_KEY_TIMESTAMPS].values,
            expected_result["annotation_df"][RESULT_KEY_TIMESTAMPS].values,
        )
        assert np.array_equal(
            result["annotation_df"]["overtake"].values,
            expected_result["annotation_df"]["overtake"].values,
        )
        assert np.array_equal(
            result["annotation_df"]["overtake_side"].values,
            expected_result["annotation_df"]["overtake_side"].values,
        )


class TestDetectDeceleration:
    def setup_method(self):
        # deceleration at x
        self.vehicle_data_straight = pd.DataFrame(
            {
                "ego_x": [1.0, 14.9, 15.9005, 15.9006],
                "ego_y": [1.0, 1.0, 1.0, 1.0],
                "carla_objects log time": [1, 2, 3, 4],
            }
        )
        self.threshold = -0.6

    def test_detect_deceleration_at_x(self):
        vehicle_data_decc = self.vehicle_data_straight
        expected_result = {
            "annotation_df": {
                "deceleration": np.array([False, False, True, False]),
                RESULT_KEY_TIMESTAMPS: vehicle_data_decc["carla_objects log time"],
            },
            "additional_dict": {},
        }
        result = detect_deceleration(vehicle_data_decc, threshold=self.threshold)
        self.check_results_equal(result, expected_result)

    def test_detect_deceleration_at_x_none(self):
        vehicle_data_decc = self.vehicle_data_straight
        vehicle_data_decc["ego_x"] = [5.0, 14.9, 36.0, 66.5]
        expected_result = {
            "annotation_df": {
                "deceleration": np.array([False, False, False, False]),
                RESULT_KEY_TIMESTAMPS: vehicle_data_decc["carla_objects log time"],
            },
            "additional_dict": {},
        }
        result = detect_deceleration(vehicle_data_decc, threshold=self.threshold)
        self.check_results_equal(result, expected_result)

    def test_detect_deceleration_at_y(self):
        vehicle_data_decc = self.vehicle_data_straight.copy()
        vehicle_data_decc["ego_y"] = self.vehicle_data_straight["ego_x"]
        vehicle_data_decc["ego_x"] = self.vehicle_data_straight["ego_y"]
        expected_result = {
            "annotation_df": {
                "deceleration": np.array([False, False, True, False]),
                RESULT_KEY_TIMESTAMPS: vehicle_data_decc["carla_objects log time"],
            },
            "additional_dict": {},
        }
        result = detect_deceleration(vehicle_data_decc, threshold=self.threshold)
        self.check_results_equal(result, expected_result)

    def test_detect_deceleration_at_y_none(self):
        vehicle_data_decc = self.vehicle_data_straight
        vehicle_data_decc["ego_y"] = [5.0, 14.9, 36.0, 66.5]
        vehicle_data_decc["ego_x"] = self.vehicle_data_straight["ego_y"]
        expected_result = {
            "annotation_df": {
                "deceleration": np.array([False, False, False, False]),
                RESULT_KEY_TIMESTAMPS: vehicle_data_decc["carla_objects log time"],
            },
            "additional_dict": {},
        }
        result = detect_deceleration(vehicle_data_decc, threshold=self.threshold)
        self.check_results_equal(result, expected_result)

    def test_detect_deceleration_random_none(self):
        vehicle_data_decc = self.vehicle_data_straight
        vehicle_data_decc["ego_x"] = [5.0, 8.9, 10.0, 12.5]
        vehicle_data_decc["ego_y"] = [1.0, 5.0, 2.0, 4.0]
        expected_result = {
            "annotation_df": {
                "deceleration": np.array([False, False, False, False]),
                RESULT_KEY_TIMESTAMPS: vehicle_data_decc["carla_objects log time"],
            },
            "additional_dict": {},
        }
        result = detect_deceleration(vehicle_data_decc, threshold=self.threshold)
        self.check_results_equal(result, expected_result)

    def test_detect_deceleration_random(self):
        vehicle_data_decc = self.vehicle_data_straight
        vehicle_data_decc["ego_y"] = [1.0, 5.0, 4.9, 4.95]
        expected_result = {
            "annotation_df": {
                "deceleration": np.array([False, False, True, False]),
                RESULT_KEY_TIMESTAMPS: vehicle_data_decc["carla_objects log time"],
            },
            "additional_dict": {},
        }
        result = detect_deceleration(vehicle_data_decc, threshold=self.threshold)
        self.check_results_equal(result, expected_result)

    def check_results_equal(self, result, expected_result):
        assert expected_result.keys() == result.keys()
        assert np.array_equal(
            result["annotation_df"][RESULT_KEY_TIMESTAMPS].values,
            expected_result["annotation_df"][RESULT_KEY_TIMESTAMPS].values,
        )
        assert np.array_equal(
            result["annotation_df"]["deceleration"],
            expected_result["annotation_df"]["deceleration"],
        )
        assert result["additional_dict"] == expected_result["additional_dict"]


class TestDetectOutOfBounds:
    def setup_method(self):
        # Sets up a circular closed track
        self.track_data = pd.DataFrame(
            {
                "inner_edge/x": [2.0, 1.4, 0.0, -1.4, -2.0, -1.4, 0.0, 1.4, 2.0],
                "inner_edge/y": [0.0, 1.4, 2.0, 1.4, 0.0, -1.4, -2.0, -1.4, 0.0],
                "outer_edge/x": [4.0, 2.8, 0.0, -2.8, -4.0, -2.8, 0.0, 2.8, 4.0],
                "outer_edge/y": [0.0, 2.8, 4.0, 2.8, 0.0, -2.8, -4.0, -2.8, 0.0],
            }
        )

    def test_out_of_bound_outer(self):
        vehicle_data = pd.DataFrame(
            {
                "ego_x": [0.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0],
                "ego_y": [-2.5, -2.5, -3.5, -3.5, -3.5, -3.5, -3.5, -3.5],
                "carla_objects log time": [1, 2, 3, 4, 5, 6, 7, 8],
            }
        )
        annotation_df = pd.DataFrame(
            {
                RESULT_KEY_TIMESTAMPS: vehicle_data["carla_objects log time"].values,
                "out_of_bounds": [False, False, True, True, True, True, True, True],
                "out_of_bounds_distances": [
                    0.0,
                    0.0,
                    0.3282660821493065,
                    0.7221853807284743,
                    1.3892443989449808,
                    2.297862575045145,
                    3.217007605063203,
                    4.1361526350812605,
                ],
            }
        )
        expected_result = {"annotation_df": annotation_df, "additional_dict": {}}
        result = detect_out_of_bounds(vehicle_data, self.track_data)
        self.check_results_equal(result, expected_result)

    def test_out_of_bound_inner(self):
        vehicle_data = pd.DataFrame(
            {
                "ego_x": [0.0, 1.0, 2.0, 2.0, 1.0, 0.0, -1.0, -2.0],
                "ego_y": [-2.5, -2.5, -1.0, 0.0, 1.0, 1.5, 2.0, 1.5],
                "carla_objects log time": [1, 2, 3, 4, 5, 6, 7, 8],
            }
        )
        annotation_df = pd.DataFrame(
            {
                RESULT_KEY_TIMESTAMPS: vehicle_data["carla_objects log time"].values,
                "out_of_bounds": [False, False, False, False, True, True, False, False],
                "out_of_bounds_distances": [
                    0.0,
                    0.0,
                    0.0,
                    0.0,
                    0.5252257314388902,
                    0.4595725150090289,
                    0.0,
                    0.0,
                ],
            }
        )
        expected_result = {"annotation_df": annotation_df, "additional_dict": {}}
        result = detect_out_of_bounds(vehicle_data, self.track_data)
        self.check_results_equal(result, expected_result)

    def test_out_of_bound_both(self):
        vehicle_data = pd.DataFrame(
            {
                "ego_x": [0.0, 1.0, 2.0, 2.0, 1.0, 0.0, -1.5, -4.0],
                "ego_y": [-2.5, -2.5, -1.0, 0.0, 1.0, 3.5, 3.5, 3.0],
                "carla_objects log time": [1, 2, 3, 4, 5, 6, 7, 8],
            }
        )
        annotation_df = pd.DataFrame(
            {
                RESULT_KEY_TIMESTAMPS: vehicle_data["carla_objects log time"].values,
                "out_of_bounds": [False, False, False, False, True, False, True, True],
                "out_of_bounds_distances": [
                    0,
                    0.0,
                    0.0,
                    0.0,
                    0.5252257314388902,
                    0.0,
                    0.13130643285972268,
                    1.1817578957375032,
                ],
            }
        )
        expected_result = {"annotation_df": annotation_df, "additional_dict": {}}
        result = detect_out_of_bounds(vehicle_data, self.track_data)
        self.check_results_equal(result, expected_result)

    def check_results_equal(self, result, expected_result):
        assert result["additional_dict"] == expected_result["additional_dict"]
        assert np.array_equal(
            result["annotation_df"][RESULT_KEY_TIMESTAMPS].values,
            expected_result["annotation_df"][RESULT_KEY_TIMESTAMPS].values,
        )
        assert np.array_equal(
            result["annotation_df"]["out_of_bounds"].values,
            expected_result["annotation_df"]["out_of_bounds"].values,
        )
        assert np.array_equal(
            result["annotation_df"]["out_of_bounds_distances"].values,
            expected_result["annotation_df"]["out_of_bounds_distances"].values,
        )


class TestDetectEgoAdoProximity:
    def setup_method(self):
        self.vehicle_data = pd.DataFrame(
            {
                "ego_x": [2.0, 1.5, 2.0, 2.5],
                "ego_y": [1.5, 2.0, 2.5, 2.0],
                "ego_orientation_x": [0.0, 0.0, 0.0, 0.0],
                "ego_orientation_y": [0.0, 0.0, 0.0, 0.0],
                "ego_orientation_z": [0.0, 0.0, 0.0, 0.0],
                "ego_orientation_w": [1.0, 1.0, 1.0, 1.0],
                "ado_x": [2.0, 2.0, 2.0, 2.0],
                "ado_y": [2.0, 2.0, 2.0, 2.0],
                "carla_objects log time": [1, 2, 3, 4],
            }
        )

    def test_straight_line_proximity(self):
        vehicle_data = self.vehicle_data
        expected_result = {
            "in_proximity": [True, True, True, True],
            "ego_ado_dist": [0.5, 0.5, 0.5, 0.5],
            RESULT_KEY_TIMESTAMPS: vehicle_data["carla_objects log time"].values,
        }
        result = detect_ego_ado_proximity(vehicle_data, dist_threshold=1.0)
        self.check_results_equal(result["annotation_df"], expected_result)

    def test_diagonal_proximity(self):
        vehicle_data = self.vehicle_data
        vehicle_data["ego_x"] = [1.5, 1.5, 2.5, 2.5]
        vehicle_data["ego_y"] = [1.5, 2.5, 2.5, 1.5]
        expected_result = {
            "in_proximity": [True, True, True, True],
            "ego_ado_dist": [0.7071, 0.7071, 0.7071, 0.7071],
            RESULT_KEY_TIMESTAMPS: vehicle_data["carla_objects log time"].values,
        }
        result = detect_ego_ado_proximity(vehicle_data, dist_threshold=1.0)
        self.check_results_equal(result["annotation_df"], expected_result)

    def test_no_proximity(self):
        vehicle_data = self.vehicle_data
        vehicle_data["ego_x"] = [2.0, 1.0, 1.0, 4.0]
        vehicle_data["ego_y"] = [0.0, 1.0, 4.0, 2.0]
        expected_result = {
            "in_proximity": [False, False, False, False],
            "ego_ado_dist": [2.0, 1.414, 2.236, 2.0],
            RESULT_KEY_TIMESTAMPS: vehicle_data["carla_objects log time"].values,
        }
        result = detect_ego_ado_proximity(vehicle_data, dist_threshold=1.0)
        self.check_results_equal(result["annotation_df"], expected_result)

    def check_results_equal(self, result, expected_result):
        assert np.array_equal(
            result[RESULT_KEY_TIMESTAMPS],
            expected_result[RESULT_KEY_TIMESTAMPS],
        )
        assert np.allclose(
            result["ego_ado_dist"], expected_result["ego_ado_dist"], atol=1e-3
        ), "Ego-Ado Distance not equal."
        assert np.array_equal(
            result["in_proximity"],
            expected_result["in_proximity"],
        )


class TestMarkMapSegmentIds:
    def setup_method(self):
        # Smooth trajectory alongside the reference line
        self.vehicle_data_smooth = pd.DataFrame(
            {
                "ego_x": [0.1, 0.1, 0.1, 1.9, 2.9, 5],
                "ego_y": [0, 1, 1.9, 1.9, 0.9, 1.1],
                "carla_objects log time": [1, 2, 3, 4, 5, 6],
            }
        )
        # Abrupt trajectory alongwith a usecase where ego position is equidistant from two possible map segments
        self.vehicle_data_abrupt = pd.DataFrame(
            {
                "ego_x": [0.1, 0.1, 0.1, 1.9, 2.9, 4, 5, 5.5, 4.5],
                "ego_y": [0, 1, 1.9, 2.2, 0.9, 1, 1.1, 3.3, 4.5],
                "carla_objects log time": [1, 2, 3, 4, 5, 6, 7, 8, 9],
            }
        )
        self.track_data = pd.DataFrame(
            {
                "refline/x": [0, 0, 2, 3, 4, 5, 6, 4, 5],
                "refline/y": [0, 2, 2, 1, 1, 1, 3, 4, 5],
                "seq_num": [1, 1, 2, 3, 3, 4, 4, 4, 5],
            }
        )

    def test_map_segment_id_smooth(self):
        result = mark_map_segment_ids(self.vehicle_data_smooth, self.track_data)
        expected_result = {
            MAP_SEGMENT_ID_KEY: [1, 1, 1, 2, 3, 4],
            RESULT_KEY_TIMESTAMPS: self.vehicle_data_smooth["carla_objects log time"].values,
        }
        self.check_results_equal(result, expected_result)

    def test_map_segment_id_abrupt(self):
        result = mark_map_segment_ids(self.vehicle_data_abrupt, self.track_data)
        expected_result = {
            MAP_SEGMENT_ID_KEY: [1, 1, 1, 2, 3, 3, 4, 4, 4],
            RESULT_KEY_TIMESTAMPS: self.vehicle_data_abrupt["carla_objects log time"].values,
        }
        self.check_results_equal(result, expected_result)

    def check_results_equal(self, result, expected_result):
        assert np.array_equal(
            result["annotation_df"][RESULT_KEY_TIMESTAMPS],
            expected_result[RESULT_KEY_TIMESTAMPS],
        )
        assert np.array_equal(
            result["annotation_df"]["map_segment_ids"],
            expected_result[MAP_SEGMENT_ID_KEY],
        )


class TestDetectSpinout:
    def setup_method(self):
        self.vehicle_data_straight = pd.DataFrame(
            {
                "ego_x": [1.0, 20.0, 30.0, 40.0],
                "ego_y": [2.0, 2.0, 2.0, 2.0],
                "carla_objects log time": [1, 2, 3, 4],
                "ego_orientation_x": [0.0, 0.0, 0.0, 0.0],
                "ego_orientation_y": [0.0, 0.0, 0.0, 0.0],
                "ego_orientation_z": [0.0, 0.0, 0.0, 0.0],
                "ego_orientation_w": [1.0, 1.0, 1.0, 1.0],
            }
        )
        # Vehicle spins 180 deg to the left while traveling along the
        # original forward trajectory
        self.vehicle_half_spin_left = pd.DataFrame(
            {
                "ego_x": [1.0, 20.0, 30.0, 40.0],
                "ego_y": [2.0, 2.0, 2.0, 2.0],
                "carla_objects log time": [1, 2, 3, 4],
                "ego_orientation_x": [0.0, 0.0, 0.0, 0.0],
                "ego_orientation_y": [0.0, 0.0, 0.0, 0.0],
                "ego_orientation_z": [0.0, 0.3827, 0.9239, 1.0],
                "ego_orientation_w": [1.0, 0.9239, 0.3827, 0.0],
            }
        )

    def test_no_spin(self):
        annotation_df = pd.DataFrame(
            {
                RESULT_KEY_TIMESTAMPS: self.vehicle_data_straight["carla_objects log time"].values,
                "spinout": [False, False, False, False],
                "sideslip_ratio": [0.0, 0.0, 0.0, 0.0],
            }
        )
        expected_result = {"annotation_df": annotation_df, "additional_dict": {}}
        result = detect_spinout(self.vehicle_data_straight, slip_thresh=0.5)
        self.check_results_equal(result, expected_result)

    def test_spin_left(self):
        annotation_df = pd.DataFrame(
            {
                RESULT_KEY_TIMESTAMPS: self.vehicle_half_spin_left[
                    "carla_objects log time"
                ].values,
                "spinout": [False, True, True, False],
                "sideslip_ratio": [0.0, -1.0, 1.0, 0.0],
            }
        )
        expected_result = {"annotation_df": annotation_df, "additional_dict": {}}
        result = detect_spinout(self.vehicle_half_spin_left, slip_thresh=0.5)
        self.check_results_equal(result, expected_result)

    def test_spin_right(self):
        spin_right_data = self.vehicle_half_spin_left
        spin_right_data["ego_orientation_z"] = [0.0, -0.3827, -0.9239, 1.0]
        annotation_df = pd.DataFrame(
            {
                RESULT_KEY_TIMESTAMPS: spin_right_data["carla_objects log time"].values,
                "spinout": [False, True, True, False],
                "sideslip_ratio": [0.0, 1.0, -1.0, 0.0],
            }
        )
        expected_result = {"annotation_df": annotation_df, "additional_dict": {}}
        result = detect_spinout(spin_right_data, slip_thresh=0.5)
        self.check_results_equal(result, expected_result)

    def test_slow_velocity_slide(self):
        slow_slide_data = self.vehicle_data_straight
        slow_slide_data["ego_x"] = [1.0, 2.0, 3.0, 4.0]
        slow_slide_data["ego_orientation_z"] = [0.3827, 0.3827, 0.3827, 0.3827]
        slow_slide_data["ego_orientation_w"] = [0.9239, 0.9239, 0.9239, 0.9239]
        annotation_df = pd.DataFrame(
            {
                RESULT_KEY_TIMESTAMPS: slow_slide_data["carla_objects log time"].values,
                "spinout": [False, False, False, False],
                "sideslip_ratio": [0.0, -1.0, -1.0, -1.0],
            }
        )
        expected_result = {"annotation_df": annotation_df, "additional_dict": {}}
        result = detect_spinout(slow_slide_data, slip_thresh=0.5)
        self.check_results_equal(result, expected_result)

    def test_fishtail(self):
        fishtail_data = self.vehicle_half_spin_left
        fishtail_data["ego_orientation_z"] = [0.0, 0.3827, 0.0, -0.3827]
        fishtail_data["ego_orientation_w"] = [1.0, 0.9239, 1.0, 0.9239]
        annotation_df = pd.DataFrame(
            {
                RESULT_KEY_TIMESTAMPS: fishtail_data["carla_objects log time"].values,
                "spinout": [False, True, False, True],
                "sideslip_ratio": [0.0, -1.0, 0.0, 1.0],
            }
        )
        expected_result = {"annotation_df": annotation_df, "additional_dict": {}}
        result = detect_spinout(fishtail_data, slip_thresh=0.5)
        self.check_results_equal(result, expected_result)

    def check_results_equal(self, result, expected_result):
        assert result["additional_dict"] == expected_result["additional_dict"]
        assert np.array_equal(
            result["annotation_df"][RESULT_KEY_TIMESTAMPS].values,
            expected_result["annotation_df"][RESULT_KEY_TIMESTAMPS].values,
        )
        assert np.array_equal(
            result["annotation_df"]["spinout"].values,
            expected_result["annotation_df"]["spinout"].values,
        )
        assert np.allclose(
            result["annotation_df"]["sideslip_ratio"].values,
            expected_result["annotation_df"]["sideslip_ratio"].values,
            atol=1e-4,
        )


class TestMarkTrialIds:
    def setup_method(self):
        # Two proper trials ending with finish or gate-to-gate time
        self.vehicle_trial_data_valid = pd.DataFrame(
            {
                "condition": [
                    "start gate passed",
                    "finish gate passed",
                    "start gate passed",
                    "start gate passed",
                    "gate-to-gate time",
                    "scenario ended",
                ],
                "carla_objects log time": [1, 2, 3, 4, 5, 6],
            }
        )
        # Two trials, one being valid and another ending abruptly without a finish
        self.vehicle_trial_data_invalid = pd.DataFrame(
            {
                "condition": [
                    "start gate passed",
                    "start gate passed",
                    "finish gate passed",
                    "start gate passed",
                    "start gate passed",
                    "scenario ended",
                ],
                "carla_objects log time": [1, 2, 3, 4, 5, 6],
            }
        )

    def test_valid_trial(self):
        expected_result = {
            "annotation_df": {
                "trial_ids": np.array([0, 0, 1, 1, 1, np.nan]),
                RESULT_KEY_TIMESTAMPS: self.vehicle_trial_data_valid["carla_objects log time"],
            },
            "additional_dict": {},
        }
        result = mark_trial_ids(self.vehicle_trial_data_valid)
        self.check_results_equal(result, expected_result)

    def test_invalid_trial(self):
        expected_result = {
            "annotation_df": {
                "trial_ids": np.array([0, 0, 0, -1, -1, -1]),
                RESULT_KEY_TIMESTAMPS: self.vehicle_trial_data_invalid["carla_objects log time"],
            },
            "additional_dict": {},
        }
        result = mark_trial_ids(self.vehicle_trial_data_invalid)
        self.check_results_equal(result, expected_result)

    def check_results_equal(self, result, expected_result, equal_nan=True):
        assert expected_result.keys() == result.keys()
        assert np.array_equal(
            result["annotation_df"][RESULT_KEY_TIMESTAMPS].values,
            expected_result["annotation_df"][RESULT_KEY_TIMESTAMPS].values,
        )
        assert np.array_equal(
            result["annotation_df"]["trial_ids"],
            expected_result["annotation_df"]["trial_ids"],
            equal_nan=equal_nan,
        )
        assert result["additional_dict"] == expected_result["additional_dict"]


class TestAnnotateLatLongCoordinates:
    def setup_method(self):
        self.vehicle_data = pd.DataFrame(
            {
                "ego_x": [0, 2, 3, 3, 4],
                "ego_y": [2, 2, 3, 5, 6],
                "carla_objects log time": [1, 2, 3, 4, 5],
            }
        )
        self.track_data = pd.DataFrame(
            {"refline/x": [1, 1, 3, 4, 5], "refline/y": [1, 3, 4, 4, 3]}
        )

    # One test for testing on both sides of the track
    def test_lat_long_both_sides(self):
        vehicle_data = self.vehicle_data
        track_data = self.track_data

        expected_result = {
            "lateral_distances": [-1.0, 1.0, 0.838409, -1.076421, -1.776471],
            "longitudinal_coords": [1.0, 1.0, 3.788854, 4.328925, 4.980776],
            "longitudinal_speed": [0.0, 0.0, 2.788854, 0.540071, 0.651851],
            RESULT_KEY_TIMESTAMPS: vehicle_data["carla_objects log time"].values,
        }
        result = annotate_lat_long_coordinates(vehicle_data, track_data)
        self.check_results_equal(result["annotation_df"], expected_result)

    def check_results_equal(self, result, expected_result):
        assert np.array_equal(
            result[RESULT_KEY_TIMESTAMPS],
            expected_result[RESULT_KEY_TIMESTAMPS],
        )
        assert np.allclose(
            result["lateral_distances"], expected_result["lateral_distances"], atol=1e-6
        ), "Lateral distances not equal."
        assert np.allclose(
            result["longitudinal_coords"], expected_result["longitudinal_coords"], atol=1e-6
        ), "longitudinal coords not equal."
        assert np.allclose(
            result["longitudinal_speed"], expected_result["longitudinal_speed"], atol=1e-6
        ), "longitudinal speed not equal."


if __name__ == "__main__":
    pytest.main()
