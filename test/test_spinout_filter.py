#!/usr/bin/env python3
# A test script for spinout detectors
# TODO(guy.rosman): replace by a real unittest script, finalize the location of unittests between e.g. test_filters_script and test_spinout_filter

import os
import pickle as pickle
from datetime import time
from typing import Dict, Optional

import matplotlib.pyplot as plt
import numpy as np
import pytest

from data_sources.compact_sim.dataloading import (
    DATA_DICT_KEY_EPISODES,
    DATA_DICT_KEY_MAP,
    load_and_cache_compactsim_data,
)
from scripts.run_data_environment import parse_arguments
from util.filters.trajectory_filters import (
    DATA_DICT_KEY_SPINOUT,
    detect_spinout,
    filter_all_episodes,
)
from util.utility import (
    SEARCH_PARAMS_KEYS_ANNOTATION_KEY,
    SEARCH_PARAMS_KEYS_EPISODE_ID,
    search_episode_transitions,
)
from util.visualization.visualization import (
    VIS_PARAM_ANIM_OUTPUT_DIR,
    VIS_PARAM_FPS,
    VIS_PARAM_SHOW_COLUMNS,
    VIS_PARAM_TIME_WINDOW,
    visualize_timepoint,
)


def create_base_test_params() -> Dict:
    args = {}
    # TODO(guy.rosman): @xiongyi/@thomas, please replace the pathnames once we have a fixed test data setup.
    args["track_map_csv"] = "~/Downloads/Thunderhill_data/track.csv"
    args["test_data_folder"] = "~/tri-hid-data-shared-autonomy/unit_test_logs"
    args["mcap_input_folder"] = ""
    args["mcap_input_file"] = os.path.join(args["test_data_folder"], "spinout2", "spinout2_0.mcap")
    args["compactsim_cache_folder"] = "~/intent/compactsim_cache/"
    return args


@pytest.mark.skip(reason="TODO, fix this out-dated test")
def test_spinout_filter():
    args = create_base_test_params()
    data_dict = load_and_cache_compactsim_data(args, override=False)
    annotation_data_dict = {}

    # Run spinout detector for all episodes
    data_dict, annotation_data_dict = filter_all_episodes(
        data_dict, annotation_data_dict, detect_spinout, DATA_DICT_KEY_SPINOUT
    )

    episode_idx = 0
    panda_frame_key = list(data_dict[DATA_DICT_KEY_EPISODES].keys())[episode_idx]

    search_params = {
        SEARCH_PARAMS_KEYS_EPISODE_ID: panda_frame_key,
        SEARCH_PARAMS_KEYS_ANNOTATION_KEY: DATA_DICT_KEY_SPINOUT,
    }
    search_results = search_episode_transitions(
        search_params=search_params, annotation_data=annotation_data_dict
    )
    assert np.mean(search_results[panda_frame_key]["detected_timestamps"]) == pytest.approx(880, 3)
    assert len(search_results[panda_frame_key]["detected_timestamps"]) > 0
