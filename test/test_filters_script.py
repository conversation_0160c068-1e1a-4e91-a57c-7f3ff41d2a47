#!/usr/bin/env python3
# A test script to load/parse the data and run filters such as overtake and spinout detections
# TODO(guy.rosman): replace by a real unittest script

import argparse
import os
import pickle
from datetime import time
from typing import Dict, Optional

from data_sources.compact_sim.dataloading import (
    DATA_DICT_KEY_EPISODES,
    DATA_DICT_KEY_MAP,
    load_and_cache_compactsim_data,
)
from scripts.run_data_environment import parse_arguments
from util.filters.trajectory_filters import (
    DATA_DICT_KEY_LATLONG,
    DATA_DICT_KEY_OVERTAKE,
    DATA_DICT_KEY_PROXIMITY,
    DATA_DICT_KEY_SPINOUT,
    annotate_lat_long_coordinates,
    detect_ego_ado_proximity,
    detect_overtake,
    detect_spinout,
)
from util.utility import (
    SEARCH_PARAMS_KEYS_ANNOTATION_KEY,
    SEARCH_PARAMS_KEYS_EPISODE_ID,
    load_annotation,
    save_annotation,
    search_transitions,
)
from util.visualization.visualization import (
    VIS_PARAM_ANIM_OUTPUT_DIR,
    VIS_PARAM_FPS,
    VIS_PARAM_SHOW_COLUMNS,
    VIS_PARAM_SHOW_RACING_LINE,
    VIS_PARAM_TIME_WINDOW,
    visualize_timepoint,
)


def main():
    args = parse_arguments()
    args["mcap_input_file"] = "overtake_right_0.mcap"
    args["compactsim_cache_folder"] = "~/hid_common/util/filters/resources/test_data_cache"

    data_dict = load_and_cache_compactsim_data(args, override=False)
    annotation_data_dict = {}

    # Take the first episode data frame taken from the folder.
    for episode_key in data_dict[DATA_DICT_KEY_EPISODES].keys():
        panda_frame = data_dict[DATA_DICT_KEY_EPISODES][episode_key]
        track_map = data_dict[DATA_DICT_KEY_MAP]

        spinout_results = detect_spinout(panda_frame, track_map)
        latlong_coordinates = annotate_lat_long_coordinates(panda_frame, track_map)
        overtake_results = detect_overtake(panda_frame, track_map, dist_threshold=8.0)
        proximity_results = detect_ego_ado_proximity(panda_frame, dist_threshold=4.0)
        annotation_data_dict[episode_key] = {}
        annotation_data_dict[episode_key][DATA_DICT_KEY_SPINOUT] = spinout_results
        annotation_data_dict[episode_key][DATA_DICT_KEY_LATLONG] = latlong_coordinates
        annotation_data_dict[episode_key][DATA_DICT_KEY_OVERTAKE] = overtake_results
        annotation_data_dict[episode_key][DATA_DICT_KEY_PROXIMITY] = proximity_results

    params = {
        # VIS_PARAM_SHOW_COLUMNS: args.columns,
        VIS_PARAM_TIME_WINDOW: 20,
        VIS_PARAM_ANIM_OUTPUT_DIR: "./overtake_anim",
        VIS_PARAM_FPS: 5,
        VIS_PARAM_SHOW_RACING_LINE: False,
    }
    timepoint = 100

    visualize_timepoint(
        vehicle_data=panda_frame,
        reference_track_data=track_map,
        timepoint=timepoint,
        params=params,
    )

    search_params = {
        SEARCH_PARAMS_KEYS_EPISODE_ID: episode_key,
        SEARCH_PARAMS_KEYS_ANNOTATION_KEY: DATA_DICT_KEY_SPINOUT,
    }

    for annotation in [
        DATA_DICT_KEY_SPINOUT,
        DATA_DICT_KEY_LATLONG,
        DATA_DICT_KEY_OVERTAKE,
    ]:
        file_name = os.path.splitext(os.path.split(episode_key)[-1])[0] + "-" + annotation + ".pkl"
        save_annotation(
            args["compactsim_cache_folder"] / file_name,
            annotation_data_dict[episode_key][annotation],
        )
    # annotation_data_dict2 = load_annotation(args['annotation_file'])

    import IPython

    IPython.embed(header="check filter results")


if __name__ == "__main__":
    main()
