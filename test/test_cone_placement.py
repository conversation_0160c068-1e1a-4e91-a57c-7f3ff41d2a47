import pandas as pd

from util.utility import get_next_cone_placements


class TestNextConePlacement:
    def setup_method(self):
        self.df = pd.read_pickle(
            "test/resources/P506_lime2023_12_05-10_03_38_0-trial_0_v1.trial.pkl"
        )["dataframe"]
        self.track = pd.read_csv("test/resources/track.csv")
        self.cones = [
            [-682, -2, 1.7],  # end of straightaway
            [-700, -144, 4.4],  # turn 1 right
            [-615, -239, 12.0],  # turn 2 left
            [-912, -117, -1.48],  # turn 3 left
            [-938, -72, -1.65],  # turn 3 right
            [-990, 2, -4.1],  # turn 4 brake zone
            [-991.5, 6, -4.2],  # turn 4 brake zone
            [-974, 80, -3.9],  # turn 4 left
            [-935, 109, -2.6],  # turn 4 right
            [-855, 171, -2.5],  # turn 5 right
            [-835, 256, -5.5],  # turn 5 left
            [-834, 330, -7.6],  # turn 5 right 2
            [-849, 518, -10.7],  # turn 6 left
            [-767, 569, -10.6],  # turn 6 right
            [-684, 611.5, -13.0],  # turn 7 brake zone
            [-682, 611.5, -13.0],  # turn 7 brake zone
            [-615, 587, -11.8],  # turn 7 left
            [-673, 535, -10.67],  # turn 8 right straight
            [-773, 420, -5.3],  # turn 8 right apex
            [-780, 338, -6.7],  # turn 9 brake zone
            [-779, 335, -6.7],  # turn 9 brake zone
            [-747, 289, -5.4],  # turn 9 right
            [-645, 365, -4.45],  # turn 10 left
        ]

    def test_upcoming_left(self):
        df = self.df[:100]  # Grab an area visually where upcoming cone is on left
        expected_result = [0] * 100  # For left, the value is 0
        result = get_next_cone_placements(df, self.track, self.cones)
        assert result == expected_result

    def test_upcoming_right(self):
        df = self.df[3600:3700]  # Grab an area visually where upcoming cone is on right
        expected_result = [1] * 100  # For right, the value is 1
        result = get_next_cone_placements(df, self.track, self.cones)
        assert result == expected_result

    def test_alternate(self):
        df = self.df[5700:7000]
        expected_result = (
            [1] * 125 + [0] * 582 + [1] * 389 + [0] * 204
        )  # Alternate pattern check, picked visually from track
        result = get_next_cone_placements(df, self.track, self.cones)
        assert result == expected_result
