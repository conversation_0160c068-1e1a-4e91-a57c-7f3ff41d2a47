import numpy as np
import pandas as pd
import pytest

from util.utility import (
    build_bbox,
    check_3d_overlap,
    compute_intervehicle_metrics,
    compute_tangent_normal,
    flatten_bbox,
)


class TestComputeInterVehicleMetrics:
    def setup_method(self):
        # Vehicle orientation at 3pi/4, pi/4, pi/2, 0 at z (or 135, 45, 90 and 0 deg)
        self.vehicle_data = pd.DataFrame(
            {
                "ego_x": [0.0, 1.0, 2.0, 3.0],
                "ego_y": [1.0, 4.0, 5.0, 6.0],
                "carla_objects log time": [1, 2, 3, 4],
                "ego_orientation_x": [0.0, 0.0, 0.0, 0.0],
                "ego_orientation_y": [0.0, 0.0, 0.0, 0.0],
                "ego_orientation_z": [0.923880, 0.382683, 0.707107, 0.0],
                "ego_orientation_w": [0.382683, 0.923880, 0.707107, 1.0],
                "ado_x": [1.0, 2.0, 3.0, 4.0],
                "ado_y": [1.0, 4.0, 5.0, 6.0],
            }
        )

        self.ego_keys = [
            "ego_x",
            "ego_y",
            "carla_objects log time",
            "ego_orientation_x",
            "ego_orientation_y",
            "ego_orientation_z",
            "ego_orientation_w",
        ]

        self.ado_keys = ["ado_x", "ado_y"]

    def test_intervehicle_metrics(self):
        result = compute_intervehicle_metrics(self.vehicle_data, self.ego_keys, self.ado_keys)
        actual_dist = result[0].values
        actual_dot = result[1]
        actual_rotation_dist = result[2]
        expected_dist = np.ones(4)
        expected_dot = np.array([-0.707107, 0.707107, 0.0, 1.0])
        expected_rotation_dist = np.array(
            [
                [-0.707107, -0.707107, 0.0],
                [0.707107, -0.707107, 0.0],
                [0.0, -1.0, 0.0],
                [1.0, 0.0, 0.0],
            ]
        )

        assert np.array_equal(actual_dist, expected_dist), "Distances do not match."
        # Since we only wrote out the expected values to 6 significant figures, only
        # assert that they are close to that level.
        assert np.allclose(actual_dot, expected_dot, atol=1e-6), "Dot products do not match."
        assert np.allclose(
            actual_rotation_dist, expected_rotation_dist
        ), "Rotation distances do not match."


class TestComputeNormalTangent:
    def setup_method(self):
        # Sets a zigzag reference line
        self.track_data = pd.DataFrame(
            {"refline/x": [2, 2, 3, 5, 4], "refline/y": [2, 4, 5, 4, 2]}
        )

    def test_diagonal_lines(self):
        track_data = self.track_data
        resampled_arr = np.array(
            [
                [2, 2],
                [2, 3],
                [2, 4],
                [2.70710678, 4.70710678],
                [3.52394332, 4.73802834],
                [4.41837051, 4.29081475],
                [4.84360115, 3.6872023],
                [4.39638755, 2.79277511],
            ]
        )
        expected_result = {
            "resampled_line": resampled_arr,
            "tangent": np.array(
                [
                    [0, 1],
                    [0, 1],
                    [0.70710678, 0.70710678],
                    [0.99928426, 0.03782817],
                    [0.89442719, -0.4472136],
                    [0.57591573, -0.81750906],
                    [-0.4472136, -0.89442719],
                    [-0.4472136, -0.89442719],
                ]
            ),
            "normal": np.array(
                [
                    [1, -0],
                    [1, -0],
                    [0.70710678, -0.70710678],
                    [0.03782817, -0.99928426],
                    [-0.4472136, -0.89442719],
                    [-0.81750906, -0.57591573],
                    [-0.89442719, 0.4472136],
                    [-0.89442719, 0.4472136],
                ]
            ),
        }
        result = compute_tangent_normal(track_data["refline/x"], track_data["refline/y"])
        self.check_results_equal(result, expected_result)

    def test_non_diagonal_lines(self):
        # Sets a straight right angled path
        track_data = pd.DataFrame({"refline/x": [1, 1, 5, 5, 3], "refline/y": [1, 3, 3, 2, 2]})
        resampled_arr = np.array(
            [[1, 1], [1, 2], [1, 3], [2, 3], [3, 3], [4, 3], [5, 3], [5, 2], [4, 2]]
        )
        expected_result = {
            "resampled_line": resampled_arr,
            "tangent": np.array(
                [[0, 1], [0, 1], [1, 0], [1, 0], [1, 0], [1, 0], [0, -1], [-1, 0], [-1, 0]]
            ),
            "normal": np.array(
                [[1, 0], [1, 0], [0, -1], [0, -1], [0, -1], [0, -1], [-1, 0], [0, 1], [0, 1]]
            ),
        }
        result = compute_tangent_normal(track_data["refline/x"], track_data["refline/y"])
        self.check_results_equal(result, expected_result)

    def check_results_equal(self, result, expected_result):
        assert np.allclose(result["resampled_line"], expected_result["resampled_line"], atol=1e-08)
        assert np.allclose(result["tangent"], expected_result["tangent"], atol=1e-08)
        assert np.allclose(result["normal"], expected_result["normal"], atol=1e-08)


class TestBuildBbox:
    def setup_method(self):
        # Bbox orientation at -pi/4, pi/4, pi/2, 0 at z (or -45, 45, 90 and 0 deg)
        self.x = np.array([[0.0], [0.0], [0.0], [0.0]])
        self.y = np.array([[0.0], [0.0], [0.0], [0.0]])
        self.z = np.array([[0.0], [0.0], [0.0], [0.0]])
        # fmt: off
        self.quat = np.array(
            [
                [0.0, 0.0, -0.382683, 0.923880],
                [0.0, 0.0,  0.382683, 0.923880],
                [0.0, 0.0,  0.707107, 0.707107],
                [0.0, 0.0,       0.0,      1.0],
            ]
        )
        self.bbox_sides = np.array(
            [
                [2.0, 2.0, 2.0],
                [2.0, 2.0, 2.0],
                [2.0, 2.0, 2.0],
                [2.0, 2.0, 2.0],
            ]
        )
        self.cuboid_bbox_sides = np.array(
            [
                [3.0, 2.0, 2.0],
                [3.0, 2.0, 2.0],
                [3.0, 2.0, 2.0],
                [3.0, 2.0, 2.0],
            ]
        )
        self.bbox_no_rot = np.array(
            [
                [ 1.0,  1.0,  1.0],
                [ 1.0, -1.0,  1.0],
                [-1.0, -1.0,  1.0],
                [-1.0,  1.0,  1.0],
                [-1.0,  1.0, -1.0],
                [-1.0, -1.0, -1.0],
                [ 1.0, -1.0, -1.0],
                [ 1.0,  1.0, -1.0],
            ]
        )
        self.bbox_yaw_45 = np.array(
            [
                [      0.0,  1.414214,  1.0],
                [ 1.414214,       0.0,  1.0],
                [      0.0, -1.414214,  1.0],
                [-1.414214,       0.0,  1.0],
                [-1.414214,       0.0, -1.0],
                [      0.0, -1.414214, -1.0],
                [ 1.414214,       0.0, -1.0],
                [      0.0,  1.414214, -1.0],
            ]
        )
        self.bbox_yaw_90 = np.array(
            [
                [-1.0,  1.0,  1.0],
                [ 1.0,  1.0,  1.0],
                [ 1.0, -1.0,  1.0],
                [-1.0, -1.0,  1.0],
                [-1.0, -1.0, -1.0],
                [ 1.0, -1.0, -1.0],
                [ 1.0,  1.0, -1.0],
                [-1.0,  1.0, -1.0],
            ]
        )
        self.bbox_yaw_minus_45 = np.array(
            [
                [ 1.414214,       0.0,  1.0],
                [      0.0, -1.414214,  1.0],
                [-1.414214,       0.0,  1.0],
                [      0.0,  1.414214,  1.0],
                [      0.0,  1.414214, -1.0],
                [-1.414214,       0.0, -1.0],
                [      0.0, -1.414214, -1.0],
                [ 1.414214,       0.0, -1.0],
            ]
        )
        self.cuboid_no_rot = np.array(
            [
                [ 1.5,  1.0,  1.0],
                [ 1.5, -1.0,  1.0],
                [-1.5, -1.0,  1.0],
                [-1.5,  1.0,  1.0],
                [-1.5,  1.0, -1.0],
                [-1.5, -1.0, -1.0],
                [ 1.5, -1.0, -1.0],
                [ 1.5,  1.0, -1.0],
            ]
        )
        self.cuboid_yaw_45 = np.array(
            [
                [ 0.353554,  1.767767,  1.0],
                [ 1.767767,  0.353554,  1.0],
                [-0.353554, -1.767767,  1.0],
                [-1.767767, -0.353554,  1.0],
                [-1.767767, -0.353554, -1.0],
                [-0.353554, -1.767767, -1.0],
                [ 1.767767,  0.353554, -1.0],
                [ 0.353554,  1.767767, -1.0],
            ]
        )
        self.cuboid_yaw_90 = np.array(
            [
                [-1.0,  1.5,  1.0],
                [ 1.0,  1.5,  1.0],
                [ 1.0, -1.5,  1.0],
                [-1.0, -1.5,  1.0],
                [-1.0, -1.5, -1.0],
                [ 1.0, -1.5, -1.0],
                [ 1.0,  1.5, -1.0],
                [-1.0,  1.5, -1.0],
            ]
        )
        self.cuboid_yaw_minus_45 = np.array(
            [
                [ 1.767767, -0.353554,  1.0],
                [ 0.353554, -1.767767,  1.0],
                [-1.767767,  0.353554,  1.0],
                [-0.353554,  1.767767,  1.0],
                [-0.353554,  1.767767, -1.0],
                [-1.767767,  0.353554, -1.0],
                [ 0.353554, -1.767767, -1.0],
                [ 1.767767, -0.353554, -1.0],
            ]
        )
        self.nan_quat = np.empty(self.quat.shape)
        self.nan_quat.fill(np.nan)
        self.nan_bbox_sides = np.empty((1, 3))
        self.nan_bbox_sides.fill(np.nan)
        self.nan_bbox = np.empty(self.bbox_no_rot.shape)
        self.nan_bbox.fill(np.nan)
        self.nan_x = np.empty(self.x.shape)
        self.nan_x.fill(np.nan)
        self.nan_y = np.empty(self.y.shape)
        self.nan_y.fill(np.nan)
        self.nan_z = np.empty(self.z.shape)
        self.nan_z.fill(np.nan)
        # fmt: on

    def test_unit_bbox_at_origin(self):
        result = build_bbox(self.x, self.y, self.z, self.quat, self.bbox_sides)
        expected = np.concatenate(
            [
                np.expand_dims(self.bbox_yaw_minus_45, axis=0),
                np.expand_dims(self.bbox_yaw_45, axis=0),
                np.expand_dims(self.bbox_yaw_90, axis=0),
                np.expand_dims(self.bbox_no_rot, axis=0),
            ],
            axis=0,
        )

        self.check_results_equal(result, expected)

    def test_unit_bbox_offset(self):
        dx = 1.0
        dy = -1.0
        dz = 0.5
        x = self.x + dx
        y = self.y + dy
        z = self.z + dz
        result = build_bbox(x, y, z, self.quat, self.bbox_sides)
        expected = np.concatenate(
            [
                np.expand_dims(self.bbox_yaw_minus_45 + np.array([dx, dy, dz]), axis=0),
                np.expand_dims(self.bbox_yaw_45 + np.array([dx, dy, dz]), axis=0),
                np.expand_dims(self.bbox_yaw_90 + np.array([dx, dy, dz]), axis=0),
                np.expand_dims(self.bbox_no_rot + np.array([dx, dy, dz]), axis=0),
            ],
            axis=0,
        )

        self.check_results_equal(result, expected)

    def test_cuboid_bbox_at_origin(self):
        result = build_bbox(self.x, self.y, self.z, self.quat, self.cuboid_bbox_sides)
        expected = np.concatenate(
            [
                np.expand_dims(self.cuboid_yaw_minus_45, axis=0),
                np.expand_dims(self.cuboid_yaw_45, axis=0),
                np.expand_dims(self.cuboid_yaw_90, axis=0),
                np.expand_dims(self.cuboid_no_rot, axis=0),
            ],
            axis=0,
        )

        self.check_results_equal(result, expected)

    def test_cuboid_bbox_offset(self):
        dx = 1.0
        dy = -3.0
        dz = 0.5
        x = self.x + dx
        y = self.y + dy
        z = self.z + dz
        result = build_bbox(x, y, z, self.quat, self.cuboid_bbox_sides)
        expected = np.concatenate(
            [
                np.expand_dims(self.cuboid_yaw_minus_45 + np.array([dx, dy, dz]), axis=0),
                np.expand_dims(self.cuboid_yaw_45 + np.array([dx, dy, dz]), axis=0),
                np.expand_dims(self.cuboid_yaw_90 + np.array([dx, dy, dz]), axis=0),
                np.expand_dims(self.cuboid_no_rot + np.array([dx, dy, dz]), axis=0),
            ],
            axis=0,
        )

        self.check_results_equal(result, expected)

    def test_nan_bbox(self):
        result = build_bbox(self.nan_x, self.nan_y, self.nan_z, self.nan_quat, self.nan_bbox_sides)
        expected = np.concatenate(
            [
                np.expand_dims(self.nan_bbox, axis=0),
            ],
            axis=0,
        )

        self.check_results_equal(result, expected)

    def check_results_equal(self, result, expected):
        # Since we only wrote out the expected values to 6 significant figures, only
        # assert that they are close to that level.
        assert np.array_equal(
            np.isnan(result), np.isnan(expected)
        ), "Bounding Box vertices do not match."
        assert np.allclose(
            result[np.isnan(result) == False], expected[np.isnan(expected) == False], atol=1e-5
        ), "Bounding Box vertices do not match."


class TestCheck3DOverlap:
    def setup_method(self):
        # fmt: off
        self.bbox_at_origin = np.array(
            [
                [ 1.0,  1.0,  1.0],
                [ 1.0, -1.0,  1.0],
                [-1.0, -1.0,  1.0],
                [-1.0,  1.0,  1.0],
                [-1.0,  1.0, -1.0],
                [-1.0, -1.0, -1.0],
                [ 1.0, -1.0, -1.0],
                [ 1.0,  1.0, -1.0],
            ]
        )
        self.bbox_offset_by_1 = np.array(
            [
                [2.0,  1.0,  1.0],
                [2.0, -1.0,  1.0],
                [0.0, -1.0,  1.0],
                [0.0,  1.0,  1.0],
                [0.0,  1.0, -1.0],
                [0.0, -1.0, -1.0],
                [2.0, -1.0, -1.0],
                [2.0,  1.0, -1.0],
            ]
        )
        self.bbox_offset_by_3 = np.array(
            [
                [4.0,  1.0,  1.0],
                [4.0, -1.0,  1.0],
                [2.0, -1.0,  1.0],
                [2.0,  1.0,  1.0],
                [2.0,  1.0, -1.0],
                [2.0, -1.0, -1.0],
                [4.0, -1.0, -1.0],
                [4.0,  1.0, -1.0],
            ]
        )
        self.bbox_rotated = np.array(
            [
                [ 1.414214, -1.414214,  1.0],
                [-1.414214, -1.414214,  1.0],
                [-1.414214,  1.414214,  1.0],
                [ 1.414214,  1.414214,  1.0],
                [ 1.414214,  1.414214, -1.0],
                [-1.414214,  1.414214, -1.0],
                [-1.414214, -1.414214, -1.0],
                [ 1.414214, -1.414214, -1.0],
            ]
        )
        self.bbox_small = np.array(
            [
                [ 0.5,  0.5,  1.0],
                [ 0.5, -0.5,  1.0],
                [-0.5, -0.5,  1.0],
                [-0.5,  0.5,  1.0],
                [-0.5,  0.5, -1.0],
                [-0.5, -0.5, -1.0],
                [ 0.5, -0.5, -1.0],
                [ 0.5,  0.5, -1.0],
            ]
        )
        self.bbox_small_raised = np.array(
            [
                [ 0.5,  0.5,  6.0],
                [ 0.5, -0.5,  6.0],
                [-0.5, -0.5,  6.0],
                [-0.5,  0.5,  6.0],
                [-0.5,  0.5,  4.0],
                [-0.5, -0.5,  4.0],
                [ 0.5, -0.5,  4.0],
                [ 0.5,  0.5,  4.0],
            ]
        )
        self.cuboid_bbox = np.array(
            [
                [ 1.5,  1.0,  1.0],
                [ 1.5, -1.0,  1.0],
                [-1.5, -1.0,  1.0],
                [-1.5,  1.0,  1.0],
                [-1.5,  1.0, -1.0],
                [-1.5, -1.0, -1.0],
                [ 1.5, -1.0, -1.0],
                [ 1.5,  1.0, -1.0],
            ]
        )
        self.nan_bbox = np.empty(self.bbox_at_origin.shape)
        self.nan_bbox.fill(np.nan)
        # fmt: on

    def test_multiple_overlap(self):
        bbox1_sequence = np.concatenate(
            [
                np.expand_dims(self.bbox_at_origin, axis=0),
                np.expand_dims(self.bbox_at_origin, axis=0),
                np.expand_dims(self.bbox_at_origin, axis=0),
                np.expand_dims(self.bbox_at_origin, axis=0),
                np.expand_dims(self.cuboid_bbox, axis=0),
            ],
            axis=0,
        )
        bbox2_sequence = np.concatenate(
            [
                np.expand_dims(self.bbox_at_origin, axis=0),
                np.expand_dims(self.bbox_offset_by_1, axis=0),
                np.expand_dims(self.bbox_rotated, axis=0),
                np.expand_dims(self.bbox_small, axis=0),
                np.expand_dims(self.bbox_at_origin, axis=0),
            ],
            axis=0,
        )
        result = check_3d_overlap(bbox1_sequence, bbox2_sequence)
        expected = np.array([True, True, True, True, True])

        self.check_results_equal(result, expected)

    def test_no_overlap(self):
        bbox1_sequence = np.concatenate(
            [
                np.expand_dims(self.bbox_at_origin, axis=0),
                np.expand_dims(self.bbox_at_origin, axis=0),
                np.expand_dims(self.bbox_small, axis=0),
                np.expand_dims(self.bbox_small, axis=0),
                np.expand_dims(self.cuboid_bbox, axis=0),
            ],
            axis=0,
        )
        bbox2_sequence = np.concatenate(
            [
                np.expand_dims(self.bbox_offset_by_3, axis=0),
                np.expand_dims(self.bbox_small_raised, axis=0),
                np.expand_dims(self.bbox_offset_by_3, axis=0),
                np.expand_dims(self.bbox_small_raised, axis=0),
                np.expand_dims(self.bbox_offset_by_3, axis=0),
            ],
            axis=0,
        )
        result = check_3d_overlap(bbox1_sequence, bbox2_sequence)
        expected = np.array([False, False, False, False, False])

        self.check_results_equal(result, expected)

    def test_some_overlap(self):
        bbox1_sequence = np.concatenate(
            [
                np.expand_dims(self.bbox_at_origin, axis=0),
                np.expand_dims(self.bbox_small, axis=0),
                np.expand_dims(self.bbox_small, axis=0),
                np.expand_dims(self.bbox_at_origin, axis=0),
            ],
            axis=0,
        )
        bbox2_sequence = np.concatenate(
            [
                np.expand_dims(self.bbox_rotated, axis=0),
                np.expand_dims(self.bbox_offset_by_3, axis=0),
                np.expand_dims(self.bbox_rotated, axis=0),
                np.expand_dims(self.bbox_small_raised, axis=0),
            ],
            axis=0,
        )
        result = check_3d_overlap(bbox1_sequence, bbox2_sequence)
        expected = np.array([True, False, True, False])

        self.check_results_equal(result, expected)

    def test_nan_overlap(self):
        bbox1_sequence = np.concatenate(
            [
                np.expand_dims(self.bbox_at_origin, axis=0),
                np.expand_dims(self.nan_bbox, axis=0),
                np.expand_dims(self.nan_bbox, axis=0),
                np.expand_dims(self.cuboid_bbox, axis=0),
            ],
            axis=0,
        )
        bbox2_sequence = np.concatenate(
            [
                np.expand_dims(self.nan_bbox, axis=0),
                np.expand_dims(self.bbox_offset_by_3, axis=0),
                np.expand_dims(self.nan_bbox, axis=0),
                np.expand_dims(self.nan_bbox, axis=0),
            ],
            axis=0,
        )
        result = check_3d_overlap(bbox1_sequence, bbox2_sequence)
        expected = np.array([False, False, False, False])

        self.check_results_equal(result, expected)

    def check_results_equal(self, result, expected):
        assert np.array_equal(
            np.isnan(result), np.isnan(expected)
        ), "Overlap results not as expected."
        assert np.array_equal(
            result[np.isnan(result) == False], expected[np.isnan(expected) == False]
        ), "Overlap results not as expected."


class TestFlattenBbox:
    def setup_method(self):
        # fmt: off
        self.bbox_no_rot = np.array(
            [
                [ 1.0,  1.0,  1.0],
                [ 1.0, -1.0,  1.0],
                [-1.0, -1.0,  1.0],
                [-1.0,  1.0,  1.0],
                [-1.0,  1.0, -1.0],
                [-1.0, -1.0, -1.0],
                [ 1.0, -1.0, -1.0],
                [ 1.0,  1.0, -1.0],
            ]
        )
        self.bbox_yaw_45 = np.array(
            [
                [ 1.414214,       0.0,  1.0],
                [      0.0, -1.414214,  1.0],
                [-1.414214,       0.0,  1.0],
                [      0.0,  1.414214,  1.0],
                [      0.0,  1.414214, -1.0],
                [-1.414214,       0.0, -1.0],
                [      0.0, -1.414214, -1.0],
                [ 1.414214,       0.0, -1.0],
            ]
        )
        self.bbox_shifted = np.array(
            [
                [6.0, -3.0,  1.0],
                [6.0, -5.0,  1.0],
                [5.0, -5.0,  1.0],
                [5.0, -3.0,  1.0],
                [5.0, -3.0, -1.0],
                [5.0, -5.0, -1.0],
                [6.0, -5.0, -1.0],
                [6.0, -3.0, -1.0],
            ]
        )
        self.cuboid_bbox = np.array(
            [
                [ 1.5,  1.0,  1.0],
                [ 1.5, -1.0,  1.0],
                [-1.5, -1.0,  1.0],
                [-1.5,  1.0,  1.0],
                [-1.5,  1.0, -1.0],
                [-1.5, -1.0, -1.0],
                [ 1.5, -1.0, -1.0],
                [ 1.5,  1.0, -1.0],
            ]
        )
        # fmt: on
        self.nan_bbox = np.empty(self.bbox_no_rot.shape)
        self.nan_bbox.fill(np.nan)

    def flatten_sample_box(self, bbox):
        index_array = np.array([1, 6, 0, 7, 3, 4, 2, 5])
        return bbox[index_array, :2]

    def test_flatten_bbox(self):
        result = flatten_bbox(
            np.concatenate(
                [
                    np.expand_dims(self.bbox_yaw_45, axis=0),
                    np.expand_dims(self.bbox_no_rot, axis=0),
                    np.expand_dims(self.bbox_shifted, axis=0),
                    np.expand_dims(self.nan_bbox, axis=0),
                    np.expand_dims(self.cuboid_bbox, axis=0),
                ],
                axis=0,
            )
        )
        expected = np.concatenate(
            [
                np.expand_dims(self.flatten_sample_box(self.bbox_yaw_45), axis=0),
                np.expand_dims(self.flatten_sample_box(self.bbox_no_rot), axis=0),
                np.expand_dims(self.flatten_sample_box(self.bbox_shifted), axis=0),
                np.expand_dims(self.flatten_sample_box(self.nan_bbox), axis=0),
                np.expand_dims(self.flatten_sample_box(self.cuboid_bbox), axis=0),
            ],
            axis=0,
        )

        self.check_results_equal(result, expected)

    def check_results_equal(self, result, expected):
        # Since we only wrote out the expected values to 6 significant figures, only
        # assert that they are close to that level.
        assert np.array_equal(
            np.isnan(result), np.isnan(expected)
        ), "Bounding Box vertices do not match."
        assert np.allclose(
            result[np.isnan(result) == False], expected[np.isnan(expected) == False], atol=1e-5
        ), "Bounding Box vertices do not match."
