#!/usr/bin/env python3
import sys
import time
import json
from datetime import datetime, timezone
from typing import List, Optional, Set

import boto3
from botocore.exceptions import ClientError, EndpointConnectionError

# ========= CONFIG =========
PROFILE = "rad"                       # <- your named profile
REGION = "us-east-1"
LOG_GROUP = "/aws/sagemaker/TrainingJobs"

# EITHER: set EXACT_STREAM to the full stream name to fetch just that one:
# Example: "ifm-full-FT-ifm-data-all-DDP-full-FT-eb-2025-08-23-21-28-33-489/algo-1-1755984598"
EXACT_STREAM: Optional[str] = "ifm-full-FT-ifm-data-all-DDP-full-FT-eb-2025-08-23-21-28-33-489/algo-1-1755984598"
EXACT_STREAM: Optional[str] = 'dream2assist-test-sweep-dream2assist-sa-2025-09-16-15-52-58-601/algo-1-1758038710'

# OR: leave EXACT_STREAM = None and set STREAM_PREFIX to grab ALL matching streams for that job:
STREAM_PREFIX: Optional[str] = None
# e.g. STREAM_PREFIX = "ifm-full-FT-ifm-data-all-DDP-full-FT-eb-2025-08-23-21-28-33-489/"
# ==========================


def mk_client():
    session = boto3.Session(profile_name=PROFILE, region_name=REGION)
    return session.client("logs")


def list_streams(client, log_group: str, exact_stream: Optional[str], prefix: Optional[str]) -> List[str]:
    if exact_stream:
        return [exact_stream]

    if not prefix:
        raise ValueError("Either EXACT_STREAM or STREAM_PREFIX must be set.")

    streams = []
    next_token = None
    while True:
        kwargs = dict(logGroupName=log_group, logStreamNamePrefix=prefix, orderBy="LogStreamName", descending=False)
        if next_token:
            kwargs["nextToken"] = next_token
        resp = client.describe_log_streams(**kwargs)
        for s in resp.get("logStreams", []):
            streams.append(s["logStreamName"])
        nt = resp.get("nextToken")
        if not nt or nt == next_token:
            break
        next_token = nt

    if not streams:
        raise RuntimeError(f"No streams found with prefix: {prefix}")
    return streams


def get_time_bounds(client, log_group: str, stream_name: str):
    """
    Try to get first and last event timestamps from describe_log_streams for tighter export.
    If unavailable, return (None, None) so paginator scans all.
    """
    resp = client.describe_log_streams(
        logGroupName=log_group,
        logStreamNamePrefix=stream_name,
        orderBy="LogStreamName",
        descending=False
    )
    for s in resp.get("logStreams", []):
        if s["logStreamName"] == stream_name:
            # firstEventTimestamp may be missing; fall back to None
            start = s.get("firstEventTimestamp")
            # lastEventTimestamp is not guaranteed; lastIngestionTime is a decent upper bound
            end = s.get("lastEventTimestamp") or s.get("lastIngestionTime")
            return start, end
    return None, None


def iso(ms: int) -> str:
    return datetime.fromtimestamp(ms/1000, tz=timezone.utc).isoformat()


def write_events_for_stream(client, log_group: str, stream_name: str, out_path: str):
    start_ms, end_ms = get_time_bounds(client, log_group, stream_name)

    # Build paginator over filter_log_events (no filterPattern -> all messages)
    paginator = client.get_paginator("filter_log_events")
    page_kwargs = dict(
        logGroupName=log_group,
        logStreamNames=[stream_name],
        interleaved=True,
    )
    if start_ms:
        page_kwargs["startTime"] = start_ms
    if end_ms:
        page_kwargs["endTime"] = end_ms

    seen_event_ids: Set[str] = set()  # guard against dupes across pages
    total = 0
    page_idx = 0

    print(f"\n==> Stream: {stream_name}")
    if start_ms or end_ms:
        print(f"    Time window: {iso(start_ms) if start_ms else 'None'}  ->  {iso(end_ms) if end_ms else 'None'}")

    with open(out_path, "w", encoding="utf-8") as f:
        while True:
            try:
                for page in paginator.paginate(**page_kwargs):
                    page_idx += 1
                    events = page.get("events", [])
                    for e in events:
                        event_id = e.get("eventId")
                        if event_id and event_id in seen_event_ids:
                            continue
                        if event_id:
                            seen_event_ids.add(event_id)

                        # Write plain text line; modify if you prefer JSONL
                        msg = e.get("message", "")
                        ts = e.get("timestamp")
                        line = msg.rstrip("\n")
                        # If you want timestamps, uncomment the next line:
                        # line = f"{datetime.utcfromtimestamp(ts/1000).isoformat()}Z {line}"
                        f.write(line + "\n")
                        total += 1

                    print(f"    Page {page_idx}: +{len(events)} events (running total: {total})")

                # If we finish the paginator loop, we’re done
                break

            except (EndpointConnectionError, ClientError) as ex:
                # Simple backoff and retry on transient issues / throttling
                err = getattr(ex, "response", {}).get("Error", {}).get("Code", "")
                print(f"    Warning: {type(ex).__name__} {err or ''}. Backing off 2s and retrying…", file=sys.stderr)
                time.sleep(2)

    print(f"    DONE: wrote {total} events to {out_path}")


def main():
    client = mk_client()
    streams = list_streams(client, LOG_GROUP, EXACT_STREAM, STREAM_PREFIX)

    if len(streams) == 1:
        out = "training_logs.txt"
        write_events_for_stream(client, LOG_GROUP, streams[0], out)
    else:
        # Multiple streams (e.g., algo-1, algo-2). Write per-stream and a combined file.
        combined_path = "training_logs_all_streams.txt"
        with open(combined_path, "w", encoding="utf-8") as combined:
            for s in streams:
                out = f"training_logs__{s.split('/')[-1].replace(':','_')}.txt"
                write_events_for_stream(client, LOG_GROUP, s, out)
                with open(out, "r", encoding="utf-8") as part:
                    combined.write(part.read())
        print(f"\nCombined file written to: {combined_path}")


if __name__ == "__main__":
    main()
