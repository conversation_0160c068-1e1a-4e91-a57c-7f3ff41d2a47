#!/bin/bash

: ' 
This script does the following task and create pipeline and help to generate annotation data using single script.

1. Generate CSVs from pickle files (subtask-1) :: collect subject wise subtitle of instrctor from pickle files
2. Snip the Videos (subtask 2) ::  for collected CSVs data. First generate ffmpeg command file and then snip using bash script.
3. Generate overlay videos (subtask 3) :: Process the snip videos to add padding at the bottom and plot the speedometer details over the video.
   Generate the overlay video commands and run those command using bash script.

Input and Ouput at each subtask:

<subject>: 601, 602, etc
<N>: 1, 2, 3, etc.

1. Subtask 1:
      Input: $EXTRACTED_TRIALS_PICKLE/<subject>-trial_<N>_v1_with_all_annotations_and_metrics.trial.pkl
            
      Output: $CSV_DIRECTORY/<subject>-trial_<N>_sub_times.csv

2. Subtask 2:
      Input: 1. $VIDEO_DIRECTORY/<subject>_coach.mp4, 
             2. $VIDEO_META/output_<subject>_videoonly_composite_meta.yaml,
             3. $CSV_DIRECTORY/<subject>-trial_<N>_sub_times.csv
      Output: 1. $SNIP_VIDEO_OUTPUT_DIRECTORY/<subject>_trial<N>_[<start_time>,<end_time>]_[0.5,10].mp4,
              2. $SNIP_VIDEO_OUTPUT_DIRECTORY/<subject>_trial<N>_[<start_time>,<end_time>]_[0.5,10].txt
              3. command_out_dir/snip_command.txt

3. Subtask 3:
      Input: 1. $SNIP_VIDEO_OUTPUT_DIRECTORY/<subject>_trial<N>_[<start_time>,<end_time>]_[0.5,10].mp4
             2. $CSV_DIRECTORY/<subject>-trial_<N>_sub_times.csv
      Output: 1. $OVERLAY_OUTPUT_DIRECTORY/<subject>_trial<N>_[<start_time>,<end_time>]_[0.5,10].mp4
              2. command_out_dir/overlay_command.txt

'

# Activate environment
eval "$(micromamba shell hook --shell bash)"
micromamba activate hid_common

# Go to top level to execute scripts.
cd ..
echo "$(pwd)"

export PYTHONPATH=.


# Input required for all the tasks
EXTRACTED_TRIALS_PICKLE="trials_final"
VIDEO_DIRECTORY="/home/<USER>/tri/tri-hid-data-shared-autonomy/24-D-05/Videos"
VIDEO_META="videos_meta"
CSV_DIRECTORY="trials_csvs"
SNIP_VIDEO_OUTPUT_DIRECTORY="snip_output"
FFMPEG_SNIP_CMD_FILE="snip_cmds.txt"
OVERLAY_OUTPUT_DIRECTORY='overlay_output'
FFMPEG_OVERLAY_CMD_FILE="overlay_cmds.txt"
PENDING_OVERLAY_VIDEO_DIRECTORY="pendding_overlay_videos"
SUBJECT_LIST="all"


if [ ! -d "$EXTRACTED_TRIALS_PICKLE" ]; then
   echo "Error :: Directory '$EXTRACTED_TRIALS_PICKLE' does not exists"
   exit 1
fi

if [ -z "$VIDEO_DIRECTORY" ]; then
   echo "Error :: Missing video directory path"
   exit 1
fi

mkdir -p trials_csvs
mkdir -p snip_output
mkdir -p overlay_output

# Subtask 1 :: Create CSVs
python ./scripts/subtitle_based_snip.py \
	--trials-dir $EXTRACTED_TRIALS_PICKLE \
	--output-folder $CSV_DIRECTORY

Subtask 2 :: Snip Videos
python ./scripts/snip_mp4.py \
	--videos-dir $VIDEO_DIRECTORY \
  --meta-dir $VIDEO_META \
	--csv-dir $CSV_DIRECTORY \
  --output-dir $SNIP_VIDEO_OUTPUT_DIRECTORY \
	--ffmpeg-cmd-file-name $FFMPEG_SNIP_CMD_FILE \
  --subject-list $SUBJECT_LIST

# # Run snip commands in parallel.
bash ./bash_scripts/ffmpeg-parallel.sh $FFMPEG_SNIP_CMD_FILE --use-gpu 1 --gpu-device 0 --process-count 5


# Subtask 3 :: Generate Overlay Videos. 
python ./scripts/overlay_on_sampled_video.py \
	--source-directory  $SNIP_VIDEO_OUTPUT_DIRECTORY \
  --sink-directory $OVERLAY_OUTPUT_DIRECTORY \
  --extracted-trials-pkl $EXTRACTED_TRIALS_PICKLE \
	--ffmpeg-cmd-file-name $FFMPEG_OVERLAY_CMD_FILE

# Run snip commands in parallel.
bash ./bash_scripts/ffmpeg-parallel.sh $FFMPEG_OVERLAY_CMD_FILE --use-gpu 1 --gpu-device 0 --process-count 5


# Copy the text files to overlay output location. Seperating the videos which are failed while doing subtask 3
mkdir -p $PENDING_OVERLAY_VIDEO_DIRECTORY

# Copy the text files to final output directory.
for txt_file in $SNIP_VIDEO_OUTPUT_DIRECTORY/*.txt; do
  base_name=$(basename "$txt_file" .txt)
  if [ -e "$OVERLAY_OUTPUT_DIRECTORY/${base_name}.mp4" ]; then
    cp "$txt_file" "$OVERLAY_OUTPUT_DIRECTORY/"
  else
    cp "$SNIP_VIDEO_OUTPUT_DIRECTORY/${base_name}.mp4" "$PENDING_OVERLAY_VIDEO_DIRECTORY/"
    cp "$txt_file" "$PENDING_OVERLAY_VIDEO_DIRECTORY/"
  fi
done

rm $SNIP_VIDEO_OUTPUT_DIRECTORY/*_speedo*

echo "Annotation data is created successfully."
