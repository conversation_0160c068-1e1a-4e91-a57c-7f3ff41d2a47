#!/bin/bash

: '
This scripts read ffmpeg commands from text file and run in parallels.

Input: command file (simple text file.)
Output: Each command has input and output video file mentioned.
        After the execution of the command output video will be generated in output directory.

Usage:
  ./ffmpeg-parallel.sh <command_file> \
      [--use-gpu <0|1>] \
      [--gpu-device <device_id>] \
      [--process-count <number>]

  command_file: File contains ffmpeg commands in each line.
  --use-gpu: 0 - Do not add GPU flags to ffmpeg command. 1 - add Use GPU flags to ffmpeg command.
                 Default is 0
  --gpu-device: valid values are between 0-N, If N GPUs are available. Default is 0
  --process-count: Number of parallel process executes the ffmpeg commands. Default is 1.


Example:
    Command File:
      line 1:  ffmpeg -ss 0 -to 10 -i source_dir/input.mp4 -vf "pad=iw:ih+100:0:0:color=white,drawtext=text='sample_text':fontfile=Arial.ttf:fontsize=32:fontcolor=black:x=10:y=10" -c:v h264 -c:a aac output_dir/output_1.mp4
      line 2:  ffmpeg -ss 10 -to 20 -i source_dir/input.mp4 -vf "pad=iw:ih+100:0:0:color=white,drawtext=text='sample_text':fontfile=Arial.ttf:fontsize=32:fontcolor=black:x=10:y=10" -c:v h264 -c:a aac output_dir/output_2.mp4

    Execution:
        1. if gpu flag is true and gpu device is 0, "-hwaccel cuda -hwaccel_device 0" will be added to ffmpeg command.
              New Commands:
                line 1:  ffmpeg -hwaccel cuda -hwaccel_device 0 -ss 0 -to 10 -i .......
                line 2:  ffmpeg -hwaccel cuda -hwaccel_device 0 -ss 10 -to 20 -i ......
        2. If process count is 2 then, above to commands are executed in parallel.
        3. output_1.mp4 and output_2.mp4 video file will be generated in output_dir.
'
# Initialize default values
USE_GPU=0
GPU_DEVICE=0
PROCESS_COUNT=1

# Function to display usage information
usage() {
  echo "Usage: $0 <command_file> [--use-gpu <0|1>] [--gpu-device <device_id>] [--process-count <number>]"
  exit 1
}

# Parse command-line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --use-gpu)
      if [[ $# -gt 1 && $2 =~ ^[0-1]$ ]]; then
        USE_GPU=$2
        shift 2
      else
        usage
      fi
      ;;
    --gpu-device)
      if [[ $# -gt 1 && $2 =~ ^[0-9]+$ ]]; then
        GPU_DEVICE=$2
        shift 2
      else
        usage
      fi
      ;;
    --process-count)
      if [[ $# -gt 1 && $2 =~ ^[0-9]+$ ]]; then
        PROCESS_COUNT=$2
        shift 2
      else
        usage
      fi
      ;;
    *)
      if [[ -z "$COMMAND_FILE" ]]; then
        COMMAND_FILE=$1
        shift
      else
        usage
      fi
      ;;
  esac
done

# Check if COMMAND_FILE is provided
if [[ -z "$COMMAND_FILE" ]]; then
  usage
fi

# Output the parsed values
echo "Command File: $COMMAND_FILE"
echo "Use GPU: $USE_GPU"
echo "Process Count: $PROCESS_COUNT"

# Example usage
if [ "$USE_GPU" -eq 1 ]; then
  echo "Running with GPU support on device $GPU_DEVICE..."
  FFMPEG_GPU_FLAGS="-hwaccel cuda -hwaccel_device $GPU_DEVICE"
  echo "----> FFFMPEG Flags: $FFMPEG_GPU_FLAGS"
  sed -i "s/ffmpeg/ffmpeg $FFMPEG_GPU_FLAGS/" $COMMAND_FILE
else
  echo "Running without GPU support..."
fi

echo "Processing with $PROCESS_COUNT parallel processes..."


# Read the commands and run them in parallel
cat "$COMMAND_FILE" | xargs -d'\n' -P "$PROCESS_COUNT" -I {} bash -c '{}' 

echo "All commands are executed."