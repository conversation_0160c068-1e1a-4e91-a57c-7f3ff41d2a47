version: 0.1
env:
  shell: bash
  variables:
    RAD_IMAGE_ENCODER_NAN_RETRIES: 1000
phases:
  pre_build:
    commands:
      - export PATH="$PATH:$HOME/.local/bin"
  build:
    commands:
      - echo Build started on `date`
      - export CONDA_ENV=hid_common
      - micromamba env list
      - micromamba run -n hid_common pip install -e hid_common_ml
#      - micromamba run -n hid_common git diff --exit-code main..HEAD -- environment.hid_common.yml || micromamba env update -f environment.hid_common.yml --prune
      -  # Clear python cache
#      - find . | grep -E "(__pycache__|\.pyc|\.pyo$)" | xargs rm -rf
      -  # Test NaN problem
#      - conda run --no-capture-output -vv -n $CONDA_ENV pip install pytest-cov
#      - micromamba run -n $CONDA_ENV pip install pytest-cov
      -  # Run unit tests
#      - conda run -n $CONDA_ENV --no-capture-output inv lint --check
#      - conda run -n $CONDA_ENV --no-capture-output inv lint --check
      - micromamba run -n hid_common inv lint --check
      - ./bash_scripts/run_unittests.sh
  post_build:
    commands:
