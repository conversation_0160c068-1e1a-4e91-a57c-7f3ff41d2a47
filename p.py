import numpy as np
import matplotlib.pyplot as plt

# Generate phase_progress values from 0 to 1
phase_progress = np.linspace(0, 1, 100)

# Compute the function values
y =  (1+20* phase_progress) ** (-0.5)
max1, min1 = y.max(), y.min()
y = (y-min1)/(max1-min1)

# Create the plot
plt.figure(figsize=(8, 6))
plt.plot(phase_progress, y, label=r'$(1 + \text{phase\_progress})^{-0.5}$', color='blue')
plt.xlabel('phase_progress')
plt.ylabel('Function Value')
plt.title('Plot of $(1 + \text{phase\_progress})^{-0.5}$')
plt.legend()
plt.grid(True)
plt.show()
