name: hid_common
channels:
  - pytorch
  - nvidia
dependencies:
  - boto3
  - conda-forge::awscli
  - conda-forge::addict
  - conda-forge::black-jupyter=23.12.1
  - conda-forge::geopy
  - conda-forge::imageio
  - conda-forge::imageio-ffmpeg
  - conda-forge::ipdb
  - conda-forge::nbdime
  - conda-forge::pyquaternion
  - conda-lock
  - coverage
  - isort=5.13.2
  - jupyter
  - matplotlib
  - more-itertools
  - numpy
  - pandas
  - Pillow
  - pip
  - pre-commit
  - pytest
  - pytest-mock
  - pytest-timeout
  - pytest-xdist
  - python=3.11
  - scikit-learn
  - scipy
  - Shapely
  - simplejson
  - tqdm
  - invoke
  - pip:
      - setuptools < 70.0
      - -e . --config-settings editable_mode=compat  # editable_mode=compat insure adding the package to sys.path
      - Geometry3D
      - opencv-python-headless
      - nvidia-ml-py3
      - mcap-ros2-support
      - datetimerange
      - typeguard
      - shapely >= 2.0.0
      - pyarrow
      - srt
      - moviepy >= 2.1.1
