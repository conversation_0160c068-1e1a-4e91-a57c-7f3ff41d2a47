#!/usr/bin/env python3
# Script for integrating the instruction category annotations for 24d05 into the trial parquets.

# Example invocation:
# python parse_crest_annotations_24d05.py --mcap-input-folder ~/Data/24-D-05/Rosbags/
# --compactsim-cache-folder ~/Data/24-D-05/new_cache/ --track-map-csv ~/Data/24-D-05/Rosbags/track.csv
# --trials-dir ~/Data/24-D-05/trials_final/ --annotation-folder-name ~/Data/24-D-05/manual_annotations


from pathlib import Path

from data_sources.thunderhill.data_readers import read_map_csv
from scripts.filter_params import AIC_INSTRUCTION_CATEGORY_ANNOTATION
from util.common_args import common_dir_arguments, parse_args
from util.trial import Trial

WORKER_IDS = [
    "private.us-east-1.311daa9188d174fb",
    "private.us-east-1.54d1c2e40e03d4df",
    "private.us-east-1.aa1918156fc0e326",
    "private.us-east-1.cc76526182a05c1d",
    "private.us-east-1.ecc34e76087853d4",
]


def parse_arguments(args=None):
    parser = parse_args(additional_arg_setters=(common_dir_arguments,))
    parser.add_argument(
        "--annotation-folder-name",
        type=str,
        help="Name of the folder containing the csvs of annotations from crest",
        default=None,
    )
    result = parser.parse_args(
        args=args,
    )
    return vars(result)


def get_concurrent_feedback_annotation_dict(trial_uid, annotation_folder_name):
    trial_annotation_csv_name = f"{trial_uid}_manual_annotated.csv"
    annotation_csv_path = annotation_folder_name / Path(trial_annotation_csv_name)
    concurrent_feedback_annotation_dict = {
        "concurrent_feedback_24d05": {
            "annotation_csv_path": annotation_csv_path,
            "worker_ids": WORKER_IDS,
            "trial_uid": trial_uid,
        }
    }
    return concurrent_feedback_annotation_dict


def main():
    args = parse_arguments()
    trials_dir = args["trials_dir"]
    map_csv_filename = str(Path(args["track_map_csv"]).expanduser().resolve())
    track_map = read_map_csv(map_csv_filename)

    manual_annotation_class = AIC_INSTRUCTION_CATEGORY_ANNOTATION
    annotation_folder_name = Path(args["annotation_folder_name"])

    name_suffix = "_with_all_annotations_and_metrics"
    all_trials = Trial.read_all(trials_dir, name_suffix=name_suffix)

    for t in all_trials:
        trial_uid = t.get_uid()
        print(f"PROCESSING TRIAL ID - {trial_uid}")

        concurrent_feedback_annotation_dict = get_concurrent_feedback_annotation_dict(
            trial_uid, annotation_folder_name
        )
        manual_annotations = t.run_manual_annotations(
            manual_annotation_class, track_map, concurrent_feedback_annotation_dict
        )
        t.write(
            data_dir=trials_dir,
            name_suffix="_with_all_annotations_and_metrics_with_instruction_category",
        )


if __name__ == "__main__":
    main()
