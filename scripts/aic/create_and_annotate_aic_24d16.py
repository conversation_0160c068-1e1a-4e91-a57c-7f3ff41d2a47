#!/usr/bin/env python3
# Script for processing the data from 24-D-16. The study was 15 subjects doing self-practice on Thunderhill data.
# This data from this study requires the standard trial by trial processing

# Example invocation:
# python create_and_annotate_aic_24d16.py --mcap-input-folder ~/Downloads/tri-hid-data-shared-autonomy/24-D-16/Rosbags
# --compactsim-cache-folder ~/compactsim_cache --track-map-csv ~/Downloads/Thunderhill_data/track.csv
# --plot-animation false --trials-dir ~/data/shared_autonomy/trials/ --is-multiple-mcaps-per-subject True

import collections
from pathlib import Path

import pandas as pd

from data_sources.compact_sim.dataloading import DATA_DICT_KEY_MAP
from data_sources.compact_sim.mcap_process_methods import process_rosout
from scripts.filter_params import AIC_FILTERS_NO_ANIM, AIC_FILTERS_WITH_ANIM
from scripts.metric_params import AIC_METRICS
from util.common_args import common_dir_arguments, parse_args
from util.filters.trajectory_filters import DATA_DICT_KEY_TRIAL_IDS, mark_trial_ids
from util.mcap_util import (
    clean_up_nans_in_scenario_condition,
    get_mcap_dfs_for_subject,
    get_valid_subject_ids,
    make_timestamp_monotonic,
    read_mcap_topics_24_d_16,
)
from util.trial import Trial
from util.utility import str2bool


def parse_arguments(args=None):
    parser = parse_args(additional_arg_setters=(common_dir_arguments,))
    parser.add_argument(
        "--plot-animation",
        type=str2bool,
        help="Plot trial animation or not",
        default=False,
    )
    result = parser.parse_args(
        args=args,
    )
    return vars(result)


def main():
    args = parse_arguments()
    # extract all arguments corresponding to different data directories
    topics = ["/rosout"]
    # these additional topics appear in teaching study and also possibly in Phase 3 of SDM
    additional_topics_dict = {}
    additional_topics_dict["topics"] = topics
    additional_topics_dict["topic_process_methods"] = {
        "/rosout": process_rosout,
    }

    # For each participant set the mcap prefix and number of mcaps with that prefix in chronological order
    (
        subject_info,
        subject_pid_sorted_mcap_names,
        data_dict,
    ) = read_mcap_topics_24_d_16(args, "24_D_16", additional_topics_dict=additional_topics_dict)

    annotation_data_dict = {}
    trials_dir = args["trials_dir"]
    Path.mkdir(Path(trials_dir), exist_ok=True)

    if args["plot_animation"]:
        auto_annotation_classes = AIC_FILTERS_WITH_ANIM
    else:
        auto_annotation_classes = AIC_FILTERS_NO_ANIM

    track_map = data_dict[DATA_DICT_KEY_MAP]
    valid_subject_ids = get_valid_subject_ids("24_D_16")
    subject_pid_df_list = get_mcap_dfs_for_subject(
        subject_pid_sorted_mcap_names, data_dict, valid_subject_ids
    )

    subject_trial_start_end_times_dict = collections.OrderedDict()
    for subject_pid, subject_df_list in subject_pid_df_list.items():
        if subject_pid not in valid_subject_ids:
            continue
        print(f"Processing subject {subject_pid}")
        subject_trial_start_end_times_dict[subject_pid] = collections.OrderedDict()
        # assume that subject_df_list is sorted
        subject_df = pd.concat(subject_df_list, ignore_index=True)
        make_timestamp_monotonic(subject_df)
        clean_up_nans_in_scenario_condition(subject_df)
        trial_ids_dict = mark_trial_ids(subject_df)
        annotation_data_dict[subject_pid] = {}
        annotation_data_dict[subject_pid][DATA_DICT_KEY_TRIAL_IDS] = trial_ids_dict[
            "annotation_df"
        ]
        all_trial_ids = annotation_data_dict[subject_pid][DATA_DICT_KEY_TRIAL_IDS]["trial_ids"]
        # valid trials are marked as [0...N]. Incomplete trials (can be due to proctor restart or time violation) are -1.
        # Downtime in between trials is marked as np.nan.
        valid_trial_ids = [int(l) for l in list(set(all_trial_ids[all_trial_ids >= 0]))]
        # parse trials for the subject and run auto and manual annotations
        for trial_idx, valid_trial_id in enumerate(valid_trial_ids):
            subject_df_valid_trial_id = subject_df[all_trial_ids == valid_trial_id]
            trial_id = subject_pid + f"-trial_{trial_idx}"
            start_ros_time = subject_df_valid_trial_id["carla_objects log time"].iloc[0]
            end_ros_time = subject_df_valid_trial_id["carla_objects log time"].iloc[-1]
            subject_trial_start_end_times_dict[subject_pid][trial_idx] = {
                "carla_objects_time_start_end": (start_ros_time, end_ros_time)
            }

            trial = Trial(trial_id, dataframe=subject_df_valid_trial_id)
            # save trial data without annotations and metrics
            trial.write(data_dir=trials_dir)
            # Run auto annotations and metrics
            auto_annotations = trial.run_auto_annotations(auto_annotation_classes, track_map, {})
            trial_metrics = trial.run_compute_metrics(AIC_METRICS, track_map, {})

            trial.write(data_dir=trials_dir, name_suffix="_with_all_annotations_and_metrics")
            print(f"PROCESSED TRIAL ID - {valid_trial_id}, SUBJECT - {subject_pid}")

    import IPython

    IPython.embed(banner1="finish trials")


if __name__ == "__main__":
    main()

# python create_and_annotate_aic_24d16.py --mcap-input-folder ~/Data/24-D-16/Rosbags/
# --compactsim-cache-folder ~/Data/24-D-16/new_cache/ --track-map-csv ~/Data/24-D-16/Rosbags/track.csv
# --plot-animation false --trials-dir ~/Data/24-D-16/trials_final --is-multiple-mcaps-per-subject True
