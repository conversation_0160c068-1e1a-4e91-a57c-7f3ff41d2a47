# Script to analyze the metadata stored in different metadata files to get a better sense of the discrepancies

# python analyse_video_metadata_24d05.py  --video-compositing-metadata-dir ~/Data/24-D-05/video_metadata_files/
# --mcap-input-folder ~/Data/24-D-05/Rosbags/ --compactsim-cache-folder ~/Data/24-D-05/new_cache/
# --track-map-csv ~/Data/24-D-05/Rosbags/track.csv  --is-multiple-mcaps-per-subject True
import os

import yaml

from data_sources.compact_sim.mcap_process_methods import process_rosout
from util.common_args import common_dir_arguments, parse_args
from util.mcap_util import read_mcap_topics_24_d_05


def parse_arguments(args=None):
    parser = parse_args(additional_arg_setters=(common_dir_arguments,))

    parser.add_argument(
        "--video-compositing-metadata-dir",
        type=str,
        help="The dir where the transcriptions are stored.",
        default=None,
    )
    result = parser.parse_args(
        args=args,
    )
    return vars(result)


def compare_metadata(
    subject_srt_files_info, subject_info, mcap_input_folder, video_compositing_metadata_dir
):
    for subject_id in subject_srt_files_info.keys():
        for coach_srt_name, rosbag_name, carla_objects_initial_ts, initial_ts in zip(
            subject_srt_files_info[subject_id]["coach"],
            subject_srt_files_info[subject_id]["srt_rosbag"],
            subject_srt_files_info[subject_id]["srt_rosbag_initial_carla_objects_log_time"],
            subject_srt_files_info[subject_id]["srt_rosbag_initial_timestamp"],
        ):
            pid_prefix_end_index = coach_srt_name.find("_")
            pid_prefix = coach_srt_name[:pid_prefix_end_index]
            print(pid_prefix, rosbag_name)

            # grab the index of rosbag_name in the corresponding subject_info dict.
            rosbag_metadata_index = [r[0] for r in subject_info[subject_id]].index(rosbag_name)
            # append index to "metadata_".
            metadata_filename = (
                f"metadata_{rosbag_metadata_index}.yaml"
                if rosbag_metadata_index > 0
                else "metadata.yaml"
            )
            # go to the correct directory in mcap_input folder and grab the metadata.yaml.
            metadata_filepath = os.path.join(mcap_input_folder, subject_id, metadata_filename)
            with open(metadata_filepath, "r") as fp:
                metadata_raw = yaml.safe_load(fp)
            # open it up and read in the opening rostime
            raw_sim_bag_start_rostime = (
                metadata_raw["rosbag2_bagfile_information"]["files"][0]["starting_time"][
                    "nanoseconds_since_epoch"
                ]
                / 10**9
            )

            # got to the video metadata diretcory and get the yaml with same name as pid_prefix. get start time
            video_metadata_file = f"output_{pid_prefix}_videoonly_composite_meta.yaml"
            video_metadata_filepath = os.path.join(
                video_compositing_metadata_dir, video_metadata_file
            )
            with open(video_metadata_filepath, "r") as fp:
                video_metadata = yaml.safe_load(fp)

            video_start_rostime = video_metadata["start_timestamp"]

            print(
                subject_id,
                pid_prefix,
                raw_sim_bag_start_rostime,
                video_start_rostime,
                carla_objects_initial_ts,
                video_start_rostime - raw_sim_bag_start_rostime,
                carla_objects_initial_ts - raw_sim_bag_start_rostime,
                initial_ts,
            )
            print()


def main():
    args = parse_arguments()
    mcap_input_folder = args["mcap_input_folder"]
    video_compositing_metadata_dir = args["video_compositing_metadata_dir"]

    topics = ["/rosout"]
    # these additional topics appear in teaching study and also possibly in Phase 3 of SDM
    additional_topics_dict = {
        "topics": topics,
        "topic_process_methods": {
            "/rosout": process_rosout,
        },
    }

    subject_info, subject_srt_files_info, _, data_dict = read_mcap_topics_24_d_05(
        args, "24_D_05", additional_topics_dict=additional_topics_dict
    )
    compare_metadata(
        subject_srt_files_info, subject_info, mcap_input_folder, video_compositing_metadata_dir
    )


if __name__ == "__main__":
    main()
