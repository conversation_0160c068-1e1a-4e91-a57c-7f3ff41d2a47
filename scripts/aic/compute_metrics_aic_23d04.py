"""
This script computes metrics on the AIC 23D04 trials.
Example:
    python compute_metrics_aic_23d04.py  --trials-dir ~/data/shared_autonomy/trials/ --track-map-csv ~/Downloads/Thunderhill_data/track.csv
"""
import collections
from pathlib import Path

from data_sources.thunderhill.data_readers import read_map_csv
from scripts.filter_params import AIC_FILTERS_NO_ANIM
from scripts.metric_params import AIC_METRICS
from util.common_args import common_dir_arguments, parse_args
from util.trial import Trial


def parse_arguments(args=None):
    parser = parse_args(additional_arg_setters=(common_dir_arguments,))

    result = parser.parse_args(
        args=args,
    )
    return vars(result)


def main():
    args = parse_arguments()
    map_csv_filename = str(Path(args["track_map_csv"]).expanduser().resolve())
    track_map = read_map_csv(map_csv_filename)

    trials_dir = args["trials_dir"]

    all_trials = Trial.read_all(trials_dir, auto_annotation_class=AIC_FILTERS_NO_ANIM)
    if True:
        # Run compute_metrics and organize the results in different structure for different parsing purposes
        trial_metrics_organized_by_uid = collections.OrderedDict()
        trial_metrics_organized_by_metric_name = collections.defaultdict(list)
        for i, trial in enumerate(all_trials):
            # List of Metric Subclasses
            trial_metrics = trial.run_compute_metrics(AIC_METRICS, track_map, {})
            trial_metrics_organized_by_uid[trial.uid] = trial_metrics
            for trial_metric in trial_metrics:
                trial_metrics_organized_by_metric_name[
                    trial_metric.get_result()["metric_name"]
                ].append(trial_metric.get_result())

        import IPython

        IPython.embed(banner1="check")


if __name__ == "__main__":
    main()
