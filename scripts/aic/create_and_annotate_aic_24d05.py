#!/usr/bin/env python3
# Script for processing the data from March-April 2024 AIC study. This data from this study requires the
# standard trial by trial processing and additionally requires the parsing and alignment of transcripts (from coach and driver)
# and temporal alignment of these with the trajectory data. This is accomplished through the AIC_MANUAL_ANNOTATION classes
# Additionally, this script also extracts "between-sessions/trials dialog (BSD)" and creates a corresponding "BSD" file (as a dictionary) for
# each trial

# Example invocation:
# python create_and_annotate_aic_24d05.py --mcap-input-folder ~/Data/24-D-05/Rosbags/
# --compactsim-cache-folder ~/Data/24-D-05/new_cache/
# --track-map-csv ~/Data/24-D-05/Rosbags/track.csv
# --plot-animation false --trials-dir ~/Data/24-D-05/trials_final
# --is-multiple-mcaps-per-subject True
# --transcriptions-dir ~/Data/24-D-05/Transcriptions_Final
# --video-compositing-metadata-dir ~/Data/24-D-05/video_metadata_files/

import collections
from pathlib import Path
from typing import Dict

import pandas as pd
import yaml

from data_sources.compact_sim.dataloading import DATA_DICT_KEY_MAP
from data_sources.compact_sim.mcap_process_methods import process_rosout, process_sound_name
from scripts.filter_params import AIC_FILTERS_NO_ANIM, AIC_FILTERS_WITH_ANIM, AIC_MANUAL_ANNOTATION
from scripts.metric_params import AIC_METRICS
from util.common_args import common_dir_arguments, parse_args
from util.filters.trajectory_filters import (
    DATA_DICT_KEY_TRIAL_IDS,
    extract_bsd_srt_for_trial_id,
    mark_trial_ids,
)
from util.mcap_util import (
    clean_up_nans_in_scenario_condition,
    get_mcap_dfs_for_subject,
    get_srt,
    get_valid_subject_ids,
    make_timestamp_monotonic,
    read_mcap_topics_24_d_05,
)
from util.trial import Trial
from util.utility import str2bool


def parse_arguments(args=None):
    parser = parse_args(additional_arg_setters=(common_dir_arguments,))
    parser.add_argument(
        "--plot-animation",
        type=str2bool,
        help="Plot trial animation or not",
        default=False,
    )
    parser.add_argument(
        "--transcriptions-dir",
        type=str,
        help="The dir where the transcriptions are stored.",
        default=None,
    )
    parser.add_argument(
        "--video-compositing-metadata-dir",
        type=str,
        help="The dir where the metadata files for the video compositing process are stored.",
        default=None,
    )
    parser.add_argument("--audio-file-dir", type=str, help="Sound File location.", default=None)

    parser.add_argument(
        "--split-trial-on-sound-name",
        type=str2bool,
        help="Split the trials on sound name details",
        default=False,
    )

    result = parser.parse_args(
        args=args,
    )
    return vars(result)


def get_srt_manual_annotation_dict(subject_srt_files_info, subject_pid, transcriptions_dir):
    """
    Function that extracts subtitles from the coach and student transcript along with
    starting rostime and timestamp information for each srt.
    """
    coach_srt_files = subject_srt_files_info[subject_pid]["coach"]
    driver_srt_files = subject_srt_files_info[subject_pid]["driver"]

    coach_srts = [
        get_srt(transcriptions_dir, subject_pid, coach_srt_file)
        for coach_srt_file in coach_srt_files
    ]
    driver_srts = [
        get_srt(transcriptions_dir, subject_pid, driver_srt_file)
        for driver_srt_file in driver_srt_files
    ]
    initial_rostime_for_each_srt = subject_srt_files_info[subject_pid][
        "srt_rosbag_initial_rostime"
    ]
    subject_manual_annotation_other_input_dict = {
        "srt": {
            "subject_coach_srts": coach_srts,
            "subject_driver_srts": driver_srts,
            "initial_rostime_for_each_srt": initial_rostime_for_each_srt,
        }
    }
    return subject_manual_annotation_other_input_dict


def save_bsd_dict(bsd_dict: Dict, trial: Trial):
    """Function to save the dictionary containing the between sessions dialog in the trial direction.
    directory for trial N contains BSD between trial N-1  and trial N

    :param bsd_dict: dictionary containing information regarding between sessions dialog
    :param trial: Trial object
    """
    trial_dir = trial.data_dir
    file_path = str(Path(trial_dir).joinpath(f"{trial.uid}_bsd_dict.yaml"))
    with open(file_path, "w") as file:
        yaml.dump(bsd_dict, file)


def main():
    args = parse_arguments()
    # extract all arguments corresponding to different data directories
    topics = ["/rosout", "/hero/sound_name"]
    # these additional topics appear in teaching study and also possibly in Phase 3 of SDM
    additional_topics_dict = {
        "topics": topics,
        "topic_process_methods": {
            "/rosout": process_rosout,
            "/hero/sound_name": process_sound_name,
        },
    }

    (
        subject_info,
        subject_srt_files_info,
        subject_pid_sorted_mcap_names,
        data_dict,
    ) = read_mcap_topics_24_d_05(args, "24_D_05", additional_topics_dict=additional_topics_dict)
    annotation_data_dict = {}
    trials_dir = args["trials_dir"]
    Path.mkdir(Path(trials_dir), exist_ok=True)

    transcriptions_dir = args["transcriptions_dir"]

    if args["plot_animation"]:
        auto_annotation_classes = AIC_FILTERS_WITH_ANIM
    else:
        auto_annotation_classes = AIC_FILTERS_NO_ANIM
    manual_annotation_classes = AIC_MANUAL_ANNOTATION

    track_map = data_dict[DATA_DICT_KEY_MAP]

    valid_subject_ids = get_valid_subject_ids("24_D_05")
    subject_pid_df_list = get_mcap_dfs_for_subject(
        subject_pid_sorted_mcap_names, data_dict, valid_subject_ids
    )

    # save the srt_rosbag_initial_rostime
    rostime_dict = {k: v["srt_rosbag_initial_rostime"] for k, v in subject_srt_files_info.items()}
    with open(Path(trials_dir) / Path("rostime_start.csv"), "w") as csvfile:
        for k in rostime_dict.keys():
            csvfile.write("%s,%s\n" % (k, rostime_dict[k]))

    subject_trial_start_end_times_dict = collections.OrderedDict()
    # concatenate all dfs for subject to a single dataframe and process individual trials
    for subject_pid, subject_df_list in subject_pid_df_list.items():
        if subject_pid not in valid_subject_ids:
            continue
        print(f"Processing subject {subject_pid}")
        subject_trial_start_end_times_dict[subject_pid] = collections.OrderedDict()
        # we are assuming that subject_df_list is sorted before concatenation
        subject_df = pd.concat(subject_df_list, ignore_index=True)
        # for some subjects rosbag recording had to be restarted, hence there was a reset in timestamps
        # this needs to be made monotic and should track the rostime
        make_timestamp_monotonic(subject_df)
        clean_up_nans_in_scenario_condition(subject_df)
        trial_ids_dict = mark_trial_ids(subject_df)
        annotation_data_dict[subject_pid] = {}
        annotation_data_dict[subject_pid][DATA_DICT_KEY_TRIAL_IDS] = trial_ids_dict[
            "annotation_df"
        ]
        all_trial_ids = annotation_data_dict[subject_pid][DATA_DICT_KEY_TRIAL_IDS]["trial_ids"]
        # valid trials are marked as [0...N]. Incomplete trials (can be due to proctor restart or time violation) are -1.
        # Downtime in between trials is marked as np.nan.
        valid_trial_ids = [int(l) for l in list(set(all_trial_ids[all_trial_ids >= 0]))]
        subject_manual_annotation_other_input_dict = get_srt_manual_annotation_dict(
            subject_srt_files_info, subject_pid, transcriptions_dir
        )
        # parse trials for the subject and run auto and manual annotations
        for trial_idx, valid_trial_id in enumerate(valid_trial_ids):
            subject_df_valid_trial_id = subject_df[all_trial_ids == valid_trial_id]
            trial_id = subject_pid + f"-trial_{trial_idx}"
            start_ros_time = subject_df_valid_trial_id["carla_objects log time"].iloc[0]
            end_ros_time = subject_df_valid_trial_id["carla_objects log time"].iloc[-1]
            subject_trial_start_end_times_dict[subject_pid][trial_idx] = {
                "carla_objects_time_start_end": (start_ros_time, end_ros_time)
            }
            trial = Trial(trial_id, dataframe=subject_df_valid_trial_id)
            # save trial data without any annotation columns or any additional annotation/metrics dict
            trial.write(data_dir=trials_dir)

            # run annotations and metrics
            manual_annotations = trial.run_manual_annotations(
                manual_annotation_classes, track_map, subject_manual_annotation_other_input_dict
            )
            auto_annotations = trial.run_auto_annotations(auto_annotation_classes, track_map, {})
            trial_metrics = trial.run_compute_metrics(AIC_METRICS, track_map, {})

            # save trial data with all annotation columns or any additional annotation/metrics dict
            trial.write(data_dir=trials_dir, name_suffix="_with_all_annotations_and_metrics")
            print(f"PROCESSED TRIAL ID - {valid_trial_id}, SUBJECT - {subject_pid}")

            # extract and save bsd
            if valid_trial_id > 0:
                print(
                    f"EXTRACTING BSD BETWEEN TRIAL {valid_trial_id-1} and TRIAL {valid_trial_id}"
                )
                prev_valid_trial_id = valid_trial_id - 1
                subject_df_prev_valid_trial_id = subject_df[all_trial_ids == prev_valid_trial_id]
                bsd_srt_info_dict = {}
                bsd_srt_info_dict["srt_info_dict"] = subject_manual_annotation_other_input_dict[
                    "srt"
                ]
                # extract end timestamp and rostime of previous trial
                bsd_srt_info_dict["prev_trial_info"] = {
                    "end_rostime": subject_df_prev_valid_trial_id["carla_objects log time"].values[
                        -1
                    ],
                    "end_timestamp": subject_df_prev_valid_trial_id["timestamp"].values[-1],
                }
                # extract start timestamp and rostime of next trial
                bsd_srt_info_dict["next_trial_info"] = {
                    "start_rostime": subject_df_valid_trial_id["carla_objects log time"].values[0],
                    "start_timestamp": subject_df_valid_trial_id["timestamp"].values[0],
                }
                bsd_srt_dict_for_trial = extract_bsd_srt_for_trial_id(bsd_srt_info_dict)
                save_bsd_dict(bsd_srt_dict_for_trial, trial)

            print(f"PROCESSED TRIAL ID - {valid_trial_id}, SUBJECT - {subject_pid}")

    import IPython

    IPython.embed(banner1="check done")


if __name__ == "__main__":
    main()
