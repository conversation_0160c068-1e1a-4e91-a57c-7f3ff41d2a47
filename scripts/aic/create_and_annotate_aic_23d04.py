#!/usr/bin/env python3
# Load mcap file and split it into trials (using start and end gate). And save the trials in folders
# Example invocation:
# python create_and_annotate_aic_trials.py --mcap-input-folder ~/Downloads/tri-hid-data-shared-autonomy/Participants/
# --compactsim-cache-folder ~/compactsim_cache --track-map-csv ~/Downloads/Thunderhill_data/track.csv
# --plot-animation true --trials-dir ~/data/shared_autonomy/trials/

from data_sources.compact_sim.dataloading import (
    DATA_DICT_KEY_EPISODES,
    DATA_DICT_KEY_MAP,
    load_and_cache_compactsim_data,
)
from data_sources.compact_sim.mcap_process_methods import (
    process_soss_namedlocation,
    process_soss_soundfinished,
    process_telemetry,
)
from scripts.filter_params import AIC_FILTERS_NO_ANIM, AIC_FILTERS_WITH_ANIM
from util.common_args import common_dir_arguments, parse_args
from util.filters.trajectory_filters import DATA_DICT_KEY_TRIAL_IDS, mark_trial_ids
from util.trial import Trial
from util.utility import str2bool


def parse_arguments(args=None):
    parser = parse_args(additional_arg_setters=(common_dir_arguments,))
    parser.add_argument(
        "--plot-animation",
        type=str2bool,
        help="Plot trial animation or not",
        default=False,
    )

    parser.add_argument(
        "--process-trials",
        type=str2bool,
        default=True,
        help="Use cache files if true else create cache file. (default is False)",
    )

    result = parser.parse_args(
        args=args,
    )
    return vars(result)


def main():
    args = parse_arguments()
    # extract all arguments corresponding to different data directories
    topics = ["/telemetry/velocity", "/soss/playatnamedlocation", "/soss/soundfinished"]
    # these additional topics appear in teaching study and also possibly in Phase 3 of SDM
    additional_topics_dict = {
        "topics": topics,
        "topic_process_methods": {
            "/telemetry/velocity": process_telemetry,
            "/soss/playatnamedlocation": process_soss_namedlocation,
            "/soss/soundfinished": process_soss_soundfinished,
        },
    }

    data_dict = load_and_cache_compactsim_data(
        args,
        override=False,
        additional_topics_dict=additional_topics_dict,
        verbose=True,
    )

    if args["process_trials"]:
        track_map = data_dict[DATA_DICT_KEY_MAP]
        annotation_data_dict = {}

        trials_dir = args["trials_dir"]
        for mcap_name, subject_df in data_dict[DATA_DICT_KEY_EPISODES].items():
            print("MARKING TRIAL ID")
            trial_ids_dict = mark_trial_ids(subject_df)
            annotation_data_dict[mcap_name] = {}
            annotation_data_dict[mcap_name][DATA_DICT_KEY_TRIAL_IDS] = trial_ids_dict[
                "annotation_df"
            ]

        if args["plot_animation"]:
            annotation_class = AIC_FILTERS_WITH_ANIM
        else:
            annotation_class = AIC_FILTERS_NO_ANIM

        for mcap_name, subject_df in data_dict[DATA_DICT_KEY_EPISODES].items():
            sub_id = mcap_name.split("_")[0]  # only extract PXXX from full mcap name
            print(f"SUBJECT ID: {sub_id}")
            all_trial_ids = annotation_data_dict[mcap_name][DATA_DICT_KEY_TRIAL_IDS]["trial_ids"]
            # valid trials are marked as [0...N]. Incomplete trials (can be due to proctor restart or time violation) are -1. Downtime in between trials is marked as np.nan.
            valid_trial_ids = [int(l) for l in list(set(all_trial_ids[all_trial_ids >= 0]))]

            # AIC P1 specific trial id modifications. This is because some of the trials were marked as invalid were due to drivers timing out.
            # For analysis we don't want to ignore them, hence manually re-add them to valid trial ids
            if sub_id == "P512":
                valid_trial_ids = [-1] + valid_trial_ids
            if sub_id == "P515":
                valid_trial_ids = valid_trial_ids[:2] + [-1] + valid_trial_ids[2:]

            # for each trial run map_segment_filter
            for trial_idx, valid_trial_id in enumerate(valid_trial_ids):
                print(f"PROCESSING TRIAL ID - {valid_trial_id}")
                subject_df_valid_trial_id = subject_df[all_trial_ids == valid_trial_id]
                trial_id = mcap_name + f"-trial_{trial_idx}"

                t = Trial(trial_id, dataframe=subject_df_valid_trial_id)
                t.write(data_dir=trials_dir)

                _ = t.run_auto_annotations(annotation_class, track_map, {})


if __name__ == "__main__":
    main()
