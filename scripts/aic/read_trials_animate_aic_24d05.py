#!/usr/bin/env python3
# Script for reading in trials generated from 24D05. Can be used for generating csv s of transcripts, animation of trials or any other post processing with the
# trials

# Example invocation:
# python read_trials_animate_aic_24d05.py --mcap-input-folder ~/Data/24-D-05/Rosbags/
# --compactsim-cache-folder ~/Data/24-D-05/new_cache/ --track-map-csv ~/Data/24-D-05/Rosbags/track.csv
# --plot-animation false --trials-dir ~/Data/24-D-05/trials_final/


import os
from pathlib import Path

import numpy as np
import pandas as pd

from data_sources.thunderhill.data_readers import read_map_csv
from scripts.filter_params import EGO_CENTRIC_ANIM_FILTER_WITH_CONES_WITH_SUBTITLES
from util.common_args import common_dir_arguments, parse_args
from util.trial import Trial
from util.utility import str2bool


def parse_arguments(args=None):
    parser = parse_args(additional_arg_setters=(common_dir_arguments,))
    parser.add_argument(
        "--plot-animation",
        type=str2bool,
        help="Plot trial animation or not",
        default=False,
    )
    parser.add_argument(
        "--csv-output-folder-name",
        type=str,
        help="Output folder in which the csvs for transcripts needs to be stored",
        default=None,
    )
    result = parser.parse_args(
        args=args,
    )
    return vars(result)


def main():
    args = parse_arguments()
    trials_dir = args["trials_dir"]
    map_csv_filename = str(Path(args["track_map_csv"]).expanduser().resolve())
    track_map = read_map_csv(map_csv_filename)

    auto_annotation_class = EGO_CENTRIC_ANIM_FILTER_WITH_CONES_WITH_SUBTITLES

    name_suffix = "_with_all_annotations_and_metrics"
    all_trials = Trial.read_all(trials_dir, name_suffix=name_suffix)
    csv_folder_name = args["csv_output_folder_name"]
    os.makedirs(csv_folder_name, exist_ok=True)

    for t in all_trials:
        t_df = t.get_dataframe()
        print(f"Processing trial {t.data['uid']}")

        # extract coach's srts and put them in a csv without duplication
        csv_name = csv_folder_name + f"{t.data['uid']}" + ".csv"

        all_change_inds = np.nonzero(
            (t_df["subject_coach_srts"] != t_df["subject_coach_srts"].shift(1)).values
        )[0]

        coach_srt_inds = []
        for ind in all_change_inds:
            if t_df["subject_coach_srts"][ind].content != "NA":
                coach_srt_inds.append(ind)

        df = pd.DataFrame(
            {
                "coach_subtitle": [
                    s.content for s in t_df["subject_coach_srts"][coach_srt_inds].values
                ],
                "start_time": [
                    s.start.seconds + s.start.microseconds * 10**-6
                    for s in t_df["subject_coach_srts"][coach_srt_inds].values
                ],
                "end_time": [
                    s.end.seconds + s.end.microseconds * 10**-6
                    for s in t_df["subject_coach_srts"][coach_srt_inds].values
                ],
            }
        )

        # considering an alternate way to compute te same entity for comparison and validation
        alternate_way = pd.DataFrame(
            [c.content for c in t_df["subject_coach_srts"].drop_duplicates().values]
        )
        assert [v[0] for v in alternate_way.values][1:] == list(df["coach_subtitle"].values)

        df.to_csv(csv_name)
        # import IPython; IPython.embed(banner1='check match')

        # run animation of each trial and save resulting video in the trial directory.
        # WARNING!!! - THIS CAN TAKE A LONG TIME
        # t.run_auto_annotations(auto_annotation_class, track_map)
    import IPython

    IPython.embed(banner1="check")


if __name__ == "__main__":
    main()
