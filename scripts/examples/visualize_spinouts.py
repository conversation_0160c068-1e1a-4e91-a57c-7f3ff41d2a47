#!/usr/bin/env python3
# An example script for visualizing detected events from a filter/auto-annotator (spinouts).

import pickle as pickle

import matplotlib.pyplot as plt

from data_sources.compact_sim.dataloading import (
    DATA_DICT_KEY_EPISODES,
    DATA_DICT_KEY_MAP,
    load_and_cache_compactsim_data,
)
from scripts.run_data_environment import parse_arguments
from util.filters.trajectory_filters import (
    DATA_DICT_KEY_SPINOUT,
    detect_spinout,
    filter_all_episodes,
)
from util.utility import (
    SEARCH_PARAMS_KEYS_ANNOTATION_KEY,
    SEARCH_PARAMS_KEYS_EPISODE_ID,
    SEARCH_PARAMS_KEYS_EPISODE_MARGIN,
    search_episode_transitions,
    timestamp_to_python_time,
)
from util.visualization.visualization import (
    VIS_PARAM_ANIM_OUTPUT_DIR,
    VIS_PARAM_FPS,
    VIS_PARAM_SHOW_COLUMNS,
    VIS_PARAM_TIME_WINDOW,
    visualize_timepoint,
)


def main():
    args = parse_arguments()
    data_dict = load_and_cache_compactsim_data(args, override=False)
    annotation_data_dict = {}

    # Run spinout detector for all episodes
    data_dict, annotation_data_dict = filter_all_episodes(
        data_dict, annotation_data_dict, detect_spinout, DATA_DICT_KEY_SPINOUT
    )

    # Go over all episodes
    for episode_idx in data_dict[DATA_DICT_KEY_EPISODES].keys():
        panda_frame = data_dict[DATA_DICT_KEY_EPISODES][episode_idx]
        track_map = data_dict[DATA_DICT_KEY_MAP]

        # Get all transitions into spinouts
        search_params = {
            SEARCH_PARAMS_KEYS_EPISODE_ID: episode_idx,
            SEARCH_PARAMS_KEYS_ANNOTATION_KEY: DATA_DICT_KEY_SPINOUT,
            SEARCH_PARAMS_KEYS_EPISODE_MARGIN: 5.0,
        }
        search_results = search_episode_transitions(
            search_params=search_params, annotation_data=annotation_data_dict
        )

        # Visualize each episode's curvature over time.
        x = data_dict["episodes"][episode_idx]["ego_x"]
        y = data_dict["episodes"][episode_idx]["ego_y"]
        cv = annotation_data_dict[episode_idx]["spinout"]["curvature_signal"]
        plt.scatter(x, y, None, cv)
        plt.axis("equal")
        plt.title(f"Curvature map, episode number {episode_idx}")
        plt.show()

        # Create arguments for visualization function
        for idx in range(len(search_results["detected_timestamps"])):
            visualization_params = {
                VIS_PARAM_SHOW_COLUMNS: None,
                VIS_PARAM_TIME_WINDOW: 10,
                VIS_PARAM_ANIM_OUTPUT_DIR: "",
                VIS_PARAM_FPS: 10,
            }

            timepoint = search_results["detected_timestamps"][idx]

            # Run visualization for each detected spinout
            visualize_timepoint(
                vehicle_data=panda_frame,
                reference_track_data=track_map,
                timepoint=timepoint,
                params=visualization_params,
            )
            plt.title(f"Detected event {idx}, episode number {episode_idx}")
            plt.show()

    import IPython

    IPython.embed(header="check filter results")


if __name__ == "__main__":
    main()
