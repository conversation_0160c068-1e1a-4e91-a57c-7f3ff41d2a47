#!/usr/bin/env python3
# An example script for visualizing detected events from a filter/auto-annotator (overtakes).

import functools
import pickle as pickle

import matplotlib.pyplot as plt

from data_sources.compact_sim.dataloading import (
    DATA_DICT_KEY_EPISODES,
    DATA_DICT_KEY_MAP,
    load_and_cache_compactsim_data,
)
from scripts.run_data_environment import parse_arguments
from util.filters.trajectory_filters import (
    DATA_DICT_KEY_OVERTAKE,
    detect_overtake,
    filter_all_episodes,
)
from util.utility import (
    SEARCH_PARAMS_KEYS_ANNOTATION_KEY,
    SEARCH_PARAMS_KEYS_EPISODE_ID,
    SEARCH_PARAMS_KEYS_EPISODE_MARGIN,
    search_episode_transitions,
    timestamp_to_python_time,
)
from util.visualization.visualization import (
    VIS_PARAM_ANIM_OUTPUT_DIR,
    VIS_PARAM_FPS,
    VIS_PARAM_SHOW_COLUMNS,
    VIS_PARAM_SHOW_RACING_LINE,
    VIS_PARAM_TIME_WINDOW,
    visualize_timepoint,
)


def main():
    args = parse_arguments()
    if not args["mcap_input_file"] and not args["mcap_input_folder"]:
        # Default example
        args["mcap_input_file"] = "overtake_right_0.mcap"
        args["compactsim_cache_folder"] = "~/hid_common/util/filters/resources/test_data_cache"

    data_dict = load_and_cache_compactsim_data(args, override=False)

    annotation_data_dict = {}
    detector = functools.partial(detect_overtake, dist_threshold=8.0)
    # Run overtake detector for all episodes
    data_dict, annotation_data_dict = filter_all_episodes(
        data_dict, annotation_data_dict, detector, DATA_DICT_KEY_OVERTAKE
    )

    # Go over all episodes
    for episode_idx in data_dict[DATA_DICT_KEY_EPISODES].keys():
        panda_frame = data_dict[DATA_DICT_KEY_EPISODES][episode_idx]
        track_map = data_dict[DATA_DICT_KEY_MAP]

        # Get all transitions into overtakes
        search_params = {
            SEARCH_PARAMS_KEYS_EPISODE_ID: episode_idx,
            SEARCH_PARAMS_KEYS_ANNOTATION_KEY: DATA_DICT_KEY_OVERTAKE,
            SEARCH_PARAMS_KEYS_EPISODE_MARGIN: 0.0,
        }
        search_results = search_episode_transitions(
            search_params=search_params, annotation_data=annotation_data_dict
        )

        # Create arguments for visualization function
        for idx in range(len(search_results["detected_timestamps"])):
            visualization_params = {
                VIS_PARAM_SHOW_COLUMNS: None,
                VIS_PARAM_TIME_WINDOW: 5,
                VIS_PARAM_ANIM_OUTPUT_DIR: "visualize_overtake",
                VIS_PARAM_FPS: 8,
                VIS_PARAM_SHOW_RACING_LINE: False,
            }

            timepoint = search_results["detected_timestamps"][idx]

            # Run visualization for each detected overtake
            visualize_timepoint(
                vehicle_data=panda_frame,
                reference_track_data=track_map,
                timepoint=timepoint,
                params=visualization_params,
            )
            plt.title(f"Detected event {idx}, episode number {episode_idx}")
            plt.show()

    import IPython

    IPython.embed(header="check filter results")


if __name__ == "__main__":
    main()
