# Library for global variables pertaining to filter parameters
from util.annotations import (
    ConcurrentFeedbackAnnotation24D05,
    EgoAdoProximityAnnotation,
    EgoCenterAnimationAnnotation,
    LatLongAnnotation,
    MapSegmentAnnotation,
    OutOfBoundsAnnotation,
    OvertakeAnnotation,
    PlotAnimationAnnotation,
    SpinoutAnnotation,
    SrtManualAnnotation,
)

OVERTAKE_FILTER_1 = {OvertakeAnnotation: {"dist_threshold": 8.0}}
PROXIMITY_FILTER_1 = {EgoAdoProximityAnnotation: {"dist_threshold": 30.0}}
DEFAULT_ANIM_FILTER = {PlotAnimationAnnotation: {"fps": 5, "dpi": 300, "anim_suffix": "mp4"}}
EGO_CENTRIC_ANIM_FILTER = {
    EgoCenterAnimationAnnotation: {
        "fps": 5,
        "dpi": 300,
        "anim_suffix": "mp4",
        "visualize_subtitles": False,
    }
}
MAP_SEGMENT_FILTER = {MapSegmentAnnotation: {"map_name": "thunderhill_west"}}
LAT_LONG_FILTER = {LatLongAnnotation: {"map_name": "thunderhill_west"}}
OUT_OF_BOUNDS_FILTER = {OutOfBoundsAnnotation: {"map_name": "thunderhill_west"}}

SPINOUT_FILTER = {SpinoutAnnotation: {"slip_thresh": 0.5}}
DEFAULT_ANIM_FILTER_WITH_CONES = {
    PlotAnimationAnnotation: {"fps": 5, "dpi": 300, "plot_cones": True, "anim_suffix": "mp4"}
}
EGO_CENTRIC_ANIM_FILTER_WITH_CONES = {
    EgoCenterAnimationAnnotation: {"fps": 5, "dpi": 300, "plot_cones": True, "anim_suffix": "mp4"}
}
EGO_CENTRIC_ANIM_FILTER_WITH_CONES_WITH_SUBTITLES = {
    EgoCenterAnimationAnnotation: {
        "fps": 5,
        "dpi": 300,
        "plot_cones": True,
        "anim_suffix": "mp4",
        "visualize_subtitles": True,
    }
}
AIC_ANIM_FILTER = {**EGO_CENTRIC_ANIM_FILTER_WITH_CONES_WITH_SUBTITLES}

QUICK_STUDY_ANIM_FILTER = {
    EgoCenterAnimationAnnotation: {
        "fps": 5,
        "dpi": 300,
        "plot_cones": True,
        "anim_suffix": "mp4",
        "plot_sound_name": True,
    }
}

# Manual Annotation
SRT_ANNOTATION = {SrtManualAnnotation: {}}
INSTRUCTION_CATEGORY_ANNOTATION = {ConcurrentFeedbackAnnotation24D05: {}}

# Study specific filters
SDM_TRAINING_FILTERS = {
    **OVERTAKE_FILTER_1,
    **PROXIMITY_FILTER_1,
    **DEFAULT_ANIM_FILTER,
    **EGO_CENTRIC_ANIM_FILTER,
    **MAP_SEGMENT_FILTER,
    **SPINOUT_FILTER,
}
AIC_FILTERS_NO_ANIM = {
    **MAP_SEGMENT_FILTER,
    **LAT_LONG_FILTER,
    **OUT_OF_BOUNDS_FILTER,
    **SPINOUT_FILTER,
}

AIC_FILTERS_WITH_ANIM = {**AIC_FILTERS_NO_ANIM, **AIC_ANIM_FILTER}
QUICK_STUDY_FILTERS_WITH_ANIM = {**QUICK_STUDY_ANIM_FILTER}
AIC_MANUAL_ANNOTATION = {**SRT_ANNOTATION}
AIC_INSTRUCTION_CATEGORY_ANNOTATION = {**INSTRUCTION_CATEGORY_ANNOTATION}
