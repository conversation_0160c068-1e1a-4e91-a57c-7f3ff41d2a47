# scripts
## Scripts information

### compute_aic_p1_metrics.py
* Script Owner: <PERSON><PERSON>
* Project: AIC
* What it does: Computes metrics on the AIC p1 trials.
* Readiness to use: Yes
### create_and_annotate_aic_23d04.py
* Script Owner: <PERSON>ak <PERSON>th
* Project:AIC
* What it does: Load mcap file and split it into trials (using start and end gate). And save the trials in folders
* Readiness to use: Yes
### create_and_annotate_sdm_trials.py
* Script Owner: <PERSON>
* Project: SDM
* What it does: Load SDM mcap files and split it into trials (using start and end gate). And save the trials in folders and run auto annotation for Overtake
* Readiness to use: Yes
### create_and_annotate_aic_24d05.py
* Script Owner: Deepak Gopinath
* Project: AIC
* What it does: Load mcap files for 15 subjects and split it into trials (using start and end gate). And save the trials in folders after running auto annotations and metric computations
* Readiness to use: Yes
### filter_params.py
* Script Owner: <PERSON><PERSON><PERSON> Cui
* Project: General
* What it does: Library for global variables pertaining to filter parameters
* Readiness to use: Yes
### load_trials.py
* Script Owner: Xiongyi Cui
* Project: General
* What it does: Loads trials from a folder
* Readiness to use: Yes
### manual_annotation.py
* Script Owner: Xiongyi Cui
* Project: General
* What it does: Loads all trials, render animation and people can set a break point and manually annotate them.
* Readiness to use: Yes
### metric_params.py
* Script Owner: Deepak Gopinath
* Project: General
* What it does: Library for global variables pertaining to metric parameters
* Readiness to use: Yes
### run_data_environment.py
* Script Owner: Thomas Balch
* Project: General
* What it does: Load/parse the data (designed to run over entire logs, referred to as episodes) and run filters, visualizations, etc, in an IPython environment.
* Readiness to use: Yes, but starting to be deprecated (2/8/2024)
### convert_pkl_to_parquet.py
* Script Owner: Zuber Chataiwala
* Project: General
* What it does: Convert the trial and annotation pickle files to parquet files.
* Readiness to use: Yes
### annotation_data_unifier.py
*  Script Owner: Zuber Chataiwala
* Project: General
* What it does: This script processes annotated JSON files, unifies annotation data with trajectory data,
visualizes the unified data, and optionally merges videos of the original footage and the visualized trajectories.
* Readiness to use: Yes