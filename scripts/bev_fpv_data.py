"""
This script loads logs of a particular trial, merges image data of carla ros2 chase camera and spectator camera with the sim bag data. Generates a pickle file as an output.
Example:
    python scripts/bev_fpv_data.py --log_folder ~/data/shared_autonomy/tri-hid-data-shared-autonomy/20240312T171236_instructor_trial/
"""
import argparse
import glob
import os
import pickle
from pathlib import Path

import pandas as pd

from data_sources.compact_sim.mcap_parser import ProcessCompactSimMcap
from data_sources.compact_sim.mcap_process_methods import *


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--log_folder", type=str, default="logs")
    parser.add_argument("--verbose", help="Print debug messages", action="store_true")

    args = parser.parse_args()

    input_dir = args.log_folder
    output_filename = "instructor_trial_data.pkl"

    # Get chase cam data files
    chase_cam_files = glob.glob(
        os.path.join(os.path.expanduser(input_dir), "**/carla_ros2_camera_chase*.mcap"),
        recursive=True,
    )
    chase_cam_files = sorted(chase_cam_files)

    # Get spectator cam data files
    spectator_cam_files = glob.glob(
        os.path.join(os.path.expanduser(input_dir), "**/carla_ros2_camera_spectator*.mcap"),
        recursive=True,
    )
    spectator_cam_files = sorted(spectator_cam_files)

    # Get sim bag data files
    sim_bag_files = glob.glob(
        os.path.join(os.path.expanduser(input_dir), "**/sim_bag_*.mcap"), recursive=True
    )
    sim_bag_files = sorted(sim_bag_files)

    process_mcap = ProcessCompactSimMcap()
    process_mcap.default_topics_to_read = []

    # Create dataframe for chase cam data
    df_chase_list = []
    for chase_cam_filename in chase_cam_files:
        df_chase = process_mcap.mcap_to_dataframe(
            log_path=chase_cam_filename,
            reference_topic="/carla_camera/chase/hero/image/compressed",
            additional_topics_dict={
                "topics": [
                    "/carla_camera/chase/hero/camera_info",
                    "/carla_camera/chase/hero/image/compressed",
                ],
                "topic_process_methods": {
                    "/carla_camera/chase/hero/camera_info": process_carla_chase_cam_info,
                    "/carla_camera/chase/hero/image/compressed": process_carla_chase_image,
                },
            },
            verbose=args.verbose,
        )
        df_chase_list.append(df_chase)

    final_df_chase = pd.concat(df_chase_list, ignore_index=True)

    # Create dataframe for spectator cam data
    df_spectator_list = []
    for spectator_cam_filename in spectator_cam_files:
        df_spectator = process_mcap.mcap_to_dataframe(
            log_path=spectator_cam_filename,
            reference_topic="/carla_camera/spectator_front/hero/image/compressed",
            additional_topics_dict={
                "topics": [
                    "/carla_camera/spectator_front/hero/camera_info",
                    "/carla_camera/spectator_front/hero/image/compressed",
                ],
                "topic_process_methods": {
                    "/carla_camera/spectator_front/hero/camera_info": process_carla_spectator_front_cam_info,
                    "/carla_camera/spectator_front/hero/image/compressed": process_carla_spectator_front_image,
                },
            },
            verbose=args.verbose,
        )
        df_spectator_list.append(df_spectator)

    final_df_spectator = pd.concat(df_spectator_list, ignore_index=True)

    # Create dataframe for sim bag data
    df_sim_bag_list = []
    process_mcap = ProcessCompactSimMcap()
    for sim_bag_filename in sim_bag_files:
        df_sim_bag = process_mcap.mcap_to_dataframe(
            log_path=sim_bag_filename,
            verbose=args.verbose,
        )
        df_sim_bag_list.append(df_sim_bag)

    final_df_sim_bag = pd.concat(df_sim_bag_list, ignore_index=True)

    merge_col = [i for i in final_df_chase.columns if "log time" in i][0]
    reference_col = [i for i in final_df_spectator.columns if "log time" in i][0]

    merged_df = pd.merge_asof(
        final_df_spectator,
        final_df_chase,
        left_on=reference_col,
        right_on=merge_col,
        direction="backward",
    )

    merge_col = [i for i in final_df_sim_bag.columns if "log time" in i][0]
    merged_df_final = pd.merge_asof(
        final_df_sim_bag,
        merged_df,
        left_on=merge_col,
        right_on=reference_col,
        direction="backward",
    )

    # Write to pickle file
    output_pathname = Path(output_filename)
    with output_pathname.open("wb") as fp:
        pickle.dump(merged_df_final, fp)


if __name__ == "__main__":
    main()
