"""EC2 capacity reservation script.

Usage:

python ec2-capacity-reservation.py -i <instance-type> \
    -az <availability-zone> \
    -p <profile> \
    -n <instance-count> \
    -t <team-tag> \
    -c <instance-match-criteria>
    -s <sleep-time>
"""


import argparse
import os
import random
import sys
import time

import boto3


def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Create an AWS EC2 Capacity Reservation.")

    parser.add_argument(
        "--instance-type", "-i", type=str, help="Type of the instance (e.g., t2.micro)"
    )
    parser.add_argument(
        "--availability-zone",
        "-az",
        type=str,
        help="Availability zone for the reservation (e.g., us-east-1a)",
    )
    parser.add_argument("--profile", "-p", type=str, help="AWS profile name")
    parser.add_argument("--instance-count", "-n", type=int, help="Number of instances to reserve")
    parser.add_argument("--team-tag", "-t", type=str, help="Tag value for the team")
    parser.add_argument(
        "--instance-match-criteria",
        "-c",
        type=str,
        help="Criteria for matching instances (e.g., open)",
    )
    parser.add_argument(
        "--sleep-time",
        "-s",
        required=False,
        default=2,
        type=int,
        help="Sleep time between to consecutive  request.",
    )

    args = parser.parse_args()
    return args


def get_availability_zones(ec2_client):
    """Retrieve the list of Availability Zones for the specified region."""
    try:
        response = ec2_client.describe_availability_zones()
        return [az["ZoneName"] for az in response["AvailabilityZones"]]
    except Exception as e:
        print(f"Error retrieving Availability Zones: {e}")
        sys.exit(1)


def create_capacity_reservation(ec2_client, args):
    """

    Args:
        ec2_client: boto3 EC2 client
        args: command line arguments contains, availability zone, instance type instance count,
        team tag, instance match criteria

    Returns:
       Response (Dict): boto3 api response.
    """
    try:
        response = ec2_client.create_capacity_reservation(
            AvailabilityZone=args.availability_zone,
            InstanceType=args.instance_type,
            InstanceCount=int(args.instance_count),
            EndDateType="unlimited",
            Tenancy="default",
            EbsOptimized=True,
            TagSpecifications=[
                {
                    "ResourceType": "capacity-reservation",
                    "Tags": [{"Key": "Team", "Value": args.team_tag}],
                }
            ],
            InstanceMatchCriteria=args.instance_match_criteria,
            InstancePlatform="Linux/UNIX",
        )
        print(dict(response))

        state = response["CapacityReservation"]["State"]
        reservation_id = response["CapacityReservation"]["CapacityReservationId"]
        if state == "active":
            print(
                f"Created a CR in az {args.availability_zone} for {args.instance_count} {args.instance_type} instances"
            )
        return state, reservation_id
    except Exception as e:
        print(f"Error: {e}")
        return "failed", None


def main():
    # Parse command-line arguments
    args = parse_arguments()

    # Set up AWS session
    session = boto3.Session(profile_name=args.profile)
    ec2_client = session.client(
        "ec2",
        region_name=os.getenv("AWS_REGION"),
        aws_access_key_id=os.getenv("AWS_ACCESS_KEY"),
        aws_secret_access_key=os.getenv("AWS_SECRET_KEY"),
        aws_session_token=os.getenv("AWS_SESSION_TOKEN"),
    )

    availability_zones = get_availability_zones(ec2_client)

    success = False
    while not success:
        for az in availability_zones:
            state, reservation_id = create_capacity_reservation(ec2_client, az, args)
            if state == "active":
                print(
                    f"Created a CR in az {args.availability_zone} for {args.instance_count} {args.instance_type} instances"
                )
                print(f"CR id : {reservation_id}")
                success = True
                break
            else:
                sleep_time = args.sleep_time
                print(f"Retrying in {sleep_time} seconds...")
                time.sleep(sleep_time)


if __name__ == "__main__":
    main()
