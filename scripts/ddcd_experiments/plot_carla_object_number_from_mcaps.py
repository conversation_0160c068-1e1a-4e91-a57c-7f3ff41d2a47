import argparse
import glob
import os
from collections import defaultdict
from itertools import chain

import matplotlib.pyplot as plt
from joblib import Parallel, delayed
from mcap_ros2.reader import read_ros2_messages


def get_mcap_object_num(mcap_file):
    msgs = []
    topic_list = ["/carla/objects"]
    for msg_i, msg in enumerate(read_ros2_messages(mcap_file, topics=topic_list)):
        if msg is None or msg.ros_msg is None:
            continue
        msgs.append([msg.log_time_ns / 1e9, len(msg.ros_msg.objects)])
    return msgs


def get_mcap_scenario_timings(mcap_file):
    msgs = defaultdict(list)  # dictionary to hold the messages
    topic_list = ["/experiment/study/ctrl/start_scenario", "/experiment/study/ctrl/stop_scenario"]
    for msg_i, msg in enumerate(read_ros2_messages(mcap_file, topics=topic_list)):
        if msg is None or msg.ros_msg is None:
            continue
        topic = msg.channel.topic
        msgs[topic].append([msg.log_time_ns / 1e9, msg.ros_msg.data])
    return msgs


def plot_mcap_carla_object_number(mcap_list, figsize=(15, 6), output_path=None):
    """Given a list of mcaps, extract the /carla/object number over time and plot along with the scenarios"""

    print(f"Processing {len(mcap_list)} mcaps in parallel (will take a bit of time).")

    # extract object numbers
    num_obj = Parallel(n_jobs=-1)(
        delayed(get_mcap_object_num)(mcap_file) for mcap_file in mcap_list
    )
    num_obj_chained = list(chain.from_iterable(num_obj))
    num_obj = sorted(num_obj_chained, key=lambda x: x[0])  # sort by timestamp

    # extract scenario information
    msgs = Parallel(n_jobs=-1)(
        delayed(get_mcap_scenario_timings)(mcap_file) for mcap_file in mcap_list
    )
    scenarios = defaultdict(list)
    for d in msgs:
        for key, value in d.items():
            scenarios[key].extend(value)
    scenarios["/experiment/study/ctrl/start_scenario"] = sorted(
        scenarios["/experiment/study/ctrl/start_scenario"], key=lambda x: x[0]
    )
    scenarios["/experiment/study/ctrl/stop_scenario"] = sorted(
        scenarios["/experiment/study/ctrl/stop_scenario"], key=lambda x: x[0]
    )

    plt.figure(figsize=figsize)
    plt.plot([_[0] for _ in num_obj], [_[1] for _ in num_obj])

    for s in scenarios["/experiment/study/ctrl/start_scenario"]:
        x = s[0]
        string = s[1]
        plt.axvline(x=x, color="blue", linestyle="-", alpha=0.7)  # Vertical line
        plt.text(
            x, plt.ylim()[1], string, color="blue", rotation=90, verticalalignment="bottom"
        )  # Text

    for s in scenarios["/experiment/study/ctrl/stop_scenario"]:
        x = s[0]
        string = s[1]
        plt.axvline(x=x, color="red", linestyle="--", alpha=0.5)  # Vertical line

    plt.xlabel("time (s)")
    plt.ylabel("number of /carla/objects")
    if output_path is not None:
        plt.savefig(output_path, dpi=600)
        plt.close()
    else:
        plt.show()
    return num_obj, scenarios


if __name__ == "__main__":
    """
    Plot the number of carla objects in an mcap file over time (takes a bit of time):
       python plot_carla_object_number_from_mcaps.py --participant_dir /data/motion-simulator-logs/Participants/P702/Part1 --output_dir tmp.png
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--participant_dir",
        type=str,
        default="/data/motion-simulator-logs/Participants/P720",
        help="path to the participant level of the log directory",
    )
    parser.add_argument(
        "--output_png",
        type=str,
        default="/data/motion-simulator-logs/Processed/Clean/Participants/P720/carla_obj_plot.png",
    )
    args = parser.parse_args()
    mcap_list = sorted(
        glob.glob(os.path.join(args.participant_dir, "*/sim_bag*/*mcap")),
        key=lambda x: (os.path.dirname(x).split("/")[-1], int(x.split("_")[-1].split(".")[0])),
    )
    plot_mcap_carla_object_number(mcap_list, figsize=(42, 10), output_path=args.output_png)
