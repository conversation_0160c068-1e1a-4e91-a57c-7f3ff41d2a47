import argparse
import glob
import os
import re
import shutil

import pandas as pd


def list_participants(data_path):
    """Return a list of participants P* in a given data path"""
    participant_list = glob.glob(os.path.join(data_path, "P*"))
    participant_list = sorted([p.split("/")[-1] for p in participant_list])
    return participant_list


def build_scenario_table(participant_path):
    """Build a table of scenarios using information from all Parts"""
    key_scenario_timing_paths = glob.glob(
        os.path.join(participant_path, "Part*", "key_scenario_timings.csv")
    )
    assert len(key_scenario_timing_paths) > 0, print(
        f"No key_scenario_timings files found in {participant_path}"
    )
    for ii, k in enumerate(key_scenario_timing_paths):
        df = pd.read_csv(k)
        df["data_path"] = os.path.dirname(k)
        df["round_id"] = os.path.dirname(k).split("/")[-1]
        if ii == 0:
            df_master = df
        else:
            df_master = (
                pd.concat([df, df_master]).sort_values(by="start_time").reset_index(drop=True)
            )

    # compute scenario duration
    df_master["duration_s"] = (df_master["end_time"] - df_master["start_time"]) / 1e9
    # find driving conditions
    df_master["driving_condition"] = df_master["scenario_name"].apply(
        lambda x: x.split("/")[-1] if x.startswith("driving") else ""
    )
    # find driving practice scenarios
    df_master["driving_practice"] = df_master["scenario_name"].apply(
        lambda x: True if x.startswith("driving/practice") else False
    )
    return df_master


def move_folder(src, dst, overwrite_existing=False):
    # check if source folder exists
    if not os.path.exists(src):
        raise FileNotFoundError(f"Source folder {src} does not exist.")

    # check if destination folder already exists
    if os.path.exists(dst):
        print(f"Destination folder {dst} already exists")
        if overwrite_existing:
            print("Remove existing destination folder and overwrite.")
            shutil.rmtree(dst)
        else:
            print("Skipping move operation.")
            return
    # move the folder
    shutil.move(src, dst)
    print(f"Folder moved from {src} to {dst}")


def remove_dirs_with_prefix(base_path, prefix):
    # list all directories in the base path
    for entry in os.listdir(base_path):
        dir_path = os.path.join(base_path, entry)
        # check if it's a directory and its name starts with the prefix
        if os.path.isdir(dir_path) and entry.startswith(prefix):
            print(f"Removing directory: {dir_path}")
            shutil.rmtree(dir_path)


def clean_log_part_name(input_basename):
    # find first occurrence of "part" (ignore case)
    match = re.search(r"part", input_basename, re.IGNORECASE)
    if not match:
        return input_basename  # return original string if "part" not found
    result = input_basename[match.start() :]  # remove everything before Part
    result = result.replace("_", "")  # remove underscores
    result = result.replace("part", "Part", 1)  # capitalize first occurrence of Part
    return result


def get_subdirectories(directory_path):
    # list all subdirs in a given directory
    return [
        entry
        for entry in os.listdir(directory_path)
        if os.path.isdir(os.path.join(directory_path, entry))
    ]


def clean_24d07_dataset(local_path_to_processed_original, participant_list=None):
    """Clean up data from mcap_processor_24d07 by removing duplicated scenarios and part structure"""

    if not participant_list:
        participant_list = list_participants(local_path_to_processed_original)
    data = {}

    print("Tidying up subfolder naming discrepancies")
    # all subfolders should be in form "Part{1,2}{,a,b,c,d,..}"
    for p in participant_list:
        data_folder = os.path.join(local_path_to_processed_original, p)
        part_names = get_subdirectories(data_folder)
        for part_name in part_names:
            clean_name = clean_log_part_name(part_name)
            src_dir = os.path.join(data_folder, part_name)
            dest_dir = os.path.join(data_folder, clean_name)
            print(f"Renaming {src_dir} to {dest_dir}.")
            shutil.move(src_dir, dest_dir)

    print("Checking for duplicated scenarios.")
    for p in participant_list:
        data[p] = build_scenario_table(os.path.join(local_path_to_processed_original, p))
        # remove letters from parts and rename to R1, R2
        data[p]["new_round_id"] = (
            data[p]["round_id"]
            .apply(lambda x: x.join(c for c in x if c.isdigit()))
            .apply(lambda x: f"R{x}")
        )
        # flag duplicates
        data[p]["is_duplicate"] = data[p].duplicated(
            subset=["scenario_name", "new_round_id"], keep="last"
        )
        num_duplicates = sum(data[p]["is_duplicate"].values)
        print(
            f"  {p}: {len(data[p]) - num_duplicates} unique scenarios ({num_duplicates} duplicates)"
        )

    print("Moving scenarios to new structure.")
    for p in participant_list:
        for ii in range(len(data[p])):
            d = data[p].iloc[ii]
            if d["is_duplicate"]:
                pass
            else:
                # move the data
                move_folder(
                    os.path.join(d["data_path"], d["scenario_name"]),
                    os.path.join(
                        os.path.dirname(d["data_path"]), d["new_round_id"], d["scenario_name"]
                    ),
                )

    print("Clean up duplicates and save consolidated logs and key_scenario_timings.csv file.")
    for p in participant_list:
        log_path = os.path.join(local_path_to_processed_original, p, "logs")
        os.makedirs(log_path, exist_ok=True)

        print("Moving mcap_processor.log and events_log.csv files.")
        events_logs = glob.glob(
            os.path.join(local_path_to_processed_original, p, "*", "events_log.csv")
        )
        if len(events_logs) > 0:
            for e in events_logs:
                shutil.move(e, os.path.join(log_path, "events_log-" + e.split("/")[-2] + ".csv"))
        mcap_logs = glob.glob(
            os.path.join(local_path_to_processed_original, p, "*", "mcap_processor.log")
        )
        if len(mcap_logs) > 0:
            for e in mcap_logs:
                shutil.move(
                    e, os.path.join(log_path, "mcap_processor-" + e.split("/")[-2] + ".log")
                )

        remove_dirs_with_prefix(os.path.join(local_path_to_processed_original, p), "Part")
        # add duplicates to log
        duplicates = data[p][data[p]["is_duplicate"]]
        duplicates.to_csv(os.path.join(log_path, "removed_duplicate_scenarios.csv"), index=False)
        # remove is_duplicate rows, remove new_round_id column
        data[p] = data[p][data[p]["is_duplicate"] != True]
        data[p] = data[p].drop(columns=["round_id", "is_duplicate", "data_path"])
        data[p].to_csv(
            os.path.join(local_path_to_processed_original, p, "key_scenario_timings.csv"),
            index=False,
        )

    print("Clean up complete.")


if __name__ == "__main__":
    """
    Further clean up data from mcap_processor_24d07.
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "processed_original_path",
        type=str,
        default="/data/motion-simulator-logs/Processed/Clean/Participants",
        help="path to the Participants/ of Processed/Clean dir",
    )
    parser.add_argument(
        "participant_list",
        type=str,
        nargs="+",
        default=["ERROR"],
        help="Participants to process (e.g. P701)",
    )
    args = parser.parse_args()

    clean_24d07_dataset(args.processed_original_path, args.participant_list)
