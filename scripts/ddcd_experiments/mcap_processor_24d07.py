#!/usr/bin/env python3
"""
Script to convert mcap files to raw dataframes and corresponding video
Developed to support 24-D-07.
Works a bit differently to existing mcap processing as it 
- has to deal with a bunch of different video/audio/tobii data
- snips out scenarios of interest
- deals with the experiment Part/ structure of 24-D-07 collection
- does not downsample/interpolate any data at this stage
Saves processed logs to a mirrored output_dir e.g. /data/motion-simulator-logs/Processed/Clean
"""

import argparse
import csv
import glob
import logging
import os
import re
import shutil
import sys
import tempfile
from collections import defaultdict

import numpy as np
import pandas as pd
import yaml
from mcap.reader import make_reader
from mcap_ros2.reader import read_ros2_messages

from data_sources.compact_sim.mcap_process_methods import *

# save logging output and print to console
with tempfile.NamedTemporaryFile(delete=False, suffix=".log") as temp_log_file:
    temp_log_path = temp_log_file.name

logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),  # print to console
        logging.FileHandler(temp_log_path),  # write to log file
    ],
)

# topics to determine start and end points for scenario extraction
TOPICS_FOR_SCENARIO_TIMING = [
    "/experiment/scenario/Thunderhill/status/state",
    "/experiment/study/ctrl/start_scenario",
    "/experiment/study/ctrl/stop_scenario",
    "/experiment/choicereactiontest/event_log",
    "/experiment/fixedgazetest/event_log",
    "/experiment/silentreadingtest/event_log",
    "/experiment/gazetrackingtest/event_log",
]

# scenarios for which we don't want to extract any data
SCENARIOS_TO_DISCARD = [
    "compact_sim_briefing",
    "panas_survey",
    "task_instructions",
    "video_motion_sickness",
    "survey",
    "instruction",
]

# scenarios referring to stationary tasks
SCENARIOS_STATIONARY_TASKS = [
    "choice_reaction",
    "gaze_tracking",
    "fixed_gaze",
    "silent_reading",
]

# key scenarios which do not involve driving
NON_DRIVING_KEY_SCENARIOS = SCENARIOS_STATIONARY_TASKS + ["tobii_calibration"]

# renaming of practice scenarios to fit naming convention
NEW_SCENARIO_NAMES = {
    "practice/practice_driving": "driving/practice/no_task",
    "practice/practice_task_nback": "driving/practice/nback_task",
    "practice/practice_task_statement": "driving/practice/statement_task",
}

# topics of interest and processors for each
TOPICS_AND_PROCESSORS = {
    "/carla/objects": process_all_carla_objects,
    "/carla/actor_list": process_carla_actor_list,
    "/carla/hero/vehicle_status": process_hero_status,
    "/experiment/tobii/calib_data": process_tobii_calib_data,
    "/experiment/tobii/test_result": process_tobii_test_result,
    "/usercontrols/status/steeringwheel_angle": create_process_simple_data(
        "usercontrols_status_steeringwheel_angle"
    ),
    "/usercontrols/status/throttle_pedal": create_process_simple_data(
        "usercontrols_status_throttle_pedal"
    ),
    "/usercontrols/status/brake_pedal": create_process_simple_data(
        "usercontrols_status_brake_pedal"
    ),
    "/telemetry/roadwheel_angle": create_process_simple_data("telemetry_roadwheel_angle"),
    "/telemetry/vehicleodometer": create_process_simple_data(
        "telemetry_vehicleodometer"
    ),  # maybe useful to say X km in sim
    "/telemetry/velocity": create_process_simple_data("telemetry_velocity"),
    "/ic/speedometer": create_process_simple_data("ic_speedometer"),
    "/webcam/audio_stamped": process_audio_data,
    "/headphone/audio_stamped": process_audio_data,
    "/experiment/tobii/frame": process_gaze_data,
    "/experiment/choicereactiontest/event_log": create_process_simple_data("choice_reaction"),
    "/experiment/fixedgazetest/event_log": create_process_simple_data("fixed_gaze"),
    "/experiment/silentreadingtest/event_log": create_process_simple_data("silent_reading"),
    "/experiment/gazetrackingtest/event_log": create_process_simple_data("gaze_tracking"),
}

# topics to ignore in non-driving scenarios
TOPICS_DRIVING_SPECIFIC = {
    "/carla/objects",
    "/carla/actor_list",
    "/carla/hero/vehicle_status",
    "/usercontrols/status/steeringwheel_angle",
    "/usercontrols/status/throttle_pedal",
    "/usercontrols/status/brake_pedal",
    "/telemetry/roadwheel_angle",
    "/telemetry/vehicleodometer",
    "/telemetry/velocity",
    "/ic/speedometer",
}

# topics to ignore in driving scenarios
TOPICS_NON_DRIVING_SPECIFIC = {
    "/experiment/choicereactiontest/event_log",
    "/experiment/fixedgazetest/event_log",
    "/experiment/silentreadingtest/event_log",
    "/experiment/gazetrackingtest/event_log",
    "/experiment/tobii/calib_data",
    "/experiment/tobii/test_result",
}


def remove_scenario_prefix(s):
    """Remove the %d_ prefix from a string"""
    return re.sub(r"^\d+_", "", s)


def any_string_match_in_list(query, data):
    for d in data:
        if query in d or d in query:
            return True
    return False


def find_scenario_timestamp(list_of_scenarios, search_string, ts_start):
    """
    Returns timestamp of earliest exact scenario name match after a given ts_start.
    Assumes list_of_scenarios is a sorted (by ts) list of (ts, name) tuples.
    """
    for scenario in list_of_scenarios:
        if scenario[1] == search_string and scenario[0] > ts_start:
            return scenario[0]
    return None


def find_events_within_times(all_events, ts_start, ts_end):
    """Return a list of events which occur between two timestamps"""
    events_of_interest = []
    for event in all_events:
        if ts_start <= event[0] <= ts_end:
            events_of_interest.append(event)
    return events_of_interest


def extract_key_scenario_timings(mcap_files, min_duration=15, debug=False):
    """Given a list of mcap files, extract an events logs and key_scenario_timings table"""
    logging.info(f"  Extracting key scenario timing from {len(mcap_files)} mcaps (takes ~seconds)")

    # from a (chronological) list of mcap_files, extract timings for events of interest
    msgs = defaultdict(list)  # dictionary to hold the messages
    for ii, m in enumerate(mcap_files):
        for msg_i, msg in enumerate(read_ros2_messages(m, topics=TOPICS_FOR_SCENARIO_TIMING)):
            if msg is None or msg.ros_msg is None:
                continue
            topic = msg.channel.topic
            if "Thunderhill" in topic:
                # assumes we are only interested in STARTING OR STOPPING Thunderhill messages
                if "STARTING" in msg.ros_msg.data or "STOPPING" in msg.ros_msg.data:
                    msgs[topic].append([msg.log_time_ns, msg.ros_msg.data])
            else:
                msgs[topic].append([msg.log_time_ns, msg.ros_msg.data])

    # sort all events by timestamp
    all_events = [item for sublist in msgs.values() for item in sublist]
    events_log = sorted(all_events, key=lambda x: x[0])

    # snip by scenario and scenarios of interest
    scenario_starts = msgs["/experiment/study/ctrl/start_scenario"]
    key_scenario_starts = []
    for s in scenario_starts:
        if not any_string_match_in_list(s[1], SCENARIOS_TO_DISCARD):
            key_scenario_starts.append(s)

    # run through all key scenarios and find snip times
    key_scenario_timings = []
    for ts_start, scenario_name in key_scenario_starts:
        # find ts_end
        scenario_name_clean = remove_scenario_prefix(scenario_name)
        ts_end = find_scenario_timestamp(
            msgs["/experiment/study/ctrl/stop_scenario"], scenario_name_clean, ts_start
        )
        # ignore broken scenarios (happens frequently in debug Tests)
        if ts_end == None:
            continue

        # extract all events within that scenario
        events = find_events_within_times(events_log, ts_start, ts_end)

        if debug:
            logging.debug(scenario_name_clean, f"{(ts_end - ts_start) / 1e9:0.1f} seconds")
            for e in events:
                logging.debug(e)

        if any_string_match_in_list(scenario_name_clean, SCENARIOS_STATIONARY_TASKS):
            # for stationary tasks, find "START" and "EXIT" timestamps
            new_ts_start = find_scenario_timestamp(events, "START", ts_start)
            new_ts_end = find_scenario_timestamp(events, "EXIT", new_ts_start)
            new_scenario = [scenario_name_clean, new_ts_start, new_ts_end]
        else:
            # for non-stationary tasks, find "STARTING" and "STOPPING" timestamps
            new_ts_start = find_scenario_timestamp(events, "STARTING", ts_start)
            new_ts_end = find_scenario_timestamp(events, "STOPPING", new_ts_start)
            new_scenario = [scenario_name_clean, new_ts_start, ts_end]

        if new_scenario[0] in NEW_SCENARIO_NAMES:
            # rename practice scenarios to clean up output directory
            new_scenario[0] = NEW_SCENARIO_NAMES[new_scenario[0]]

        # check scenario is valid before adding
        if (
            new_scenario[1] is not None
            and new_scenario[2] is not None
            and new_scenario[2] - new_scenario[1] > min_duration * 1e9
        ):
            key_scenario_timings.append(new_scenario)

    # add a "unique" check - to flag scenarios which are repeated later in the log as 0
    for ii, s in enumerate(key_scenario_timings):
        if s[0] in [_[0] for _ in key_scenario_timings[ii + 1 :]]:
            s.append(0)
        else:
            s.append(1)

    if debug:
        logging.debug("Printing all sorted events:")
        for s in events_log:
            logging.debug(s)
    return events_log, key_scenario_timings


def print_key_scenario_timings(key_scenario_timings):
    logging.info("Key scenarios + timings (0 in final column indicates scenario will be ignored):")
    if len(key_scenario_timings) == 0:
        logging.warning("  No complete scenarios of interest found, nothing to process.")
    else:
        for ii, scenario_info in enumerate(key_scenario_timings):
            if len(scenario_info) == 3:
                (scenario, start_time, end_time) = scenario_info
                unique = 1
            else:
                (scenario, start_time, end_time, unique) = scenario_info
            logging.info(
                f"{ii+1}: {scenario:>52} {(end_time - start_time)/1e9:>7.0f}s {start_time} {end_time} {unique}"
            )


def load_metadata_yaml(metadata_yaml_path, display=False):
    with open(metadata_yaml_path, "r") as file:
        metadata = yaml.safe_load(file)
        metadata = metadata["rosbag2_bagfile_information"]
        if display:
            logging.debug(f"Inspecting metadata: {metadata_yaml_path}")
            logging.debug(f"Duration (mins): {metadata['duration']['nanoseconds']/1e9/60:0.1f}")
            logging.debug(f"Starting_time: {metadata['starting_time']}")
            logging.debug(f"Message_count: {metadata['message_count']}")
            logging.debug(f"Num. mcap files: {len(metadata['relative_file_paths'])}")
            logging.debug(
                f"Num. topics_with_message_count: {len(metadata['topics_with_message_count'])}"
            )
            for _ in sorted(
                metadata["topics_with_message_count"],
                key=lambda item: item["topic_metadata"]["name"],
            ):
                logging.debug(
                    _["topic_metadata"]["name"], _["topic_metadata"]["type"], _["message_count"]
                )
    return metadata


def find_metadata_yaml_paths(log_dir):
    """Find metadata yaml paths in a log_dir and assign to pre-set names"""
    mcap_types = {
        "cam_front": "carla_ros2_camera_spectator_front_rgb_2*",
        # "cam_flow": "carla_ros2_camera_spectator_front_optical_flow_2*",
        "cam_depth": "carla_ros2_camera_spectator_front_depth_2*",
        "cam_seg": "carla_ros2_camera_spectator_front_semantic_segmentation_2*",
        "sim_bag": "sim_bag*",
        "cam_face": "webcam_video*",
    }
    metadata_yaml_paths = {}
    for k, v in mcap_types.items():
        paths = glob.glob(os.path.join(log_dir, v, "metadata.yaml"))
        if len(paths) == 1:
            metadata_yaml_paths[k] = paths[0]
        elif len(paths) == 0:
            logging.warning(f"  No metadata file found in {log_dir}.")
            metadata_yaml_paths[k] = os.path.join(log_dir, v)
        else:
            logging.warning(f"  Found {len(paths)} metadata yamls for {v}")
            metadata_yaml_paths[k] = paths
    return metadata_yaml_paths


def find_key_scenario_timings(output_dir):
    """Check for existence of key_scenario_timings.csv file and return list of lists if exists"""
    file_path = os.path.join(output_dir, "key_scenario_timings.csv")
    if os.path.exists(file_path):
        logging.info("Found existing key scenario timings file.")
        df = pd.read_csv(file_path)
        data = df.values.tolist()
        return data
    else:
        return None


def save_csv(data, file_path, fieldnames=None):
    with open(file_path, "w", newline="") as file:
        if isinstance(data, dict):
            if fieldnames == None:
                fieldnames = data.keys()
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            writer.writeheader()
            rows = [dict(zip(data.keys(), values)) for values in zip(*data.values())]
            for row in rows:
                writer.writerow(row)
        elif isinstance(data, list):
            writer = csv.writer(file)
            writer.writerow(fieldnames)
            writer.writerows(data)


def find_string_with_suffix(strings, suffix):
    for string in strings:
        if string.endswith(suffix):
            return string
    return None


def make_output_dir(output_dir, verbose=False):
    if not os.path.exists(output_dir):
        if verbose:
            logging.debug(f"Creating directory: {output_dir}")
        os.makedirs(output_dir)


def extract_video(
    video_metadata_yaml,
    video_output_dir,
    time_start_ns=0,
    time_end_ns=np.iinfo(np.int64).max,
    downsample=1,
    crop=None,
    output_format="ffv1",
    save_sample_img=True,
):
    if video_metadata_yaml.endswith(".yaml"):
        # use info from the metdata file
        metadata = load_metadata_yaml(video_metadata_yaml, display=False)
        mcap_files = [
            os.path.join(os.path.dirname(video_metadata_yaml), m)
            for m in metadata["relative_file_paths"]
        ]
        topics_of_interest = set(
            _["topic_metadata"]["name"] for _ in metadata["topics_with_message_count"]
        )
    else:
        # search the directory for mcap files anyway (sometimes metadata is missing)
        mcap_files = glob.glob(os.path.join(video_metadata_yaml, "*.mcap"))
        if len(mcap_files) == 0:
            logging.warning(f"    No mcap files found for {video_metadata_yaml}, skipping...")
            return
        else:
            mcap_files = sorted(mcap_files)
            with open(mcap_files[0], "rb") as f:
                reader = make_reader(f)
                topics_of_interest = set()
                for _, channel, _ in reader.iter_messages():
                    topics_of_interest.add(channel.topic)

    camera_name = os.path.basename(video_output_dir)

    # extract the video resolution from the first frame
    camera_info_topic = find_string_with_suffix(topics_of_interest, "camera_info")
    if camera_info_topic is None:
        # this is the case for webcam data
        assert len(topics_of_interest) == 1
        msg = next(read_ros2_messages(mcap_files[0], topics=camera_info_topic))
        np_arr = np.frombuffer(msg.ros_msg.data, np.uint8)
        image_np = cv2.imdecode(np_arr, cv2.COLOR_RGBA2BGR)
        height, width = image_np.shape[:2]
    else:
        # carla camera data has camera_info
        camera_info = next(read_ros2_messages(mcap_files[0], topics=camera_info_topic))
        height = camera_info.ros_msg.height
        width = camera_info.ros_msg.width

    # initialize output video and output video info
    if output_format == "ffv1":
        videowriter_format = cv2.VideoWriter_fourcc(*"FFV1")
        video_output_path = os.path.join(video_output_dir, "video.mkv")
    elif output_format == "mjpg":
        videowriter_format = cv2.VideoWriter_fourcc(*"MJPG")
        video_output_path = os.path.join(video_output_dir, "video.avi")
    if os.path.exists(video_output_path) and os.path.getsize(video_output_path) > 1e6:
        # Note: this is to handle multiple webcam files.
        # The assumption is that videos for individual scenarios do NOT straddle webcam files.
        logging.warning(f"    Video already exists. Skipping.")
        return

    if save_sample_img:
        sample_img_path = os.path.join(video_output_dir, "sample.png")

    output_height = int(height / downsample)
    output_width = int(width / downsample)

    if crop:
        # crop is in for [width, height, startingX, startingY]
        assert crop[0] > 0 and crop[1] > 0 and crop[2] >= 0 and crop[3] >= 0
        assert output_height >= crop[1] + crop[3], print(output_height, crop)
        assert output_width >= crop[0] + crop[2], print(output_height, crop)
        output_height = crop[1]
        output_width = crop[0]

    output_video = cv2.VideoWriter(
        video_output_path,
        videowriter_format,
        30,
        (output_width, output_height),
    )
    frame_count = 0

    # remove camera_info topic (doesn't change)
    topics_of_interest.discard(camera_info_topic)
    first_frame_ns = 0
    last_frame_ns = 0

    frame_log_time = camera_name + "_frame log time"
    publish_time = camera_name + "_publish_time_ns"
    header_time = camera_name + "_header_time_ns"
    frame_id = camera_name + "_frame"

    frame_timing = {
        frame_log_time: [],
        publish_time: [],
        header_time: [],
        frame_id: [],
    }

    for ii, m in enumerate(mcap_files):
        for msg_i, msg in enumerate(
            read_ros2_messages(
                m, topics=topics_of_interest, start_time=time_start_ns, end_time=time_end_ns
            )
        ):
            current_time_ns = int(msg.log_time_ns)
            if time_start_ns <= current_time_ns <= time_end_ns:
                msg_type = msg.channel.topic.split("/")[-1]

                if frame_count == 0:
                    first_frame_ns = current_time_ns
                last_frame_ns = current_time_ns
                np_arr = np.frombuffer(msg.ros_msg.data, np.uint8)

                if msg_type == "compressed":
                    image_np = cv2.imdecode(np_arr, cv2.COLOR_RGBA2BGR)

                elif msg_type == "image":
                    image_tmp = np_arr.reshape((height, width, 4))
                    image_np = cv2.cvtColor(image_tmp, cv2.COLOR_RGBA2BGR)
                if downsample > 1:
                    assert width % downsample == 0 and height % downsample == 0
                    # don't worry about aliasing since this is just for debugging
                    image_np = image_np[::downsample, ::downsample, :]

                # crop image if specified
                if crop:
                    image_np = image_np[
                        crop[3] : crop[3] + crop[1], crop[2] : crop[2] + crop[0], :
                    ]
                output_video.write(image_np)

                if frame_count == 0 and save_sample_img:
                    cv2.imwrite(sample_img_path, image_np)

                frame_timing[frame_log_time].append(msg.log_time_ns / 1e9)
                frame_timing[publish_time].append(msg.publish_time_ns)
                stamp_time = 1e9 * float(msg.ros_msg.header.stamp.sec) + float(
                    msg.ros_msg.header.stamp.nanosec
                )
                frame_timing[header_time].append(stamp_time)
                frame_timing[frame_id].append(frame_count)
                frame_count += 1
    if last_frame_ns == first_frame_ns:
        logging.warning(
            f"    No frames found for {camera_name} within time range ({time_start_ns/1e9:.0f}, {time_end_ns/1e9:.0f})"
        )
    else:
        logging.info(
            f"    {frame_count} frames found at {frame_count * 1e9 / (last_frame_ns - first_frame_ns):0.2f} Hz."
        )
    output_video.release()

    # save frame timing file as csv
    save_csv(frame_timing, os.path.join(video_output_dir, "frame_timing.csv"))


def mcap_to_topic_dict(
    mcap_list, topics, start_time=0, end_time=np.iinfo(np.int64).max, output_dir=None
):
    """
    Read mcap messages into data dict
    Inputs:
        mcap_list (list of strings): list of mcap paths to read.
        topics (list of strings): List of topics to read from log.
        start_time (int): Start time for epoch of interest in nanoseconds
        end_time (int): End time for epoch of interest in nanoseconds
        output_dir (str): Path to output directory
    Output:
        Dictionary of messages corresponding to input topics.
    """

    msg_dict = {}  # dictionary to hold the messages

    for tt, topic in enumerate(topics):
        logging.info(f"    Processing {tt+1}/{len(topics)} topic: {topic}")

        # run through each topic one by one (not very concerned with efficiency)
        msgs = []
        for ii, mcap_path in enumerate(mcap_list):
            for msg_i, msg in enumerate(
                read_ros2_messages(
                    mcap_path, topics=topic, start_time=start_time, end_time=end_time
                )
            ):
                if msg is None:
                    continue
                msgs.append(msg)

        topic_process_method = TOPICS_AND_PROCESSORS[topic]
        if len(msgs) > 0:
            logging.info(
                f"      {len(msgs)} messages found at {len(msgs) * 1e9 / (end_time - start_time):0.2f} Hz."
            )
            processed_msgs = topic_process_method(msgs, output_dir=output_dir)
            msg_dict[topic] = processed_msgs
    return msg_dict


def assert_all_equal(lst):
    assert all(x == lst[0] for x in lst), "Not all elements in the list are equal"


def extract_sim_bag(mcap_list, output_dir, time_start_ns=0, time_end_ns=np.iinfo(np.int64).max):
    """Extract topic data of interest from a list of sim_bag mcaps, and write to per-topic csvs"""
    scenario = os.path.dirname(output_dir).split("/")[-1]
    if not any(_ in scenario for _ in NON_DRIVING_KEY_SCENARIOS):
        # for driving scenarios, remove stationary test and tobii topics
        topics_list = set(TOPICS_AND_PROCESSORS.keys()) - set(TOPICS_NON_DRIVING_SPECIFIC)
        topics_list = list(topics_list)
    else:
        # if scenario is non-driving, remove driving topics from topics list
        topics_list = set(TOPICS_AND_PROCESSORS.keys()) - set(TOPICS_DRIVING_SPECIFIC)
        topics_list = list(topics_list)

    msg_dict = mcap_to_topic_dict(
        mcap_list,
        topics=topics_list,
        start_time=time_start_ns,
        end_time=time_end_ns,
        output_dir=output_dir,
    )
    for topic, datadict in msg_dict.items():
        if datadict:
            # write as separate csvs, since it makes inspection easier
            assert_all_equal([len(v) for k, v in datadict.items()])
            csv_filename = topic[1:].replace("/", "_") + ".csv"
            csv_path = os.path.join(output_dir, csv_filename)
            with open(csv_path, "w", newline="") as file:
                writer = csv.DictWriter(file, fieldnames=datadict.keys())
                writer.writeheader()
                rows = [dict(zip(datadict.keys(), values)) for values in zip(*datadict.values())]
                for row in rows:
                    writer.writerow(row)
    return


if __name__ == "__main__":
    """
    Example to initially process a log:
      python mcap_processor_24d07.py --log_dir /data/motion-simulator-logs/Participants/P702/Part1 --output_dir [root level above Tests or Participants]

    If key scenarios look good, add --process_all to process everything in the log, or --process_X to process specific X.

    Otherwise, edit the key_scenario_timings.csv file in the output_dir, or process specific scenarios with --scenarios SCENARIO_ID_1 SCENARIO_ID_2.

    Add --downsample N to downsample output videos if desired/for speed (default 1).
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--log_dir",
        type=str,
        default="/home/<USER>/data/motion-simulator-logs/Participants/P702/Part1",
        help="path to the top-level of the log directory",
    )
    parser.add_argument("--process_sim_bag", action="store_true", help="process the sim bag")
    parser.add_argument("--process_cam_face", action="store_true", help="process the webcam")
    parser.add_argument(
        "--cam_face_crop",
        type=int,
        nargs="+",
        default=[960, 960, 300, 0],
        help="Set web cam cropping parameters (no crop = 1920 1080 0 0).",
    )
    parser.add_argument(
        "--downsample", type=int, default=1, help="downsampling rate for all video"
    )
    parser.add_argument(
        "--min_scenario_duration",
        type=int,
        default=15,
        help="minimum duration for a complete scenario (sec)",
    )
    parser.add_argument(
        "--process_cam_front",
        action="store_true",
        help="process spectator_front rgb",
    )
    parser.add_argument(
        "--process_cam_depth",
        action="store_true",
        help="process spectator_front depth",
    )
    parser.add_argument(
        "--process_cam_seg",
        action="store_true",
        help="process spectator_front semantic segmentation",
    )
    parser.add_argument("--process_all", action="store_true", help="process all data")
    parser.add_argument("--verbose", action="store_true", help="print debugging info")
    parser.add_argument(
        "--output_dir",
        type=str,
        default="/home/<USER>/data/motion-simulator-logs/Processed/Clean/",
    )
    parser.add_argument(
        "--scenarios", type=int, nargs="+", default=[], help="Scenarios to process (by number 1+)"
    )

    args = parser.parse_args()

    if args.process_all:
        args.process_sim_bag = True
        args.process_cam_face = True
        args.process_cam_front = True
        args.process_cam_depth = True
        args.process_cam_seg = True

    # extract experiment part_id
    participant_or_test, subject_id, experiment_id = args.log_dir.split("/")[-3:]
    if subject_id != "Tests":
        logging.info(f"Processing logs for subject: {subject_id}, experiment: {experiment_id}")

        # create output path if it doesn't exist
        output_dir = os.path.join(args.output_dir, participant_or_test, subject_id, experiment_id)
        logging.info(f"  Processed data will be saved to {output_dir}")
        make_output_dir(output_dir)
    else:
        participant_or_test, experiment_id = args.log_dir.split("/")[-2:]

        logging.info(f"Processing logs for test: {experiment_id}")

        # create output path if it doesn't exist
        output_dir = os.path.join(args.output_dir, participant_or_test, experiment_id)
        logging.info(f"  Processed data will be saved to {output_dir}")
        make_output_dir(output_dir)

    # find metadata yaml paths
    metadata_yaml_paths = find_metadata_yaml_paths(args.log_dir)

    # look for key scenario timings file, if not found then extract and save
    key_scenario_timings = find_key_scenario_timings(output_dir)

    if metadata_yaml_paths["sim_bag"].endswith(".yaml"):
        # use info from the metadata file

        metadata = load_metadata_yaml(metadata_yaml_paths["sim_bag"], display=args.verbose)
        sim_bag_mcap_files = [
            os.path.join(os.path.dirname(metadata_yaml_paths["sim_bag"]), m)
            for m in metadata["relative_file_paths"]
        ]
    else:
        # search the directory for mcap files anyway (sometimes metadata is missing)
        # sort key to order by number: _0.mcap, _1.mcap, _2.mcap..., _10.mcap
        sim_bag_mcap_files = sorted(
            glob.glob(os.path.join(metadata_yaml_paths["sim_bag"], "*.mcap")),
            key=lambda x: int(x.split("_")[-1].split(".")[0]),
        )
        if len(sim_bag_mcap_files) == 0:
            logging.warning(
                f"    No sim_bag mcaps found: can't process data into scenarios, quitting."
            )
            sys.exit()

    if key_scenario_timings is None:
        logging.info("  Could not find key scenario timings file.")
        # load and print some basic info from the metadata.yaml
        events_log, key_scenario_timings = extract_key_scenario_timings(
            sim_bag_mcap_files, args.min_scenario_duration
        )
        # save events_log and key_scenario_timings
        save_csv(
            events_log,
            os.path.join(output_dir, "events_log.csv"),
            ["log_time_ns", "event"],
        )
        save_csv(
            key_scenario_timings,
            os.path.join(output_dir, "key_scenario_timings.csv"),
            ["scenario_name", "start_time", "end_time", "unique"],
        )
    print_key_scenario_timings(key_scenario_timings)

    # for each key scenario, snip the rosbags and save relevant data
    scenario_count = 0
    for scenario_info in key_scenario_timings:
        if len(scenario_info) == 3:
            (scenario, start_time, end_time) = scenario_info
            unique = 1
        else:
            (scenario, start_time, end_time, unique) = scenario_info
        scenario_count += 1
        if (args.scenarios and scenario_count not in args.scenarios) or not unique:
            continue
        log_msg = "*" * 3 + f"  Scenario {scenario_count}/{len(key_scenario_timings)}: " + scenario
        logging.info(log_msg)
        scenario_output_dir = os.path.join(output_dir, scenario)
        make_output_dir(scenario_output_dir)

        if args.process_sim_bag:
            data_output_dir = os.path.join(scenario_output_dir, "sim_bag")
            logging.info(f"  Processing sim_bag...")
            make_output_dir(data_output_dir, verbose=args.verbose)
            extract_sim_bag(
                sim_bag_mcap_files,
                data_output_dir,
                time_start_ns=start_time,
                time_end_ns=end_time,
            )

        # Ignore CARLA cameras unless in a driving scenario
        if not any(_ in scenario for _ in NON_DRIVING_KEY_SCENARIOS):
            if args.process_cam_front:
                data_output_dir = os.path.join(scenario_output_dir, "cam_front")
                logging.info("  Processing cam_front...")
                make_output_dir(data_output_dir, verbose=args.verbose)

                extract_video(
                    metadata_yaml_paths["cam_front"],
                    data_output_dir,
                    time_start_ns=start_time,
                    time_end_ns=end_time,
                    downsample=args.downsample,
                    output_format="mjpg",
                )

            if args.process_cam_depth:
                data_output_dir = os.path.join(scenario_output_dir, "cam_depth")
                logging.info("  Processing cam_depth...")
                make_output_dir(data_output_dir, verbose=args.verbose)

                extract_video(
                    metadata_yaml_paths["cam_depth"],
                    data_output_dir,
                    time_start_ns=start_time,
                    time_end_ns=end_time,
                    downsample=args.downsample,
                )

            if args.process_cam_seg:
                data_output_dir = os.path.join(scenario_output_dir, "cam_seg")
                logging.info("  Processing cam_seg...")
                make_output_dir(data_output_dir, verbose=args.verbose)

                extract_video(
                    metadata_yaml_paths["cam_seg"],
                    data_output_dir,
                    time_start_ns=start_time,
                    time_end_ns=end_time,
                    downsample=args.downsample,
                )

        if args.process_cam_face:
            data_output_dir = os.path.join(scenario_output_dir, "cam_face")
            logging.info("  Processing cam_face...")
            make_output_dir(data_output_dir, verbose=args.verbose)
            assert len(args.cam_face_crop) == 4
            crop_params = [int(_ / args.downsample) for _ in args.cam_face_crop]

            if isinstance(metadata_yaml_paths["cam_face"], list):
                # case of multiple separate webcam folders
                for p in metadata_yaml_paths["cam_face"]:
                    extract_video(
                        p,
                        data_output_dir,
                        time_start_ns=start_time,
                        time_end_ns=end_time,
                        downsample=args.downsample,
                        crop=crop_params,
                        output_format="mjpg",
                    )
            else:
                extract_video(
                    metadata_yaml_paths["cam_face"],
                    data_output_dir,
                    time_start_ns=start_time,
                    time_end_ns=end_time,
                    downsample=args.downsample,
                    crop=crop_params,
                    output_format="mjpg",
                )

    # copy temp log file to output_dir and clean up
    final_log_path = os.path.join(output_dir, "mcap_processor.log")
    shutil.copy(temp_log_path, final_log_path)
    os.remove(temp_log_path)
    logging.info("Processing complete. Log saved to mcap_processor.log")
