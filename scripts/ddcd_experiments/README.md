# ddcd_experiment scripts

Scripts in this subfolder are specific to the DD/CD study (24-D-07).

`generate_clean_data.sh` was used for extraction of clean 24-D-07 experiment data from raw CSIM logs, which calls on `mcap_processor_24d07.py` for mcap processing and `clean_data.py` for experiment-specific file clean up.

`plot_carla_object_number_from_mcaps.py` is useful for monitoring the number of carla objects over time from a bunch of input mcaps: a bug which was discovered during which certain types of driving hazard scenarios in 24-D-07 cause /carla/objects logging to break and the number of logged objects to freeze for future scenarios.  

