#!/bin/bash

# Generate Processed/Clean data from Processed/Original or raw mcap data for a given participant.
# bash generate_clean_data.sh PARTICIPANT_ID INPUT_PATH OUTPUT_PATH
# e.g. bash generate_clean_data.sh P716 /data/motion-simulator-logs/Participants /data/motion-simulator-logs/Processed/Clean/Participants
# don't forget to refresh AWS credentials for profile "hid": aws sso login --profile hid

set -euxo pipefail

# Default values
DEFAULT_PARTICIPANT_ID="P703"
DEFAULT_INPUT_PATH="/data/motion-simulator-logs/Participants"
DEFAULT_OUTPUT_PATH="/data/motion-simulator-logs/Processed/Clean/Participants"

# Read inputs or use default values
PARTICIPANT_ID="${1:-$DEFAULT_PARTICIPANT_ID}"
INPUT_PATH="${2:-$DEFAULT_INPUT_PATH}"
OUTPUT_PATH="${3:-$DEFAULT_OUTPUT_PATH}"

# set participant-specific s3 and local paths
PATH_S3_RAW=s3://tri-hid-data-shared-autonomy/24-D-07/Participants/${PARTICIPANT_ID}
PATH_S3_ORIGINAL=s3://tri-hid-data-shared-autonomy/24-D-07/Processed/Original/Participants/${PARTICIPANT_ID}
PATH_S3_CLEAN=s3://tri-hid-data-shared-autonomy/24-D-07/Processed/Clean/Participants/${PARTICIPANT_ID}
PATH_LOCAL_RAW=${INPUT_PATH}/${PARTICIPANT_ID}
PATH_LOCAL_CLEAN=${OUTPUT_PATH}/${PARTICIPANT_ID}

# check that ${PATH_LOCAL_CLEAN} does not yet exist
if [ -d "$PATH_LOCAL_CLEAN" ]; then
    echo "$PATH_LOCAL_CLEAN already exists, exiting."
    exit 0
fi

# check that ${PATH_LOCAL_RAW} does not yet exist
if [ -d "$PATH_LOCAL_RAW" ]; then
    echo "$PATH_LOCAL_RAW already exists, exiting."
    exit 0
fi

# check that ${PATH_S3_CLEAN} does not yet exist
if aws s3 ls --profile hid ${PATH_S3_CLEAN} > /dev/null 2>&1; then
    echo "${PATH_S3_CLEAN} already exists, exiting."
fi

# create unconsolidated data at ${PATH_LOCAL_CLEAN}
if aws s3 ls --profile hid ${PATH_S3_ORIGINAL} > /dev/null 2>&1; then
    echo "${PATH_S3_ORIGINAL} already exists, beginning download."
    sleep 2
    aws s3 sync --profile hid ${PATH_S3_ORIGINAL} ${PATH_LOCAL_CLEAN} --exclude "*cam_front/video.mkv" --exclude "*cam_face/video.mkv"
else
    if aws s3 ls --profile hid ${PATH_S3_RAW} > /dev/null 2>&1; then
        echo "Could not find processed data. Downloading ${PATH_S3_RAW}."
        sleep 2
        aws s3 sync --profile hid ${PATH_S3_RAW} ${PATH_LOCAL_RAW}
        echo "Processing raw data to ${PATH_LOCAL_CLEAN}."
        for PART in `ls ${PATH_LOCAL_RAW}/`; do 
            python data_sources/ddcd_experiments/mcap_processor_24d07.py \
                --log_dir ${PATH_LOCAL_RAW}/$PART \
                --output_dir `dirname ${OUTPUT_PATH}` \
                --process_all;
        done
    else
        echo "No raw data found at ${PATH_S3_RAW}, exiting."
        echo "Are you sure you have valid credentials? (aws sso login --profile hid)"
        exit 0
    fi
fi

# consolidate the data at ${PATH_LOCAL_CLEAN}
echo "Running clean_original_data.py"
python data_sources/ddcd_experiments/clean_data.py ${OUTPUT_PATH} ${PARTICIPANT_ID}

# Upload to S3_CLEAN
echo "Processing complete."
echo "After inspection, to upload the clean data to s3, run:"
echo "aws s3 sync --profile hid ${PATH_LOCAL_CLEAN} ${PATH_S3_CLEAN}"