"""
This script processes pickle (.pkl) files to generate Parquet and/or YAML files.

The script takes a directory path as an argument and processes each .pkl file within the directory. It assumes that each .pkl file either contains:

    - A dictionary where one of the keys holds a DataFrame, or
    - A DataFrame directly.
It extracts the data stored into the pickle files, converts them into the dataframe and saved them as Parquet files.
Additionally, it saves other non-dataframe object into the YAML file.
The script also handles the conversion of specific subtitle data structure within the dataframe.

Usage:
    python3 convert_pkl_to_parquet.py <dir_path>

Arguments:
    dir_path: The directory path where the pickle files are located.

Functionality:
1. Iterates through all the .pkl files into the provided directory.
2. Extract data from the .pkl files and identifies the version number from the filenames.
3. Converts specific subtitle data within the dataframe to SRT block for compatiblity with Parquet.
4. Saves the dataframe content to parquet file with the updated version name in filenames.
5. Saves other non-dataframe objects to a corrosponding YAML files.
6. Generates a text file listing all processed file paths.

Dependencies:
- pandas
- srt
"""
import os
import pickle
import re
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path

import pandas as pd
import srt
import yaml

# Define a regex pattern to extract the version part from the filename
pattern1 = re.compile(r"_v(\d+).*$")

# Define a regex for difference between trail and annotation file
pattern2 = re.compile(r"_v\d+\.trial$")

paths = []


def fetch_version(filename):
    # Search for the pattern in the filename
    match = pattern1.search(filename)
    is_trial = False
    if pattern2.search(filename):
        is_trial = True
    if match:
        # Extract the version number
        version = match.group(1)
        return version, is_trial
    else:
        return None, None


# Function to save non-dataframe objects to a YAML file
def save_objects_to_yaml(obj, filename):
    with open(filename, "w") as file:
        yaml.dump(obj, file)


# Function to load objects from a YAML file
def load_file_from_yaml(filename):
    config = {}
    with open(filename, "r") as file:
        try:
            config = yaml.safe_load(file)
        except yaml.YAMLError as exc:
            print(exc)
    return config


# Function to Convert an srt.Subtitle object to a dictionary
def subtitle_to_dict(subtitle):
    if not isinstance(subtitle.start, timedelta):
        subtitle.start = timedelta(seconds=subtitle.start)

    if not isinstance(subtitle.end, timedelta):
        subtitle.end = timedelta(seconds=subtitle.end)
    return subtitle.to_srt()


# Function to convert dictionary back to srt.Subtitle
def dict_to_subtitle(row):
    return list(srt.parse(row))[0]


def check_the_content(file_path):
    # Add the code for the deserializiug the data
    df = pd.read_parquet(file_path)
    columns_to_check = ["subject_coach_srts", "subject_driver_srts"]

    for column in columns_to_check:
        if column in df.columns:
            df[column] = df[column].apply(dict_to_subtitle)
    return df


# Function to process a .pkl file
def process_pkl_file(file_path: str):
    directory, filename = file_path.rsplit("/", 1)
    filename = filename.rsplit(".pkl", 1)[0]
    version, is_trial = fetch_version(filename)
    if is_trial:
        filename = filename.replace("v" + version, "v" + str(int(version) + 1))
    # Load the .pkl file
    with open(file_path, "rb") as file:
        data = pickle.load(file)

    # Check if the data is a dictionary and contains a DataFrame
    if isinstance(data, dict):
        data_frame = None
        other_objects = {}
        unwanted_keys = [
            "additional_annotation_dict",
            "additional_metrics_dict",
            "additional_dict",
            "additional_autoannotation_dict",
        ]

        for key, value in data.items():
            if isinstance(value, pd.DataFrame):
                data_frame = value
                if "subject_coach_srts" in data_frame.columns:
                    data_frame["subject_coach_srts"] = data_frame["subject_coach_srts"].apply(
                        subtitle_to_dict
                    )
                if "subject_driver_srts" in data_frame.columns:
                    data_frame["subject_driver_srts"] = data_frame["subject_driver_srts"].apply(
                        subtitle_to_dict
                    )
            elif key not in unwanted_keys:
                if key == "version_num":
                    if is_trial:
                        value += 1
                    else:
                        key = "filter_version_num"
                other_objects[key] = value

        output_filename = f"{directory}/{filename}"

        # Save DataFrame to Parquet file
        if data_frame is not None:
            data_frame.to_parquet(f"{output_filename}.parquet")
        else:
            print("No DataFrame found in the .pkl file.")

        # Save other objects to a YAML file
        if other_objects:
            save_objects_to_yaml(other_objects, f"{output_filename}.yaml")

        # Try loading the YAML file to ensure it was saved correctly
        try:
            load_file_from_yaml(f"{output_filename}.yaml")
        except Exception as e:
            print(f"GOT ERROR AT PATH {output_filename}.yaml:", str(e))

        try:
            df = check_the_content(f"{output_filename}.parquet")
        except Exception as e:
            print(f"An error occurred while processing the file: {e}")
    elif isinstance(data, pd.DataFrame):
        data.to_parquet(f"{directory}/{filename}.parquet")
    else:
        print("The file contains data in an unexpected format.")


# Main function to process all .pkl files in the provided directory
def main(directory):
    # Iterate through all .pkl files in the directory
    for path in directory.rglob("*.pkl"):
        print(f"Path:  {path}")
        paths.append(str(path))
        try:
            process_pkl_file(str(path))
        except Exception as e:
            print(e)
            continue

    # Save the list of processed file paths to a text file
    with open("all_paths.txt", "w") as f:
        f.write("\n".join(paths))


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python script.py <directory_path>")
        sys.exit(1)

    path = Path(sys.argv[1])
    # Record the start time
    start_time = time.time()

    if path.is_dir():
        main(path)
    elif path.is_file() and path.suffix == ".pkl":
        print(f"Path:  {path}")
        paths.append(str(path))
        try:
            process_pkl_file(str(path))
        except Exception as e:
            print(e)
    else:
        print(f"The provided path '{path}' is neither a directory nor a .pkl file.")

    # Record the end time
    end_time = time.time()

    # Calculate the elapsed time
    elapsed_time = end_time - start_time

    print(f"Time taken: {elapsed_time} seconds")
