"""
<PERSON><PERSON><PERSON> for processing the data from the instruction-following quick studies being started in Oct/Nov of 2024.
Ego vehicle position is extracted, as well as sound names (spoken audio) that are played for the driver.

Example invocation:
python create_quick_studies_24d13.py \
    --mcap-input-folder ~/Downloads/tri-hid-data-shared-autonomy/fm-data/quick_studies_participants/ \
    --compactsim-cache-folder ~/compactsim_cache --track-map-csv ~/path/to/directory_holding_tracks \
    --plot-animation false --is-multiple-mcaps-per-subject True \
    --trials-dir /home/<USER>/Data/instruction_following/example_log/trials
    --audio-file-dir /home/<USER>/fm-data/quick_study_instruction_following/audio_files

"""

import collections
import re
from pathlib import Path
from typing import Dict, List, Tuple

import librosa
import pandas as pd

from data_sources.compact_sim.dataloading import DATA_DICT_KEY_EPISODES, DATA_DICT_KEY_MAP
from data_sources.compact_sim.mcap_process_methods import (
    process_rosout,
    process_sound_name,
    process_speedometer,
)
from scripts.aic.create_and_annotate_aic_24d05 import parse_arguments
from scripts.filter_params import AIC_FILTERS_NO_ANIM, QUICK_STUDY_FILTERS_WITH_ANIM
from scripts.metric_params import AIC_METRICS
from util.filters.trajectory_filters import DATA_DICT_KEY_TRIAL_IDS, trials_by_laps
from util.mcap_util import make_timestamp_monotonic, read_mcap_topics_24_d_13
from util.trial import Trial


def get_audio_duration(file_path: Path) -> float:
    """Returns the duration of the audio file in seconds.

    Args:
        file_path (Path): Audio file path.

    Returns:
        float: Length of audio file in seconds.
    """
    try:
        duration = librosa.get_duration(path=file_path)
        return duration
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        raise e


def create_audio_to_duration_map(args: Dict) -> Dict:
    """Returns a dictionary with filenames as keys and duration in seconds as values.

    Args:
        args(dict): Command line arguments.

    Return:
        Dict: audio file to duration map
    """

    sound_directory = args["audio_file_dir"]
    audio_to_duration_dict = {}

    if sound_directory:
        audio_directory_path = Path(sound_directory)
        # Loop through all files in the directory
        for filename in audio_directory_path.iterdir():
            file_path: Path = filename.resolve()
            name = filename.name

            # Convert filename to sound name used in trail details.
            sound_name = name.replace(".mp3", "").replace("_", " ")

            # Process only MP3 files
            if file_path.exists() and name.lower().endswith(".mp3"):
                duration = get_audio_duration(file_path)
                audio_to_duration_dict[sound_name] = duration

    return audio_to_duration_dict


def update_sound_name_details(
    trial_data: pd.DataFrame, sound_name_details: Dict
) -> Tuple[pd.DataFrame, List]:
    """Update the sound name column by removing extra entries.

    Args:
        trial_data (pd.DataFrame): Trail data
        sound_name_details (Dict): Sound file (sound name) to duration map.
    Returns:
        Tuple (pd.DataFrame, sound_play_details): Updated trial data and sound played details.
    """
    sound_play_details = []

    stop_timestamp = None
    sound_name = ""
    for index, row in trial_data.iterrows():
        # (1) Detect the first occurrence of sound where sound_name column is not None.
        #     Set stop_timestep to the timestep that the sound finished and store the sound name.
        if pd.notnull(row["sound_name"]) and not stop_timestamp:
            sound_name = row["sound_name"]
            duration = sound_name_details[sound_name]
            stop_timestamp = row["timestamp"] + duration

            sound_play_details.append(
                {sound_name: (row["timestamp"], row["timestamp"] + duration)}
            )
        # (2) If we have a stop_timestamp, forward propagate the known sound name until that time is finished.
        elif stop_timestamp and row["timestamp"] < stop_timestamp:
            trial_data.at[index, "sound_name"] = sound_name
        # (3) Once we pass the end of the sound being played, delete stop timestamp, go back to (1)
        elif stop_timestamp and row["timestamp"] >= stop_timestamp:
            stop_timestamp = None

    return trial_data, sound_play_details


def extract_up_to_last_number(filename_in):
    # Use regex to capture everything up to the last underscore followed by digits
    match = re.match(r"^(.*)_[0-9]+$", filename_in)
    if match:
        return match.group(1)
    return filename_in


def get_map_for_trial(subject_pid, track_map_dict):
    track_map = None
    # Assign track map based on subject_PID
    if (
        "west" in subject_pid.lower()
        or "th" in subject_pid.lower()
        or "hill" in subject_pid.lower()
    ):
        track_map = track_map_dict["thunder_hill"]
        # print(f"Using Thunder Hill map")
    elif (
        "ws" in subject_pid.lower()
        or "willow" in subject_pid.lower()
        or "spring" in subject_pid.lower()
    ):
        track_map = track_map_dict["willow_springs"]
        # print(f"Using Willow Springs map")
    else:
        print(f"Cannot identify map for participant {subject_pid} -- Skipping")
    return track_map


def split_trial_data_for_each_sound_play(
    trial_data, sound_play_details, pre_window_length=2, post_window_length=8
):
    split_trial_details = []
    file_name_postfix = []
    sound_names = []

    for trial_details in sound_play_details:
        for sound_name, details in trial_details.items():
            start_time = details[0] - pre_window_length
            end_time = details[1] + post_window_length

            split_trial = trial_data[
                (trial_data["timestamp"] > start_time) & (trial_data["timestamp"] < end_time)
            ]
            split_trial_details.append(split_trial)

            post_fix = f"[{start_time},{end_time}]"
            file_name_postfix.append(post_fix)
            sound_names.append(sound_name)

    return split_trial_details, file_name_postfix, sound_names


def process_sound_names_quick_studies(msgs, **kwargs):
    """Processes sound_names in quick studies, putting a _None_ 0.1 seconds after each sound name"""
    data = collections.defaultdict(list)
    for m in msgs:
        data["sound_name"].append(m.ros_msg.data.replace("standard", "").replace("_", " ").strip())
        data["sound_name log time"].append(m.log_time_ns / 1e9)
        data["sound_name"].append(None)
        data["sound_name log time"].append((m.log_time_ns + 1e8) / 1e9)
    return dict(data)


def main(study_name="ifm_quick_studies"):
    args = parse_arguments()

    sound_name_to_duration_map = create_audio_to_duration_map(args)

    # extract all arguments corresponding to different data directories
    topics = ["/rosout", "/hero/sound_name", "/hero/ic/speedometer"]
    # these additional topics appear in teaching study and also possibly in Phase 3 of SDM
    additional_topics_dict = {
        "topics": topics,
        "topic_process_methods": {
            "/rosout": process_rosout,
            "/hero/sound_name": process_sound_names_quick_studies,
            "/hero/ic/speedometer": process_speedometer,
        },
    }

    (
        subject_pid_sorted_mcap_names,
        data_dict,
    ) = read_mcap_topics_24_d_13(args, study_name, additional_topics_dict=additional_topics_dict)
    annotation_data_dict = {}
    trials_dir = args["trials_dir"]
    Path(trials_dir).mkdir(parents=True, exist_ok=True)

    # TODO (andrew.silva) Maybe introduce the audio transcription piece?
    # transcriptions_dir = args["transcriptions_dir"]

    if args["plot_animation"]:
        auto_annotation_classes = QUICK_STUDY_FILTERS_WITH_ANIM
    else:
        auto_annotation_classes = AIC_FILTERS_NO_ANIM

    track_map_dict = data_dict[DATA_DICT_KEY_MAP]
    subject_pid_df_list = collections.defaultdict(list)
    # For each mcap we got back
    for ep_mcap_dir, ep_mcap_data in data_dict[DATA_DICT_KEY_EPISODES].items():
        # Get the directory (this should be a something like 'sim_bag_2024_08_31-10_09_39' (yyyy_mm_dd-hh_mn_sc))
        ep_mcap_dir = extract_up_to_last_number(ep_mcap_dir)
        # For our participant data directories
        for subject_pid, subject_directories in subject_pid_sorted_mcap_names.items():
            # For the participant's "sim_bag" directories
            for subject_directory in subject_directories:
                # subject_directory is ('sim_bag_name', num_mcaps), so get first element here:
                subject_sim_bag_dir = subject_directory[0]
                # If this mcap belongs to this participant, save it
                if ep_mcap_dir == subject_sim_bag_dir:
                    subject_pid_df_list[subject_pid].append(ep_mcap_data)

    # save the start carla object log time for each rosbag as a csv
    subject_trial_start_end_times_dict = collections.OrderedDict()
    # concatenate all dfs for subject to a single dataframe and process individual trials
    for subject_pid, subject_df_list in subject_pid_df_list.items():
        print(f"Processing subject {subject_pid}")
        subject_trial_start_end_times_dict[subject_pid] = collections.OrderedDict()
        # assume that subject_df_list is sorted
        subject_df = pd.concat(subject_df_list, ignore_index=True)

        trial_ids_dict = trials_by_laps(subject_df)

        if trial_ids_dict is None:
            # Note: trial_ids is empty if the corresponding directory is empty, or has been processed improperly
            # For example, if CARLA crashes and leaves an empty .mcap for a participant
            print(f"Subject {subject_pid} data dictionary is empty -- SKIPPING!")
            continue

        # for some subjects rosbag recording had to be restarted, hence there was a reset in timestamps
        # this needs to be made monotic and should track the rostime
        make_timestamp_monotonic(subject_df)

        annotation_data_dict[subject_pid] = {}
        annotation_data_dict[subject_pid][DATA_DICT_KEY_TRIAL_IDS] = trial_ids_dict[
            "annotation_df"
        ]
        all_trial_ids = annotation_data_dict[subject_pid][DATA_DICT_KEY_TRIAL_IDS]["trial_ids"]

        valid_trial_ids = [int(lap) for lap in list(set(all_trial_ids[all_trial_ids >= 0]))]
        # parse trials for the subject and run auto and manual annotations
        for trial_idx, valid_trial_id in enumerate(valid_trial_ids):
            subject_df_valid_trial_id = subject_df[all_trial_ids == valid_trial_id]

            sound_play_details = []
            if sound_name_to_duration_map:
                # Update sound name column details.
                subject_df_valid_trial_id, sound_play_details = update_sound_name_details(
                    subject_df_valid_trial_id, sound_name_to_duration_map
                )

            if args["split_trial_on_sound_name"]:
                (
                    split_trial_details,
                    file_name_post_fix,
                    sound_names,
                ) = split_trial_data_for_each_sound_play(
                    subject_df_valid_trial_id, sound_play_details
                )
            else:
                split_trial_details = [subject_df_valid_trial_id]
                file_name_post_fix = [""]
                sound_names = [""]

            trial_id = subject_pid + f"-trial_{trial_idx}"

            start_ros_time = subject_df_valid_trial_id["carla_objects log time"].iloc[0]
            end_ros_time = subject_df_valid_trial_id["carla_objects log time"].iloc[-1]

            subject_trial_start_end_times_dict[subject_pid][trial_idx] = {
                "carla_objects_time_start_end": (start_ros_time, end_ros_time)
            }

            for index, (audio_detail_df, post_fix, sound_name) in enumerate(
                zip(split_trial_details, file_name_post_fix, sound_names)
            ):
                trial = Trial(trial_id, dataframe=audio_detail_df)

                sub_dir = ""
                if args["split_trial_on_sound_name"]:
                    sub_dir = str(index) + "_" + sound_name.replace(" ", "_")

                # save trial data with all columns, incl. all objects
                trial.write(data_dir=trials_dir, sub_dir=sub_dir)
                # keep track of all the keys, because later we want to remove the carla objects keys
                all_keys_before_annotations_and_metrics = trial.get_dataframe().keys()
                track_map = get_map_for_trial(subject_pid, track_map_dict)
                if track_map is None:
                    continue
                auto_annotations = trial.run_auto_annotations(
                    auto_annotation_classes, track_map, {}
                )
                # trial_metrics = trial.run_compute_metrics(AIC_METRICS, track_map, {})

                name_suffix = "_" + post_fix + f"_{index}_" + "_with_all_annotations_and_metrics"
                # save trial data with all annotations and metrics computation, removing some keys
                trial.write(data_dir=trials_dir, name_suffix=name_suffix, sub_dir=sub_dir)


if __name__ == "__main__":
    main(study_name="ifm_quick_studies")
