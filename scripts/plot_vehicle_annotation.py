"""
This script processes and visualizes vehicle data, annotation data, and track data. 
It supports parsing and mapping data from multiple sources, creating spatial 
distributions, and generating combined visualizations of trial data.

Data Sources:
- Trial data can be downloaded from: 
  s3://tri-hid-data-shared-autonomy/24-D-05/trials_final/
- GPT-annotated CSVs for those trials are available at:
  s3://tri-hid-data-shared-autonomy/24-D-05/gpt_annotated_csvs_new/

Usage:
    python plot_vehicle_annotation.py --trial-dir path/to/trials 
                     --annotation-dir path/to/annotations 
                     --track-path path/to/track.csv 
                     [--marker-distinction]
                     [--map-segment segment_id]
                     [--anotation-file-tail _gpt_annotated.csv]
                     [--save-result]
                     [--save-as default.png]
                     [--viz-dir path/to/viz]
                     [--participant_ids P601 P602]
                     [--trial_numbers trial_1 trial_2]

Example:
    python plot_vehicle_annotation.py --trial-dir ./trials 
                     --annotation-dir ./annotations 
                     --track-path ./track.csv 
                     --marker-distinction 
                     --map-segment 1
                     --anotation-file-tail _gpt_annotated.csv
                     --save-result
                     --save-as default.png
                     --viz-dir path/to/viz
                     --participant_ids P601 P602
                     --trial_numbers trial_1 trial_2

"""

import argparse
import re
from pathlib import Path

import pandas as pd

from util.visualization.visualization import plot_annotated_spatial_distribution


def parse_arguments() -> argparse.Namespace:
    """
    Parse command-line arguments.

    Returns:
        argparse.Namespace: Parsed arguments including:
            - trial_dir (str): Root directory containing trial data parquet files.
            - annotation_dir (str): Directory containing annotation CSV files.
            - track_path (str): Path to the track CSV file.
            - marker_distinction (bool): Enable marker distinction for trials.
            - map_segment (int, optional): ID of a map segment to filter and plot.
            - annotation_file_tail(str, optional): Tail of the files to match.
            - save_result(bool): Saves the plot as a PNG file.
            - save_as(str): Filename to save the plot.
            - viz_dir(str): Directory to save the plot.
            - participant_ids(list): List of participants ID.
            - trial_numbers(list): List of trial numbers.
    """
    parser = argparse.ArgumentParser(
        description="Process and unify vehicle, annotation, and track data."
    )
    parser.add_argument(
        "--trial-dir",
        type=str,
        required=True,
        help="Directory containing parquet files describing vehicle data.",
    )
    parser.add_argument(
        "--annotation-dir",
        type=str,
        required=True,
        help="Directory containing annotation files where categories are defined.",
    )
    parser.add_argument(
        "--track-path", type=str, required=True, help="CSV file representing track data."
    )
    parser.add_argument(
        "--marker-distinction",
        action="store_true",
        help="Enable marker distinction to differentiate trials.",
    )
    parser.add_argument(
        "--map-segment",
        type=int,
        default=None,
        help="Map segment to the plot (if not provided, the entire track will be plotted).",
    )
    parser.add_argument(
        "--annotation-file-tail", default="_gpt_annotated.csv", help="Tail of the files to match."
    )
    parser.add_argument("--save-result", action="store_true", help="Saves the plot as a PNG file.")

    parser.add_argument(
        "--save-as",
        default="default.png",
        type=str,
        help='Filename to save the plot (default is "default.png").',
    )
    parser.add_argument(
        "--viz-dir",
        default=".",
        type=str,
        help="Directory where to save the plot (default is current directory).",
    )

    parser.add_argument(
        "--participant_ids",
        type=str,
        nargs="+",
        help="List of Participant IDs (space-separated, e.g., P601 P602 P603)",
    )

    # Argument for Trial Numbers
    parser.add_argument(
        "--trial_numbers",
        type=str,
        nargs="+",
        help="List of Trial Numbers (space-separated, e.g., trial_0 trial_1 trial_2)",
    )
    return parser.parse_args()


def remove_version_suffix(identifier: str) -> str:
    """
    Remove version suffix from an identifier.
    For example, 'trial_v2' would become 'trial'.

    Args:
        identifier (str): Identifier string.

    Returns:
        str: Identifier without the version suffix.
    """
    return re.sub(r"_v\d+$", "", identifier)


def create_file_mapping(
    dir1_path: Path, dir2_path: Path, annotation_file_tail: str, patterns: list = None
) -> list:
    """
    Creates a mapping between parquet files in dir1 and CSV files in dir2 based on common identifiers.
    The function reads the files and returns a list of tuples containing a pair of DataFrames:
    (vehicle_data, annotated_data).

    Args:
        dir1_path (Path): Path object of directory containing trial data parquet files.
        dir2_path (Path): Path object of directory containing annotation CSV files.
        annotation_file_tail (str): Tail of the files to match.
        patterns (list): patterns to match to.

    Returns:
        list: List of tuples [(vehicle_df, annotation_df), ...], where:
              vehicle_df (pd.DataFrame): DataFrame containing vehicle trial data.
              annotation_df (pd.DataFrame): DataFrame containing annotated categories.
    """

    # Mapping from identifiers to parquet file paths
    parquet_files = {}
    for item in dir1_path.iterdir():
        if item.is_dir() and patterns and item.name in patterns:
            # If it's a folder, look inside for the required parquet file
            for file in item.iterdir():
                if file.suffix == ".parquet" and file.name.endswith(
                    "with_all_annotations_and_metrics.trial.parquet"
                ):
                    identifier = remove_version_suffix(
                        file.stem.removesuffix("_with_all_annotations_and_metrics.trial")
                    )
                    parquet_files[identifier] = file
                    break
        elif (
            item.is_file()
            and item.suffix == ".parquet"
            and item.name.endswith("with_all_annotations_and_metrics.trial.parquet")
        ):
            # If the file is directly in dir1
            identifier = remove_version_suffix(
                item.stem.removesuffix("_with_all_annotations_and_metrics.trial")
            )
            parquet_files[identifier] = item

    # Mapping from identifiers to CSV file paths
    csv_files = {}
    for file in dir2_path.iterdir():
        if file.is_file() and file.suffix == ".csv" and file.name.endswith(annotation_file_tail):
            common_part = file.name.removesuffix(annotation_file_tail)
            csv_files[common_part] = file

    # Intersection of common identifiers
    common_parts = set(parquet_files.keys()) & set(csv_files.keys())

    # Create a mapping of common identifiers to pairs of DataFrames
    file_mapping = [
        (pd.read_parquet(parquet_files[key]), pd.read_csv(csv_files[key])) for key in common_parts
    ]

    return file_mapping


if __name__ == "__main__":
    args = parse_arguments()
    # Default to all PIDs and all trial numbers if none are provided
    participant_ids = args.participant_ids if args.participant_ids else []
    trial_numbers = (
        args.trial_numbers if args.trial_numbers else []
    )  # Empty list if no trial numbers provided

    trials_dir = Path(args.trial_dir)
    annotation_dir = Path(args.annotation_dir)

    if not participant_ids:
        # If no Participant IDs are provided, list all available PIDs
        participant_ids = list(
            set(item.name.split("-")[0] for item in trials_dir.iterdir() if item.is_dir())
        )

    if not trial_numbers:
        # If no trial numbers are provided, list all available trials for the participants
        trial_numbers = list(
            set(
                item.name.split("-")[1]
                for item in trials_dir.iterdir()
                if any(item.name.split("-")[0] == pid for pid in participant_ids)
            )
        )

    # Now process each combination of participant_id and trial_number
    patterns = [
        f"{participant_id}-{trial_number}"
        for participant_id in participant_ids
        for trial_number in trial_numbers
    ]

    mapping = create_file_mapping(trials_dir, annotation_dir, args.annotation_file_tail, patterns)
    track_map = pd.read_csv(args.track_path)
    viz_details = {
        "save_result": args.save_result,
        "save_as": args.save_as,
        "viz_dir": args.viz_dir,
    }
    plot_annotated_spatial_distribution(
        mapping,
        track_map,
        args.marker_distinction,
        map_segment_id=args.map_segment,
        viz_details=viz_details,
    )
