#!/usr/bin/env python3
# A run script to load/parse the data and run filters, visualizations, etc, in an IPython environment.
# Example invocation:
# python /home/<USER>/hid_common/scripts/run_data_environment.py --mcap-input-folder ~/Downloads/tri-hid-data-shared-autonomy/Participants/  --compactsim-cache-folder ~/intent/compactsim_cache --track-map-csv ~/Downloads/Thunderhill_data/track.csv --annotation-file ~/compactsim_cache/annotation_dict.pkl

import argparse
import pickle as pickle
from datetime import time
from pathlib import Path

import IPython
import matplotlib.pyplot as plt

from data_sources.compact_sim.dataloading import (
    DATA_DICT_KEY_EPISODES,
    DATA_DICT_KEY_MAP,
    load_and_cache_compactsim_data,
)
from data_sources.compact_sim.mcap_process_methods import (
    process_soss_namedlocation,
    process_soss_soundfinished,
    process_telemetry,
)
from util.common_args import common_dir_arguments, parse_args
from util.filters.trajectory_filters import (
    DATA_DICT_KEY_DECEL,
    DATA_DICT_KEY_LATLONG,
    DATA_DICT_KEY_MAP_SEGMENT_IDS,
    DATA_DICT_KEY_OVERTAKE,
    DATA_DICT_KEY_SPINOUT,
    DATA_DICT_KEY_TRIAL_IDS,
    annotate_lat_long_coordinates,
    detect_deceleration,
    detect_overtake,
    detect_spinout,
    mark_map_segment_ids,
    mark_trial_ids,
)
from util.utility import load_annotation, save_annotation
from util.visualization.visualization import visualize_timepoint


def parse_arguments(args=None):
    parser = parse_args(additional_arg_setters=(common_dir_arguments,))
    parser.add_argument(
        "--annotation-file",
        type=str,
        help="Annotation data to read / write (currently assumes there's only one).",
        default="~/compactsim_cache/annotation_dataframe.pkl",
    )

    parser.add_argument("-v", "--visualize", action="store_true", help="Visualize data statistics")

    result = parser.parse_args(args=args)
    return vars(result)


def main():
    args = parse_arguments()
    # extract all arguments corresponding to different data directories
    topics = ["/telemetry/velocity", "/soss/playatnamedlocation", "/soss/soundfinished"]
    # these additional topics appear in teaching study and also possibly in Phase 3 of SDM
    additional_topics_dict = {}
    additional_topics_dict["topics"] = topics
    additional_topics_dict["topic_process_methods"] = {
        "/telemetry/velocity": process_telemetry,
        "/soss/playatnamedlocation": process_soss_namedlocation,
        "/soss/soundfinished": process_soss_soundfinished,
    }

    data_dict = load_and_cache_compactsim_data(
        args,
        override=False,
        additional_topics_dict=additional_topics_dict,
        verbose=True,
    )
    track_map = data_dict[DATA_DICT_KEY_MAP]

    cache_folder = Path(args["compactsim_cache_folder"]).expanduser().resolve()

    annotation_data_dict = {}
    for mcap_name, subject_df in data_dict[DATA_DICT_KEY_EPISODES].items():
        print("SUBJECT ID ", mcap_name.split("_")[0])
        lat_long_coord_dict = annotate_lat_long_coordinates(subject_df, track_map)
        deceleration_result_dict = detect_deceleration(subject_df, track_map)
        spinout_result_dict = detect_spinout(subject_df, track_map)
        overtake_results = detect_overtake(subject_df, track_map, dist_threshold=8.0)
        trial_ids_dict = mark_trial_ids(subject_df)
        map_segment_ids_dict = mark_map_segment_ids(subject_df, track_map)

        annotation_data_dict[mcap_name] = {}
        annotation_data_dict[mcap_name][DATA_DICT_KEY_SPINOUT] = spinout_result_dict
        annotation_data_dict[mcap_name][DATA_DICT_KEY_LATLONG] = lat_long_coord_dict
        annotation_data_dict[mcap_name][DATA_DICT_KEY_OVERTAKE] = overtake_results
        annotation_data_dict[mcap_name][DATA_DICT_KEY_DECEL] = deceleration_result_dict
        annotation_data_dict[mcap_name][DATA_DICT_KEY_TRIAL_IDS] = trial_ids_dict
        annotation_data_dict[mcap_name][DATA_DICT_KEY_MAP_SEGMENT_IDS] = map_segment_ids_dict
        # save the trial marking annotation plot for verification
        plt.figure()
        plt.plot(subject_df["condition"].fillna("nan").values)
        plt.plot(trial_ids_dict["annotation_df"]["trial_ids"])
        plt.savefig(cache_folder.joinpath(f"{mcap_name} + .png"))

    # Save annotation dict into pkl
    save_annotation(args["annotation_file"], annotation_data_dict)
    annotation_data_dict2 = load_annotation(args["annotation_file"])

    # Pick a time point (that's valid in the dataframe) to visualize
    timepoint = 100
    visualize_timepoint(
        vehicle_data=subject_df,
        reference_track_data=track_map,
        timepoint=timepoint,
        params={
            "fps": 10,
            "anim_output_dir": "./anim",
            "time_window": 10,
            "display": True,
            "show_columns": None,
        },
    )

    # This is where we can access the data in a commandline interface

    IPython.embed(header="handle results in data_dict")


if __name__ == "__main__":
    main()
