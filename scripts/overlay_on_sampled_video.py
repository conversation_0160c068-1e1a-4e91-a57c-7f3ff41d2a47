"""Loop over all the csv files in "trials_csvs" folder. 
- Plot and generate speedometer video using details in csvs.
- Generate ffmpeg command for each original video to Overlay speedometer video over original video 
with white space padding at the bottom.

Usage Example:
    python scripts/overlay_on_sampled_video.py \
    --source-directory <snipped-video-directory> \
    --sink-directory <overlay-video-output-directory> \
    --extracted-trials-pkl <location-of-pickle-files-location> \

Note: we are not using multiprocess with subprocess module to run ffmpeg command. It is very slow.
To run all commands from file in parallel, we use bash script "ffmpeg-parallel.sh"
located under folder "bash_script".
"""

import argparse
import multiprocessing
import re
import time
from io import BytesIO
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from PIL import Image

from util.ffmpeg_utility import get_video_details_ffprobe
from util.visualization.visualization import save_mp4, subsample_dataframe


def create_speedometer(row: pd.Series, width: int, max_speed: int = 100) -> np.array:
    """Create a matplotlib plot and return the numpy array of generated plot.

    Args:
        row : Row of a dataframe which contains the details of plot to be generated.
        width: Width of the plot
        max_speed: Upper cap of speedometer. Defaults to 100.

    Returns:
        Numpy array: Array of generated plot.
    """
    # Create a figure with the specified dimensions
    fig, ax = plt.subplots(figsize=(width / 100.0, 0.2))  # Adjust size to match 1280x480 pixels

    # Create axes for the speedometer, throttle/brake, and steering
    ax_speedometer = fig.add_axes((0.1, 0.1, 0.25, 0.3))  # Adjust position and size
    ax_throttle_brake = fig.add_axes((0.4, 0.1, 0.25, 0.3))  # Adjust position and size
    ax_steering = fig.add_axes((0.7, 0.1, 0.25, 0.3))  # Adjust position and size

    # Plot speedometer
    ax_speedometer.barh(y=0.1, width=row["speedometer"], height=0.1, color="green")
    ax_speedometer.set_yticks([])
    ax_speedometer.set_xlim(0, max_speed)
    ax_speedometer.set_title("Speed", fontsize=10)

    # Plot throttle and brake
    ax_throttle_brake.barh(" ", np.maximum(0, row["throttle"]), color="blue")
    ax_throttle_brake.barh(" ", np.minimum(0, -1 * row["brake"]), color="red")
    ax_throttle_brake.axvline(0, color="black", linewidth=1)
    ax_throttle_brake.set_xlim(-1, 1)
    ax_throttle_brake.set_title("brake/throttle", fontsize=10)

    # Plot steering
    ax_steering.barh(" ", np.maximum(0, row["steering"]), color="blue", label="Speed")
    ax_steering.barh(" ", np.minimum(0, row["steering"]), color="red")
    ax_steering.axvline(0, color="black", linewidth=1)
    ax_steering.set_xlim(-1, 1)
    ax_steering.set_title("steering", fontsize=10)

    # Hide the main axis and set the background color
    ax.axis("off")

    # Save figure to a BytesIO buffer
    buf = BytesIO()
    fig.savefig(buf, format="png", bbox_inches="tight", pad_inches=0, transparent=True)
    buf.seek(0)

    # Open the PNG image with PIL
    img = Image.open(buf).convert("RGBA")

    # Convert the PIL image to a NumPy array
    img_array: np.array = np.array(img)

    plt.close(fig)  # Close the figure to free memory
    return img_array


def create_speedometer_video(
    input_video: str, input_pickle: str, video_details: Dict, overwrite: bool
) -> str:
    """Create a speedometer video and store

    Args:
        input_video (str): Input video file path.
        input_pickle (str): Pickle file path.
        video_details (Dict): Video details like width, height of frame, FPS, and number of frames.
        overwrite (bool): Overwrite the speedometer video if true else skipped.

    Returns:
        str: Generated video's file path.
    """
    output_video_name: str = input_video.replace(".mp4", "_speedo.mp4")

    output_speedo_file: Path = Path(output_video_name)
    if not output_speedo_file.is_file() or overwrite:
        width: int = video_details["width"]
        nframes: int = video_details["total_frames"]
        fps: float = video_details["fps"]

        # Define the regular expression pattern to match values between `[` and `]`
        pattern: str = r"\[(.*?)\]"

        # Find all matches using re.findall()
        matches: List = re.findall(pattern, input_video)

        lists_of_floats: List = [tuple(map(float, match.split(","))) for match in matches]

        start, end = lists_of_floats[0]
        start_delta, end_delta = lists_of_floats[1]

        start_time: float = start - start_delta
        end_time: float = end + end_delta

        df: pd.DataFrame = pd.read_pickle(input_pickle)["dataframe"]
        df["subs"] = df["subject_coach_srts"].apply(lambda obj: obj.content)

        df: pd.DataFrame = df[
            (df["carla_objects log time"] >= start_time)
            & (df["carla_objects log time"] <= end_time)
        ]
        df: pd.DataFrame = subsample_dataframe(df, fps)
        df: pd.DataFrame = subsample_dataframe(df, fps)

        num_rows, _ = df.shape

        stop_limit: int = min(num_rows, nframes)

        # Store all the numpy array of all the plotted images in this list.
        generated_plot_array_list: List = []

        frame_count: int = 0
        for index, row in df.iterrows():
            img_array: np.array = create_speedometer(row, width)
            generated_plot_array_list.append(img_array)
            frame_count: int = frame_count + 1
            if frame_count >= stop_limit:
                break

        save_mp4(generated_plot_array_list, output_video_name, fps)
    else:
        print("Skip generation of speedometer. File is already  exists!!!!")

    return output_video_name


def generate_overlay_video_cmd(
    input_video: str,
    speedometer_video: str,
    overlay_video_output_path: str,
    video_details: Optional[Dict] = None,
):
    """Create an overly video with h264 codec

    Args:
        input_video (str): Input video path.
        speedometer_video (str): speedometer video.
        overlay_video_output_path (str): overlay video output path.
        video_details (Dict): Video details like pixel format and bit rate
    """
    bit_rate: str = video_details.get("bit_rate")
    pix_fmt: str = video_details.get("pix_fmt")

    command: List = ["ffmpeg"]

    command.extend(
        [
            "-y",  # overwrite the video if exist
            "-i",
            input_video,
            "-i",
            speedometer_video,
            "-filter_complex",
            '"[1:v]format=rgba,colorchannelmixer=aa=0.9[plot];[0:v][plot]overlay=(W-w)/2:(H-h-50):format=auto"',
            "-c:v",
            "h264",
            "-codec:a",
            "copy",
        ]
    )

    if bit_rate:
        command.extend(["-b:v", bit_rate])
    if pix_fmt:
        command.extend(["-pix_fmt", pix_fmt])

    command.extend([overlay_video_output_path])

    return " ".join(command)


def process_single_video(input_dict: Dict) -> Dict:
    """Create an overlay video from single input file.

    Args:
        input_dict (Dict): Single video process input details

    Returns:
        Dict: Details required for FFMPEG overlay.
    """

    source_video_path: str = input_dict["source_video_path"]
    source_pickle_path: str = input_dict["source_pickle_path"]
    sink_video_path: str = input_dict["sink_video_path"]
    video_details: Dict = input_dict["video_details"]
    overwrite: bool = input_dict["overwrite"]

    speedometer_video_path: str = create_speedometer_video(
        source_video_path, source_pickle_path, video_details, overwrite
    )

    output_video_file: Path = Path(sink_video_path)
    if not output_video_file.is_file() or overwrite:
        overlay_video_cmd = generate_overlay_video_cmd(
            source_video_path, speedometer_video_path, sink_video_path, video_details
        )
    else:
        overlay_video_cmd = ""

    return {
        "source_video_path": source_video_path,
        "speedometer_video_path": speedometer_video_path,
        "sink_video_path": sink_video_path,
        "video_details": video_details,
        "overlay_video_cmd": overlay_video_cmd,
    }


def get_pickle_file_path_from_snipped_video_file_name(input_video_path: str) -> Tuple[str, str]:
    """Parse the input video file name to generate a pickle file name for processing the respective trial.

    Args:
        input_video_path (str): _description_

    Returns:
        Tuple[str, str]: return the pickle file path.
    """
    # All the pickle files ends with this postfix
    post_fix: str = "_v1_with_all_annotations_and_metrics.trial.pkl"

    # Extract the base name without extension
    base_name: str = Path(input_video_path).stem

    # Example: Extract the relevant part for initials
    # Assuming the format is consistent, get the part before the first underscore
    parts: List = base_name.split("_")
    initials: str = f"{parts[0]}-{parts[1][:5]}_{parts[1][5:]}"

    # Generate new path using the initials
    # Here we just add "_initials" before the file extension for demonstration
    source_pickle_dir: str = f"{initials}"
    pickle_file_name: str = f"{initials}{post_fix}"

    return source_pickle_dir, pickle_file_name


def generate_overlay_in_parallel(
    source_dir: str,
    sink_dir: str,
    extracted_trials_pkl: str,
    overwrite: bool,
):
    """Run all the videos in parallel and generate overlay video.

    Args:
        source_dir (str): Snipped video directory
        sink_dir (str): Output directory for overlay video.
        extracted_trials_pkl (str): Directory of pickle file for all the trials.
        overwrite (bool): overwrite the video if true else not overwrite.
    """
    Path("/tmp/sub1/sub2").mkdir(parents=True, exist_ok=True)

    video_details: Optional[Dict] = None
    fps: Optional[float] = None
    multiprocess_input_data: List = []

    source: Path = Path(source_dir)
    sink: Path = Path(sink_dir)
    # Loop over all files in the directory and generate inputs for multiprocess
    for video_file in source.glob("*.mp4"):
        if not video_file.name.endswith("_speedo.mp4"):
            source_video_path: str = str(source.joinpath(video_file))
            sink_video_path: str = str(sink.joinpath(video_file.name))

            (
                source_pickle_file_dir,
                source_pickle_file_name,
            ) = get_pickle_file_path_from_snipped_video_file_name(source_video_path)
            source_pickle_path: str = str(
                Path(extracted_trials_pkl)
                .joinpath(source_pickle_file_dir)
                .joinpath(source_pickle_file_name)
            )

            # All the snipped video has the same quality. So, Extract video details from single video.
            if not video_details:
                try:
                    video_details: Dict = get_video_details_ffprobe(source_video_path)
                    print(f"Video details: {video_details}")
                except Exception as e:
                    continue

            single_process_input = {
                "source_video_path": source_video_path,
                "source_pickle_path": source_pickle_path,
                "sink_video_path": sink_video_path,
                "video_details": video_details,
                "overwrite": overwrite,
            }

            multiprocess_input_data.append(single_process_input)

    ffmpeg_overlay_cmd_list: List = []
    start = time.time()

    TOTAL_TASKS: int = len(multiprocess_input_data)
    CPU_COUNT: int = multiprocessing.cpu_count() - 2

    PROCESSES: int = min(CPU_COUNT, TOTAL_TASKS)
    print(f"Number of workers: {PROCESSES}")

    with multiprocessing.Pool(processes=PROCESSES) as pool:
        # Map the worker function to the chunks
        for result in pool.imap_unordered(process_single_video, multiprocess_input_data):
            overlay_video_cmd: str = result["overlay_video_cmd"]
            source_video_file: str = result["source_video_path"]
            if overlay_video_cmd:
                ffmpeg_overlay_cmd_list.append(overlay_video_cmd)
                print(f"Complete Processing File: {source_video_file}")

    print(f"Total time taken for multiprocessing is {time.time() - start}")
    return ffmpeg_overlay_cmd_list


def parse_arguments():
    """
    Parse command-line arguments and return the parsed arguments.

    :return: Parsed arguments
    """
    # Create the parser
    parser = argparse.ArgumentParser(
        description="Generate FFMPEG commands to generate overlay videos for instruction effect labelling job."
    )

    # Define the command-line arguments
    parser.add_argument(
        "--source-directory",
        type=str,
        default="outputnew",
        help="Directory where the source files are located",
    )
    parser.add_argument(
        "--sink-directory",
        type=str,
        default="outputnew",
        help="Directory where the output files will be stored",
    )
    parser.add_argument(
        "--extracted-trials-pkl",
        type=str,
        default="extracted_trials_new",
        help="Name of the extracted trials pickle file",
    )

    parser.add_argument(
        "--ffmpeg-cmd-file-name",
        type=str,
        help="File conatins the list FFMPEG commands to create overlay videos",
    )

    parser.add_argument(
        "--overwrite",
        action="store_true",
        help="Overwrite the existing videos if true else skiped.",
    )

    # Parse the arguments
    all_args = parser.parse_args()

    return all_args


if __name__ == "__main__":
    args = parse_arguments()

    ffmpeg_overlay_cmd: List = generate_overlay_in_parallel(
        args.source_directory, args.sink_directory, args.extracted_trials_pkl, args.overwrite
    )

    with open(args.ffmpeg_cmd_file_name, "w") as f:
        for cmd in ffmpeg_overlay_cmd:
            f.write(f"{cmd}\n")
