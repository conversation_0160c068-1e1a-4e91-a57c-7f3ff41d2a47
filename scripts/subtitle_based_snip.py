"""Loop over all the pickle files in "trials_final" folder. 
Avoid reading first 3 and last 2 trials of each subject. Read all the trials details from pickle
and write those to respective csv files(PXXX-trial_XX_sub_times.csv).

Usage Example:
    python ./scripts/subtitle_based_snip.py \
	    --trials-dir <**trials_final** directory> \
	    --output-folder <folder path for csv output>
"""
import glob
import os
import pickle
import sys
from collections import defaultdict
from typing import Dict, List

import pandas as pd

from util.common_args import common_dir_arguments, parse_args


def parse_arguments(args=None):
    """
    Parse command-line arguments and return the parsed arguments.

    :return: Parsed arguments
    """
    parser = parse_args(additional_arg_setters=(common_dir_arguments,))
    parser.add_argument(
        "--output-folder", type=str, help="output folder for storing csv files", required=True
    )
    result = parser.parse_args(
        args=args,
    )
    return vars(result)


def extract_attributes(obj) -> pd.Series:
    """Extract values from object and return the pandas series to add columns to dataframe.
    Args:
        obj (str): Python object

    :return: Pandas Series
    """
    subtitle: str = obj.content
    start: int = obj.start
    end: int = obj.end
    return pd.Series([subtitle, start, end], index=["subtitles", "start", "end"])


def get_timedelta(obj) -> int:
    """Extract values from object and return total seconds calculated from object details.
    Args:
        obj (str): Python object

    :return: Integer
    """
    return obj.total_seconds()


def get_filtered_trials(main_dir) -> Dict:
    """For each subject remove first 3 and last 2 trial details because those files will not have meaningful data.
    Args:
        main_dir (str): Directory path contains the subject wise trial details.

    :return: (Dictionary) subject to trial maps.
    """
    # Dictionary to hold trial directories for each top-level directory
    trial_dirs: Dict = defaultdict(list)

    # Get the list of top-level directories
    try:
        top_level_dirs: List = [
            d for d in os.listdir(main_dir) if os.path.isdir(os.path.join(main_dir, d))
        ]
    except FileNotFoundError:
        print(f"Directory {main_dir} does not exist.")
        return

    subject_to_trial_map: Dict = {}
    # Process each top-level directory
    for top_level in top_level_dirs:
        experiment = top_level.split("-")[0]
        if experiment in subject_to_trial_map:
            current_list: List = subject_to_trial_map[experiment]
            current_list.append(top_level)
        else:
            subject_to_trial_map[experiment] = [top_level]

    for experiment, grouped_trial in subject_to_trial_map.items():
        grouped_trial.sort(key=lambda x: int(x.split("_")[1]))

        # Removing the first three and last two trials.
        grouped_trial_updated: List = grouped_trial[3:-2]
        subject_to_trial_map[experiment] = grouped_trial_updated

    total_trials: int = 0
    # Sort and get the last two trial directories for each top-level directory
    for experiment, grouped_trial in subject_to_trial_map.items():
        total_trials = total_trials + len(grouped_trial)

    print(f"Total collected trials: {total_trials}")

    return subject_to_trial_map


if __name__ == "__main__":
    args = parse_arguments()

    print("Generating CSVs ....")
    # Get all the necessary files
    file_pattern = "*_with_all_annotations_and_metrics.trial.pkl"
    matched_files = glob.glob(os.path.join(args["trials_dir"], "**", file_pattern), recursive=True)

    subject_to_trial_map = get_filtered_trials(args["trials_dir"])

    for file in matched_files:
        # Get the parent directory path
        parent_dir_path: str = os.path.dirname(file)

        # Extract the parent directory name
        parent_dir_name: str = os.path.basename(parent_dir_path)

        subject: str = parent_dir_name.split("-")[0]

        if parent_dir_name in subject_to_trial_map[subject]:
            with open(file, "rb") as f:
                data_dict: pd.DataFrame = pickle.load(f)["dataframe"]

            # Create a new column with subtitles instead of subtitle objects
            data_dict[["subtitles", "start", "end"]] = data_dict["subject_coach_srts"].apply(
                extract_attributes
            )
            data_dict: pd.DataFrame = data_dict[data_dict["subtitles"] != "NA"]
            # Make a column that marks changes
            data_dict["change"] = data_dict["subtitles"] != data_dict["subtitles"].shift(1)
            # Assign an id to the contiguos groups
            data_dict["group_id"] = data_dict["change"].cumsum()
            data_dict: pd.DataFrame = data_dict.drop(columns=["change"])

            # Group by block_id and get start and end times
            block_times: pd.DataFrame = (
                data_dict.groupby(["group_id", "subtitles"])
                .agg(
                    start_time=("carla_objects log time", "first"),
                    end_time=("carla_objects log time", "last"),
                    sub_start=("start", "first"),
                    sub_end=("end", "first"),
                )
                .reset_index(drop=False)
            )

            # Check if any snip details have duration greater than 10. Use of debugging.
            # block_times["diff_in_rostime"] = block_times["end_time"] - block_times["start_time"]
            # find_outlier: pd.DataFrame = block_times[block_times["diff_in_rostime"] > 10]

            block_times["subtitle_duration"] = (
                block_times["sub_end"] - block_times["sub_start"]
            ).apply(get_timedelta)
            block_times = block_times.drop(columns=["group_id", "sub_start", "sub_end"])
            block_times = block_times.dropna(subset=["subtitles"])
            # Rename the columns
            block_times: pd.DataFrame = block_times.rename(
                columns={
                    "subtitles": "coach_subtitle",
                    "start_time": "start time",
                    "end_time": "end time",
                }
            )

            try:
                # Save as CSV file
                csv_path: str = os.path.join(
                    args["output_folder"], f"{file.split('/')[-2]}_sub_times.csv"
                )
                block_times.to_csv(csv_path, index=False, encoding="utf-8")

            except Exception as e:
                print(f"Following exception occurred while processing {file} - {e}")
                sys.exit()

    print("All CSVs generated successfully..!")
