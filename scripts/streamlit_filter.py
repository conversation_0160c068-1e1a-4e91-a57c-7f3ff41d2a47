import argparse
import base64
from io import BytesIO
from pathlib import Path

import pandas as pd
import streamlit as st
from PIL import Image

from data_sources.thunderhill.data_readers import read_map_csv
from scripts.filter_params import DEFAULT_ANIM_FILTER, OVERTAKE_FILTER_1, SDM_TRAINING_FILTERS
from util.common_args import common_dir_arguments, parse_args
from util.filters.trajectory_filters import (
    DATA_DICT_KEY_DECEL,
    DATA_DICT_KEY_LATLONG,
    DATA_DICT_KEY_OUT_OF_BOUNDS,
    DATA_DICT_KEY_OVERTAKE,
    DATA_DICT_KEY_SPINOUT,
)
from util.trial import Trial

NUM_COLUMNS = 3
FILTERS = [
    DATA_DICT_KEY_OVERTAKE,
    DATA_DICT_KEY_SPINOUT,
    DATA_DICT_KEY_DECEL,
    DATA_DICT_KEY_OUT_OF_BOUNDS,
    DATA_DICT_KEY_LATLONG,
]

my_css = """
    <style>
        table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
        }
        th {
            background-color: #f2f2f2;
        }
        img {
            max-width: 400px;
            max-height: 400px;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
        td:first-child {
            width: 50px;  
        }
    </style>
"""


@st.cache_data(show_spinner=False)
def split_frame(input_df, rows):
    df = [input_df.loc[i : i + rows - 1, :] for i in range(0, len(input_df), rows)]
    return df


def get_first_frame(gif_path):
    """
    Extracts the first frame from a GIF and returns it as a BytesIO object.

    Parameters:
    - gif_path (str): Path to the input GIF file.

    Returns:
    - BytesIO: BytesIO object containing the first frame image.
    """
    with Image.open(gif_path) as gif:
        # Extract the first frame
        first_frame = gif.convert("RGBA").resize(gif.size)

        # Save the first frame to a BytesIO buffer
        buffer = BytesIO()
        first_frame.save(buffer, format="PNG")
        buffer.seek(0)
        return buffer


def display_filtered_gifs(trials_dir):
    st.set_page_config(layout="wide")
    annotation_class = SDM_TRAINING_FILTERS
    all_trials = Trial.read_all(trials_dir, auto_annotation_class=annotation_class)

    and_filter = st.sidebar.checkbox("AND between filters (Unchecked for OR)", value=True)

    selected_filters = [
        filter_type for filter_type in FILTERS if st.sidebar.checkbox(filter_type, value=False)
    ]

    filtered_trials = all_trials
    # Filter trials based on user-selected events
    if selected_filters:
        if and_filter:
            aggr_logic = all
        else:
            aggr_logic = any
        filtered_trials = []
        for trial in all_trials:
            # If a trial satisfy all/one-of the filters (has at least 1 true value over the timestamps).
            if aggr_logic(
                filter_key in trial.dataframe and any(trial.get_dataframe()[filter_key])
                for filter_key in selected_filters
            ):
                filtered_trials.append(trial)

    data = {"UID": [], "Tile View": []}

    for trial in filtered_trials:
        gif_path = trial.data["anim_path"]
        first_frame_buffer = get_first_frame(gif_path)
        contents = first_frame_buffer.getvalue()
        data_url = base64.b64encode(contents).decode("utf-8")
        tile_view = f"data:image/png;base64,{data_url}"

        data["UID"].append(trial.uid)
        data["Tile View"].append(tile_view)

    data_df = pd.DataFrame({"UID": data["UID"], "GIF preview": data["Tile View"]})

    bottom_menu = st.columns((4, 1, 1))
    with bottom_menu[2]:
        batch_size = st.selectbox("Page Size", options=[3, 5])
    with bottom_menu[1]:
        total_pages = len(data_df) // batch_size + (len(data_df) % batch_size > 0)
        current_page = st.number_input("Page", min_value=1, max_value=total_pages, step=1)
    with bottom_menu[0]:
        st.markdown(f"Page **{current_page}** of **{total_pages}** ")

    pages = split_frame(data_df, batch_size)
    data = pages[current_page - 1]
    df_html = data.to_html(
        escape=False, formatters={"GIF preview": lambda x: f'<img src="{x}">'}, index=False
    )
    # Display the HTML table
    st.markdown(my_css + df_html, unsafe_allow_html=True)


def parse_arguments(args=None):
    parser = parse_args(additional_arg_setters=(common_dir_arguments,))

    result = parser.parse_args(args=args)
    return vars(result)


def main():
    args = parse_arguments()

    display_filtered_gifs(args["trials_dir"])


if __name__ == "__main__":
    main()
