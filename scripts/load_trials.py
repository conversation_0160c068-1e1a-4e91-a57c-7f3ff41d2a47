"""
This script loads trials from a folder
Example:
    python scripts/load_trials.py  --trials-dir ~/data/shared_autonomy/trials/
"""
from pathlib import Path

from data_sources.thunderhill.data_readers import read_map_csv
from scripts.filter_params import (
    DEFAULT_ANIM_FILTER,
    MAP_SEGMENT_FILTER,
    OVERTAKE_FILTER_1,
    SDM_TRAINING_FILTERS,
)
from util.common_args import common_dir_arguments, parse_args
from util.trial import Trial


def parse_arguments(args=None):
    parser = parse_args(additional_arg_setters=(common_dir_arguments,))

    result = parser.parse_args(
        args=args,
    )
    return vars(result)


def main():
    args = parse_arguments()
    map_csv_filename = str(Path(args["track_map_csv"]).expanduser().resolve())
    track_map = read_map_csv(map_csv_filename)

    trials_dir = args["trials_dir"]

    annotation_class = SDM_TRAINING_FILTERS

    all_trials = Trial.read_all(trials_dir)
    if True:
        # Run auto annotations
        for trial in all_trials:
            trial.run_auto_annotations(annotation_class, track_map)

    # Animation path
    anim = all_trials[0].data["anim_path"]


if __name__ == "__main__":
    main()
