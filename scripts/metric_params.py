# Library for global variables pertaining to metric parameters
from util.metrics import (
    BrakingScoreMetric,
    FrictionUtilizationScore,
    OutOfBoundsMetric,
    RacingLineScoreMetric,
    SmoothnessScoreMetric,
    SteeringScoreMetric,
    ThrottleScoreMetric,
    TrialTimeMetric,
)

# Metrics
TRIAL_TIME_METRIC = {TrialTimeMetric: {"compute_per_segment": True}}
STEERING_SCORE_METRIC = {SteeringScoreMetric: {"compute_per_segment": True}}
RACING_LINE_SCORE_METRIC = {RacingLineScoreMetric: {"compute_per_segment": True}}
OOB_METRIC = {OutOfBoundsMetric: {"compute_per_segment": True}}
BRAKING_SCORE_METRIC = {BrakingScoreMetric: {"compute_per_segment": True}}
THROTTLE_SCORE_METRIC = {ThrottleScoreMetric: {"compute_per_segment": True}}
SMOOTHNESS_SCORE_METRIC = {SmoothnessScoreMetric: {"compute_per_segment": True}}
FRICTION_UTILIZATION_SCORE_METRIC = {FrictionUtilizationScore: {"compute_per_segment": True}}


AIC_METRICS = {
    **TRIAL_TIME_METRIC,
    **STEERING_SCORE_METRIC,
    **RACING_LINE_SCORE_METRIC,
    **OOB_METRIC,
    **BRAKING_SCORE_METRIC,
    **THROTTLE_SCORE_METRIC,
    **SMOOTHNESS_SCORE_METRIC,
    **FRICTION_UTILIZATION_SCORE_METRIC,
}
