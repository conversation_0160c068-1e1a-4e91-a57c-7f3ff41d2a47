#!/bin/bash

# Check if all required arguments are provided
if [ $# -ne 3 ]; then
    echo "Usage: $0 <MCAP_FOLDER_PATH> <OUTPUT_FOLDER> <RACETRACK_PATH>"
    exit 1
fi

MCAP_FOLDER_PATH="$1"
OUTPUT_FOLDER="$2"
RACETRACK_PATH="$3"

# Ensure paths ends with a slash for consistency
MCAP_FOLDER_PATH="${MCAP_FOLDER_PATH%/}/"
OUTPUT_FOLDER="${OUTPUT_FOLDER%/}/"

for file in "$MCAP_FOLDER_PATH"*; do
    if [ -f "$file" ]; then
        # Get the filename without the path
        mcap_filename=$(basename "$file")
        mcap_filepath=$(realpath "$file")
        # Get the filename without the extension
        subfolder_name="${mcap_filename%.*}"
        subfolder_path="$OUTPUT_FOLDER$subfolder_name"
        # Create a subdirectory with the same name as the file (without extension)
        mkdir -p $subfolder_path

        # Only run it if corresponding csv is not found
        if [ -f "$subfolder_path/$mcap_filename.csv" ]; then
            echo "$mcap_filename.csv already exists. Using that csv."
        else
            echo "Converting MCAP to csv for $mcap_filename..."
            # Use mcap_parser to convert mcap to csv (csv name defaults to mcap_data.csv, One can change it using --save_fn flag)
            python data_sources/compact_sim/mcap_parser.py --filename $mcap_filepath --save_fn $subfolder_path/$mcap_filename.csv
            echo "CSV generated successfully..!"
        fi

        # Run the snip script
        python scripts/snip_sim_bag.py --csv-file $subfolder_path/$mcap_filename.csv --output-folder $subfolder_path --mcap-file-path $mcap_filepath --track-file-path $RACETRACK_PATH

        # Split the main MCAP
        if [ ! -d "kappe-env" ]; then
            # Make a virtual env with python 3.10 as kappa is problematic with python 3.11
            echo "Creating kappe-env virtual environment"
            sudo apt install python3.10-venv
            python3.10 -m venv kappe-env
            source kappe-env/bin/activate
            pip install kappe
        else
            echo "kappe-env virtual environment found. Using the existing one."
            source kappe-env/bin/activate
        fi

        echo "Splitting the MCAP file $mcap_filename"
        kappe cut $mcap_filepath --config $subfolder_path/config.yaml --output $subfolder_path --overwrite=True
        echo "MCAP split for $mcap_filename successful..!"
        deactivate
    fi
done
