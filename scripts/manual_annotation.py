"""
This script loads all trials, render animation and people can set a break point and manually annotate them.
Example:
    python scripts/manual_annotation.py  --trials-dir ~/data/shared_autonomy/trials/
"""
import json
import logging
from pathlib import Path

from data_sources.thunderhill.data_readers import read_map_csv
from scripts.filter_params import DEFAULT_ANIM_FILTER, OVERTAKE_FILTER_1, SDM_TRAINING_FILTERS
from util.common_args import common_dir_arguments, parse_args
from util.trial import PlotAnimationAnnotation, Trial
from util.utility import str2bool


def parse_arguments(args=None):
    parser = parse_args(additional_arg_setters=(common_dir_arguments,))
    parser.add_argument("--load-existing-annotations", type=str2bool, default=False)

    result = parser.parse_args(
        args=args,
    )
    return vars(result)


def main():
    args = parse_arguments()
    map_csv_filename = str(Path(args["track_map_csv"]).expanduser().resolve())
    track_map = read_map_csv(map_csv_filename)

    trials_dir = args["trials_dir"]

    if Path("manual_annotation.json").exists():
        if args["load_existing_annotations"]:
            with open("manual_annotation.json", "r") as file:
                manual_annotation = json.load(file)
        else:
            logging.error(
                f"Found existing annotation file, "
                f"either delete it or load it (continue annotate) by using --load-existing-annotations"
            )
            return

    annotation_class = SDM_TRAINING_FILTERS

    all_trials = Trial.read_all(trials_dir, auto_annotation_class=SDM_TRAINING_FILTERS)
    part_trials = [(t.uid.split("_")[0], t) for t in all_trials]
    trials_group = {}
    for uid, trials in part_trials:
        if uid not in trials_group:
            trials_group[uid] = []

        trials_group[uid].append(trials)

    manual_annotation = {}
    participants = sorted(list(trials_group))

    for participant in participants:
        trials = trials_group[participant]
        annotation = manual_annotation[participant] = {}
        trials = sorted(trials, key=lambda x: int(x.uid.split("trial_")[-1]))
        for trial in trials:
            trial.run_auto_annotations(
                {PlotAnimationAnnotation: {"fps": 4, "dpi": 200}}, track_map
            )
            uid = trial.uid
            dataframe = trial.get_dataframe()
            overtake_df = dataframe[["timestamp", "start_scenario_name", "overtake"]]
            annotation[trial.uid] = {}

            # Do this to note the overtake timestamps
            # annotation[trial.uid]['overtake_timestamp'] = [3555, 4000]
            # Do this if it doesn't have overtakes
            # annotation[trial.uid]['overtake_timestamp'] = []

            # Set break point at the line below and execute annotation (lines above)
            a = 0

            with open("manual_annotation.json", "w") as outfile:
                json.dump(manual_annotation, outfile)


if __name__ == "__main__":
    main()
