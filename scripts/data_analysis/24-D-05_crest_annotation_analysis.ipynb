#%% md
## This is a jupyter notebook to compile some basic stats on the instruction category annotations. 
#### 1) Check whether all utterances have been annotated by the annotator
#### 2) Evaluate inter annotator agreement
#### 3) Understanding the distribution of instruction classes
#### 4) Understand intra class coefficient measures as Fleiss Kappa score. 
#%%
import pandas as pd
from pathlib import Path
import collections
import matplotlib.pyplot as plt
import numpy as np
import statsmodels
from statsmodels.stats.inter_rater import fleiss_kappa
#%%
annotation_folder_path = "/home/<USER>/Data/24-D-05/manual_annotation/"
annotation_file_paths = list(Path.iterdir(Path(annotation_folder_path)))
annotator_ids = [
    "private.us-east-1.311daa9188d174fb",
    "private.us-east-1.54d1c2e40e03d4df",
    "private.us-east-1.aa1918156fc0e326",
    "private.us-east-1.cc76526182a05c1d",
    "private.us-east-1.ecc34e76087853d4",
]
#%%
annotator_subkeys = []
num_utterances = 0
num_annotations = 0
num_valid_utterances = 0
for annotation_file_path in annotation_file_paths:
    # print(annotation_file_path.name)
    df = pd.read_csv(annotation_file_path)

    if len(annotator_subkeys) == 0:
        for k in df.keys():
            for aid in annotator_ids:
                if aid in k:
                    annotator_subkeys.append(k)
    valid_utterances = int(df.shape[0] - sum(df["coach_subtitle"].isna().values))
    num_valid_utterances += valid_utterances
    # print(df.shape[0], valid_utterances)
    num_utterances += df[annotator_subkeys].shape[0]
    # valid_annotations =
    num_annotations += df[annotator_subkeys].shape[0] * len(annotator_ids)
    if df[annotator_subkeys].isna().any().any():
        print("missing anno")

    if df[annotator_subkeys].isna().shape[0] != df.shape[0]:
        print(annotation_file_path.name)
#%%
all_annotations = collections.defaultdict(list)
total_rows = 0
for annotation_file_path in annotation_file_paths:
    # print(annotation_file_path.name)
    df = pd.read_csv(annotation_file_path)
    total_rows += df.shape[0]
    for aid in annotator_ids:
        utterances, ts, cs, ss = (
            df["coach_subtitle"].values,
            df[f"{aid}_Type"].values,
            df[f"{aid}_Category"].values,
            df[f"{aid}_Subcategory"].values,
        )

        for u, t, c, s in zip(utterances, ts, cs, ss):
            all_annotations[aid].append((u, (t, c, s)))


print("Number of annotations per annotator", [len(all_annotations[aid]) for aid in annotator_ids])
print("Number of utterances annotated", num_utterances)
for aid in annotator_ids:
    print("Number of annotations for", aid, len(set([a[1] for a in all_annotations[aid]])))
    if len(set([a[1] for a in all_annotations[aid]])) == 42:
        all_categories = set([a[1] for a in all_annotations[aid]])
#%% md
### Total number of annotations PER ANNOTATOR  = 22548 and it is equal to the total number of utterances (which is the sum of rows in all csvs). THIS MEANS THAT ALL UTTERANCES WERE ANNOTATED BY ALL ANNOTATORS

### There are a total of 42 unique categories (including other,other,other and unsure). 2 of the annotators have used up 41, another 2 have used up 42 and one annotator 40 of those categories. Some annotators have not used (other, other, other) and unsure. 
#%%
for aid in annotator_ids:
    assert num_utterances == len(all_annotations[aid])

agreement_ratio = []
threshold = 0.6
annotations_above_threshold = 0
utterance_annotations_above_threshold = []
for i in range(num_utterances):
    instance_annotations = [all_annotations[aid][i][1] for aid in annotator_ids]
    utterance = all_annotations[annotator_ids[0]][i][0]
    unique_annotations = set(instance_annotations)
    most_common_annotation = max(unique_annotations, key=instance_annotations.count)
    percentage_of_annotators_with_most_common_annotation = instance_annotations.count(
        most_common_annotation
    ) / len(annotator_ids)
    # num_time
    # print(percentage_of_annotators_with_most_common_annotation)
    if percentage_of_annotators_with_most_common_annotation >= threshold:
        annotations_above_threshold += 1
        utterance_annotations_above_threshold.append(
            (
                utterance,
                most_common_annotation,
                instance_annotations,
                percentage_of_annotators_with_most_common_annotation,
            )
        )
    # break
#%%
print(len(utterance_annotations_above_threshold), num_utterances)
print(len(utterance_annotations_above_threshold) / num_utterances)
#%% md
### For an agreement decision threshold of 0.6 (that is 3 out of 5 annotators annotated the example the same way), the number of annotations above that threshold is 22025 out of 22548. This is about 98% of all annotations. 
### THIS MEANS THAT ANNOTATORS MUST HAVE INTERPRETED THE INSTRUCTIONS AND WOULD HAVE IDENTIFIED THE PATTERNS IN THE EXAMPLES PROVIDED IN A CONSISTENT WAY. UNDER THE ASSUMPTION THAT THEY WERE NOT TALKING TO EACH OTHER DURING THE ANNOTATION PROCESS. 
#%%
num_instruction_annotation_above_threshold = 0
num_str_annotation_above_threshold = 0
for ua in utterance_annotations_above_threshold:
    # print(type(ua[0]))
    if type(ua[0]) != str:
        if np.isnan(ua[0]):
            continue
    num_str_annotation_above_threshold += 1
    if ua[1][0] == "instruction":
        num_instruction_annotation_above_threshold += 1

    # break
#%%
print(num_instruction_annotation_above_threshold / num_str_annotation_above_threshold)
print(num_instruction_annotation_above_threshold / len(utterance_annotations_above_threshold))
print(
    len(utterance_annotations_above_threshold),
    num_str_annotation_above_threshold,
    num_instruction_annotation_above_threshold,
)
# less than 200 above_thresh annotations are not str.
#%% md
### Out of the 22025 annotations that are above the 0.6 threshold, 21863 of the annotations are for utterances that are not nan. 15534 of the annotations have "instruction" as the type. This amounts to around 70% of the 22025 annotations (or 71% of the 21863 annotations)being instruction type. THIS MEANS THAT THERE ARE 15534 UTTERANCES WITH INSTRUCTION TYPE AND THEREFORE CAN BE USED FOR CONCURRENT FEEDBACK TRAINING
#%%
instruction_annotation_category_count = collections.Counter()
all_annotation_category_count = collections.Counter()
for ua in utterance_annotations_above_threshold:
    if type(ua[0]) != str:
        if np.isnan(ua[0]):
            continue
    all_annotation_category_count[ua[1]] += 1
    if ua[1][0] == "instruction":
        instruction_annotation_category_count[ua[1]] += 1
        # print(ua[0])
#%%
# print(annotation_category_count)
# Extract keys and values
def generate_bar_graph_for_categories(category_list, title="Instruction Category"):
    labels = list(category_list.keys())
    labels = [str(l) for l in labels]
    values = list(category_list.values())

    # # Create the bar plot
    plt.bar(labels, values)

    # Set x-tick labels
    plt.xticks(labels, rotation=90)

    # Add labels and title
    plt.xlabel("Categories")
    plt.ylabel("Counts")
    plt.title(f"Bar Graph of {title} Counts")
    plt.show()
#%%
generate_bar_graph_for_categories(instruction_annotation_category_count)
generate_bar_graph_for_categories(all_annotation_category_count, title="All categories")
#%% md
### The first bar graph is the distribution of different instruction classes from annotations that are above the decision threshold.. There is clear dominance of throttle control, followed by an equal level of lateral placement of car and steering related instrcution, There is also a good amount of "looking at a landmark" type instruction as well. Suprisingly, compared to throttle on and off, the number of braking instruction is limited (this is probably because on TH West, much of the track good be driven without explicit braking, but just by controlling the speed with throttle. 
### The second bar graph consider ALL categories including the non instruction categories. We also see that over 1.2k have been marked as (other, other, other). 
#%%
# fleiss kappa expects data in the form a 2D array with utterances as rows, annotation categories as columsn and each cell containing the number of annotators who
# annotated utterance i into category j

num_categories = len(all_categories)
all_categories = list(all_categories)
all_row_is = []
for i in range(num_utterances):
    instance_annotations = [all_annotations[aid][i][1] for aid in annotator_ids]
    index_of_annotator_annotation = [list(all_categories).index(ia) for ia in instance_annotations]
    row_i = np.array([0] * num_categories)
    for idx in index_of_annotator_annotation:
        row_i[idx] += 1
    all_row_is.append(row_i)


print(fleiss_kappa(np.vstack(all_row_is)))
#%% md
### The above cell computes the Fleiss Kappa score which is a form of Intrclass correlation coefficient for categorical variables. FOr this annotation set the Fleiss kappa score is about 89.8%. https://en.wikipedia.org/wiki/Fleiss%27_kappa 