"""
This script reads a csv file of simbag and groups data by non-zero speedometer values and creates pkl files for each group
Example:
    python scripts/snip_sim_bag.py --csv-file ~/data/shared_autonomy/tri-hid-data-shared-autonomy/24-D-05/dump/20240410T184724_SDM_JonG/sim_bag_2024_04_10-18_47_24/sim_bag_2024_04_10-18_47_24_0.mcap
"""

import argparse
import os
import pickle
import re
import subprocess
import sys

import matplotlib
import pandas as pd
import yaml

matplotlib.use("Agg")
from util.visualization.visualization import ego_centric_animation, save_mp4

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--csv-file",
        type=str,
        help="simbag file name after converting mcap to csv",
        required=True,
    )
    parser.add_argument(
        "--output-folder",
        type=str,
        help="name of the folder in which to store mp4s and mcaps",
        required=True,
    )
    parser.add_argument(
        "--mcap-file-path",
        type=str,
        help="MCAP file path",
        required=True,
    )
    parser.add_argument(
        "--track-file-path",
        type=str,
        help="path to the race-track file",
        required=True,
    )
    args = parser.parse_args()

    # Setting necessary variables
    data = pd.read_csv(args.csv_file)
    os.makedirs(args.output_folder, exist_ok=True)
    track = pd.read_csv(args.track_file_path)
    params = {
        "fps": 10,
        "show_racing_line": False,
        "plot_cones": True,
        "visualize_subtitles": False,
    }

    # Create a mask for non-zero values
    non_zero_mask = data["speedometer"] != 0

    # Use cumsum to assign group numbers to continuous non-zero values
    data["group"] = (non_zero_mask != non_zero_mask.shift()).cumsum()

    # Replace group numbers where speed is zero
    data.loc[data["speedometer"] == 0, "group"] = 0

    split_list = []

    print("Generating pickle files and MP4s...")
    # Separate dataframes for each group other than 0
    for group_num in data["group"].unique():
        if group_num != 0:
            group_df = data[data["group"] == group_num]
            split_list.append(
                {
                    "start": float(group_df["carla_objects log time"].iloc[0]),
                    "end": float(group_df["carla_objects log time"].iloc[-1]),
                    "name": f"group_{group_num}.mcap",
                }
            )
            file_name = f"group_{group_num}.pkl"
            file_path = os.path.join(args.output_folder, file_name)
            with open(file_path, "wb") as f:
                pickle.dump(group_df, f)

            # Create the animation and save as mp4
            try:
                frames = ego_centric_animation(group_df, track, params)
                save_mp4(frames, f"{args.output_folder}/group_{group_num}.mp4", params["fps"])
            except Exception as e:
                print(f"Exception caught - {e}")
                pass
    print("Pickle files and MP4s generated successfully..!")

    # Generate a config file for Kappe tool to snip the MCAP
    data = {"keep_tf_tree": False, "splits": split_list}
    file_path = os.path.join(args.output_folder, "config.yaml")

    # Write data to YAML file
    with open(file_path, "w", encoding="utf-8") as file:
        yaml.dump(data, file, allow_unicode=True)
    print("Config file for Kappe generated successfully..!")
