"""Generate FFMPEG snip command for each csv available in csv directory.

Usage Example:
    python ./scripts/snip_mp4.py \
        --videos-dir <directory contains full video of each subject> \
        --meta-dir <original video meta directory> \
        --csv-dir <directory contains csv for speedometer video> \
        --output-dir <sniped video output directory> \
        --ffmpeg-cmd-file-name <filename to write a ffmpeg commands> \
        --subject-list <comma separated list of subjects>

Note: we are not using multiprocess with subprocess module to run ffmpeg command. It is very slow.
To run all commands from file in parallel, we use bash script "ffmpeg-parallel.sh"
located under folder "bash_script".
"""

import multiprocessing
import os
import time
from typing import Dict, List

import pandas as pd
import yaml

from util.common_args import common_dir_arguments, parse_args

delta_N_pairs: List[List] = [[0.5, 10]]


def generate_ffmpeg_trim_cmd(
    input_filepath: str,
    rost1: float,
    rost2: float,
    ros_start_time: int,
    output_folder: str,
    output_filename: str,
) -> str:
    """Generate ffmpeg trip command and return the command string.

    Args:
        input_filepath (str): Input video file path.
        rost1 (float): ros start time.
        rost2 (float): ros end time .
        ros_start_time (int): ros reference time to calculate start and end time for video.
        output_folder (str): Location where the video file be stored.
        output_filename (str): Output filename.

    Return:
        FFMPEG command for triming the the input video.
    """
    output_file_path: str = os.path.join(output_folder, output_filename)

    # Get video aligned times from ros times
    t1: float = rost1 - ros_start_time
    t2: float = rost2 - ros_start_time

    command: List = ["ffmpeg"]

    video_filter: str = f"\"pad=iw:ih+100:0:0:color=white,drawtext=text='%{{pts\\:hms\\:{str(t1)}}}':fontfile=Arial.ttf:fontsize=32:fontcolor=black:x=10:y=10\""

    command.extend(
        [
            "-ss",
            f"{t1}",
            "-to",
            f"{t2}",
            "-i",
            f"{input_filepath}",
            "-vf",
            video_filter,
            "-c:v",
            "h264",
            "-c:a",
            "aac",
            f"{output_file_path}",
        ]
    )

    try:
        # Generate the command
        return " ".join(command)
    except Exception as e:
        print(f"Following error occured while processing - {e}")


def parse_comma_separated_values(value):
    """Parse comma separated command-line argumrnts and return list.

    :return: List of subject to be processed.
    """
    # Split the string by comma and strip any extra whitespace
    return [item.strip() for item in value.split(",")]


def parse_arguments(args=None):
    """
    Parse command-line arguments and return the parsed arguments.

    :return: Parsed arguments
    """
    parser = parse_args(additional_arg_setters=(common_dir_arguments,))
    parser.add_argument(
        "--videos-dir",
        type=str,
        help="Path to the directory containing main video files",
        required=True,
    )
    parser.add_argument(
        "--meta-dir",
        type=str,
        help="Path to the directory containing metadata of the videos",
        required=True,
    )
    parser.add_argument(
        "--csv-dir",
        type=str,
        help="Path to the directory containing CSVs obtained from the trials",
        required=True,
    )
    parser.add_argument("--output-dir", type=str, help="Output folder", required=True)

    parser.add_argument(
        "--ffmpeg-cmd-file-name",
        type=str,
        help="File conatins the list FFMPEG commands to snip files",
    )

    parser.add_argument(
        "--subject-list",
        type=parse_comma_separated_values,
        help="Comma seperated subject list.",
    )

    parser.add_argument(
        "--overwrite",
        action="store_true",
        help="Overwrite the existing sniped video if true else skiped.",
    )

    result = parser.parse_args(
        args=args,
    )
    return vars(result)


def generate_ffmpeg_commands_for_single_video(inputs: Dict) -> List[str]:
    """Generate ffmpeg trip command for each input video and its triming details
    and return the list command string.

    Args:
        input (dict): Input video details with triming details.

    Return:
        List of FFMPEG command for triming the the input video.
    """
    collected_cmds: List = []
    args: Dict = inputs["args"]
    video_file: str = inputs["video_file"]

    subject_id: str = video_file[:5]
    subject_id: str = subject_id.replace("_", "")
    video_file_path: str = os.path.join(args["videos_dir"], video_file)

    overwrite: bool = args["overwrite"]

    # Get the related metadata file to video and get the ros time corresponding to t=0
    for filename in os.listdir(args["meta_dir"]):
        if subject_id in filename:
            meta_file_path: str = os.path.join(args["meta_dir"], filename)
            with open(meta_file_path, "r") as file:
                yaml_data: yaml.SafeLoader = yaml.safe_load(file)
                ros_start_time: int = yaml_data["start_timestamp"]
            break

    # Iterate over all the CSVs obtained from the trials
    for filename in os.listdir(args["csv_dir"]):
        if subject_id in filename:
            tmp: str = filename.split("_")
            trial_id: str = f"{tmp[0][5:]}{tmp[1]}"  # Get PXXX-trial_N as subject ID
            csv_file_path: str = os.path.join(args["csv_dir"], filename)
            df: pd.DataFrame = pd.read_csv(csv_file_path)

            # Iterate over the rows of the csv
            for index, row in df.iterrows():
                rost1: float = row["start time"] - 0.5
                rost2: float = row["end time"] + 10

                output_filename: str = (
                    f"{subject_id}_{trial_id}_[{row['start time']},{row['end time']}]_[0.5,10].mp4"
                )

                output_txt_filename: str = output_filename.replace(".mp4", ".txt")

                output_file_path: str = os.path.join(args["output_dir"], output_filename)
                if not os.path.exists(output_file_path) or overwrite:
                    try:
                        with open(os.path.join(args["output_dir"], output_txt_filename), "w") as f:
                            # Write coach subtitle to text file
                            f.write(row["coach_subtitle"])
                    except Exception as e:
                        print(f"Following error occured while creating txt file - {e}")

                    cmd: str = generate_ffmpeg_trim_cmd(
                        video_file_path,
                        rost1,
                        rost2,
                        ros_start_time,
                        args["output_dir"],
                        output_filename,
                    )
                    collected_cmds.append(cmd)
                else:
                    # Don't create file if exists already
                    print(f"{output_file_path} already exists..!")

    return collected_cmds


if __name__ == "__main__":
    args = parse_arguments()

    subject_list = args["subject_list"]

    if len(subject_list) == 1 and subject_list[0].lower() == "all":
        include_all_subject = True
    # Store the multiprocess input details.
    multiprocess_inputs: List = []

    # Iterate over all the main video files and generating multiprocess input details.
    print("------------------------------------------")
    for video_file in os.listdir(args["videos_dir"]):
        for subject in subject_list:
            if video_file.startswith(subject) or include_all_subject:
                inputs: Dict = {"args": args, "video_file": video_file}
                print(f"Selected video file: {video_file}")
                multiprocess_inputs.append(inputs)
    print("------------------------------------------")

    TOTAL_TASKS: int = len(multiprocess_inputs)
    CPU_COUNT: int = multiprocessing.cpu_count() - 1

    PROCESSES: int = min(CPU_COUNT, TOTAL_TASKS)

    print("Creating pool with %d processes\n" % PROCESSES)

    # Store trim command for all video to this list.
    ffmpeg_snip_cmd_list: List = []

    start: int = time.time()
    with multiprocessing.Pool(PROCESSES) as pool:
        for cmd_list in pool.imap_unordered(
            generate_ffmpeg_commands_for_single_video, multiprocess_inputs
        ):
            ffmpeg_snip_cmd_list: List[str] = ffmpeg_snip_cmd_list + cmd_list

    with open(args["ffmpeg_cmd_file_name"], "w") as f:
        for cmd in ffmpeg_snip_cmd_list:
            f.write(f"{cmd}\n")

    print(f"Overall time taken: {time.time() - start}")
