"""Snip all the trials for selected/all the subjects.

Ths snipping process is in two-step.

1. Run this script and generate text file which contains all the snip commands.
2. Use ./bash_scripts/ffmpeg-parallel.sh to run all the commands from the file in parallel.

Usage Example:
    python ./scripts/snip_trials.py \
        --video-dir <Directory contains subject wise videos.> \
        --trials-dir <**trials_final** directory> \
        --ffmpeg-cmd-file-name <Text file name in which ffmpeg commands will be written. default: trial_snip_cmds.txt> \
        --pid-list <List of subjects. e.g. P1601,P1602 default: 'all'> \
        --output-folder <folder path for csv output default: snip_trials>
"""
import glob
import multiprocessing
import os
import re
import time
from pathlib import Path
from typing import Dict, List, Tuple

import yaml

from util.common_args import common_dir_arguments, parse_args
from util.trial import Trial


def parse_arguments():
    """
    Parse command-line arguments and return the parsed arguments.

    :return: Parsed arguments
    """
    parser = parse_args(additional_arg_setters=(common_dir_arguments,))
    parser.add_argument(
        "--output-folder",
        type=str,
        default="snip_trials",
        help="output folder for storing csv files",
    )

    parser.add_argument(
        "--video-dir",
        type=str,
        required=True,
        help="Path of directory contains the PID wise video details.",
    )

    parser.add_argument(
        "--pid-list",
        type=str,
        default="all",
        help="Comma seperated list of PIDs. e.g. P1601,P1602",
    )

    parser.add_argument(
        "--ffmpeg-cmd-file-name",
        type=str,
        default="trial_snip_cmds.txt",
        help="Text file name in which all the snip command will be written.",
    )

    result = parser.parse_args()
    return vars(result)


def generate_ffmpeg_trim_cmd(
    input_filepath: str,
    trial_start_rostime: float,
    trial_end_rostime: float,
    video_start_rostime: float,
    output_folder: str,
    output_filename: str,
) -> str:
    """Generate ffmpeg trip command and return the command string.

    Args:
        input_filepath (str): Input video file path.
        trial_start_rostime (float): first carla object log time of specific trial
        trial_end_rostime (float): last carla object log time of specific trial
        video_start_rostime (float): ros reference time to calculate start and end time for video.
        output_folder (str): Location where the video file be stored.
        output_filename (str): Output filename.

    Return:
        FFMPEG command for trimming the input video.
    """
    output_file_path: str = os.path.join(output_folder, output_filename)

    # Get video aligned times from ros times
    start_sec: float = trial_start_rostime - video_start_rostime
    end_sec: float = trial_end_rostime - video_start_rostime

    command: List = ["ffmpeg"]

    video_filter: str = f"\"drawtext=text='%{{pts\\:hms\\:{str(start_sec)}}}':fontfile=Arial.ttf:fontsize=32:fontcolor=black:x=10:y=10\""

    command.extend(
        [
            "-ss",
            f"{start_sec}",
            "-to",
            f"{end_sec}",
            "-i",
            f"{input_filepath}",
            "-vf",
            video_filter,
            "-c:v",
            "h264",
            "-c:a",
            "aac",
            f"{output_file_path}",
        ]
    )

    try:
        # Generate the command
        return " ".join(command)
    except Exception as e:
        print(f"Following error occurred while processing command : {command} - {e}")


def generate_ffmpeg_commands_for_single_trial(input_data: Tuple) -> str:
    """Generate ffmpeg snip command for single trial.

    Args:
        input_data (Tuple): Input data required to process single trial.

    Return:
        str: ffmpeg command string.
    """
    parquet_file: str = input_data[0]
    pid_to_video_details: Dict = input_data[1]
    pid_to_sub_pid_details: Dict = input_data[2]
    output_directory: str = input_data[3]

    cmd: str = ""

    # Get the PID from trial directory name.
    parent_dir_name: str = Path(parquet_file).parent.name
    pid: str = str(parent_dir_name).split("-")[0]

    # process only if pid is present in selected trials list.
    if pid in pid_to_sub_pid_details:
        trial_data = Trial().read(file_path=parquet_file)["dataframe"]
        trial_start_rostime: float = trial_data["carla_objects log time"].iloc[0]
        trial_end_rostime: float = trial_data["carla_objects log time"].iloc[-1]

        # When multiple videos available for single PIDs, Select appropriate video using video metadata details.
        sub_pids = pid_to_sub_pid_details[pid]
        for sub_pid in sub_pids:
            video_metadata = pid_to_video_details[sub_pid]["metadata"]
            video_start_rostime: float = video_metadata["start_timestamp"]
            video_end_rostime: float = video_metadata["final_timestamp"]

            if (
                trial_start_rostime >= video_start_rostime
                and trial_end_rostime < video_end_rostime
            ):
                trial_num = str(parent_dir_name).split("_")[1]
                out_file_name: str = (
                    f"{pid}_{trial_num}_[{trial_start_rostime}_{trial_end_rostime}].mp4"
                )
                video_file: str = str(pid_to_video_detail[sub_pid]["video_file"])
                cmd: str = generate_ffmpeg_trim_cmd(
                    video_file,
                    trial_start_rostime,
                    trial_end_rostime,
                    video_start_rostime,
                    output_directory,
                    out_file_name,
                )
                break
    return cmd


def process_trials_in_parallel(
    args: Dict, pid_to_video_map: Dict, pid_to_sub_pid_map: Dict
) -> None:
    """Process all the trials in parallel and generate snip commands. Write all the snip commands to text file.
    Args:
        args (dict): Dictionary of command line arguments.
        pid_to_video_map (dict): Dictionary contains video details for each PID
        pid_to_sub_pid_map (dict): Dictionary contains the list of sub PIDS for each PID

    Return:
        None
    """
    trial_dir: str = args["trials_dir"]
    output_dir: str = args["output_folder"]
    trial_dir_path: Path = Path(trial_dir)

    # Create output directory if not present.
    output_dir_path: Path = Path(output_dir)
    output_dir_path.mkdir(parents=True, exist_ok=True)

    # Get all the parquet files from all the trials folder.
    file_pattern = "*_with_all_annotations_and_metrics.trial.parquet"
    path_with_pattern: Path = trial_dir_path.joinpath("**").joinpath(file_pattern)
    matched_files: List = glob.glob(str(path_with_pattern), recursive=True)

    # Collect multiprocess inputs.
    multiprocess_inputs: List = []
    for parquet_file in matched_files:
        multiprocess_inputs.append(
            (parquet_file, pid_to_video_map, pid_to_sub_pid_map, output_dir)
        )

    TOTAL_TASKS: int = len(multiprocess_inputs)
    CPU_COUNT: int = multiprocessing.cpu_count() - 1

    PROCESSES: int = min(CPU_COUNT, TOTAL_TASKS)

    print("Creating pool with %d processes\n" % PROCESSES)

    # Store trim command for all trials to this list.
    ffmpeg_snip_cmd_list: List = []

    start: float = time.time()
    with multiprocessing.Pool(PROCESSES) as pool:
        for cmd in pool.imap_unordered(
            generate_ffmpeg_commands_for_single_trial, multiprocess_inputs
        ):
            if cmd:
                ffmpeg_snip_cmd_list: List[str] = ffmpeg_snip_cmd_list + [cmd]

    with open(args["ffmpeg_cmd_file_name"], "w") as f:
        for cmd in ffmpeg_snip_cmd_list:
            f.write(f"{cmd}\n")
    print(f"Total time taken: {time.time() - start}")


def load_yaml_file(yaml_file: Path) -> Dict:
    """Read the YAML file and return dictionary.

    Args:
        yaml_file (Path): yaml file location

    Return:
        Dict: dictionary of yaml file.
    """
    with open(yaml_file, "r") as file:
        data = yaml.safe_load(file)
    return data


def get_pid_to_video_details(args: Dict) -> Tuple:
    """Process all the trials in parallel and generate snip commands. Write all the snip commands to text file.
    Args:
        args (dict): Dictionary of command line arguments.

    Return:
        Tuple: Dictionary with PID to video details, Dictionary with PID to sub PIDs list.
    """

    selected_pids_str: str = args["pid_list"]
    video_directory: str = args["video_dir"]

    selected_pids = []
    if selected_pids_str != "all":
        selected_pids = selected_pids_str.split(",")

    pid_to_video_details_map = {}
    pid_to_sub_pid_map = {}

    video_dir_path = Path(video_directory)

    # Collect the pid wise video details.
    try:
        meta_file_suffix = "_videoonly_composite_meta.yaml"
        for d in video_dir_path.iterdir():
            video_details = {}

            # Search for PID (e.g. P1601)
            match = re.match(r"output_(P\d+)", d.name)

            # Process only if it is directory name and contains PID in its name.
            if match and d.is_dir():
                pid = match.group(1)

                # This will capture the multiple video details for same PID. e.g. P1602_a
                sub_pid = "_".join(d.name.split("_")[1:])

                video_details["sub_pid"] = sub_pid
                video_details["path"] = d.resolve()

                video_file = None
                metadata = None
                # Loop over the directory and collect video file name and video metadata details.
                for file in d.iterdir():
                    if file.is_file() and file.suffix.lower() == ".mp4":
                        video_file = file

                    if file.is_file() and file.name.endswith(meta_file_suffix):
                        metadata = load_yaml_file(file.resolve())

                video_details["video_file"] = video_file
                video_details["metadata"] = metadata

                # Filter PIDs if required.
                if (selected_pids and pid in selected_pids) or (selected_pids_str == "all"):
                    pid_to_video_details_map[sub_pid] = video_details
                    pid_to_sub_pid_map[pid] = pid_to_sub_pid_map.get(pid, []) + [sub_pid]

    except Exception as e:
        raise e

    # Raise and exit script if no video details are found in video directory.
    if not pid_to_video_details_map:
        raise KeyError("No matching video details for given PIDs are found in video directory.")

    return pid_to_video_details_map, pid_to_sub_pid_map


if __name__ == "__main__":
    cmd_args = parse_arguments()

    pid_to_video_detail, pid_to_sub_pid_detail_dict = get_pid_to_video_details(cmd_args)

    process_trials_in_parallel(cmd_args, pid_to_video_detail, pid_to_sub_pid_detail_dict)

    print("All snip commands are generated successfully..!")
