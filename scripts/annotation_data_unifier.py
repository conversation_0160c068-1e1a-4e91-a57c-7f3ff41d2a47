"""
Script: Annotation Data Unifier

Description:
This script processes annotated JSON files, unifies annotation data with trajectory data,
visualizes the unified data, and optionally merges videos of the original footage and the visualized trajectories.

Inputs:
  1. Annotated JSON files (stored in a specified input directory) containing structured data for analysis.
  2. Trial parquet files with trajectory data (specified via a trial directory).
  3. Track map CSV file to provide information about the track layout.
  4. Configuration options including video directory, output directory, visualization options, and more.

Outputs:
  1. Filtered trial data based on timestamp ranges, stored as parquet DataFrames in the output directory.
  2. Ego-centric visualization videos of the trajectory, saved in the output directory under the sub-directory 'ego_centric_viz'.
  3. Optionally, merged videos (original footage + visualization) saved under 'merge_viz'.

Example Script Run:
    python annotation_data_unifier.py
        --track_map_path test/resources/track.csv
        --trial_dir /path/to/trial_data
        --trial_version 2
        --output_dir /path/to/output
        --video_dir /path/to/video_download
        --fps 10
        --dpi 300
        --time_window 10
        --input_dir /path/to/annotated_jsons
        --max_worker 4
        --max_worker_viz 2
        --merge_viz
        --end_at_coaching    # OPTIONAL FLAG TO CHANGE TIME HORIZON FROM [-0.5, +10] TO [-time_window, END_OF_UTTERANCE]

Steps Performed by the Script:
1. Parse JSON Files Concurrently: Reads multiple JSON files to extract relevant S3 URIs that point to MP4 video files.
2. Parse S3 URI: Extracts metadata such as video ID, participant ID, and timestamps using a predefined regex pattern.
3. Generate Parquet File Path: Creates a path to the trial parquet file based on the parsed metadata.
4. Load and Filter Trajectory Data: Reads the trajectory data from the parquet file and filters it based on timestamp windows.
5. Visualize Trajectories (Optional): Uses visualization functions to create animations of vehicle trajectories and saves them as MP4 files.
6. Merge Visualizations with Original Video (Optional): Combines the visualization and original video side-by-side into a single output.

Assumptions:
1. The S3 URI format is strictly controlled and matches the expected pattern in `PATTERN`.
2. The input JSON files have a consistent structure where the required data can be extracted.
3. The trial parquet files contain the key 'dataframe' with a DataFrame representing trajectory data.
4. The environment has access to AWS resources if any files need to be downloaded via S3 (though this feature is currently commented out).
"""
import argparse
import json
import os

# Set the environment variable for ffmpeg
# os.environ["IMAGEIO_FFMPEG_EXE"] = "/usr/bin/ffmpeg"
import re
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
from pathlib import Path

import boto3
import pandas as pd
from moviepy import VideoFileClip, clips_array

from util.visualization.visualization import ego_centric_animation, save_mp4

# Compile the regex pattern for parsing the S3 URI
PATTERN = re.compile(
    r".*/(?P<video_id>[\w-]+)/(?P<participant_id>P\d+)_trial(?P<trial>\d+)_"
    r"\[(?P<start_timestamp>[\d.]+),(?P<end_timestamp>[\d.]+)\]_"
    r"\[(?P<prev_time_buffer>[\d.]+),(?P<post_time_buffer>[\d.]+)\]\.mp4"
)

DATA_DIR = "data"
EGO_CENTRIC_VIZ_DIR = "ego_centric_viz"
MERGE_VIZ_DIR = "merge_viz"


class AnnotationDataUnifier:
    """
    Class for unifying annotation data with trajectory data, parsing S3 URIs,
    filtering trial data and visualizing trajectories.
    """

    def __init__(self, s3_uri: str, config: dict):
        """
        Initialize the class with the given S3 URI and configuration.

        Args:
            s3_uri (str): S3 URI of the MP4 file.
            config (dict): Configuration parameters.
        """
        self.s3_uri = s3_uri
        self.config = config
        self.parsed_data = None
        self.trial_df = None
        self.filtered_trajectory_data = None
        self.track_map = pd.read_csv(self.config["track_map_path"])
        self.output_file_name = Path(self.s3_uri).name

        # self.session = boto3.Session(profile_name=self.config["aws_profile"])

    def parse_s3_uri(self):
        """
        Parse the S3 URI to extract metadata such as video ID, participant ID,
        trial number, and time buffers.
        """

        re_match = re.match(PATTERN, self.s3_uri)

        if not re_match:
            raise ValueError("Invalid S3 URI format")

        # Storing the parsed data
        self.parsed_data = {
            "video_id": re_match.group("video_id"),
            "participant_id": re_match.group("participant_id"),
            "trial": re_match.group("trial"),
            "start_timestamp": float(re_match.group("start_timestamp")),
            "end_timestamp": float(re_match.group("end_timestamp")),
            "prev_time_buffer": float(re_match.group("prev_time_buffer")),
            "post_time_buffer": float(re_match.group("post_time_buffer")),
        }

    def generate_parquet_path(self):
        """
        Generate the path to the parquet file based on parsed data.

        Returns:
            Path: The path to the trial parquet file.
        """
        if self.parsed_data is None:
            raise ValueError("No parsed data. Call parse_s3_uri first.")

        return (
            Path(self.config["trial_dir"])
            / f"{self.parsed_data['participant_id']}-trial_{self.parsed_data['trial']}"
            / f"{self.parsed_data['participant_id']}-trial_{self.parsed_data['trial']}_v{self.config['trial_version']}_with_all_annotations_and_metrics_with_instruction_category.trial.parquet"
        )

    def fetch_trajectory_data(self, start_time: float, end_time: float):
        """
        Fetch and filter the trajectory data based on start and end times.

        Args:
            start_time (float): Start timestamp (ROS timestamp).
            end_time (float): End timestamp (ROS timestamp).

        Returns:
            DataFrame: Filtered trajectory data.
        """
        if self.trial_df is None:
            raise ValueError("Trial dataframe is not loaded")
        filtered_df = self.trial_df.loc[
            (self.trial_df["carla_objects log time"] >= start_time)
            & (self.trial_df["carla_objects log time"] <= end_time)
        ]
        return filtered_df

    def parse_parquet_data(self, parquet_path: str):
        """
        Parse the parquet file to extract trial data and filter trajectory data.

        Args:
            parquet_path (str): Path to the parquet file.
        """
        try:
            self.trial_df = pd.read_parquet(parquet_path)

            if self.trial_df is not None:
                start_time, end_time = self.get_trajectory_window()

                self.filtered_trajectory_data = self.fetch_trajectory_data(start_time, end_time)

                output_path = (
                    Path(self.config["output_dir"])
                    / DATA_DIR
                    / f"filtered_{Path(self.output_file_name).stem}.parquet"
                )

                self.filtered_trajectory_data.to_parquet(output_path)
            else:
                raise ValueError(f"No 'dataframe' key found into the parquet file: {parquet_path}")
        except Exception as e:
            raise RuntimeError(f"Error parsing the parquet file: {e}")

    def get_trajectory_window(self):
        """
        Get the time window for trajectory filtering based on start and end times.

        Returns:
            tuple: Start and end times for the trajectory window.
        """
        if self.parsed_data is None:
            raise ValueError("No parsed data. Call parse_s3_uri first.")
        # If we don't want to end at the coaching, we are just getting the start/end times from annotation
        if not self.config["end_at_coaching"]:
            start_time = self.parsed_data["start_timestamp"] - self.parsed_data["prev_time_buffer"]
            end_time = self.parsed_data["end_timestamp"] + self.parsed_data["post_time_buffer"]
        else:
            # If we are ending at coaching, start is -time_window, end is utterance step 0
            start_time = self.parsed_data["start_timestamp"] - self.config["time_window"]
            end_time = self.parsed_data["end_timestamp"]

        return start_time, end_time

    def visualize_trajectory_video(self):
        """
        Visualize and save the trajectory animation as a video.
        """
        params = {
            "fps": self.config.get("fps", 5),
            "dpi": self.config.get("dpi", 300),
            "anim_suffix": "mp4",
            "plot_cones": self.config.get("plot_cones", True),
            "visualize_subtitles": self.config.get("visualize_subtitles", True),
            "show_racing_line": self.config.get("show_racing_line", True),
            "anim_output_dir": f"{self.config['output_dir']}/{EGO_CENTRIC_VIZ_DIR}/ego_centric_{self.output_file_name}",
            "time_window": self.config.get("time_window", 5),
        }

        frames = ego_centric_animation(self.filtered_trajectory_data, self.track_map, params)

        save_mp4(frames, params["anim_output_dir"], params["fps"])

    def get_the_s3_file(self):
        file_path = Path(f"{self.config['video_dir']}/{self.output_file_name}")
        object_path = self.s3_uri.replace("s3://" + self.config["bucket_name"] + "/", "")
        if file_path.exists():
            print(f"File {self.s3_uri} already downloaded. Skipping download.")
            return

        try:
            # Use the session to create an S3 client
            s3 = self.session.client("s3")

            # Download the file
            s3.download_file(self.config["bucket_name"], object_path, str(file_path))

            print(f"File {self.s3_uri} downloaded successfully.")
        except Exception as e:
            raise ValueError(f"Failed to download s3 file: {e}")

    def show_visualization_side_by_side(self):
        """
        Show and save two video clips (original and trajectory visualization) side by side.
        """
        video_path = f"{self.config['video_dir']}/{self.output_file_name}"
        trajectory_path = f"{self.config['output_dir']}/{EGO_CENTRIC_VIZ_DIR}/ego_centric_{self.output_file_name}"
        # Load both videos
        video_clip = VideoFileClip(video_path)
        trajectory_clip = VideoFileClip(trajectory_path)

        # Ensure both clips have the same height by resizing the trajectory clip
        trajectory_clip_resized = trajectory_clip.resize(height=video_clip.h)

        final_clip = clips_array([[video_clip, trajectory_clip_resized]])

        # Write the output to a file
        final_clip.write_videofile(
            f"{self.config['output_dir']}/{MERGE_VIZ_DIR}/merged_{self.output_file_name}"
        )

    def execute(self):
        """
        Execute the annotation data unification process, including parsing the S3 URI,
        loading the parquet data, visualizing the trajectory, and optionally merging videos.
        """
        self.parse_s3_uri()
        parquet_path = self.generate_parquet_path()
        self.parse_parquet_data(parquet_path)

        # self.get_the_s3_file()
        if self.config["merge_viz"]:
            with ProcessPoolExecutor(max_workers=self.config["max_worker_viz"]) as executor:
                future_ego = executor.submit(self.visualize_trajectory_video)
                future_ego.result()
                future_merge = executor.submit(self.show_visualization_side_by_side)
                future_merge.result()


def process_json_file(json_path: str):
    """
    Process a JSON file to extract data.

    Args:
        json_path (str): Path to the JSON file.

    Returns:
        dict: Data extracted from the JSON file.
    """
    try:
        with open(json_path, "r") as file:
            data = json.load(file)

            return data
    except json.JSONDecodeError as e:
        print(f"Error decoding the json file {json_path}: {e}")
    except Exception as e:
        print(f"Error reading the file {json_path}: {e}")


def process_json_files_concurrently(json_files: list, max_worker: int):
    """
    Processes a list of JSON files concurrently using a thread pool and extracts specific S3 URIs.

    Args:
        json_files (list): A list of paths to JSON files that need to be processed.
        max_worker (int): The maximum number of worker threads to use for concurrent processing.

    Returns:
        set: A set of unique S3 URIs extracted from the "imageSource" key in the "24-08-instruction-effect-400-data-objects"
             section of the JSON files. If an item does not have the required structure, it will be ignored.
    """
    video_source_list = []
    with ThreadPoolExecutor(max_workers=max_worker) as executor:
        futures = [executor.submit(process_json_file, json_file) for json_file in json_files]

        for future in as_completed(futures):
            data = future.result()

            if data:
                for item in data:
                    item = item.get("consolidatedAnnotation", {}).get("content", {})
                    job_id_key = list(item.keys())
                    if len(job_id_key) > 0:
                        job_id_key = job_id_key[0]
                        item = item[job_id_key]
                        video_source_list.append(item.get("imageSource", {}).get("s3Uri"))
                    else:
                        # If there is no key, the JSON didn't have content or a job_id, and it might be corrupted.
                        print("WARNING -- Annotation JSON data may be incomplete.")
    return set(filter(None, video_source_list))


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run Annotation Data Unifier.")
    parser.add_argument("--track_map_path", required=True, help="Path to the track CSV file.")
    parser.add_argument("--trial_dir", required=True, help="Path to the trial parquet files.")
    parser.add_argument(
        "--trial_version",
        type=int,
        required=True,
        help="Specify the version of the trial Parquet files to use. For example, '--trial_version 2' will target files named with '_v2.trial.parquet'",
    )
    parser.add_argument(
        "--output_dir", required=True, help="Directory to save visualized outputs."
    )
    parser.add_argument(
        "--video_dir", required=True, help="Directory where downloaded MP4s are stored."
    )
    parser.add_argument("--fps", type=int, default=5, help="Frames per second for animation.")
    parser.add_argument("--dpi", type=int, default=300, help="DPI for visualizations.")
    parser.add_argument(
        "--time_window", type=int, default=10, help="Time window for visualization."
    )
    parser.add_argument("--input_dir", required=True, help="Input directory of annotated JSON.")
    parser.add_argument("--merge_viz", action="store_true", help="Merge MP4s.")
    parser.add_argument(
        "--end_at_coaching",
        action="store_true",
        help="Change the time window from video time to [-time_window, end_of_coaching].",
    )

    parser.add_argument(
        "--max_worker", type=int, default=1, help="Maximum number of workers for processing data."
    )
    parser.add_argument(
        "--max_worker_viz",
        type=int,
        default=1,
        help="Maximum number of workers for generating visualizations.",
    )

    # parser.add_argument("--aws_profile", required=True, help="AWS profile name.")
    # parser.add_argument("--bucket_name", required=True, help="S3 bucket name.")
    args = parser.parse_args()

    if args.end_at_coaching:
        print(
            f"WARNING -- We will end trajectory data with coaching, so VIDEO TIMESTAMPS WILL NOT LINE UP "
            f"(in other words, any associated visualizations, images, video feeds, etc. will be wrong)."
        )

    config = vars(args)
    config["input_dir"] = str(Path(config["input_dir"]).expanduser().resolve())
    config["video_dir"] = str(Path(config["video_dir"]).expanduser().resolve())
    config["output_dir"] = Path(config["output_dir"]).expanduser().resolve()
    # Create data output directory
    (config["output_dir"] / DATA_DIR).mkdir(parents=True, exist_ok=True)

    directories = [DATA_DIR]

    if config["merge_viz"]:
        directories.extend([EGO_CENTRIC_VIZ_DIR, MERGE_VIZ_DIR])
        for directory in directories:
            (config["output_dir"] / directory).mkdir(parents=True, exist_ok=True)
    config["output_dir"] = str(config["output_dir"])

    input_path = Path(config["input_dir"])

    if not input_path.exists():
        print(f"The path {input_path} does not exist.")
        exit(1)

    if not input_path.is_dir():
        print(f"{input_path} is not a valid directory")

    else:
        # Find all .json files recursively
        json_files = list(input_path.rglob("*.json"))

        if not json_files:
            print(f"No JSON files found in {input_path}")

        video_source_set = process_json_files_concurrently(json_files, config["max_worker"])

        if video_source_set:
            with ThreadPoolExecutor(max_workers=config["max_worker"]) as executor:
                futures = [
                    executor.submit(AnnotationDataUnifier(video_source, config).execute)
                    for video_source in video_source_set
                ]

                for future in as_completed(futures):
                    future.result()
