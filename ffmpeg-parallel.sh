#!/bin/bash

# File containing ffmpeg commands
COMMAND_FILE="$1"
GPU_DEVICE="$2"

if [ -z "$COMMAND_FILE" ] 
then
  echo "Error :: Invalid Command line argument"
  echo " ------> Usage: $0 <command-file>"
  exit 1
fi

if [ -z "$GPU_DEVICE" ] 
then
   GPU_DEVICE=1
fi

# Number of parallel jobs (adjust according to your needs)
NUM_JOBS=40


echo "Input File: $COMMAND_FILE"
echo "Use GPU Device: $GPU_DEVICE"

FFMPEG_GPU_FLAGS="-hwaccel cuda -hwaccel_device $GPU_DEVICE"

echo "FFFMPEG Flags: $FFMPEG_GPU_FLAGS"

sed -i "s/ffmpeg/ffmpeg $FFMPEG_GPU_FLAGS/" $COMMAND_FILE

# Read the commands and run them in parallel
cat "$COMMAND_FILE" | xargs -d'\n' -P "$NUM_JOBS" -I {} bash -c '{}' 

echo "All commands are executed."
